#### **创建网存实例V2**

用于创建网存实例的优化版本接口，使用雪花算法ID生成，工厂方法创建实体，支持事务处理。支持批量创建，单次最多创建100个实例。

**接口地址**

> /openapi/netpadv2/net/storage/res/create

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名|示例值|参数类型|是否必填|参数描述|
|---|---|---|---|---|
|clusterCode|001|String|是|集群编码，标识实例所属集群|
|specificationCode|SPEC_001|String|是|规格代码，定义实例的硬件规格|
|imageId|IMG_123456|String|是|镜像ID，用于创建实例的系统镜像|
|number|5|Integer|是|实例数量，必须在1-100之间|
|storageSize|32|Integer|是|存储大小(GB)，必须大于0，支持：4,16,32,64,128,256|
|screenLayoutCode|LAYOUT_001|String|否|屏幕布局编码，非随机ADI模板且未传入ADI模板参数时必填|
|randomADITemplates|false|Boolean|否|是否随机选择ADI模板，默认false|
|realPhoneTemplateId|12345|Long|否|ADI模板ID，指定使用的ADI模板|
|countryCode|SG|String|否|国家编码，默认SG|

**响应参数**

| 参数名         | 示例值              | 参数类型     | 参数描述          |
| ----------- | ---------------- | -------- | ------------- |
| code        | 200              | Integer  | 状态码（200表示成功）  |
| msg         | success          | String   | 接口请求状态信息      |
| ts          | 1742536327373    | Long     | 时间戳           |
| data        | [ {...} ]        | Object[] | 创建结果列表        |
| ├─ padCode  | ACN1234567890123 | String   | 实例编码          |
| ├─ padName  | 网存实例001         | String   | 实例名称          |
| ├─ status   | 0                | Integer  | 实例状态          |

**请求示例**

```json
{
  "clusterCode": "001",
  "specificationCode": "SPEC_001",
  "imageId": "IMG_123456",
  "number": 5,
  "storageSize": 32,
  "screenLayoutCode": "LAYOUT_001",
  "randomADITemplates": false,
  "countryCode": "SG"
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN1234567890123",
      "padName": "网存实例001",
      "status": 0
    },
    {
      "padCode": "ACN1234567890124",
      "padName": "网存实例002", 
      "status": 0
    }
  ],
  "ts": 1742536327373
}
```

**错误码**

| 错误码    | 错误说明                       | 操作建议                       |
| ------ | -------------------------- | -------------------------- |
| 100000 | 请求参数不正确                    | 检查请求参数格式和必填字段             |
| 100000 | 请求参数不能为空                   | 确保请求体不为空                   |
| 100000 | 集群编码不能为空                   | 提供有效的集群编码                  |
| 100000 | 规格代码不能为空                   | 提供有效的规格代码                  |
| 100000 | 镜像ID不能为空                   | 提供有效的镜像ID                  |
| 100000 | 实例数量必须在1-100之间             | 调整实例数量到有效范围内               |
| 100000 | 存储大小必须大于0                  | 提供有效的存储大小                  |
| 100000 | 屏幕布局编码不能为空                 | 在非随机ADI模板模式下提供屏幕布局编码      |
| 110044 | 实例规格不存在                    | 确认规格代码是否正确，联系管理员确认规格配置     |
| 110041 | 镜像不存在                      | 确认镜像ID是否正确，检查镜像是否可用        |
| 110045 | 屏幕布局不存在                    | 确认屏幕布局编码是否正确               |
| 110099 | ADI模板不存在,请检查参数             | 确认ADI模板ID是否正确              |
| 220003 | 暂不支持当前存储规格,请参考文档设置         | 使用支持的存储规格：4,16,32,64,128,256GB |
| 220009 | 当前网存容量不足,请联系管理员            | 联系管理员增加网存容量或减少创建数量         |
| 110077 | 获取实例MAC失败，请重试              | 稍后重试或联系管理员                 |
| 500    | 系统异常                       | 联系管理员处理系统异常                |

