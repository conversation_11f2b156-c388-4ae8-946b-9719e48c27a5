package net.armcloud.paascenter.task.manager;

import net.armcloud.paascenter.common.redis.service.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * NoticeEdgeClusterManager 测试类
 * 
 * <AUTHOR>
 * @date 2025/08/05
 */
@ExtendWith(MockitoExtension.class)
class NoticeEdgeClusterManagerTest {

    @Mock
    private RedisService redisService;

    @InjectMocks
    private NoticeEdgeClusterManager noticeEdgeClusterManager;

    @BeforeEach
    void setUp() {
        // 设置环境变量
        ReflectionTestUtils.setField(noticeEdgeClusterManager, "springProfilesActive", "test");
    }

    @Test
    void testRecordNotificationFailure_FirstFailure() {
        // 准备测试数据
        String clusterAddress = "http://test-cluster:8080/api/notify";
        
        // Mock Redis 行为
        when(redisService.increment(anyString())).thenReturn(1);
        when(redisService.expire(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        
        // 执行测试
        int result = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
        
        // 验证结果
        assertEquals(1, result);
        verify(redisService, times(1)).increment(anyString());
        verify(redisService, times(1)).expire(anyString(), eq(1L), eq(TimeUnit.MINUTES));
    }

    @Test
    void testRecordNotificationFailure_MultipleFailures() {
        // 准备测试数据
        String clusterAddress = "http://test-cluster:8080/api/notify";
        
        // Mock Redis 行为 - 模拟第3次失败
        when(redisService.increment(anyString())).thenReturn(3);
        
        // 执行测试
        int result = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
        
        // 验证结果
        assertEquals(3, result);
        verify(redisService, times(1)).increment(anyString());
        // 第3次失败不会设置过期时间（只有第1次会设置）
        verify(redisService, never()).expire(anyString(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testRecordNotificationFailure_ThresholdExceeded() {
        // 准备测试数据
        String clusterAddress = "http://test-cluster:8080/api/notify";
        
        // Mock Redis 行为 - 模拟第6次失败，超过阈值
        when(redisService.increment(anyString())).thenReturn(6);
        when(redisService.hasKey(anyString())).thenReturn(false); // 预警间隔内未发送过预警
        doNothing().when(redisService).setCacheObject(anyString(), anyString(), anyLong(), any(TimeUnit.class));
        
        // 执行测试
        int result = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
        
        // 验证结果
        assertEquals(6, result);
        verify(redisService, times(1)).increment(anyString());
        // 验证预警间隔锁的设置
        verify(redisService, times(1)).setCacheObject(anyString(), eq("1"), eq(5L), eq(TimeUnit.MINUTES));
    }

    @Test
    void testRecordNotificationFailure_EmptyAddress() {
        // 测试空地址
        int result1 = noticeEdgeClusterManager.recordNotificationFailure("");
        int result2 = noticeEdgeClusterManager.recordNotificationFailure(null);
        int result3 = noticeEdgeClusterManager.recordNotificationFailure("   ");
        
        // 验证结果
        assertEquals(0, result1);
        assertEquals(0, result2);
        assertEquals(0, result3);
        
        // 验证没有调用Redis操作
        verify(redisService, never()).increment(anyString());
    }

    @Test
    void testGetFailureCount_ExistingCount() {
        // 准备测试数据
        String clusterAddress = "http://test-cluster:8080/api/notify";
        
        // Mock Redis 行为
        when(redisService.getCacheObject(anyString())).thenReturn("3");
        
        // 执行测试
        int result = noticeEdgeClusterManager.getFailureCount(clusterAddress);
        
        // 验证结果
        assertEquals(3, result);
        verify(redisService, times(1)).getCacheObject(anyString());
    }

    @Test
    void testGetFailureCount_NoCount() {
        // 准备测试数据
        String clusterAddress = "http://test-cluster:8080/api/notify";
        
        // Mock Redis 行为 - 返回null表示没有计数
        when(redisService.getCacheObject(anyString())).thenReturn(null);
        
        // 执行测试
        int result = noticeEdgeClusterManager.getFailureCount(clusterAddress);
        
        // 验证结果
        assertEquals(0, result);
        verify(redisService, times(1)).getCacheObject(anyString());
    }

    @Test
    void testGetFailureCount_EmptyAddress() {
        // 测试空地址
        int result1 = noticeEdgeClusterManager.getFailureCount("");
        int result2 = noticeEdgeClusterManager.getFailureCount(null);
        int result3 = noticeEdgeClusterManager.getFailureCount("   ");
        
        // 验证结果
        assertEquals(0, result1);
        assertEquals(0, result2);
        assertEquals(0, result3);
        
        // 验证没有调用Redis操作
        verify(redisService, never()).getCacheObject(anyString());
    }

    @Test
    void testRecordNotificationFailure_RedisException() {
        // 准备测试数据
        String clusterAddress = "http://test-cluster:8080/api/notify";
        
        // Mock Redis 异常
        when(redisService.increment(anyString())).thenThrow(new RuntimeException("Redis连接异常"));
        
        // 执行测试
        int result = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
        
        // 验证结果 - 异常情况下返回0
        assertEquals(0, result);
        verify(redisService, times(1)).increment(anyString());
    }
}
