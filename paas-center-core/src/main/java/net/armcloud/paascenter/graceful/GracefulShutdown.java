package net.armcloud.paascenter.graceful;

import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.rocketmq.configure.InitialLoadingConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.serviceregistry.Registration;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class GracefulShutdown  implements SmartLifecycle {

    @Autowired(required = false)
    private NacosServiceRegistry nacosServiceRegistry;

    @Autowired
    private InitialLoadingConsumer initialLoadingConsumer;

    @Autowired
    private Registration registration;

    private boolean isRunning = true; // 标记服务是否运行


    @Override
    public void stop(Runnable callback) {
        log.info("服务即将关闭，开始优雅下线...");

        //  先从 Nacos 注销服务**
        if (nacosServiceRegistry != null) {
            nacosServiceRegistry.deregister(registration);
            log.info("服务已从 Nacos 注销：{}", registration.getServiceId());
        }

        //  停止mq消息处理
        initialLoadingConsumer.shutdown();

        //  等待 Nacos 通知 Gateway 处理**
        try {
            Thread.sleep(5000); // 5秒等待时间，确保 Gateway 停止流量
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("服务下线完成，正在关闭...");
        callback.run(); // 通知 Spring 可以继续关闭 Web 服务器
    }

    @Override
    public boolean isRunning() {
        return isRunning;
    }

    @Override
    public void start() {
        isRunning = true;
    }

    @Override
    public void stop() {
        isRunning = false;
    }
}
