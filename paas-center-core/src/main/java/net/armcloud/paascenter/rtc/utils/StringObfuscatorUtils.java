package net.armcloud.paascenter.rtc.utils;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class StringObfuscatorUtils {
    /**
     * 混淆字符串
     *
     * @param str 要混淆的字符串
     * @return 混淆后的字符串
     */
    public static String obfuscate(String str, String accessKey) {
        byte[] strBytes = str.getBytes(StandardCharsets.UTF_8);
        byte[] keyBytes = accessKey.getBytes(StandardCharsets.UTF_8);
        byte[] obfuscatedBytes = new byte[strBytes.length];

        for (int i = 0; i < strBytes.length; i++) {
            obfuscatedBytes[i] = (byte) (strBytes[i] ^ keyBytes[i % keyBytes.length]);
        }

        return Base64.getEncoder().encodeToString(obfuscatedBytes);
    }

    /**
     * 还原字符串
     *
     * @param str 要还原的字符串
     * @return 还原后的字符串
     */
    public static String deobfuscate(String str, String accessKey) {
        byte[] obfuscatedBytes = Base64.getDecoder().decode(str);
        byte[] keyBytes = accessKey.getBytes(StandardCharsets.UTF_8);
        byte[] deobfuscatedBytes = new byte[obfuscatedBytes.length];

        for (int i = 0; i < obfuscatedBytes.length; i++) {
            deobfuscatedBytes[i] = (byte) (obfuscatedBytes[i] ^ keyBytes[i % keyBytes.length]);
        }

        return new String(deobfuscatedBytes, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
        String finalStr  = null;
        String origin = "MjdHXApAREVcHFodVi4sVV5VHVNfXwgHCgJxcQ==";
        if(StringUtils.isBlank(origin)){
            finalStr = "";
        }else {
            finalStr = StringObfuscatorUtils.deobfuscate(origin, "AC22030022631");
        }

        System.err.println("stun:stun.l.google.com:19302".equals(finalStr));
    }
}