package net.armcloud.paascenter.rtc.service.strategy.room.model.bo;

import lombok.Data;

import java.util.List;

@Data
public class ApplyShareTokenBO {
    private Long customerId;

    private String userId;

    private String terminal;

    private Boolean pushPublicStream = false;

    private Integer expire = 86400;

    private String streamVersion;

    private List<Pad> pads;

    @Data
    public static class Pad {
        private String padCode;

        private VideoStream videoStream;

        @Data
        public static class VideoStream {
            private String resolution;
            private String frameRate;
            private String bitrate;
        }
    }
}
