package net.armcloud.paascenter.rtc.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.entity.paas.PadRtcTokenLog;
import net.armcloud.paascenter.rtc.mapper.PadRtcTokenLogMapper;
import net.armcloud.paascenter.rtc.service.IPadRtcTokenLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class PadRtcTokenLogServiceImpl extends ServiceImpl<PadRtcTokenLogMapper, PadRtcTokenLog> implements IPadRtcTokenLogService {

    @Resource
    private PadRtcTokenLogMapper padRtcTokenLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveTokenLog(RoomTokenDTO dto, PadRoom padRoom, String roomToken, Integer publishStream) {
        PadRtcTokenLog tokenLog = new PadRtcTokenLog();
        tokenLog.setRoomId(padRoom.getId());
        tokenLog.setPadCode(dto.getPadCode());
        tokenLog.setUserId(dto.getUserId());
        tokenLog.setExpire(dto.getExpire());
        tokenLog.setRoomToken(roomToken);
        tokenLog.setPublishStream(publishStream);
        return padRtcTokenLogMapper.insert(tokenLog) > NumberConsts.ZERO;
    }
}
