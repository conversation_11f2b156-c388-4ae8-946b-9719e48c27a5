package net.armcloud.paascenter.rtc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.dto.rtc.ApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.ManageApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.vo.rtc.DissolveRoomVO;
import net.armcloud.paascenter.common.model.vo.rtc.RefreshRoomTokenVO;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.dto.ApplyShareTokenSDKDTO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.dto.BatchApplyTokenDTO;

import java.util.List;

public interface IPadRoomService extends IService<PadRoom> {
    /**
     * @param customerId   客户ID
     * @param appId        rtc应用id
     * @param appKey       rtc应用key
     * @param roomTokenDTO roomTokenDTO
     * @return sdk 与 rtc 通信token
     */
    RoomTokenVO getRoomRtcTokenService(String validaToken, Long customerId, String appId, String appKey, RoomTokenDTO roomTokenDTO);

    /**
     * 刷新token
     *
     * @param oldToken String
     * @return RefreshRoomTokenVO
     */
    RefreshRoomTokenVO refreshTokenService(String appId, String appKey, String oldToken);

    /**
     * 解散房间
     *
     * @param padCodes 云机编号
     * @return 解散房间 停止rtc推流
     */
    DissolveRoomVO dissolveRoomService(Long customerId, List<String> padCodes);

    /**
     * 批量申请rtc token
     */
    List<RoomTokenVO> batchApplyToken(long customerId, BatchApplyTokenDTO roomTokenDTO);

    /**
     * 申请共享房间TOKEN
     */
    RoomTokenVO applyShareToken(long customerId, ApplyShareTokenDTO dto);

    /**
     * SDK申请火山共享房间TOKEN
     */
    RoomTokenVO applyShareSDKToken(Long customerId, String validaToken, ApplyShareTokenSDKDTO dto);

    /**
     * 管理后台申请共享房间TOKEN
     */
    RoomTokenVO manageApplyShareToken(ManageApplyShareTokenDTO dto);
}
