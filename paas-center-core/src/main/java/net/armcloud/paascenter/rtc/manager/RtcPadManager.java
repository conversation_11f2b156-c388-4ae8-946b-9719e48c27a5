package net.armcloud.paascenter.rtc.manager;

import net.armcloud.paascenter.common.client.internal.vo.PadInfoVO;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.rtc.mapper.RtcPadMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static net.armcloud.paascenter.rtc.exception.code.RtcExceptionCode.NO_STREAMING_PERMISSION_INSTANCE;

@Component
public class RtcPadManager {
    private final RtcPadMapper rtcPadMapper;
    private final IPadService padService;

    public PadInfoVO getPodInfo(String padCode) {
        return padService.getPadInfoByCode(padCode);
    }

    public void verifyPermissions(List<String> padCodes, long customerId) {
        List<Pad> pads = rtcPadMapper.listByPadCodes(padCodes);
        if (padCodes.size() != pads.size()) {
            throw new BasicException(NO_STREAMING_PERMISSION_INSTANCE);
        }

        boolean customerNoPermission = pads.stream().anyMatch(pad -> !Objects.equals(pad.getCustomerId(), customerId));
        if (customerNoPermission) {
            throw new BasicException(NO_STREAMING_PERMISSION_INSTANCE);
        }
    }

    public RtcPadManager(RtcPadMapper rtcPadMapper,IPadService padService) {
        this.rtcPadMapper = rtcPadMapper;
        this.padService = padService;
    }

}
