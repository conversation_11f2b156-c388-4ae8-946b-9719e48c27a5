package net.armcloud.paascenter.rtc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RtcPadRoomMapper extends BaseMapper<PadRoom> {
    List<PadRoom> listByPadCode(@Param("padCodes") List<String> padCodes);
}
