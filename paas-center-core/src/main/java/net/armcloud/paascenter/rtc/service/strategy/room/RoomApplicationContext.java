package net.armcloud.paascenter.rtc.service.strategy.room;

import net.armcloud.paascenter.common.core.exception.BasicException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class RoomApplicationContext {
    private static final Map<Integer, String> STREAM_TYPE_REF_BEAN_NAME_MAP = new HashMap<>();
    @Autowired
    private  ApplicationContext applicationContext;

    public static void putBeanName(int streamType, String beanName) {
        STREAM_TYPE_REF_BEAN_NAME_MAP.put(streamType, beanName);
    }

    public IRoomStrategy getInstance(int streamType) {
        String beanName = STREAM_TYPE_REF_BEAN_NAME_MAP.get(streamType);
        if (StringUtils.isBlank(beanName)) {
            throw new BasicException();
        }

        return (IRoomStrategy) applicationContext.getBean(beanName);
    }

}
