package net.armcloud.paascenter.rtc.service.strategy.room.model.dto;

import net.armcloud.paascenter.common.model.dto.rtc.ApplyShareTokenDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class ApplyShareTokenSDKDTO extends ApplyShareTokenDTO {
    @NotBlank(message = "uuid cannot not null")
    private String uuid;
}
