package net.armcloud.paascenter.rtc.aspect;

import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.http.PaaSHttpUtils;
import net.armcloud.paascenter.rtc.annotation.PaaSToken;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

/**
 * paas 签名验证 aop
 */
@Slf4j
@Aspect
@Component
public class PaaSTokenAspect {
    @Resource
    private RedisService redisService;

    @Before("@annotation(paaSToken)")
    public void validatePaaSToken(PaaSToken paaSToken) {
        // 获取请求的 HttpServletRequest 对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        HttpServletRequest request;
        if (attributes == null) {
            throw new BasicException(BasicExceptionCode.UNABLE_OBTAIN_HTTP_SERVLET_REQUEST_OBJECT);
        }

        request = attributes.getRequest();
        Map<String, String> headerMap = PaaSHttpUtils.getAllHeaders(request);
        if (headerMap != null && headerMap.containsKey(Constants.TOKEN)) {
            String validaToken = headerMap.get(Constants.TOKEN);

            String key = RedisKeyPrefix.SDK_TOKEN + validaToken;
            String token = redisService.getCacheObject(key);
//            log.info("validaToken={}", validaToken);
            if (Objects.isNull(token)) {
                throw new BasicException(BasicExceptionCode.INVALID_TOKEN);
            }

            String[] tokenArray = token.replace("\"" ,"").split(Constants.UNDERLINE);
//            log.info("tokenArray={}", tokenArray);
            if (tokenArray.length < NumberConsts.ONE) {
                throw new BasicException(BasicExceptionCode.INVALID_TOKEN);
            }

            //验证token
            if (!validaToken.equals(tokenArray[NumberConsts.ONE])) {
                throw new BasicException(BasicExceptionCode.VERIFICATION_TOKEN_FAILED);
            }
            request.setAttribute("customerId", tokenArray[NumberConsts.ZERO]);
        } else {
            throw new BasicException(BasicExceptionCode.REQUEST_HEADER_MISSING_NECESSARY_PARAMETER_TOKEN);
        }
    }


}
