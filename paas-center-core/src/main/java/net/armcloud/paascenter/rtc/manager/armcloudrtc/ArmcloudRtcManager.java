package net.armcloud.paascenter.rtc.manager.armcloudrtc;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.client.armcloudrtc.dto.ApplyEndpointDTO;
import net.armcloud.paascenter.common.client.armcloudrtc.stub.ArmcloudRTCEndpointFeignStub;
import net.armcloud.paascenter.common.client.armcloudrtc.vo.ApplyEndpointVO;
import net.armcloud.paascenter.common.utils.FeignUtils;
import net.armcloud.paascenter.rtc.config.ArmcloudRTCAppConfiguration;
import net.armcloud.paascenter.rtc.manager.armcloudrtc.component.ArmcloudAccessToken;
import net.armcloud.paascenter.rtc.manager.armcloudrtc.constants.ArmcloudAccessTokenPrivileges;
import net.armcloud.paascenter.rtc.utils.AESUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ArmcloudRtcManager {
    private final ArmcloudRTCEndpointFeignStub armcloudRTCEndpointFeignStub;
    private final ArmcloudRTCAppConfiguration armcloudRTCAppConfiguration;

    /**
     * 生成Armcloud token
     *
     * @param expire            有效时间（单位：秒）
     * @param publishStreamFlag 是否具备推流权限
     */
    public String generateAccessToken(String roomCode, String userId, int expire, Boolean publishStreamFlag) {
        ArmcloudAccessToken token = new ArmcloudAccessToken(armcloudRTCAppConfiguration.getAppId(), armcloudRTCAppConfiguration.getAppKey(), roomCode, userId);
        token.setExpireAt(System.currentTimeMillis() + expire * 1000);
        //// 添加订阅流权限
        token.addPrivilege(ArmcloudAccessTokenPrivileges.SUBSCRIBE_STREAM, System.currentTimeMillis() + expire * 1000);
        if (Boolean.TRUE.equals(publishStreamFlag)) {
            // 添加发布流权限
            token.addPrivilege(ArmcloudAccessTokenPrivileges.PUBLISH_STREAM, System.currentTimeMillis() + expire * 1000);
        }
        return token.serialize();
    }

    public ApplyEndpointVO applyEndpoint(String padCode) {
        ApplyEndpointDTO applyEndpointDTO = new ApplyEndpointDTO();
        applyEndpointDTO.setPadCode(padCode);
        String data = FeignUtils.getContent(armcloudRTCEndpointFeignStub.apply(applyEndpointDTO));
        String jsonData =AESUtils.decrypt(data, armcloudRTCAppConfiguration.getAppKey());
        return JSON.parseObject(jsonData, ApplyEndpointVO.class);
    }


    public ArmcloudRtcManager(ArmcloudRTCEndpointFeignStub armcloudRTCEndpointFeignStub, ArmcloudRTCAppConfiguration armcloudRTCAppConfiguration) {
        this.armcloudRTCEndpointFeignStub = armcloudRTCEndpointFeignStub;
        this.armcloudRTCAppConfiguration = armcloudRTCAppConfiguration;
    }
}
