package net.armcloud.paascenter.rtc.manager.armcloudrtc.component;

import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Map;
import java.util.TreeMap;

public class ByteBufWrapper {
    ByteBuffer buffer = ByteBuffer.allocate(1024).order(ByteOrder.LITTLE_ENDIAN);

    public ByteBufWrapper() {
    }

    public ByteBufWrapper(byte[] bytes) {
        this.buffer = ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN);
    }

        public byte[] asBytes() {
            byte[] out = new byte[buffer.position()];
            ((Buffer) buffer).rewind(); // 显式转换为 Buffer 调用 rewind()
            buffer.get(out, 0, out.length);
            return out;
        }


    // packUint16
    public ByteBufWrapper put(short v) {
        buffer.putShort(v);
        return this;
    }

    public ByteBufWrapper put(byte[] v) {
        put((short)v.length);
        buffer.put(v);
        return this;
    }

    // packUint32
    public ByteBufWrapper put(int v) {
        buffer.putInt(v);
        return this;
    }

    public ByteBufWrapper put(long v) {
        buffer.putLong(v);
        return this;
    }

    public ByteBufWrapper put(String v) {
        return put(v.getBytes());
    }

    public ByteBufWrapper put(TreeMap<Short, String> extra) {
        put((short)extra.size());

        for (Map.Entry<Short, String> pair : extra.entrySet()) {
            put(pair.getKey());
            put(pair.getValue());
        }

        return this;
    }

    public ByteBufWrapper putIntMap(TreeMap<Short, Integer> extra) {
        put((short)extra.size());

        for (Map.Entry<Short, Integer> pair : extra.entrySet()) {
            put(pair.getKey());
            put(pair.getValue());
        }

        return this;
    }

    public ByteBufWrapper putLongMap(TreeMap<Short, Long> extra) {
        put((short)extra.size());

        for (Map.Entry<Short, Long> pair : extra.entrySet()) {
            put(pair.getKey());
            put(pair.getValue());
        }

        return this;
    }

    public Short readShort() {
        return buffer.getShort();
    }


    public int readInt() {
        return buffer.getInt();
    }

    public long readLong() {
        return buffer.getLong();
    }

    public byte[] readBytes() {
        Short length = readShort();
        byte[] bytes = new byte[length];
        buffer.get(bytes);
        return bytes;
    }

    public String readString() {
        byte[] bytes = readBytes();
        return new String(bytes);
    }

    public TreeMap readMap() {
        TreeMap<Short, String> map = new TreeMap<>();

        Short length = readShort();

        for (short i = 0; i < length; ++i) {
            Short k = readShort();
            String v = readString();
            map.put(k, v);
        }

        return map;
    }

    public TreeMap<Short, Integer> readShortRefIntegerMap() {
        TreeMap<Short, Integer> map = new TreeMap<>();

        Short length = readShort();

        for (short i = 0; i < length; ++i) {
            Short k = readShort();
            Integer v = readInt();
            map.put(k, v);
        }

        return map;
    }

    public TreeMap<Short, Long> readShortRefLongMap() {
        TreeMap<Short, Long> map = new TreeMap<>();

        Short length = readShort();

        for (short i = 0; i < length; ++i) {
            Short k = readShort();
            Long v = readLong();
            map.put(k, v);
        }

        return map;
    }
}