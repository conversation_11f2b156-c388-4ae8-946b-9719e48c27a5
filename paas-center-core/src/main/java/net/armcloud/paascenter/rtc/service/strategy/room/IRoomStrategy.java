package net.armcloud.paascenter.rtc.service.strategy.room;

import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.ApplyShareTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.BatchApplyTokenBO;
import net.armcloud.paascenter.rtc.service.strategy.room.model.bo.GetRoomTokenStrategyBO;

import java.util.List;

public interface IRoomStrategy {
    /**
     * 申请单个RTC连接token 目前此接口是提供给SDK用的
     */
    RoomTokenVO generateToken(GetRoomTokenStrategyBO param);

    /**
     * 批量申请TOEKN，此接口为PAAS端调用
     */
    List<? extends RoomTokenVO> batchGenerateToken(BatchApplyTokenBO param);

    /**
     * 申请共享房间TOKEN
     */
    RoomTokenVO applyShareToken(ApplyShareTokenBO applyShareTokenBO);
}
