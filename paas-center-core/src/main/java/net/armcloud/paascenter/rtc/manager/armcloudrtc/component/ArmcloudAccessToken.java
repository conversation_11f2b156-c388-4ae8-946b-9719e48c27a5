package net.armcloud.paascenter.rtc.manager.armcloudrtc.component;

import net.armcloud.paascenter.rtc.manager.armcloudrtc.constants.ArmcloudAccessTokenPrivileges;
import net.armcloud.paascenter.rtc.utils.EncryptionUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Arrays;
import java.util.TreeMap;

@ToString
@Getter
public class ArmcloudAccessToken {
    /**
     * 业务侧应用id
     */
    private String appId;

    /**
     * 业务侧应用id key
     */
    private String appKey;

    /**
     * 业务侧房间id
     */
    private String roomId;

    /**
     * 业务侧用户id
     */
    private String userId;

    /**
     * 创建时间戳
     */
    private long issuedAt;

    /**
     * token过期时间戳（秒）
     */
    @Setter
    private long expireAt;

    /**
     * 随机数
     */
    private int nonce;

    /**
     * 权限
     * <p>
     * key：权限标识
     * value：有效时间戳（单位：秒）
     */
    private TreeMap<Short, Long> privileges;

    /**
     * 请求数据签名
     */
    private byte[] signature;

    /**
     * 系统全局房间唯一id
     */
    public String getRoomUniqueId() {
        return appId + "_" + roomId;
    }

    public ArmcloudAccessToken(String appId, String appKey, String roomId, String userId) {
        this.appId = appId;
        this.appKey = appKey;
        this.roomId = roomId;
        this.userId = userId;
        this.issuedAt = System.currentTimeMillis();
        this.nonce = EncryptionUtils.randomInt();
        this.privileges = new TreeMap<>();
    }

    public static String getVersion() {
        return "001";
    }

    public void addPrivilege(ArmcloudAccessTokenPrivileges privilege, long expireTimestamp) {
        this.privileges.put(privilege.getValue(), expireTimestamp);

        if (privilege.getValue().equals(ArmcloudAccessTokenPrivileges.PUBLISH_STREAM.getValue())) {
            this.privileges.put(ArmcloudAccessTokenPrivileges.PUBLISH_VIDEO_STREAM.getValue(), expireTimestamp);
            this.privileges.put(ArmcloudAccessTokenPrivileges.PUBLISH_AUDIO_STREAM.getValue(), expireTimestamp);
            this.privileges.put(ArmcloudAccessTokenPrivileges.PUBLISH_DATA_STREAM.getValue(), expireTimestamp);
        }
    }

    public byte[] packMsg() {
        ByteBufWrapper buffer = new ByteBufWrapper();
        return buffer.put(this.nonce)
                .put(this.issuedAt)
                .put(this.expireAt)
                .put(this.roomId)
                .put(this.userId)
                .putLongMap(this.privileges)
                .asBytes();
    }

    public String serialize() {
        byte[] msg = this.packMsg();
        try {
            this.signature = EncryptionUtils.hmacSign(this.appKey, msg);
        } catch (Exception e) {
            e.printStackTrace();
        }

        ByteBufWrapper buffer = new ByteBufWrapper();
        byte[] content = buffer.put(msg).put(signature).asBytes();
        return getVersion() + this.appId + EncryptionUtils.base64Encode(content);
    }

    public boolean verify(String key) {
        if (this.expireAt > 0 && System.currentTimeMillis() > this.expireAt) {
            return false;
        }

        this.appKey = key;

        byte[] signature;

        try {
            signature = EncryptionUtils.hmacSign(this.appKey, this.packMsg());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return Arrays.equals(this.signature, signature);
    }

    public boolean isExpired() {
        return System.currentTimeMillis() > expireAt;
    }

    public static ArmcloudAccessToken parse(String raw) {
        if (raw.length() <= EncryptionUtils.VERSION_LENGTH + EncryptionUtils.APP_ID_LENGTH) {
            return null;
        }

        if (!getVersion().equals(raw.substring(0, EncryptionUtils.VERSION_LENGTH))) {
            return null;
        }

        ArmcloudAccessToken token = new ArmcloudAccessToken("", "", "", "");
        token.appId = raw.substring(EncryptionUtils.VERSION_LENGTH, EncryptionUtils.VERSION_LENGTH + EncryptionUtils.APP_ID_LENGTH);

        byte[] content = EncryptionUtils.base64Decode(raw.substring(EncryptionUtils.VERSION_LENGTH + EncryptionUtils.APP_ID_LENGTH));
        ByteBufWrapper buffer = new ByteBufWrapper(content);
        byte[] msg = buffer.readBytes();
        token.signature = buffer.readBytes();

        ByteBufWrapper msgBuf = new ByteBufWrapper(msg);
        token.nonce = msgBuf.readInt();
        token.issuedAt = msgBuf.readLong();
        token.expireAt = msgBuf.readLong();
        token.roomId = msgBuf.readString();
        token.userId = msgBuf.readString();
        token.privileges = msgBuf.readShortRefLongMap();

        return token;
    }


}

