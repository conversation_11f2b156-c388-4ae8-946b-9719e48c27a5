package net.armcloud.paascenter.rtc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenDTO;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.entity.paas.PadRtcTokenLog;


public interface IPadRtcTokenLogService extends IService<PadRtcTokenLog> {


    /**
     * 保存token日志
     *
     * @param dto       RoomTokenDTO
     * @param padRoom   PadRoom
     * @param roomToken roomToken
     * @return Boolean
     */
    Boolean saveTokenLog(RoomTokenDTO dto, PadRoom padRoom, String roomToken, Integer publishStream);
}
