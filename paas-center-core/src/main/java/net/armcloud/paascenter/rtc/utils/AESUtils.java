package net.armcloud.paascenter.rtc.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

@Slf4j
public class AESUtils {
    private static final String AES = "AES";
    private static final String AES_CIPHER_ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_TAG_LENGTH = 16;
    private static final int GCM_IV_LENGTH = 12;

    /**
     * Generates a SecretKeySpec from a given string key
     */
    private static SecretKeySpec getKeyFromPassword(String password) throws NoSuchAlgorithmException {
        MessageDigest sha = MessageDigest.getInstance("SHA-256");
        byte[] key = sha.digest(password.getBytes());
        return new SecretKeySpec(key, AES);
    }

    /**
     * Generates a new Initialization Vector (IV)
     */
    public static byte[] generateIv() {
        byte[] iv = new byte[GCM_IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    /**
     * Encrypts a plain text using AES algorithm and returns both the cipher text and IV
     */
    public static String encrypt(String input, String key) {
        try {
            SecretKeySpec secretKeySpec = getKeyFromPassword(key);
            byte[] iv = generateIv();
            Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);
            byte[] cipherText = cipher.doFinal(input.getBytes());

            // Encode IV and cipher text to Base64 and concatenate them with a separator
            String ivString = Base64.getEncoder().encodeToString(iv);
            String cipherTextString = Base64.getEncoder().encodeToString(cipherText);
            return ivString + ":" + cipherTextString;
        } catch (Exception e) {
            log.error("encrypt error >>>input:{} key:{}", input, key, e);
            return null;
        }

    }

    /**
     * Decrypts an encrypted text using AES algorithm
     */
    public static String decrypt(String encryptedData, String key) {
        try {
            SecretKeySpec secretKeySpec = getKeyFromPassword(key);

            // Split the encrypted data into IV and cipher text
            String[] parts = encryptedData.split(":");
            String ivString = parts[0];
            String cipherTextString = parts[1];

            byte[] iv = Base64.getDecoder().decode(ivString);
            byte[] cipherText = Base64.getDecoder().decode(cipherTextString);

            Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGORITHM);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);
            byte[] plainText = cipher.doFinal(cipherText);
            return new String(plainText);
        } catch (Exception e) {
            log.error("decrypt error >>>encryptedData:{} key:{}", encryptedData, key, e);
            return null;
        }

    }

    /**
     * Encodes the input byte array to a Base64 string
     */
    public static String encodeToString(byte[] input) {
        return Base64.getEncoder().encodeToString(input);
    }

    // Encodes the input string to a Base64 string
    public static String encodeToString(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    /**
     * Decodes the input Base64 string to a byte array
     */
    public static byte[] decodeToBytes(String input) {
        return Base64.getDecoder().decode(input);
    }

    /**
     * Decodes the input Base64 string to a regular string
     */
    public static String decodeToString(String input) {
        byte[] decodedBytes = Base64.getDecoder().decode(input);
        return new String(decodedBytes);
    }

    /**
     * Encodes the input byte array to a Base64 byte array
     */
    public static byte[] encodeToBytes(byte[] input) {
        return Base64.getEncoder().encode(input);
    }

    /**
     * Decodes the input Base64 byte array to a byte array
     */
    public static byte[] decodeToBytes(byte[] input) {
        return Base64.getDecoder().decode(input);
    }

    public static void main(String[] args) throws Exception {
        String plainText = "Hello, World!";
        String key = "AC22030010001"; // 任意字符串作为密钥

        // Encrypt the plain text
        String encryptedData = encodeToString(encrypt(plainText, key));
        System.out.println("Encrypted text: " + encryptedData);

        // Decrypt the cipher text
        String decryptedText = decrypt(decodeToString(encryptedData), key);
        System.out.println("Decrypted text: " + decryptedText);
    }
}

