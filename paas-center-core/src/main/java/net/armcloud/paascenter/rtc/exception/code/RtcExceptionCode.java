package net.armcloud.paascenter.rtc.exception.code;

import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RtcExceptionCode implements ExceptionCode {
    ROOM_CORRESPONDING_TO_PAD_DOES_NOT_EXIST(120001, "pad对应的房间不存在"),
    PUSH_STREAMING_SERVICE_EXCEPTION(120002, "连接云机失败，请重试！"),
    ABORTING_STREAMING_ERROR_SERVICE_INTERNAL_CALL_EXCEPTION(120003, "中止推流错误，指令发送失败"),
    ABORTING_STREAMING_ERROR_INSTRUCTION_SERVICE_EXCEPTION(120004, "中止推流错误，指令服务异常"),
    NO_STREAMING_PERMISSION_INSTANCE(120005, "实例不存在"),
    UUID_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_UUID(120006, "token绑定的uuid与请求uuid不一致"),
    ORIGINAL_TOKEN_INFORMATION_DOES_NOT_EXIST(120007, "原token信息不存在,请检查"),
    INSTANCES_NOT_SUPPORT_SIMULTANEOUS_OPERATIONS(120008, "当前批次实例存在不同的推拉流方式,请联系管理员处理"),
    PADCODE_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_PADCODE(120009, "token绑定的padCode与请求padCode不一致"),

    ;


    private final int status;
    private final String msg;
}
