package net.armcloud.paascenter.commscenter.manager;

import net.armcloud.paascenter.common.client.internal.dto.GetPadByCloudVendorDTO;
import net.armcloud.paascenter.common.client.internal.facade.PadInternalFacade;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.utils.FeignUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CommscenterPadManager {

    private final PadInternalFacade padInternalFeignFacade;

    public Pad getByCloudVendor(int cloudVendorType, String padOutCode) {
        GetPadByCloudVendorDTO dto = new GetPadByCloudVendorDTO();
        dto.setCloudVendorType(cloudVendorType);
        dto.setPadOutCode(padOutCode);
        return FeignUtils.getContent(padInternalFeignFacade.getByCloudVendor(dto));
    }

    public List<Pad> listAllPad() {
        return FeignUtils.getContent(padInternalFeignFacade.listAllPad());
    }

    public CommscenterPadManager(PadInternalFacade padInternalFeignFacade) {
        this.padInternalFeignFacade = padInternalFeignFacade;
    }

}
