package net.armcloud.paascenter.commscenter.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.CleanServerDTO;
import net.armcloud.paascenter.common.client.internal.facade.ServerInternalFacade;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.commscenter.service.ServerService;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class ServerInternalController implements ServerInternalFacade {
    private final ServerService serverService;

    @Override
    public Result cleanServer(CleanServerDTO request) {
        serverService.cleanServer(request);
        return Result.ok();
    }

    public ServerInternalController(ServerService serverService) {
        this.serverService = serverService;
    }
}
