package net.armcloud.paascenter.commscenter.manager;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.CloseConnectionDTO;
import net.armcloud.paascenter.common.core.constant.comms.ServerPadLogConstants;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.bo.comms.ServerCertificateCacheBO;
import net.armcloud.paascenter.common.model.entity.comms.ServerPad;
import net.armcloud.paascenter.common.model.entity.comms.ServerPadLog;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.common.utils.FeignUtils;
import net.armcloud.paascenter.common.utils.http.HttpClientUtils;
import net.armcloud.paascenter.commscenter.config.mq.PadConnectStatusMessageConfig;
import net.armcloud.paascenter.commscenter.mapper.ServerPadLogMapper;
import net.armcloud.paascenter.commscenter.mapper.ServerPadMapper;
import net.armcloud.paascenter.commscenter.model.cache.ServerCache;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.SYSTEM_EXCEPTION;
import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.CacheHashFieldKey.SERVER_CACHE_HASH_FIELD_KEY;
import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.PAD_CODE_CONNECTION_KEY_PREFIX;


@Slf4j
@Component
public class PadConnectionManager {
    private final RedisService redisService;
    private final ServerPadMapper serverPadMapper;
    private final ServerPadLogMapper serverPadLogMapper;
    private final DefaultRocketMqProducerWrapper rocketMqProducerService;
    private final PadConnectStatusMessageConfig padConnectStatusMessageConfig;

    private ThreadPoolExecutor singleThreadExecutor = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public ServerCache getServer(String padCode) {
        String key = PAD_CODE_CONNECTION_KEY_PREFIX + padCode;
        return getServerByKey(key);
    }

    public ServerCache getServerByKey(String key) {
        boolean keyExist = redisService.opsForHashHasKey(key, SERVER_CACHE_HASH_FIELD_KEY);
        if (!keyExist) {
            return null;
        }

        String data = redisService.getCacheMapValue(key, SERVER_CACHE_HASH_FIELD_KEY);
        return JSON.parseObject(data, ServerCache.class);
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void asyncSaveRecord(ServerCertificateCacheBO serverCertificateCache) {
        String padCode = serverCertificateCache.getPadCode();
//        log.info("asyncSaveRecord>>> padCode:{} tradeId:{}", padCode, tradeId);

        Long serverId = serverCertificateCache.getId();
        // 添加pad关联服务器记录
        ServerPad serverPad = new ServerPad();
        serverPad.setPadCode(padCode);
        serverPad.setCommsServerId(serverId);
        serverPad.setChannelId(serverCertificateCache.getChannelId());
        serverPad.setTs(serverCertificateCache.getTs());
        serverPad.setTradeId(serverCertificateCache.getTradeId());
        serverPad.setChannelId(serverCertificateCache.getChannelId());
        serverPadMapper.insert(serverPad);

        if(serverId != null){
            // 添加关联服务器缓存
            ServerCache serverCache = ServerCache.convert(serverCertificateCache);
            String key = PAD_CODE_CONNECTION_KEY_PREFIX + padCode;
            String jsonData = JSON.toJSONString(serverCache);
//            log.info("asyncSaveRecord>>> padCode:{} tradeId:{} key:{} jsonData:{}", padCode, tradeId, key, jsonData);
            redisService.setCacheMapValue(key, SERVER_CACHE_HASH_FIELD_KEY, jsonData);
        }


        // 添加关联日志
        ServerPadLog serverPadLog = new ServerPadLog();
        BeanUtils.copyProperties(serverPad, serverPadLog);
        serverPadLog.setEventType(ServerPadLogConstants.EventType.ONLINE);
        serverPadLogMapper.insert(null, serverPadLog);


    }

    public void deleteCacheByPadCode(String padCode) {
        String key = PAD_CODE_CONNECTION_KEY_PREFIX + padCode;
        redisService.deleteObject(key);
    }

    public void deleteCacheAndSendCloseMessage(long serverId) {
        List<PadWSStatusMessage> padCloseMessages = new ArrayList<>();
        redisService.keys(PAD_CODE_CONNECTION_KEY_PREFIX + "*").forEach(connectKey -> {
            ServerCache serverCache = getServerByKey(connectKey);
            if (serverCache == null) {
                return;
            }

            if (serverCache.getId() != serverId) {
                return;
            }

            String padCode = connectKey.replace(PAD_CODE_CONNECTION_KEY_PREFIX, "");
            PadWSStatusMessage padWSStatusMessage = new PadWSStatusMessage()
                    .setPadCode(padCode)
                    .setToken("")
                    .setServerId(serverId)
                    .setChannelId(serverCache.getChannelId())
                    .setTs(System.currentTimeMillis())
                    .setTradeId(UUID.randomUUID().toString().replace("-", "") + "_" + serverId)
                    .setConnected(false);
            padCloseMessages.add(padWSStatusMessage);
        });

        if (CollectionUtils.isEmpty(padCloseMessages)) {
            return;
        }

        List<String> keys = new ArrayList<>(padCloseMessages.size());
        padCloseMessages.forEach(padCode -> keys.add(PAD_CODE_CONNECTION_KEY_PREFIX + padCode.getPadCode()));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(keys)) {
            redisService.deleteObject(keys);
        }

        log.info("从redis中查询对象集合padCloseMessages大小:{}",padCloseMessages.size());
        sendPadCloseMessage(padCloseMessages);
    }

    public void sendPadCloseMessage(List<PadWSStatusMessage> padWSStatusMessages) {
        if (CollectionUtils.isEmpty(padWSStatusMessages)) {
            return;
        }
        padWSStatusMessages.parallelStream().forEach(padWSStatusMessage -> {
            singleThreadExecutor.execute(() -> {
                String padCode = padWSStatusMessage.getPadCode();
                String msg = JSON.toJSONString(padWSStatusMessage);
                log.info("padCode:{} channel:{} start send closed message:{}", padCode, padWSStatusMessage.getChannelId(), msg);
                rocketMqProducerService.producerOrderlyMessage(padConnectStatusMessageConfig.getTopic(), padCode, msg);
            });
        });
    }

    public void closeCommsConnection(ServerCache serverCache, String padCode, boolean sendDisconnectWsCallbackMsg) {
        log.info("closeCommsConnection >>> padCode:{} serverCache:{}", padCode, JSON.toJSONString(serverCache));
        String domain = "http://" + serverCache.getPublicIp() + ":" + serverCache.getPublicInterfacePort();
        String url = domain + "/comms/internal/pad/closeConnection";
        String data = JSON.toJSONString(new CloseConnectionDTO(padCode, sendDisconnectWsCallbackMsg, serverCache.getChannelId()));
        try {
            String resultStr = HttpClientUtils.doPost(url, data);
            FeignUtils.getContent(JSON.parseObject(resultStr, Result.class));
        } catch (Exception e) {
            log.error("closeCommsConnection exception>>>>url:{},data:{}", url, data, e);
            throw new BasicException(SYSTEM_EXCEPTION);
        }

        String tradeId = serverCache.getTradeId();
        log.info("closeCommsConnection deleteByPadCode >>> padCode:{} tradeId:{}", padCode, tradeId);
        serverPadMapper.deleteByPadCode(padCode);
        log.info("closeCommsConnection deleteCacheByPadCode >>> padCode:{} tradeId:{}", padCode, tradeId);
        deleteCacheByPadCode(padCode);
    }

    public PadConnectionManager(ServerPadMapper serverPadMapper, ServerPadLogMapper serverPadLogMapper,
                                RedisService redisService, DefaultRocketMqProducerWrapper rocketMqProducerService, PadConnectStatusMessageConfig padConnectStatusMessageConfig) {
        this.serverPadMapper = serverPadMapper;
        this.serverPadLogMapper = serverPadLogMapper;
        this.redisService = redisService;
        this.rocketMqProducerService = rocketMqProducerService;
        this.padConnectStatusMessageConfig = padConnectStatusMessageConfig;
    }

}
