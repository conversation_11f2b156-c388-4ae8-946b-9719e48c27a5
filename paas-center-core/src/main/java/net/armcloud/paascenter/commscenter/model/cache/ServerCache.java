package net.armcloud.paascenter.commscenter.model.cache;

import lombok.Data;
import net.armcloud.paascenter.common.model.bo.comms.ServerCertificateCacheBO;

import java.io.Serializable;

/**
 * pad与服务器连接信息
 */
@Data
public class ServerCache implements Serializable {
    /**
     * 服务器ID
     */
    private Long id;

    /**
     * 服务器公网IP
     */
    private String publicIp;

    /**
     * 服务器内网IP
     */
    private String internalIp;

    /**
     * 服务器公网端口
     */
    private Integer publicPort;

    /**
     * 服务器内网端口
     */
    private Integer internalPort;

    /**
     * 公网接口端口
     */
    private Integer publicInterfacePort;

    /**
     * 内网接口端口
     */
    private Integer internalInterfacePort;

    private String channelId;

    private Long ts;

    private String tradeId;

    public static ServerCache convert(ServerCertificateCacheBO certificateCache) {
        ServerCache serverCache = new ServerCache();
        serverCache.setId(certificateCache.getId());
        serverCache.setPublicIp(certificateCache.getPublicIp());
        serverCache.setInternalIp(certificateCache.getInternalIp());
        serverCache.setPublicPort(certificateCache.getPublicPort());
        serverCache.setInternalPort(certificateCache.getInternalPort());
        serverCache.setPublicInterfacePort(certificateCache.getPublicInterfacePort());
        serverCache.setInternalInterfacePort(certificateCache.getInternalInterfacePort());
        serverCache.setChannelId(certificateCache.getChannelId());
        serverCache.setTs(certificateCache.getTs());
        serverCache.setTradeId(certificateCache.getTradeId());
        return serverCache;
    }
}
