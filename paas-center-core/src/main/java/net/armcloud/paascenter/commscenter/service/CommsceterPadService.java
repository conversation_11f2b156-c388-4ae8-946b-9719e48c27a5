package net.armcloud.paascenter.commscenter.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.constant.comms.ServerPadLogConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.bo.comms.ServerCertificateCacheBO;
import net.armcloud.paascenter.common.model.dto.commscenter.dto.ReportDisconnectedDTO;
import net.armcloud.paascenter.common.model.dto.commscenter.dto.ReportPadConnectedDTO;
import net.armcloud.paascenter.common.model.entity.comms.Server;
import net.armcloud.paascenter.common.model.entity.comms.ServerPad;
import net.armcloud.paascenter.common.model.entity.comms.ServerPadLog;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.commscenter.manager.PadConnectionManager;
import net.armcloud.paascenter.commscenter.manager.ServerCertificateManager;
import net.armcloud.paascenter.commscenter.mapper.ServerMapper;
import net.armcloud.paascenter.commscenter.mapper.ServerPadLogMapper;
import net.armcloud.paascenter.commscenter.mapper.ServerPadMapper;
import net.armcloud.paascenter.commscenter.model.cache.ServerCache;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static net.armcloud.paascenter.commscenter.exception.code.ServerExceptionCode.CONNECTION_PERMISSION_DENIED_EXCEPTION;
import static net.armcloud.paascenter.commscenter.exception.code.ServerExceptionCode.CURRENT_SERVER_AVAILABLE_EXCEPTION;


@Service
@Slf4j
public class CommsceterPadService {
    private final ServerMapper serverMapper;
    private final ServerPadMapper serverPadMapper;
    private final PadConnectionManager padConnectionManager;
    private final ServerPadLogMapper serverPadLogMapper;
    private final ServerCertificateManager serverCertificateManager;

    /**
     * 上报pad连接comms服务器
     * <p>
     * 从token取对应服务器信息
     * 建立pad与服务器关联记录
     * 服务器连接数+1
     *
     * @param request
     * @return
     */
    /**
     * 改为监听MQ连接消息处理 { PadService#reportConnected(PadWSStatusMessage)}
     */
    @Deprecated
    public String reportConnected(ReportPadConnectedDTO request) {
        String token = request.getToken();
        ServerCertificateCacheBO serverCertificateCache = serverCertificateManager.getCacheCertificateByToken(token);
        if (Objects.isNull(serverCertificateCache)) {
            throw new BasicException(CONNECTION_PERMISSION_DENIED_EXCEPTION);
        }

        long serverId = serverCertificateCache.getId();
        if (!Objects.equals(serverId, request.getServerId())) {
            throw new BasicException(CONNECTION_PERMISSION_DENIED_EXCEPTION);
        }

        // 处理重复连接
        handlerInstanceRepeatConnect(serverCertificateCache);

        boolean updateSuccess = serverMapper.incrConnectionTotal(serverId) > 0;
        if (!updateSuccess) {
            throw new BasicException(CURRENT_SERVER_AVAILABLE_EXCEPTION);
        }

        padConnectionManager.asyncSaveRecord(serverCertificateCache);
        return serverCertificateCache.getPadCode();
    }

    public void reportConnected(PadWSStatusMessage message) {
        String padCode = message.getPadCode();
        String tradeId = message.getTradeId();

        ServerCache serverCache = padConnectionManager.getServer(padCode);
        if (Objects.isNull(serverCache)) {
//            log.info("reportConnected >>> padCode:{} tradeId:{} serverCache is null", padCode, tradeId);
            saveConnectInfo(message);
            return;
        }

        if(StrUtil.isNotEmpty(tradeId)){
            // 丢弃重复消息
            boolean repeatMessage = Objects.equals(message.getTradeId(), serverCache.getTradeId());
            if (repeatMessage) {
//                log.info("reportConnected >>> padCode:{} tradeId:{} serverCache:{} is repeat message skip", padCode, tradeId, JSON.toJSONString(serverCache));
                return;
            }

            // 丢弃同连接重复连接
            boolean sameServerChannelRepeatConnect = Objects.equals(message.getServerId(), serverCache.getId())
                    && Objects.equals(message.getChannelId(), serverCache.getChannelId());
            if (sameServerChannelRepeatConnect) {
//                log.info("reportConnected >>> padCode:{} tradeId:{} serverCache:{} is same server channel repeat connect skip", padCode, tradeId, JSON.toJSONString(serverCache));
                return;
            }

            // 断开重复连接(不让指令服务器发送实例断开消息。因为到这个步骤说明已经有实例已经连接上了）
            // 为保证业务数据最终一致性，丢掉此次主动断开老连接的数据。如果再发一次断开消息会覆盖这次连接正常的状态
            padConnectionManager.closeCommsConnection(serverCache, padCode, true);
        }
        saveConnectInfo(message);
    }

    private void saveConnectInfo(PadWSStatusMessage message) {
        String padCode = message.getPadCode();
//        log.info("saveConnectInfo >>> padCode:{} tradeId:{}", padCode, tradeId);

        Long serverId = message.getServerId();
        ServerCertificateCacheBO serverCertificateCacheBO = new ServerCertificateCacheBO();
        serverCertificateCacheBO.setId(serverId);
        serverCertificateCacheBO.setPadCode(padCode);
        serverCertificateCacheBO.setTs(message.getTs());
        serverCertificateCacheBO.setTradeId(message.getTradeId());
        serverCertificateCacheBO.setChannelId(message.getChannelId());
        if(serverId != null){
            serverMapper.incrConnectionTotal(serverId);

            Server server = serverMapper.getById(serverId);
            serverCertificateCacheBO.setPublicIp(server.getPublicIp());
            serverCertificateCacheBO.setInternalIp(server.getInternalIp());
            serverCertificateCacheBO.setPublicPort(server.getPublicPort());
            serverCertificateCacheBO.setInternalPort(server.getInternalPort());
            serverCertificateCacheBO.setPublicInterfacePort(server.getPublicInterfacePort());
            serverCertificateCacheBO.setInternalInterfacePort(server.getInternalInterfacePort());
        }

        padConnectionManager.asyncSaveRecord(serverCertificateCacheBO);
    }


    /**
     * 改为监听MQ连接消息处理 {@link CommsceterPadService#reportConnected(PadWSStatusMessage)}
     */
    @Deprecated
    private void handlerInstanceRepeatConnect(ServerCertificateCacheBO certificate) {
        String padCode = certificate.getPadCode();
        ServerCache serverCache = padConnectionManager.getServer(padCode);
        if (Objects.isNull(serverCache)) {
            return;
        }

        // 断开重复连接(不让指令服务器发送实例断开消息。因为到这个步骤说明已经有实例已经连接上了）
        // 为保证业务数据最终一致性，丢掉此次主动断开老连接的数据。如果再发一次断开消息会覆盖这次连接正常的状态
        padConnectionManager.closeCommsConnection(serverCache, padCode, true);
    }

    /**
     * 改为监听MQ连接消息处理 {@link CommsceterPadService#reportDisconnected(PadWSStatusMessage)}
     */
    @Async
    public void reportDisconnected(ReportDisconnectedDTO request) {
        String padCode = request.getPadCode();
        log.info("reportDisconnected>>>>request:{}", JSON.toJSONString(request));
        padConnectionManager.deleteCacheByPadCode(padCode);
        ServerPad serverPad = serverPadMapper.getByPadCode(padCode);
        if (Objects.isNull(serverPad)) {
            return;
        }

        serverPadMapper.deleteByPadCode(padCode);
        serverMapper.decrConnectionTotal(serverPad.getCommsServerId());
    }

    public void reportDisconnected(PadWSStatusMessage message) {
        String padCode = message.getPadCode();
        String tradeId =message.getTradeId();

        Long serverId = message.getServerId();

        ServerPadLog serverPadLog = new ServerPadLog();
        serverPadLog.setPadCode(padCode);
        serverPadLog.setCommsServerId(serverId);
        serverPadLog.setTs(message.getTs());
        serverPadLog.setTradeId(message.getTradeId());
        serverPadLog.setChannelId(message.getChannelId());
        serverPadLog.setEventType(ServerPadLogConstants.EventType.OFFLINE);
        serverPadLogMapper.insert(null, serverPadLog);

        ServerCache serverCache = padConnectionManager.getServer(padCode);
        if (Objects.isNull(serverCache)) {
//            log.info("reportDisconnected >>> padCode:{} tradeId:{} serverCache is null", padCode, tradeId);
            serverPadMapper.deleteByPadCode(padCode);
            return;
        }

        if(serverId != null){
            // 只清理与当前缓存一致的连接信息
            boolean sameChannelDisconnect = Objects.equals(serverCache.getId(), serverId)
                    && Objects.equals(serverCache.getChannelId(), message.getChannelId());
            if (!sameChannelDisconnect) {
//                log.info("reportDisconnected >>> padCode:{} tradeId:{} skip same channel disconnect", padCode, tradeId);
                return;
            }
            serverMapper.decrConnectionTotal(serverId);
        }


//        log.info("reportDisconnected >>> padCode:{} tradeId:{} delete cache", padCode, tradeId);
        padConnectionManager.deleteCacheByPadCode(padCode);
        serverPadMapper.deleteByPadCode(padCode);

    }

    public CommsceterPadService(ServerCertificateManager serverCertificateManager, ServerMapper serverMapper,
                                PadConnectionManager padConnectionManager, ServerPadMapper serverPadMapper, ServerPadLogMapper serverPadLogMapper) {
        this.serverCertificateManager = serverCertificateManager;
        this.serverMapper = serverMapper;
        this.padConnectionManager = padConnectionManager;
        this.serverPadMapper = serverPadMapper;
        this.serverPadLogMapper = serverPadLogMapper;
    }
}
