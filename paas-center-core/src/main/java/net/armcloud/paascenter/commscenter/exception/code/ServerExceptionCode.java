package net.armcloud.paascenter.commscenter.exception.code;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;

@Getter
@AllArgsConstructor
public enum ServerExceptionCode implements ExceptionCode {
    CONNECTION_PERMISSION_DENIED_EXCEPTION(150001, "没有连接权限"),
    NO_SERVER_AVAILABLE_EXCEPTION(150002, "无可用服务器"),
    CURRENT_SERVER_AVAILABLE_EXCEPTION(150003, "当前服务器不可用"),
    ;

    private final int status;
    private final String msg;
}
