package net.armcloud.paascenter.commscenter.manager;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.model.bo.comms.ServerCertificateCacheBO;
import net.armcloud.paascenter.common.redis.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.OUT_VENDOR_RELATION_TOKEN_CACHE_KEY_PREFIX;
import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.TOKEN_CONNECT_INFO_CACHE_KEY_PREFIX;
import static net.armcloud.paascenter.commscenter.service.ServerService.TOKEN_EFFECTIVE_DURATION_SECOND;


@Component
public class ServerCertificateManager {
    private final RedisService redisService;

    public ServerCertificateCacheBO getCertificateByVendor(Integer cloudVendorType, String padOutCode, Long versionCode) {
        String key = OUT_VENDOR_RELATION_TOKEN_CACHE_KEY_PREFIX + cloudVendorType + ":" + padOutCode;
        if (Objects.nonNull(versionCode)) {
            key = key + ":" + versionCode;
        }

        Boolean exist = redisService.hasKey(key);
        if (Boolean.FALSE.equals(exist)) {
            return null;
        }

        // 因之前GameServer之前首次拿到Token后，之后不会再重新调接口拿新Token。所以token统一都设置的缓存时间非常长。
        // 现在GameServer修复了这个问题，为保证TokenToken信息及时刷新需取消Token设置非常久的问题。此段代码为处理历史缓存数据，待之后需删除此段代码
        if (redisService.getExpire(key) > TOKEN_EFFECTIVE_DURATION_SECOND) {
            redisService.expire(key, TOKEN_EFFECTIVE_DURATION_SECOND, TimeUnit.SECONDS);
        }

        return getCacheCertificateByToken(redisService.getCacheObject(key));
    }

    public ServerCertificateCacheBO getCacheCertificateByToken(String token) {
        String key = TOKEN_CONNECT_INFO_CACHE_KEY_PREFIX + token;
        String cacheStr = redisService.getCacheObject(key);
        if (StringUtils.isNotBlank(cacheStr)) {
            return JSON.parseObject(cacheStr, ServerCertificateCacheBO.class);
        }

        return null;
    }

    public ServerCertificateCacheBO cacheCertificateByVendorInfo(int cloudVendorType, String externalCode,
                                                                 Supplier<ServerCertificateCacheBO> cacheSupplier,
                                                                 long cacheSecond) {
        // 缓存token关联连接信息
        ServerCertificateCacheBO connectInfo = cacheSupplier.get();
        String token = connectInfo.getClientCertificate().getToken();
        String key = TOKEN_CONNECT_INFO_CACHE_KEY_PREFIX + token;
        redisService.setCacheObject(key, JSON.toJSONString(connectInfo));
        redisService.expire(key, cacheSecond, TimeUnit.SECONDS);

        // 缓存第三方pad关联token信息
        String outVendorRefTokenKey = OUT_VENDOR_RELATION_TOKEN_CACHE_KEY_PREFIX + cloudVendorType + ":" + externalCode;
        redisService.setCacheObject(outVendorRefTokenKey, token);
        redisService.expire(outVendorRefTokenKey, cacheSecond, TimeUnit.SECONDS);
        return connectInfo;
    }

    public ServerCertificateManager(RedisService redisService) {
        this.redisService = redisService;
    }

}
