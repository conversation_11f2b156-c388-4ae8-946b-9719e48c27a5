package net.armcloud.paascenter.commscenter.mapper;

import net.armcloud.paascenter.common.model.entity.comms.ServerPad;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ServerPadMapper {

    @Delete("delete from  server_pad where pad_code = #{padCode}")
    void deleteByPadCode(@Param("padCode") String padCode);

    void insert(ServerPad serverPad);

    ServerPad getByPadCode(@Param("padCode") String padCode);

    ServerPad getByPadCode2(@Param("padCode") String padCode);



    @Delete("delete from server_pad where comms_server_id = #{serverId} ")
    void deleteByServerId(@Param("serverId") long serverId);

    List<String> listPadCodeByServerId(@Param("serverId") long serverId);

}
