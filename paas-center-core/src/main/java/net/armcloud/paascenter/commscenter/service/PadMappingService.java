package net.armcloud.paascenter.commscenter.service;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.comms.PadMappingCache;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.http.HttpClientUtils;
import net.armcloud.paascenter.commscenter.manager.CommscenterPadManager;
import net.armcloud.paascenter.commscenter.mapper.ServerMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.commscenter.constant.CacheKeyPrefixConstant.PAD_OUT_CODE_REF_PAD_LIST_KEY;


@Slf4j
@Service
public class PadMappingService {
    private final CommscenterPadManager commscenterPadManager;
    private final RedisService redisService;
    private final ServerMapper serverMapper;

    public void refresh() {
        List<Pad> pads = commscenterPadManager.listAllPad();
        if (CollectionUtils.isEmpty(pads)) {
            return;
        }

        List<PadMappingCache> padMappingCaches = new ArrayList<>(pads.size());
        pads.forEach(pad -> {
            PadMappingCache mappingCache = new PadMappingCache();
            mappingCache.setPadCode(pad.getPadCode());
            mappingCache.setPadOutCode(pad.getPadOutCode());
            padMappingCaches.add(mappingCache);
        });

        redisService.setCacheObject(PAD_OUT_CODE_REF_PAD_LIST_KEY, JSON.toJSONString(padMappingCaches));

        serverMapper.listAll().forEach(server -> {
            String domain = "http://" + server.getPublicIp() + ":" + server.getPublicInterfacePort();
            String url = domain + "/comms/internal/om/refresh";
            try {
                String result = HttpClientUtils.doPost(url, "");
                log.info("url :{} refresh PadMapping result:{}>>>", url, result);
            } catch (Exception e) {
                log.error("url :{} refresh PadMapping error>>>", url, e);
            }
        });
    }

    public PadMappingService(CommscenterPadManager commscenterPadManager, RedisService redisService, ServerMapper serverMapper) {
        this.commscenterPadManager = commscenterPadManager;
        this.redisService = redisService;
        this.serverMapper = serverMapper;
    }
}
