package net.armcloud.paascenter.commscenter.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.CloudVendorType.ARM_CLOUD_VENDOR_TYPE;
import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.CloudVendorType.VOLCENGINE_CLOUD_VENDOR_TYPE;


@Getter
@Setter
public class ApplyCertificateDTO {
    @NotBlank(message = "padOutCode cannot null")
    private String padOutCode;

    @Min(value = VOLCENGINE_CLOUD_VENDOR_TYPE, message = "不支持的厂商类型")
    @Max(value = ARM_CLOUD_VENDOR_TYPE, message = "不支持的厂商类型")
    @NotNull(message = "厂商类型不能为空")
    private Integer cloudVendorType;

    private String imageId;

    private Long versionCode;

    private String versionName;
}
