package net.armcloud.paascenter.commscenter.mapper;

import net.armcloud.paascenter.common.model.entity.comms.Server;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface ServerMapper {

    Server getById(@Param("id") long id);

    Server chooseServerByDcId(@Param("dcId") long dcId);

    int incrConnectionTotal(@Param("id") long id);

    void decrConnectionTotal(@Param("id") long id);

    @Update("update server set current_connect_total = 0 where id = #{id} ")
    void cleanConnectSize(@Param("id") long id);

    List<Server> listAll();

}
