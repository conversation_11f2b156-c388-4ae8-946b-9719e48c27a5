package net.armcloud.paascenter.commscenter.controller.api;

import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.bo.comms.ServerCertificateCacheBO;
import net.armcloud.paascenter.commscenter.model.dto.ApplyCertificateDTO;
import net.armcloud.paascenter.commscenter.service.ServerService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
public class ServerController {

    private final ServerService serverService;

    /**
     * 申请连接凭据
     */
    @PostMapping(value = "/comms-center/open/server/applyCertificate")
    public Result<ServerCertificateCacheBO.ClientCertificate> applyCertificate(@Valid @RequestBody ApplyCertificateDTO dto) {
        return Result.ok(serverService.applyCertificate(dto));
    }

    public ServerController(ServerService serverService) {
        this.serverService = serverService;
    }
}
