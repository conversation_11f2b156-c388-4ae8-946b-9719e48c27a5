package net.armcloud.paascenter.commscenter.mapper;

import net.armcloud.paascenter.common.core.annotation.TableNameConcat;
import net.armcloud.paascenter.common.model.entity.comms.AppCmdRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface
AppCmdRecordMapper {
    @TableNameConcat(tableNamePrefix = "app_cmd_record_")
    int batchInsert(@Param("tableName") String tableName, @Param("list") List<AppCmdRecord> list);

    @TableNameConcat(tableNamePrefix = "app_cmd_record_")
    List<AppCmdRecord> listByPadCodeAndPackageName(@Param("tableName") String tableName, @Param("padCode") String padCode, @Param("packageName") String packageName);

    @TableNameConcat(tableNamePrefix = "app_cmd_record_")
    void batchUpdateSyncStatusDoneById(@Param("tableName") String tableName, @Param("ids") List<Long> ids);

    @TableNameConcat(tableNamePrefix = "app_cmd_record_")
    void batchUpdateSyncStatusDoneByRequestId(@Param("tableName") String tableName, @Param("requestIds") List<String> requestIds);
}