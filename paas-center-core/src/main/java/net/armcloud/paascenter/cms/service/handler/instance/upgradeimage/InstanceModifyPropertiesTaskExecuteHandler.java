 package net.armcloud.paascenter.cms.service.handler.instance.upgradeimage;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.CBSHarborManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.exception.code.InstanceExceptionCode.NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION;
 
 /**
  * <AUTHOR>
  */
 @Slf4j
 @Service
 public class InstanceModifyPropertiesTaskExecuteHandler extends AbstractInstanceTaskHandler {
     private final CBSHarborManager cbsHarborManager;
     private final InstanceManager cbsInstanceManager;
     private final HarborConfigManage harborConfigManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     private final net.armcloud.paascenter.cms.manager.InstanceManager instanceManager;
     private final InstanceDetailImageSuccService instanceDetailImageSuccService;
 
     public void start(InstanceTask instanceTask) {
         super.asyncExecute(() -> tryStart(instanceTask));
     }
 
     private void tryStart(InstanceTask instanceTask) {
         String instanceTaskJson = JSON.toJSONString(instanceTask);
         log.info("InstanceModifyPropertiesTaskExecuteHandler.tryStart executeTask start handler instanceTask:{}", instanceTaskJson);
         String key = EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX + instanceTask.getInstanceName();
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (lock == null) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, key);
             return;
         }
 
         RLock deviceTaskLock = null;
         try {
             // 云机任务与实例任务互斥，要执行任务则需要拿到板卡锁
             String deviceIp = instanceTask.getDeviceIp();
             String deviceTaskLockKey = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
             if (redissonDistributedLock.isHeldByCurrentThread(deviceTaskLockKey)) {
                 log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
                 return;
             }
 
             deviceTaskLock = redissonDistributedLock.tryLock(key, 0, 300);
             if (Objects.isNull(deviceTaskLock)) {
                 log.debug("thread {} not get lock {} skip....", currentThreadId, key);
                 return;
             }
 
             boolean success = super.updateStatus(instanceTask, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
 
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getLatestByMasterTaskId(instanceTask.getType(), instanceTask.getId());
             if (taskRelInstanceDetail == null) {
                 throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
             }
 
             TaskRelInstanceDetail createTaskRelPadDetail = getCreateTaskRelPadDetail(instanceTask.getInstanceName(), DCUtils.getDcId());
             if (createTaskRelPadDetail == null) {
                 throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
             }
 
             removeInstance(instanceTask);
             virtualizeNewInstance(instanceTask, createTaskRelPadDetail, taskRelInstanceDetail);
         } catch (Exception e) {
             log.error("InstanceUpgradeImageTaskHandler executeTask error>>>>{}", JSON.toJSONString(instanceTask), e);
             super.updateStatus(instanceTask, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         } finally {
             redissonDistributedLock.unlock(lock);
             if (deviceTaskLock != null) {
                 redissonDistributedLock.unlock(deviceTaskLock);
             }
         }
     }
 
     private TaskRelInstanceDetail getCreateTaskRelPadDetail(String instanceName, long dcId) {
         TaskRelInstanceDetail upgradeImageTaskRelPadDetail = instanceDetailImageSuccService.getLastInfo(instanceName);
         return upgradeImageTaskRelPadDetail;
     }
 
     private void virtualizeNewInstance(InstanceTask task, TaskRelInstanceDetail originCreateDetail, TaskRelInstanceDetail newCreateDetail) {
         cbsHarborManager.loginHarbor(task.getDeviceIp());
         String instanceName = task.getInstanceName();
         log.info("InstanceRestartTaskHandler instanceName {} >>> virtualizeNewInstance task:{} originCreateDetail:{} newCreateDetail:{}",
                 instanceName, JSON.toJSONString(task), JSON.toJSONString(originCreateDetail), JSON.toJSONString(newCreateDetail));
 
         CreatePadBO createPadBO = JSON.parseObject(originCreateDetail.getContainerProperty(), CreatePadBO.class);
         //清除数据
 //        createPadBO.setClearContainerData(Boolean.TRUE.equals(task.getClearDiskData()));
         if (!StringUtils.isBlank(newCreateDetail.getAndroidProp())) {
             createPadBO.setDeviceAndroidProps(newCreateDetail.getAndroidProp());
         }
 
         if (Objects.nonNull(newCreateDetail.getWidth())&&newCreateDetail.getWidth()>0) {
             createPadBO.setWidth(newCreateDetail.getWidth());
         }
 
         if (Objects.nonNull(newCreateDetail.getHeight())&&newCreateDetail.getHeight()>0) {
             createPadBO.setHeight(newCreateDetail.getHeight());
         }
 
         if (Objects.nonNull(newCreateDetail.getFps())&&newCreateDetail.getFps()>0) {
             createPadBO.setFps(newCreateDetail.getFps());
         }
 
         if (Objects.nonNull(newCreateDetail.getDpi())&&newCreateDetail.getDpi()>0) {
             createPadBO.setDpi(newCreateDetail.getDpi());
         }
         if (StringUtils.isNotEmpty(newCreateDetail.getDns())) {
             String[] dnsStrArr = newCreateDetail.getDns().split(";");
             if(dnsStrArr.length>=2){
                 createPadBO.setDns2(dnsStrArr[1]);
             }
             createPadBO.setDns1(dnsStrArr[0]);
         }
         instanceManager.setDefaultDNS(createPadBO);
         String requestDataJson = JSON.toJSONString(createPadBO);
         taskRelInstanceDetailMapper.updateContainerProperty(newCreateDetail.getId(), requestDataJson);
         cbsInstanceManager.create(createPadBO);
     }
 
     private void removeInstance(InstanceTask task) {
         String deviceIp = task.getDeviceIp();
         String instanceName = task.getInstanceName();
 
         log.debug("InstanceRestartTaskHandler removeInstance deviceIp:{} instanceName:{}", deviceIp, instanceName);
         try {
             boolean instanceExist = cbsInstanceManager.listAll(deviceIp).stream()
                     .anyMatch(instance -> instanceName.equals(instance.getName()));
             if (!instanceExist) {
                 log.debug("InstanceRestartTaskHandler removeInstance deviceIp:{} instanceName:{} not found skip remove", deviceIp, instanceName);
                 return;
             }
 
             cbsInstanceManager.remove(deviceIp, instanceName, task.getClearDiskData());
         } catch (Exception e) {
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         }
     }
 
     protected InstanceModifyPropertiesTaskExecuteHandler(CmsTaskManager cmsTaskManager,
                                                          RedissonDistributedLock redissonDistributedLock,
                                                          InstanceManager cbsInstanceManager,
                                                          InstanceTaskMapper instanceTaskMapper,
                                                          InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                          DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                          TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                                                          HarborConfigManage harborConfigManage,
                                                          CBSHarborManager cbsHarborManager,
                                                          net.armcloud.paascenter.cms.manager.InstanceManager instanceManager,
                                                          InstanceDetailImageSuccService instanceDetailImageSuccService) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.cbsInstanceManager = cbsInstanceManager;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.harborConfigManage = harborConfigManage;
         this.cbsHarborManager = cbsHarborManager;
         this.instanceManager = instanceManager;
         this.instanceDetailImageSuccService = instanceDetailImageSuccService;
     }
 }