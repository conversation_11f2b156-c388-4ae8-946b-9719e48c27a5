 package net.armcloud.paascenter.cms.utils;
 
 import lombok.extern.slf4j.Slf4j;
 
 @Slf4j
 public class DCUtils {
     public static long getDcId() {
         // 从环境变量中读取
         try {
             return Long.parseLong(System.getenv("DC_ID"));
         } catch (Exception e) {
             log.error("load dcId error >>>>", e);
             throw new RuntimeException("load dcId error");
         }
     }
 
 }