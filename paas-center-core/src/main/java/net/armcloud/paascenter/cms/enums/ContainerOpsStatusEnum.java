 package net.armcloud.paascenter.cms.enums;
 
 import lombok.Getter;
 
 @Getter
 public enum ContainerOpsStatusEnum {
     SUCCESS(0, "success"),
     FAIL(-1, "fail"),
     OPS_PARAM_NULL(100, "operate param is null"),
     OPS_NECESSARY_PARAM_ABSENT(101, "necessary operate param absent"),
     OPS_STR_PARAM_IS_BLANK(102, "necessary string operate param is blank"),
     ;
     public final Integer code;
     public final String msg;
 
     ContainerOpsStatusEnum(Integer code, String msg) {
         this.code = code;
         this.msg = msg;
     }
 
 }