package net.armcloud.paascenter.cms.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/3/29 14:56
 * @Description:
 */
@Data
@ApiModel("容器网络关闭DTO")
public class ContainerNetShutdownDTO {
    @ApiModelProperty("容器名字(用于后续容器操作标识)")
    private String name;
    @ApiModelProperty("是否异步执行任务")
    private Boolean async = false;
    @ApiModelProperty("是否重置容器数据")
    private String ResetContainerData;
    @ApiModelProperty("是否回收RBD")
    private Boolean RemoveRBD;
    @ApiModelProperty("存储ID")
    private String StorageId;
    @ApiModelProperty("是否强制删除(网存实例关机时是否需要强制删除数据)")
    private Boolean forceDelete = false;

}
