 package net.armcloud.paascenter.cms.service.handler.device.restart;
 
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.List;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.DEVICE_RESTART;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY;
 
 @Slf4j
 @Service
 public class DeviceRestartTaskResultHandler extends AbstractDeviceTaskHandler {
     private final SystemManager systemManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final RedissonDistributedLock redissonDistributedLock;
 
     /**
      * 验证进行中的任务结果
      */
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<DeviceTask> deviceTasks = cmsDeviceTaskMapper.listWaitVerifyResultTask(DCUtils.getDcId(), DEVICE_RESTART.getIntValue());
             if (CollectionUtils.isEmpty(deviceTasks)) {
                 return;
             }
 
             deviceTasks.forEach(deviceTask -> {
                 String deviceIp = deviceTask.getIp();
                 try {
                     if (systemManager.health(deviceIp)) {
                         super.updateStatus(deviceTask, SUCCESS, "", EXECUTING.getStatus());
                     }
                 } catch (Exception e) {
                     super.updateStatus(deviceTask, FAIL_ALL, "板卡程序连接失败", EXECUTING.getStatus());
                 }
             });
         } catch (Exception e) {
             log.error("DeviceRestartTaskResultHandler error>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     public DeviceRestartTaskResultHandler(RedissonDistributedLock redissonDistributedLock,
                                           DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                           SystemManager systemManager, CmsTaskManager cmsTaskManager,
                                           DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper,
                                           DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                           InstanceDetailImageSuccService instanceDetailImageSuccService, TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, defaultRocketMqProducerWrapper, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.systemManager = systemManager;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
     }
 }