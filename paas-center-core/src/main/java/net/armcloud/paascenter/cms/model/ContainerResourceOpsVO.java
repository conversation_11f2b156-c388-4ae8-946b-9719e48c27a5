 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.*;
 
 @Data
 @AllArgsConstructor
 @NoArgsConstructor
 @ApiModel("容器操作基础类")
 public class ContainerResourceOpsVO implements IBaseOpsVo {
 
     @ApiModelProperty("返回状态码")
     private Integer code;
 
     @ApiModelProperty("返回消息")
     private String msg;
 
     @ApiModelProperty("实际的命令执行返回结果")
     private String dataPayload;
 }