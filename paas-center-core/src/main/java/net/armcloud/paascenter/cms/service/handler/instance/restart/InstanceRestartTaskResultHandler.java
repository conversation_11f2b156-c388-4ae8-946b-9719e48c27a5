 package net.armcloud.paascenter.cms.service.handler.instance.restart;
 
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Date;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.INSTANCE_RESTART;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_INSTANCE_RESTART_VERIFY_LOCK_KEY;
 import static net.armcloud.paascenter.cms.utils.DCUtils.getDcId;
 import static java.util.Collections.singletonList;
 
 @Slf4j
 @Service
 public class InstanceRestartTaskResultHandler extends AbstractInstanceTaskHandler {
     private final InstanceManager instanceManager;
     private final InstanceTaskMapper instanceTaskMapper;
     private final RedissonDistributedLock redissonDistributedLock;
 
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     public void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start verify running task result....", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_INSTANCE_RESTART_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_INSTANCE_RESTART_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_INSTANCE_RESTART_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_INSTANCE_RESTART_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<InstanceTask> tasks = instanceTaskMapper.listByStatusAndTargetTaskTypes(getDcId(), EXECUTING.getStatus(),
                     singletonList(INSTANCE_RESTART.getIntValue()));
             if (CollectionUtils.isEmpty(tasks)) {
                 return;
             }
 
             tasks.stream()
                     .collect(Collectors.groupingBy(InstanceTask::getDeviceIp))
                     .forEach((deviceIp, taskList) -> {
                         List<InstanceInfoResponse> instanceInfoResponses;
                         try {
                             instanceInfoResponses = instanceManager.listAll(deviceIp);
                         } catch (Exception e) {
                             return;
                         }
 
                         Map<String, InstanceInfoResponse> instanceInfoResponseMap = instanceInfoResponses.stream()
                                 .collect(Collectors.toMap(InstanceInfoResponse::getName, obj -> obj, (o1, o2) -> o1));
                         taskList.forEach(task -> {
                             String instanceName = task.getInstanceName();
                             InstanceInfoResponse instanceInfoResponse = instanceInfoResponseMap.get(instanceName);
                             if (Objects.isNull(instanceInfoResponse)) {
                                 return;
                             }
 
                             Date startUpTime = instanceInfoResponse.convertStartTimeToDate();
                             if (Objects.isNull(startUpTime)) {
                                 return;
                             }
 
                             if (task.getStartTime().after(startUpTime)) {
                                 return;
                             }
 
                             // 任务结束时间优先取实例启动时间
                             super.updateStatus(task, TaskStatusConstants.SUCCESS, "", EXECUTING.getStatus());
                         });
                     });
         } catch (Exception e) {
             log.error("InstanceRestartTaskResultHandler>>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     protected InstanceRestartTaskResultHandler(CmsTaskManager cmsTaskManager,
                                                RedissonDistributedLock redissonDistributedLock, InstanceManager instanceManager,
                                                InstanceTaskMapper instanceTaskMapper,
                                                InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceManager = instanceManager;
         this.instanceTaskMapper = instanceTaskMapper;
     }
 }