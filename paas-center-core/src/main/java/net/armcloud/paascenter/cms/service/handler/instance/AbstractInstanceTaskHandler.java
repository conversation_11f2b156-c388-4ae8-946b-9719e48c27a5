 package net.armcloud.paascenter.cms.service.handler.instance;
 
 import cn.hutool.core.bean.BeanUtil;
 import com.alibaba.fastjson2.JSON;
 import com.google.common.util.concurrent.ThreadFactoryBuilder;
 import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
 import net.armcloud.paascenter.common.model.mq.container.ContainerInstanceTaskResultMQ;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Value;
 
 import javax.annotation.PostConstruct;
 import java.util.Date;
 import java.util.concurrent.ExecutorService;
 import java.util.concurrent.LinkedBlockingQueue;
 import java.util.concurrent.ThreadPoolExecutor;
 import java.util.concurrent.TimeUnit;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 
 @Slf4j
 public abstract class AbstractInstanceTaskHandler {
     private final CmsTaskManager cmsTaskManager;
     private final InstanceTaskMapper instanceTaskMapper;
     private final InstanceTaskStatusMQConfig instanceTaskStatusMQConfig;
     private final DefaultRocketMqProducerWrapper rocketMqProducerService;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     public final InstanceDetailImageSuccService instanceDetailImageSuccService;
 
     @Value("${thread.instance.core-size}")
     private Integer coreSize;
 
     @Value("${thread.instance.max-size}")
     private Integer maxSize;
 
     private static final int CORE_POOL_SIZE;
 
     private static final int QUEUE_SIZE = 30000;
 
     static {
         CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors();
     }
 
     private static final int MAXIMUM_POOL_SIZE = CORE_POOL_SIZE * 2 + 1;
     private static final long KEEP_ALIVE_TIME = 30L;
     private static final int LINKED_BLOCKING_QUEUE_CAPACITY = Integer.MAX_VALUE;
 
     private ExecutorService threadPool;
 
     protected void asyncExecute(Runnable command) {
         threadPool.execute(command);
     }
 
     protected boolean updateStatus(InstanceTask instanceTask, TaskStatusConstants status, String msg, Integer originStatus) {
         boolean success = false;
         long taskId = instanceTask.getId();
 
         if (status == WAIT_EXECUTE) {
             success = instanceTaskMapper.updateStatusById(taskId, status.getStatus(), msg, null, null, null, originStatus) > 0;
         }
 
         if (status == EXECUTING) {
             Date timeoutTime = cmsTaskManager.getTimeoutTime(instanceTask.getType());
             success = instanceTaskMapper.updateStatusById(taskId, status.getStatus(), msg, new Date(), null, timeoutTime, originStatus) > 0;
         }
 
         if (status != WAIT_EXECUTE && status != EXECUTING) {
             success = instanceTaskMapper.updateStatusById(taskId, status.getStatus(), msg, null, new Date(), null, originStatus) > 0;
         }
 
         //100,204任务执行成功 则插入task_rel_instance_detail_image_succ表
         if(status == SUCCESS && (TaskTypeEnum.DEVICE_CREATE.getIntValue().equals(instanceTask.getType()) || TaskTypeEnum.INSTANCE_UPGRADE_IMAGE.getIntValue().equals(instanceTask.getType()))){
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getByMasterTaskIdAndSubTaskId(instanceTask.getType(),instanceTask.getId(),instanceTask.getId());
             if(taskRelInstanceDetail != null){
                 TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = BeanUtil.copyProperties(taskRelInstanceDetail,TaskRelInstanceDetailImageSucc.class);
                 instanceDetailImageSuccService.saveAndClear(taskRelInstanceDetailImageSucc);
             }
         }
 
         if (success) {
             ContainerInstanceTaskResultMQ message = new ContainerInstanceTaskResultMQ();
             message.setPadCode(instanceTask.getInstanceName());
             message.setMasterTaskId(instanceTask.getId());
             message.setMasterTaskStatus(status.getStatus());
             message.setMsg(msg);
             //发送消息到topic:vcp_cms_instance_task_status
             rocketMqProducerService.producerNormalMessage(instanceTaskStatusMQConfig.getTopic(), JSON.toJSONString(message));
         }
 
         return success;
     }
 
     /**
      * 获取最新的镜像信息
      * @param instanceName
      * @return
      */
     protected TaskRelInstanceDetail getCreateTaskRelPadDetail(String instanceName) {
         TaskRelInstanceDetail upgradeImageTaskRelPadDetail = instanceDetailImageSuccService.getLastInfoAndReplace(instanceName);
         return upgradeImageTaskRelPadDetail;
     };
 
     protected AbstractInstanceTaskHandler(CmsTaskManager cmsTaskManager, InstanceTaskMapper instanceTaskMapper,
                                           InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                           DefaultRocketMqProducerWrapper rocketMqProducerService,
                                           InstanceDetailImageSuccService instanceDetailImageSuccService,
                                           TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         this.cmsTaskManager = cmsTaskManager;
         this.instanceTaskMapper = instanceTaskMapper;
         this.instanceTaskStatusMQConfig = instanceTaskStatusMQConfig;
         this.rocketMqProducerService = rocketMqProducerService;
         this.instanceDetailImageSuccService = instanceDetailImageSuccService;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
     }
 
     @PostConstruct
     private void initThread() {
         threadPool = new ThreadPoolExecutor(coreSize, maxSize,
                 KEEP_ALIVE_TIME, TimeUnit.SECONDS, new LinkedBlockingQueue<>(QUEUE_SIZE), new ThreadFactoryBuilder()
                 .setNameFormat("InstanceTaskPool-%d").build(), new ThreadPoolExecutor.AbortPolicy());
     }
 }