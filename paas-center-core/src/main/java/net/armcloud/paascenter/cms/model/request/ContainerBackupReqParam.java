 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 @Data
 public class ContainerBackupReqParam {
 
     private String containerName;
 
     private String minioEndpoint;
 
     private String minioAccessKey;
 
     private String minioSecretKey;
 
     private String minioBucket;
 
     /**
      * 容器备份的文件存储在minIO中的路径
      * 注意：此字段值是目录，备份成功后会在此目录下存放多个文件。注意文件目录重复导致源文件被覆盖问题!!!
      * 参数值示例：/container_backup/AC32010210011/20241115
      */
     private String minioStorageDirectory;
 
 }