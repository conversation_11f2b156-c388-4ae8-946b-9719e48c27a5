 package net.armcloud.paascenter.cms.service.handler.device.create;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.cms.enums.ContainerImageStatusEnum;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.*;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.apache.commons.lang3.StringUtils;
 import org.redisson.api.RLock;
 import org.springframework.beans.factory.annotation.Value;
 import org.springframework.stereotype.Service;
 
 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Objects;
 import java.util.Optional;
 
 import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.ENABLE_STANDARD_ROUTE;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.*;
 import static net.armcloud.paascenter.cms.exception.code.ImageExceptionCode.LOGIN_IMAGE_REPOSITORY_FAIL_EXCEPTION;
 
 @Slf4j
 @Service
 public class DeviceVirtualizeTaskExecuteHandler extends AbstractDeviceTaskHandler {
     private final DiskManager diskManager;
     private final ImageManager imageManager;
     private final SystemManager systemManager;
     private final NetworkManager networkManager;
     private final InstanceManager instanceManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final HarborConfigManage harborConfigManage;
     private final ConfigurationMapper configurationMapper;
     private final RedissonDistributedLock redissonDistributedLock;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
 
     @Value("${generate-macvlan-in-common-mode-file}")
     private Boolean generateMacvlanInCommonModeFile;
 
     public void start(DeviceTask task) {
         super.asyncExecute(() -> tryStart(task));
     }
 
     private void tryStart(DeviceTask task) {
         String deviceIp = task.getIp();
         long taskId = task.getId();
         String dataJson = JSON.toJSONString(task);
 
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start exec device virtualize task:{}", currentThreadId, dataJson);
         String key = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} exec device virtualize task:{} not get lock key {}", currentThreadId, dataJson, key);
             return;
         }
 
         try {
             boolean updateSuccess = super.updateStatus(task, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!updateSuccess) {
                 return;
             }
 
             cleanExistInstance(task);
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getLatestByMasterTaskId(task.getType(), taskId);
             partitionDisk(task, taskRelInstanceDetail);
             initNetwork(task);
             restartDocker(deviceIp, taskId);
             loginHarbor(deviceIp, taskId);
             pullImage(task, taskRelInstanceDetail);
         } catch (Exception e) {
             log.error("execDeviceVirtualizeTask error>>>> masterTaskId:{}", taskId, e);
             super.updateStatus(task, FAIL_ALL, e.getMessage(), null);
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     /**
      * 拉取镜像
      */
     private void pullImage(DeviceTask task, TaskRelInstanceDetail taskRelInstanceDetail) {
         HarborConfig harborConfig = harborConfigManage.get();
         String imageRepository = harborConfig.getUrl() + "/" + harborConfig.getProject() + "/" + taskRelInstanceDetail.getImageId();
         String deviceIp = task.getIp();
         String imageTag = taskRelInstanceDetail.getImageTag();
         long taskId = task.getId();
 
         int pullStatus = imageManager.getPullStatus(deviceIp, imageRepository, imageTag);
         if (pullStatus == ContainerImageStatusEnum.SUCCESS.getCode()) {
             String progressDescription = "\n" + LocalDateTime.now() + " " + imageRepository + " " + imageTag + "镜像拉取成功";
             cmsDeviceTaskMapper.updateProgressDescription(taskId, progressDescription);
             return;
         }
 
         if (pullStatus == ContainerImageStatusEnum.PENDING.getCode()) {
             String progressDescription = "\n" + LocalDateTime.now() + " " + imageRepository + " " + imageTag + "镜像拉取中";
             cmsDeviceTaskMapper.updateProgressDescription(taskId, progressDescription);
             return;
         }
 
         String progressDescription = "\n" + LocalDateTime.now() + " " + imageRepository + " " + imageTag + " 开始拉取镜像 ";
         cmsDeviceTaskMapper.updateProgressDescription(taskId, progressDescription);
         imageManager.pull(deviceIp, imageRepository, imageTag, true);
     }
 
     private void restartDocker(String deviceIp, long taskId) {
         try {
             systemManager.restartDocker(deviceIp);
             cmsDeviceTaskMapper.updateProgressDescription(taskId, "\n" + LocalDateTime.now() + " Docker重启成功");
         } catch (Exception e) {
             log.error("restartDocker error>>>> deviceIp:{}", deviceIp, e);
             throw new BasicException(DEVICE_RESTART_DOCKER_FAILED_EXCEPTION);
         }
     }
 
     private void partitionDisk(DeviceTask deviceTask, TaskRelInstanceDetail taskRelInstanceDetail) {
         long taskId = deviceTask.getId();
         String deviceIp = deviceTask.getIp();
         try {
             long containerStorageSize = Optional.ofNullable(taskRelInstanceDetail)
                     .map(TaskRelInstanceDetail::getDisk).map(Long::valueOf).orElse(0L);
             diskManager.partition(deviceTask.getIp(), deviceTask.getHostStorageSize(), containerStorageSize,
                     deviceTask.getContainerSize(), deviceTask.getIsolateDisk());
             cmsDeviceTaskMapper.updateProgressDescriptionAndDiskDone(taskId, "\n" + LocalDateTime.now() + " 磁盘隔离完成", true);
         } catch (Exception e) {
             log.error("deviceIp:{} 磁盘格式化失败:", deviceIp, e);
             throw new BasicException(DEVICE_PARTITION_DISK_FAILED_EXCEPTION);
         }
     }
 
     private void cleanExistInstance(DeviceTask task) {
         String deviceIp = task.getIp();
         long deviceTaskId = task.getId();
 
         List<InstanceInfoResponse> existInstances = instanceManager.listAll(deviceIp);
         if (CollectionUtils.isEmpty(existInstances)) {
             cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, LocalDateTime.now() + " 前置检查完成");
             return;
         }
 
         StringBuilder progressDescription = new StringBuilder();
         progressDescription.append(LocalDateTime.now()).append(" start remove already exists instances:[");
         existInstances.forEach(instanceInfoResponse ->
                 progressDescription
                         .append(instanceInfoResponse.getName())
                         .append("@")
                         .append(instanceInfoResponse.getExtId())
                         .append("]"));
 
         cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, progressDescription.toString());
         String errorMsg = instanceManager.destroyAll(deviceIp);
         if (StringUtils.isNotBlank(errorMsg)) {
             throw new BasicException(PROCESSING_FAILED.getStatus(), "历史数据清理失败");
         }
 
         cmsDeviceTaskMapper.updateProgressDescription(deviceTaskId, "\n" + LocalDateTime.now() + " " + " 历史数据清理完成");
     }
 
     private void loginHarbor(String deviceIp, long taskId) {
         String loginHarborTemplateCmd = "echo %s | docker login %s --username %s --password-stdin";
         HarborConfig harborConfig = harborConfigManage.get();
         String loginCmd = String.format(loginHarborTemplateCmd, harborConfig.getPassword(), harborConfig.getUrl(), harborConfig.getUsername());
         String loginResult = systemManager.execSyncCmd(deviceIp, loginCmd);
         if (StringUtils.isBlank(loginResult) || !loginResult.contains("Succeeded")) {
             throw new BasicException(LOGIN_IMAGE_REPOSITORY_FAIL_EXCEPTION.getStatus(), "login harbor fail");
         }
 
         cmsDeviceTaskMapper.updateProgressDescription(taskId, "\n" + LocalDateTime.now() + " Harbor登录成功");
     }
 
     private void initNetwork(DeviceTask deviceTask) {
         String deviceIp = deviceTask.getIp();
         long taskId = deviceTask.getId();
         log.debug("start initNetwork deviceIp={} deviceTask={}", deviceIp, JSON.toJSONString(deviceTask));
 
         try {
             String macvlanInCommonMode = configurationMapper.selectValueByKey(ENABLE_STANDARD_ROUTE);
             if (StringUtils.isNotBlank(macvlanInCommonMode)) {
                 generateMacvlanInCommonModeFile = Boolean.parseBoolean(macvlanInCommonMode);
             }
 
             if (Boolean.TRUE.equals(generateMacvlanInCommonModeFile)) {
                 systemManager.execSyncCmd(deviceIp, "touch /root/macvlanInCommonMode");
             }
 
             networkManager.macVlanCreate(deviceIp, deviceTask.getSubnet(), deviceTask.getIpRange(),
                     deviceTask.getGateway(), deviceTask.getNetworkDeviceName());
 
             String progress = "\n" + LocalDateTime.now() + " 网络创建完成";
             cmsDeviceTaskMapper.updateProgressDescriptionAndNetworkDone(taskId, progress, true);
         } catch (Exception e) {
             log.error("initNetwork deviceIp={} error>>>", deviceIp, e);
             throw new BasicException(DEVICE_NETWORK_CREATE_FAILED_EXCEPTION.getStatus(), DEVICE_NETWORK_CREATE_FAILED_EXCEPTION.getMsg() + e.getMessage());
         }
     }
 
     public DeviceVirtualizeTaskExecuteHandler(CmsTaskManager cmsTaskManager, InstanceManager instanceManager,
                                               RedissonDistributedLock redissonDistributedLock,
                                               TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                                               DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                               DefaultRocketMqProducerWrapper rocketMqProducerService,
                                               DeviceTaskStatusMQConfig deviceTaskStatusMQConfig,
                                               NetworkManager networkManager, SystemManager systemManager, DiskManager diskManager,
                                               CmsDeviceTaskMapper cmsDeviceTaskMapper, ImageManager imageManager, HarborConfigManage harborConfigManage, ConfigurationMapper configurationMapper,
                                               InstanceDetailImageSuccService instanceDetailImageSuccService) {
         super(cmsTaskManager, deviceInstanceTaskMapper, rocketMqProducerService, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceManager = instanceManager;
         this.redissonDistributedLock = redissonDistributedLock;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.networkManager = networkManager;
         this.systemManager = systemManager;
         this.diskManager = diskManager;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
         this.imageManager = imageManager;
         this.harborConfigManage = harborConfigManage;
         this.configurationMapper = configurationMapper;
     }
 }