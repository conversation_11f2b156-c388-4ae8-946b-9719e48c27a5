 package net.armcloud.paascenter.cms.manager.cbs.model.bo;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.cms.model.request.VirtualizeDeviceRequest;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import lombok.Data;
 import org.apache.commons.lang3.StringUtils;
 
 @Data
 public class CreatePadBO {
     private String deviceIp;
     private String name;
     private Long cpuLimit;
     private Long memoryLimit;
     private Long storageLimit;
     private String ip;
     private String hostname;
     private String imageRepository;
     private String imageTag;
     private String extId;
     private Boolean async;
     private Boolean isolateDisk;
     private Integer hostStorageSize;
     private Integer containerConcurrentSize;
     private Integer containerIndex;
     private Integer width;
     private Integer height;
     private Integer fps;
     private Integer dpi;
     private String otherAndroidProp;
     private Boolean clearContainerData;
     private String macvlanName;
     private String dns1;
     private String dns2;
     private String downloadUrlOfADI;
     private String passwordOfADI;
     private String deviceAndroidProps;
     private String mac;
     private String androidCertData;
 
     public static CreatePadBO builder(DeviceTask deviceTask, TaskRelInstanceDetail taskRelPadDetail, HarborConfig harborConfig) {
         String instanceName = taskRelPadDetail.getInstanceName();
         CreatePadBO createPadBO = new CreatePadBO();
         createPadBO.setDeviceIp(deviceTask.getIp());
         createPadBO.setHostname(instanceName);
         createPadBO.setName(instanceName);
         createPadBO.setCpuLimit(taskRelPadDetail.getCpu().longValue());
         createPadBO.setMemoryLimit(taskRelPadDetail.getMemory().longValue());
         createPadBO.setStorageLimit(taskRelPadDetail.getDisk().longValue());
         createPadBO.setIp(taskRelPadDetail.getIp());
         String imageRepository = harborConfig.getUrl() + "/" + harborConfig.getProject() + "/" + taskRelPadDetail.getImageId();
         createPadBO.setImageRepository(imageRepository);
         createPadBO.setImageTag(taskRelPadDetail.getImageTag());
         createPadBO.setAsync(true);
         createPadBO.setExtId(taskRelPadDetail.getIdentificationCode());
         createPadBO.setIsolateDisk(Boolean.TRUE.equals(deviceTask.getIsolateDisk()));
         createPadBO.setHostStorageSize(deviceTask.getHostStorageSize());
         createPadBO.setContainerConcurrentSize(deviceTask.getContainerSize());
         createPadBO.setContainerIndex(taskRelPadDetail.getContainerIndex());
         createPadBO.setWidth(taskRelPadDetail.getWidth());
         createPadBO.setHeight(taskRelPadDetail.getHeight());
         createPadBO.setFps(taskRelPadDetail.getFps());
         createPadBO.setDpi(taskRelPadDetail.getDpi());
         createPadBO.setOtherAndroidProp(taskRelPadDetail.getAndroidProp());
         setDnsInfo(createPadBO, taskRelPadDetail.getDns());
         setADIInfo(createPadBO, taskRelPadDetail.getAdiJson());
         createPadBO.setDeviceAndroidProps(taskRelPadDetail.getDeviceAndroidProp());
         createPadBO.setMac(taskRelPadDetail.getMac());
 
         String networkDeviceName = deviceTask.getNetworkDeviceName();
         if (StringUtils.isNotBlank(networkDeviceName) && networkDeviceName.contains(".")) {
             int lastIndex = networkDeviceName.lastIndexOf(".");
             String macVlanSuffix = networkDeviceName.substring(lastIndex + 1);
             String macvlanName = "macvlan" + macVlanSuffix;
             createPadBO.setMacvlanName(macvlanName);
         }
         return createPadBO;
     }
 
     public static void setADIInfo(CreatePadBO createPadBO, String adiJson) {
         if (StringUtils.isBlank(adiJson)) {
             return;
         }
 
         VirtualizeDeviceRequest.ADI adi = JSON.parseObject(adiJson, VirtualizeDeviceRequest.ADI.class);
         if (adi == null) {
             return;
         }
 
         createPadBO.setDownloadUrlOfADI(adi.getTemplateUrl());
         createPadBO.setPasswordOfADI(adi.getTemplatePassword());
         createPadBO.setAndroidCertData(adi.getAndroidCertData());
     }
 
     public static void setDnsInfo(CreatePadBO createPadBO, String dns) {
         if (StringUtils.isBlank(dns)) {
             return;
         }
 
         String[] dnsArr = dns.split(",");
         if (dnsArr.length <= 0) {
             return;
         }
 
         createPadBO.setDns1(dnsArr[0]);
         if (dnsArr.length > 1) {
             createPadBO.setDns2(dnsArr[1]);
         }
     }
 }