 package net.armcloud.paascenter.cms.service;
 
 import cn.hutool.core.bean.BeanUtil;
 import cn.hutool.core.collection.CollUtil;
 import cn.hutool.core.util.StrUtil;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailImageSuccMapper;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 
 import javax.annotation.Resource;
 import java.util.Arrays;
 import java.util.List;
 import java.util.Objects;

 /**
  * 任务关联实例信息详情表(镜像相关执行成功记录)
  */
 @Slf4j
 @Service
 public class InstanceDetailImageSuccService {
 
     @Resource
     private TaskRelInstanceDetailImageSuccMapper taskRelInstanceDetailImageSuccMapper;
 
     /**
      * 保存并清除历史
      * 任务成功时保存
      * 目前只保留最近7条数据
      * @param param
      */
     @Transactional(rollbackFor = Exception.class)
     public void saveAndClear(TaskRelInstanceDetailImageSucc param){
         if(StrUtil.isEmpty(param.getContainerProperty())){
             log.info("saveAndClear containerProperty is null,id:{}",param.getId());
             return;
         }
         param.setId(null);
         List<Long> assignDataIds = taskRelInstanceDetailImageSuccMapper.getAssignDataIds(null,param.getInstanceName(), null,0);
         TaskRelInstanceDetail lastInfo = getLastInfo(param.getInstanceName());
         //一键新机等任务没有写入网存标记,从上次的记录中获取.这样网存标记可以在每次新写入的时候传递
         if(Objects.nonNull(lastInfo)){
             param.setNetStorageResFlag((long)lastInfo.getNetStorageResFlag());
             param.setNetStorageResId(lastInfo.getNetStorageResId());
         }
         int respCount = taskRelInstanceDetailImageSuccMapper.insert(param);
         if(respCount > 0 && CollUtil.isNotEmpty(assignDataIds)){
             taskRelInstanceDetailImageSuccMapper.deleteBatchIds(assignDataIds);
         }
     }
 
     /**
      * 获取指定实例最近一次镜像相关信息(100,204)
      * @param padCode
      */
     public TaskRelInstanceDetail getLastInfo(String padCode){
         TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = taskRelInstanceDetailImageSuccMapper.selectOne(new QueryWrapper<>(TaskRelInstanceDetailImageSucc.class)
                 .eq("instance_name",padCode)
                 .in("task_type", Arrays.asList(100,204,209,213))
                 .orderByDesc("id")
                 .last("limit 1"));
         TaskRelInstanceDetail taskRelInstanceDetail = BeanUtil.copyProperties(taskRelInstanceDetailImageSucc,TaskRelInstanceDetail.class);
         return taskRelInstanceDetail;
     }
 
     /**
      * 获取指定实例最近一次镜像相关信息(100,204)
      * @param padCode
      */
     public TaskRelInstanceDetail getLastInfoAndReplace(String padCode){
         TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = taskRelInstanceDetailImageSuccMapper.selectOne(new QueryWrapper<>(TaskRelInstanceDetailImageSucc.class)
                 .eq("dc_id",DCUtils.getDcId())
                 .eq("instance_name",padCode)
                 .in("task_type", Arrays.asList(100,204,209,213))
                 .orderByDesc("id")
                 .last("limit 1"));
         TaskRelInstanceDetail taskRelInstanceDetail = BeanUtil.copyProperties(taskRelInstanceDetailImageSucc,TaskRelInstanceDetail.class);
         return taskRelInstanceDetail;
     }
 
 }