 package net.armcloud.paascenter.cms.service.handler.instance.datarestore;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.cms.model.response.GetContainerRestoreStatusRespVO;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceRestoreManage;
 import net.armcloud.paascenter.cms.mapper.InstanceBackupDataProgressMapper;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.List;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.INSTANCE_RESTORE_BACKUP_DATA;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY;
 import static net.armcloud.paascenter.cms.utils.DCUtils.getDcId;
 import static java.util.Collections.singletonList;
 
 @Slf4j
 @Service
 public class InstanceRestoreTaskResultHandler extends AbstractInstanceTaskHandler {
     private final InstanceTaskMapper instanceTaskMapper;
     private final InstanceRestoreManage instanceRestoreManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final InstanceBackupDataProgressMapper instanceBackupDataProgressMapper;
 
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start verify running task result....", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<InstanceTask> tasks = instanceTaskMapper.listByStatusAndTargetTaskTypes(getDcId(), EXECUTING.getStatus(),
                     singletonList(INSTANCE_RESTORE_BACKUP_DATA.getIntValue()));
             if (CollectionUtils.isEmpty(tasks)) {
                 return;
             }
 
             tasks.forEach(task -> {
                 try {
                     handlerRestoreProgress(task);
                 } catch (Exception e) {
                     log.error("handlerRestoreProgress error>>>> task:{}", JSON.toJSONString(task), e);
                 }
             });
         } catch (Exception e) {
             log.error("InstanceDataBackupTaskResultHandler error>>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void handlerRestoreProgress(InstanceTask task) {
         String deviceIp = task.getDeviceIp();
         String instanceName = task.getInstanceName();
         InstanceBackupDataProgress dataProgress = instanceBackupDataProgressMapper.getByInstanceTaskId(task.getId());
         GetContainerRestoreStatusRespVO response = instanceRestoreManage.status(deviceIp, instanceName, dataProgress.getPath());
         if (response.inProgress()) {
             return;
         }
 
         if (response.failed()) {
             super.updateStatus(task, FAIL_ALL, "恢复失败", EXECUTING.getStatus());
             return;
         }
 
         if (response.succeed()) {
             super.updateStatus(task, SUCCESS, "", EXECUTING.getStatus());
         }
     }
 
     protected InstanceRestoreTaskResultHandler(CmsTaskManager cmsTaskManager, InstanceTaskMapper instanceTaskMapper,
                                                InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                InstanceRestoreManage instanceRestoreManage,
                                                RedissonDistributedLock redissonDistributedLock,
                                                InstanceBackupDataProgressMapper instanceBackupDataProgressMapper,
                                                InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceTaskMapper = instanceTaskMapper;
         this.instanceRestoreManage = instanceRestoreManage;
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceBackupDataProgressMapper = instanceBackupDataProgressMapper;
     }
 }