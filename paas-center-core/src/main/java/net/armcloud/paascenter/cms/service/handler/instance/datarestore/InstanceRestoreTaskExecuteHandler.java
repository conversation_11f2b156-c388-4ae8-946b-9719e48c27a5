 package net.armcloud.paascenter.cms.service.handler.instance.datarestore;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceRestoreManage;
 import net.armcloud.paascenter.cms.mapper.InstanceBackupDataProgressMapper;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX;
 
 @Slf4j
 @Service
 public class InstanceRestoreTaskExecuteHandler extends AbstractInstanceTaskHandler {
     private final InstanceManager instanceManager;
     private final InstanceRestoreManage instanceRestoreManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final InstanceBackupDataProgressMapper instanceBackupDataProgressMapper;
 
     public void start(InstanceTask instanceTask) {
         super.asyncExecute(() -> tryStart(instanceTask));
     }
 
     private void tryStart(InstanceTask instanceTask) {
         String instanceTaskJson = JSON.toJSONString(instanceTask);
         log.info("InstanceRestoreTaskExecuteHandler executeTask start handler instanceTask:{}", instanceTaskJson);
         String key = EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX + instanceTask.getInstanceName();
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, key);
             return;
         }
 
         RLock deviceTaskLock = null;
         try {
             // 云机任务与实例任务互斥，要执行任务则需要拿到板卡锁
             String deviceIp = instanceTask.getDeviceIp();
             String deviceTaskLockKey = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
             if (redissonDistributedLock.isHeldByCurrentThread(deviceTaskLockKey)) {
                 log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
                 return;
             }
 
             deviceTaskLock = redissonDistributedLock.tryLock(key, 0, 300);
             if (Objects.isNull(deviceTaskLock)) {
                 log.debug("thread {} not get lock {} skip....", currentThreadId, key);
                 return;
             }
 
             restore(instanceTask);
         } catch (Exception e) {
             log.error("InstanceRestoreTaskExecuteHandler error>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
             if (deviceTaskLock != null) {
                 redissonDistributedLock.unlock(deviceTaskLock);
             }
         }
     }
 
     private void restore(InstanceTask task) {
         try {
             boolean success = super.updateStatus(task, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
 
             String deviceIp = task.getDeviceIp();
             String instanceName = task.getInstanceName();
             boolean instanceExist = instanceManager.listAll(deviceIp).stream()
                     .anyMatch(instance -> instanceName.equals(instance.getName()));
             if (!instanceExist) {
                 super.updateStatus(task, FAIL_ALL, instanceName + " 未找到此实例", EXECUTING.getStatus());
                 return;
             }
 
             InstanceBackupDataProgress dataProgress = instanceBackupDataProgressMapper.getByInstanceTaskId(task.getId());
             instanceRestoreManage.restore(deviceIp, instanceName, dataProgress.getPath());
         } catch (Exception e) {
             log.error("InstanceRestoreTaskExecuteHandler padCode:{} task:{} error>>>>", task.getInstanceName(), JSON.toJSONString(task), e);
             super.updateStatus(task, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         }
     }
 
     protected InstanceRestoreTaskExecuteHandler(CmsTaskManager cmsTaskManager, InstanceTaskMapper instanceTaskMapper,
                                                 InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                 DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                 InstanceManager instanceManager, InstanceRestoreManage instanceRestoreManage,
                                                 RedissonDistributedLock redissonDistributedLock,
                                                 InstanceBackupDataProgressMapper instanceBackupDataProgressMapper,
                                                 InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                 TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceManager = instanceManager;
         this.instanceRestoreManage = instanceRestoreManage;
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceBackupDataProgressMapper = instanceBackupDataProgressMapper;
     }
 }