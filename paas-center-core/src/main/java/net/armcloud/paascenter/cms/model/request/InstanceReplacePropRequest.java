 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 
 @Data
 public class InstanceReplacePropRequest {
     @Valid
     @NotNull(message = "instances cannot null")
     private InstanceReplacePropRequest.Instance instances;
 
     @Data
     public static class Instance{
 
         /**
          * 设备IP
          */
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         /**
          * 实例信息
          */
         @NotBlank(message = "padCode cannot null")
         private String padCode;
 
         /**
          * 安卓属性
          */
         private String deviceAndroidProps;
 
         /**
          * mac地址
          */
 
         private String mac;

         /**
          * 实例类型 (默认本地实例)
          */
         private Integer netStorageFlag = 0;

         /**
          * 网存存储Id
          */
         private String netStorageId;

         private ADI adi;

         private VirtualizeDeviceRequest.Device.Pad oldParam;
 
         /**
          * 是否真机
          */
         private Boolean isReal = false;
         @Data
         public static class ADI{
             /**
              * 云真机模板地址
              */
             private String templateUrl;
 
             /**
              * 云真机模板密码
              */
             private String templatePassword;
 
             /**
              * 安卓证书数据
              */
             private String androidCertData;
 
             /**屏幕布局-宽*/
             private Long layoutWidth;
             /**屏幕布局-高*/
             private Long layoutHigh;
             /**屏幕布局-像素密度*/
             private Long layoutDpi;
             /**屏幕布局-刷新率*/
             private Long layoutFps;
             /**
              * adi模板Id
              */
             private Long realPhoneTemplateId;
         }
     }
 }