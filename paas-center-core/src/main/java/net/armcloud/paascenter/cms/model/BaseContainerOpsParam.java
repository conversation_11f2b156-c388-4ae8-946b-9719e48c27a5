 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModelProperty;
 import io.swagger.annotations.ApiParam;
 import lombok.Data;
 import io.swagger.annotations.ApiModel;
 
 @ApiModel("基础容器操作参数")
 @Data
 public class BaseContainerOpsParam {
 
     @ApiModelProperty("容器名字(用于后续容器操作标识)")
     private String name;
 
     @ApiModelProperty("是否异步执行任务")
     private Boolean async = false;
 }