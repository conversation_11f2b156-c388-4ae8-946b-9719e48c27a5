 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class PadsRequest {
     @Valid
     @Size(min = 1, message = "pads cannot null")
     @NotNull(message = "pads cannot null")
     private List<Pad> pads;
 
     @Data
     public static class Pad {
         @NotBlank(message = "ip cannot null")
         private String ip;
         private String padCode;
     }
 }