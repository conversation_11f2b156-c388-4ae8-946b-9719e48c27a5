 package net.armcloud.paascenter.cms.enums;
 
 import lombok.Getter;
 
 @Getter
 public enum ContainerImageStatusEnum {
     SUCCESS(0, "image pull success"),
     FAIL(-1, "image pull fail"),
     PENDING(1, "image is pulling"),
     ;
     public final Integer code;
     public final String msg;
 
     ContainerImageStatusEnum(Integer code, String msg) {
         this.code = code;
         this.msg = msg;
     }
 
 }