 package net.armcloud.paascenter.cms.service.handler.device.cbsupdate;
 
 import cn.hutool.core.date.DateUnit;
 import cn.hutool.core.date.DateUtil;
 import cn.hutool.core.util.StrUtil;
 import com.alibaba.fastjson2.JSONObject;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.CbsSelfUpdateStatusResponse;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.model.SysSelfUpdateDTO;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.*;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.DEVICE_CBS_SELF_UPDATE;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY;
 
 @Slf4j
 @Service
 public class DeviceCbsUpdateTaskResultHandler extends AbstractDeviceTaskHandler {
     private final SystemManager systemManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final RedissonDistributedLock redissonDistributedLock;
 
     /**
      * 验证进行中的任务结果
      */
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<DeviceTask> deviceTasks = cmsDeviceTaskMapper.listWaitVerifyResultTask(DCUtils.getDcId(), DEVICE_CBS_SELF_UPDATE.getIntValue());
             if (CollectionUtils.isEmpty(deviceTasks)) {
                 return;
             }
 
             deviceTasks.forEach(deviceTask -> {
                 //先判断一下当前时间是否大于任务开始10秒以上 如果是则继续执行 否则跳过 （给cbs接收请求预留一点时间）
                 if(deviceTask.getStartTime() != null){
                     long secondsBetween = DateUtil.between(deviceTask.getStartTime(), new Date(), DateUnit.SECOND);
                     if(secondsBetween <= 10){
                         return;
                     }
                 }
                 String deviceIp = deviceTask.getIp();
                 CbsSelfUpdateStatusResponse cbsSelfUpdateStatusResponse = systemManager.cbsUpdateStatus(deviceIp);
                 if(cbsSelfUpdateStatusResponse != null){
                     //状态为0时 表示cbs已执行完
                     if(cbsSelfUpdateStatusResponse.getStatus() == 0 && StrUtil.isNotEmpty(deviceTask.getRequestParamJson())){
                         SysSelfUpdateDTO sysSelfUpdateDTO = JSONObject.parseObject(deviceTask.getRequestParamJson(),SysSelfUpdateDTO.class);
                         if(sysSelfUpdateDTO.getVersion().equals(cbsSelfUpdateStatusResponse.getVersion())){
                             if(StrUtil.isNotEmpty(cbsSelfUpdateStatusResponse.getLastFailedReason())){
                                 super.updateStatus(deviceTask, FAIL_ALL, cbsSelfUpdateStatusResponse.getLastFailedReason(), EXECUTING.getStatus());
                             }else{
                                 Map<String,String> map = new HashMap<>(1);
                                 map.put("cbsVersion",cbsSelfUpdateStatusResponse.getVersion());
                                 super.updateStatus(deviceTask, SUCCESS, "", EXECUTING.getStatus(),JSONObject.toJSONString(map));
                             }
                         }else{
                             super.updateStatus(deviceTask, FAIL_ALL, cbsSelfUpdateStatusResponse.getLastFailedReason(), EXECUTING.getStatus());
                         }
                     }
                 }
             });
         } catch (Exception e) {
             log.error("DeviceCbsUpdateTaskResultHandler error>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     public DeviceCbsUpdateTaskResultHandler(RedissonDistributedLock redissonDistributedLock,
                                             DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                             SystemManager systemManager, CmsTaskManager cmsTaskManager,
                                             DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper,
                                             DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                             InstanceDetailImageSuccService instanceDetailImageSuccService, TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, defaultRocketMqProducerWrapper, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.systemManager = systemManager;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
     }
 }