 package net.armcloud.paascenter.cms.model.response;
 
 import lombok.Data;
 import lombok.EqualsAndHashCode;
 import net.armcloud.paascenter.cms.model.BaseVO;
 
 @Data
 @EqualsAndHashCode(callSuper = true)
 public class GetContainerRestoreStatusRespVO extends BaseVO {
     private Integer status;
 
     public boolean inProgress() {
         return getStatus() == 1;
     }
 
     public boolean failed() {
         return getStatus() == 2;
     }
 
     public boolean succeed() {
         return getStatus() == 0;
     }
 
     public GetContainerRestoreStatusRespVO builderInProgress() {
         super.buildSuccess();
         this.status = 1;
         return this;
     }
 
     public GetContainerRestoreStatusRespVO builderFailed() {
         super.buildSuccess();
         this.status = 2;
         return this;
     }
 
     public GetContainerRestoreStatusRespVO builderSucceed() {
         super.buildSuccess();
         this.status = 0;
         return this;
     }
 }