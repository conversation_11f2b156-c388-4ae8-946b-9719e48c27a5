 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 import java.util.Map;
 
 @Data
 public class InstanceUpdatePropRequest {
     @Valid
     @Size(min = 1, message = "instances cannot null")
     @NotNull(message = "instances cannot null")
     private List<Instance> instances;
 
     /**
      * 修改完成是否自动重启(true:重启;false:不重启)
      */
     private Boolean restart;
 
     @Data
     public static class Instance {
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         @NotBlank(message = "padCode cannot null")
         private String padCode;
 
         @NotBlank(message = "props cannot null")
         private Map<String, String> props;
     }
 }