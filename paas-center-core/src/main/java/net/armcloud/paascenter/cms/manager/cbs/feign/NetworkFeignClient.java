 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.model.ContainerNetworkLimitParam;
 import net.armcloud.paascenter.cms.model.ContainerNetworkOpsParam;
 import net.armcloud.paascenter.cms.model.ContainerResourceOpsVO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-network", url = "placeholder")
 public interface NetworkFeignClient {
 
     @PostMapping(value = "/container/net/macvlan/create")
     String macVlanCreate(URI deviceHost, @RequestBody ContainerNetworkOpsParam param);
 
     @PostMapping(value = "/container/net/speed/limit")
     ContainerResourceOpsVO networkSpeedLimit(URI deviceHost, @RequestBody ContainerNetworkLimitParam param);
 }