 package net.armcloud.paascenter.cms.service.handler.instance;
 
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.List;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.TIMEOUT;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_HANDLER_INSTANCE_TIMEOUT_TASK_LOCK_KEY;
 
 @Slf4j
 @Service
 public class InstanceTaskTimeoutHandler extends AbstractInstanceTaskHandler {
     private final InstanceTaskMapper instanceTaskMapper;
     private final RedissonDistributedLock redissonDistributedLock;
 
     public void start() {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start exec instance task timeout handler....", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_HANDLER_INSTANCE_TIMEOUT_TASK_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_HANDLER_INSTANCE_TIMEOUT_TASK_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_HANDLER_INSTANCE_TIMEOUT_TASK_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_HANDLER_INSTANCE_TIMEOUT_TASK_LOCK_KEY);
             return;
         }
 
         try {
             handlerDeviceTask();
         } catch (Exception e) {
             throw new RuntimeException("error>>>>>", e);
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void handlerDeviceTask() {
         List<InstanceTask> instanceTasks = instanceTaskMapper.listTimeout(DCUtils.getDcId());
         if (CollectionUtils.isEmpty(instanceTasks)) {
             return;
         }
 
         instanceTasks.forEach(instanceTask -> super.updateStatus(instanceTask, TIMEOUT, "任务执行时间超过最大等待时间", EXECUTING.getStatus()));
     }
 
     public InstanceTaskTimeoutHandler(RedissonDistributedLock redissonDistributedLock,
                                       InstanceTaskMapper instanceTaskMapper, CmsTaskManager cmsTaskManager,
                                       InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                       DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper,
                                       InstanceDetailImageSuccService instanceDetailImageSuccService,
                                       TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, defaultRocketMqProducerWrapper,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceTaskMapper = instanceTaskMapper;
     }
 }