 package net.armcloud.paascenter.cms.mapper;
 
 import net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.List;
 
 @Mapper
 public interface InstanceBackupDataProgressMapper {
     int insert(InstanceBackupDataProgress record);
 
     int batchInsert(@Param("list") List<InstanceBackupDataProgress> list);
 
     InstanceBackupDataProgress getByInstanceTaskId(@Param("instanceTaskId") long instanceTaskId);
 
     void update(InstanceBackupDataProgress dataProgress);
 
 }