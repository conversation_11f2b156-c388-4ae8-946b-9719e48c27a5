 package net.armcloud.paascenter.cms.service.handler.instance.upgradeimage;
 
 import cn.hutool.core.bean.BeanUtil;
 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSONObject;
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.model.request.InstanceReplacePropRequest;
 import net.armcloud.paascenter.cms.model.request.InstanceReplaceRealAdiTemplateRequest;
 import net.armcloud.paascenter.cms.config.HarborConfig;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.HarborConfigManage;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.CBSHarborManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Date;
 import java.util.Map;
 import java.util.Objects;
 import java.util.concurrent.RejectedExecutionException;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.exception.code.InstanceExceptionCode.NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION;
 
 /**
  * 升级真机adi模板
  */
 @Slf4j
 @Service
 public class InstanceReplaceRealPadTaskExecuteHandler extends AbstractInstanceTaskHandler {
     private final CBSHarborManager cbsHarborManager;
     private final InstanceManager cbsInstanceManager;
     private final HarborConfigManage harborConfigManage;
     private final RedissonDistributedLock redissonDistributedLock;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     private final InstanceManager instanceManager;
 
     private final net.armcloud.paascenter.cms.manager.InstanceManager dnsInstanceManager;
 
     public void start(InstanceTask instanceTask) {
         try {
             super.asyncExecute(() -> tryStart(instanceTask));
         } catch (RejectedExecutionException e) {
             log.error("线程池:InstanceTaskPool,队列已满，存在任务丢失", e);
             DingTalkRobotClient.sendMessage("CMS服务-线程池:InstanceTaskPool,队列已满，存在任务丢失");
         }
     }
 
     private void tryStart(InstanceTask instanceTask) {
         String instanceTaskJson = JSON.toJSONString(instanceTask);
         log.info(" InstanceReplaceRealPadTaskExecuteHandler  executeTask start handler instanceTask:{}", instanceTaskJson);
         String key = EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX + instanceTask.getInstanceName();
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (lock == null) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, key);
             return;
         }
 
         RLock deviceTaskLock = null;
         try {
             // 云机任务与实例任务互斥，要执行任务则需要拿到板卡锁
             String deviceIp = instanceTask.getDeviceIp();
             String deviceTaskLockKey = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
             if (redissonDistributedLock.isHeldByCurrentThread(deviceTaskLockKey)) {
                 log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
                 return;
             }
 
             deviceTaskLock = redissonDistributedLock.tryLock(key, 0, 300);
             if (Objects.isNull(deviceTaskLock)) {
                 log.debug("thread {} not get lock {} skip....", currentThreadId, key);
                 return;
             }
 
             boolean success = super.updateStatus(instanceTask, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
 
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getLatestByMasterTaskId(instanceTask.getType(), instanceTask.getId());
             if (taskRelInstanceDetail == null) {
                 throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
             }
             //不需要修改adi信息,走设置安卓属性逻辑,然后再调用一下重置
             if (Objects.isNull(taskRelInstanceDetail.getAdiJson())) {
                 //先修改安卓改机属性,然后再调用重置
                 Map<String, String> map = JSON.parseObject(taskRelInstanceDetail.getDeviceAndroidProp(), Map.class);
                 //追加adb关闭属性
                 map.put("persist.sys.cloud.madb_enable", "0");
                 instanceManager.updateAndroidProp(deviceIp, instanceTask.getInstanceName(), map, false);
                 instanceManager.reset(instanceTask, TaskTypeEnum.INSTANCE_REPLACE_PROP.getIntValue());
                 return;
             }
 
             //带adi信息的,走先删后新建逻辑
             TaskRelInstanceDetail createTaskRelPadDetail = getCreateTaskRelPadDetail(instanceTask.getInstanceName());
             if (createTaskRelPadDetail == null) {
                 throw new BasicException(NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION);
             }
             removeInstance(instanceTask);
             virtualizeNewInstance(instanceTask, createTaskRelPadDetail, taskRelInstanceDetail);
         } catch (Exception e) {
             log.error("InstanceUpgradeImageTaskHandler executeTask error>>>>{}", JSON.toJSONString(instanceTask), e);
             super.updateStatus(instanceTask, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         } finally {
             redissonDistributedLock.unlock(lock);
             if (deviceTaskLock != null) {
                 redissonDistributedLock.unlock(deviceTaskLock);
             }
         }
     }
 
 
     private void virtualizeNewInstance(InstanceTask task, TaskRelInstanceDetail originCreateDetail, TaskRelInstanceDetail newCreateDetail) {
         cbsHarborManager.loginHarbor(task.getDeviceIp());
         String instanceName = task.getInstanceName();
         log.info("InstanceReplaceRealAdiTaskExecuteHandler instanceName {} >>> virtualizeNewInstance task:{} originCreateDetail:{} newCreateDetail:{}",
                 instanceName, JSON.toJSONString(task), JSON.toJSONString(originCreateDetail), JSON.toJSONString(newCreateDetail));
         CreatePadBO createPadBO = JSON.parseObject(originCreateDetail.getContainerProperty(), CreatePadBO.class);
         //设置一下安卓属性
         createPadBO.setDeviceAndroidProps(newCreateDetail.getDeviceAndroidProp());
         originCreateDetail.setDeviceAndroidProp(newCreateDetail.getDeviceAndroidProp());
         HarborConfig harborConfig = harborConfigManage.get();
         if (StringUtils.isNotEmpty(newCreateDetail.getImageId())) {
             String imageRepository = harborConfig.getUrl() + "/" + harborConfig.getProject() + "/" + originCreateDetail.getImageId();
             createPadBO.setImageRepository(imageRepository);
         }
         Boolean newCreateAdiIsEmpty = false;
         //获取adi数据
         //判断空对象以及空字符串
         if (StringUtils.isNotEmpty(newCreateDetail.getAdiJson()) && !Objects.equals(newCreateDetail.getAdiJson(), "{}")) {
             newCreateAdiIsEmpty = true;
             InstanceReplacePropRequest.Instance.ADI adi = JSONUtil.toBean(newCreateDetail.getAdiJson(), InstanceReplacePropRequest.Instance.ADI.class);
             InstanceReplacePropRequest.Instance.ADI originAdi = null;
             if (StringUtils.isNotBlank(originCreateDetail.getAdiJson())) {
                 originAdi = JSONUtil.toBean(originCreateDetail.getAdiJson(), InstanceReplacePropRequest.Instance.ADI.class);
             }
             if (StringUtils.isNotBlank(adi.getTemplateUrl())) {
                 createPadBO.setDownloadUrlOfADI(adi.getTemplateUrl());
                 if (Objects.nonNull(originAdi)) {
                     originAdi.setTemplateUrl(adi.getTemplateUrl());
                 }
             }
             if (StringUtils.isNotBlank(adi.getAndroidCertData())) {
                 createPadBO.setAndroidCertData(adi.getAndroidCertData());
                 if (Objects.nonNull(originAdi)) {
                     originAdi.setAndroidCertData(adi.getAndroidCertData());
                 }
             }
             if (StringUtils.isNotBlank(adi.getTemplatePassword())) {
                 createPadBO.setPasswordOfADI(adi.getTemplatePassword());
                 if (Objects.nonNull(originAdi)) {
                     originAdi.setTemplatePassword(adi.getTemplatePassword());
                 }
             }
 
             originCreateDetail.setAdiJson(toJSONStringOrNull(originAdi));
             if (adi.getLayoutWidth() != null) {
                 createPadBO.setWidth(adi.getLayoutWidth().intValue());
                 createPadBO.setHeight(adi.getLayoutHigh().intValue());
                 createPadBO.setDpi(adi.getLayoutDpi().intValue());
                 createPadBO.setFps(adi.getLayoutFps().intValue());
                 originCreateDetail.setWidth(adi.getLayoutWidth().intValue());
                 originCreateDetail.setHeight(adi.getLayoutHigh().intValue());
                 originCreateDetail.setDpi(adi.getLayoutDpi().intValue());
                 originCreateDetail.setFps(adi.getLayoutFps().intValue());
             }
         } else if (originCreateDetail.getWidth() != null && originCreateDetail.getWidth() > 0) {
             createPadBO.setWidth(originCreateDetail.getWidth());
             createPadBO.setHeight(originCreateDetail.getHeight());
             createPadBO.setDpi(originCreateDetail.getDpi());
             createPadBO.setFps(originCreateDetail.getFps());
         }
 
         if (StringUtils.isNotEmpty(originCreateDetail.getAdiJson()) && !newCreateAdiIsEmpty) {
             InstanceReplaceRealAdiTemplateRequest.ADI adi = JSONUtil.toBean(originCreateDetail.getAdiJson(), InstanceReplaceRealAdiTemplateRequest.ADI.class);
             createPadBO.setDownloadUrlOfADI(adi.getTemplateUrl());
             createPadBO.setPasswordOfADI(adi.getTemplatePassword());
         }
         createPadBO.setImageTag(originCreateDetail.getImageTag());
 
         createPadBO.setExtId(originCreateDetail.getIdentificationCode());
         createPadBO.setClearContainerData(true);
         if (StringUtils.isBlank(createPadBO.getMac())) {
             createPadBO.setMac(newCreateDetail.getMac());
         }
         createPadBO.setMac(newCreateDetail.getMac());
         dnsInstanceManager.setDefaultDNS(createPadBO);
         String requestDataJson = JSON.toJSONString(createPadBO);
         taskRelInstanceDetailMapper.updateContainerProperty(newCreateDetail.getId(), requestDataJson);
         cbsInstanceManager.create(createPadBO);
         originCreateDetail.setContainerProperty(JSONObject.toJSONString(createPadBO));
         saveTaskRelInstanceDetailImageSucc(originCreateDetail);
     }
 
 
 
     public static String toJSONStringOrNull(Object object) {
         // 判断对象是否为 null 或者是空对象
         if (object == null ) {
             return null;
         }
         return JSONObject.toJSONString(object);
     }
 
     /**
      * 下发之后写入最新得镜像记录表
      * @param originCreateDetail
      */
     private void saveTaskRelInstanceDetailImageSucc(TaskRelInstanceDetail originCreateDetail){
         TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = BeanUtil.copyProperties(originCreateDetail,TaskRelInstanceDetailImageSucc.class);
         taskRelInstanceDetailImageSucc.setId(null);
         taskRelInstanceDetailImageSucc.setTaskType(TaskTypeEnum.INSTANCE_REPLACE_PROP.getIntValue());
         taskRelInstanceDetailImageSucc.setCreateTime(new Date());
         taskRelInstanceDetailImageSucc.setUpdateTime(new Date());
         TaskRelInstanceDetail lastInfo = instanceDetailImageSuccService.getLastInfo(originCreateDetail.getInstanceName());
         if(Objects.nonNull(lastInfo)){
             taskRelInstanceDetailImageSucc.setNetStorageResFlag((long)lastInfo.getNetStorageResFlag());
             taskRelInstanceDetailImageSucc.setNetStorageResId(lastInfo.getNetStorageResId());
         }
         instanceDetailImageSuccService.saveAndClear(taskRelInstanceDetailImageSucc);
     }
 
 
     private void removeInstance(InstanceTask task) {
         String deviceIp = task.getDeviceIp();
         String instanceName = task.getInstanceName();
 
         log.debug("InstanceReplaceRealAdiTaskExecuteHandler removeInstance deviceIp:{} instanceName:{}", deviceIp, instanceName);
         try {
             boolean instanceExist = cbsInstanceManager.listAll(deviceIp).stream()
                     .anyMatch(instance -> instanceName.equals(instance.getName()));
             if (!instanceExist) {
                 log.debug("InstanceReplaceRealAdiTaskExecuteHandler removeInstance deviceIp:{} instanceName:{} not found skip remove", deviceIp, instanceName);
                 return;
             }
 
             cbsInstanceManager.remove(deviceIp, instanceName, task.getClearDiskData());
         } catch (Exception e) {
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         }
     }
 
     protected InstanceReplaceRealPadTaskExecuteHandler(CmsTaskManager cmsTaskManager,
                                                        RedissonDistributedLock redissonDistributedLock,
                                                        InstanceManager cbsInstanceManager,
                                                        InstanceTaskMapper instanceTaskMapper,
                                                        InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                        DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                        TaskRelInstanceDetailMapper taskRelInstanceDetailMapper,
                                                        HarborConfigManage harborConfigManage,
                                                        CBSHarborManager cbsHarborManager,
                                                        InstanceManager instanceManager,
                                                        net.armcloud.paascenter.cms.manager.InstanceManager dnsInstanceManager,
                                                        InstanceDetailImageSuccService instanceDetailImageSuccService) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.cbsInstanceManager = cbsInstanceManager;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.harborConfigManage = harborConfigManage;
         this.cbsHarborManager = cbsHarborManager;
         this.instanceManager = instanceManager;
         this.dnsInstanceManager = dnsInstanceManager;
     }
 }