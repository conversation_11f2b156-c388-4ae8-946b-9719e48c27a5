 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 @Data
 @ApiModel("实例备份上传参数")
 public class BackupRestoreParam extends BackupParam {
     @ApiModelProperty("minio地址")
     private String endpoint;
 
     @ApiModelProperty("minio AK")
     private String accessKey;
 
     @ApiModelProperty("minio SK")
     private String secretKey;
 
     @ApiModelProperty("minio桶")
     private String bucket;
 
     @ApiModelProperty("minio存储filePath")
     private String storageFilePath;
 
 }