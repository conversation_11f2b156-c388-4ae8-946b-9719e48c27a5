 package net.armcloud.paascenter.cms.service.handler.instance.prop;
 
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.time.Instant;
 import java.util.Date;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.INSTANCE_REPLACE_PROP;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_INSTANCE_RESET_VERIFY_LOCK_KEY;
 import static net.armcloud.paascenter.cms.utils.DCUtils.getDcId;
 import static java.util.Collections.singletonList;
 
 @Slf4j
 @Service
 public class InstanceUpdatePropTaskResultHandler extends AbstractInstanceTaskHandler {
     private final InstanceManager instanceManager;
     private final InstanceTaskMapper instanceTaskMapper;
     private final RedissonDistributedLock redissonDistributedLock;
 
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start verify running task result....", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_INSTANCE_RESET_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_INSTANCE_RESET_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_INSTANCE_RESET_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_INSTANCE_RESET_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<InstanceTask> tasks = instanceTaskMapper.listByStatusAndTargetTaskTypes(getDcId(), EXECUTING.getStatus(),
                     singletonList(INSTANCE_REPLACE_PROP.getIntValue()));
             if (CollectionUtils.isEmpty(tasks)) {
                 return;
             }
 
             tasks.stream()
                     .collect(Collectors.groupingBy(InstanceTask::getDeviceIp))
                     .forEach((deviceIp, taskList) -> {
                         List<InstanceInfoResponse> instanceInfoResponses;
                         try {
                             //获取板卡下的所有实例状态
                             instanceInfoResponses = instanceManager.listAll(deviceIp);
                         } catch (Exception e) {
                             return;
                         }
 
                         Map<String, InstanceInfoResponse> instanceInfoResponseMap = instanceInfoResponses.stream()
                                 .collect(Collectors.toMap(InstanceInfoResponse::getName, obj -> obj, (o1, o2) -> o1));
                         taskList.forEach(task -> {
                             String instanceName = task.getInstanceName();
                             InstanceInfoResponse instanceInfoResponse = instanceInfoResponseMap.get(instanceName);
 
                             if (Objects.isNull(instanceInfoResponse)) {
                                 return;
                             }
 
                             Date startUpTime = instanceInfoResponse.convertStartTimeToDate();
                             if (Objects.isNull(startUpTime)) {
                                 return;
                             }
                             //启动时间在任务执行之后并且断启动时间超过10S！因为gameserver心跳包是5S掉线,防止这里状态成功后,gameServer没起来,用户马上调用
                             if ( startUpTime.toInstant().getEpochSecond()<task.getStartTime().toInstant().getEpochSecond() || Instant.now().getEpochSecond()-startUpTime.toInstant().getEpochSecond() < 6) {
                                 return;
                             }
 
                             super.updateStatus(task, TaskStatusConstants.SUCCESS, "", EXECUTING.getStatus());
                         });
                     });
         } catch (Exception e) {
             log.error("InstanceResetTaskResultHandler>>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
 
     protected InstanceUpdatePropTaskResultHandler(CmsTaskManager cmsTaskManager, RedissonDistributedLock redissonDistributedLock,
                                                   InstanceManager instanceManager, InstanceTaskMapper instanceTaskMapper,
                                                   InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                   DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                   InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                   TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceManager = instanceManager;
         this.instanceTaskMapper = instanceTaskMapper;
     }
 }