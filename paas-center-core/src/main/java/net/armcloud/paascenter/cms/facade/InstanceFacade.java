 package net.armcloud.paascenter.cms.facade;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.model.request.*;
 import net.armcloud.paascenter.cms.model.response.*;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.util.List;
 
 public interface InstanceFacade {
     /**
      * 升级镜像
      */
     @PostMapping(value = "/armcloud-container/open/instance/upgradeImage")
     Result<List<InstanceUpgradeImageResponse>> upgradeImage(@RequestBody InstanceUpgradeImageRequest req);
 
     /**
      * 重启实例
      */
     @PostMapping(value = "/armcloud-container/open/instance/restart")
     Result<List<InstanceRestartResponse>> restart(@RequestBody InstanceRestartRequest req);
 
     /**
      * 重置实例
      */
     @PostMapping(value = "/armcloud-container/open/instance/reset")
     Result<List<InstanceResetResponse>> reset(@RequestBody InstanceResetRequest req);
 
     /**
      * 一键新机
      */
     @PostMapping(value = "/armcloud-container/open/instance/replaceProp")
     Result<List<InstanceResetResponse>> replaceProp(@RequestBody InstanceReplacePropRequest req);
 
 
     /**
      * 网络限速
      */
     @PostMapping(value = "/armcloud-container/open/instance/networkLimit")
     Result<List<InstanceNetworkLimitResponse>> networkLimit(@RequestBody InstanceNetworkLimitRequest req);
 
     /**
      * 修改安卓改机属性
      */
     @PostMapping(value = "/armcloud-container/open/instance/updateProp")
     Result<List<InstanceUpdatePropResponse>> updateProp(@RequestBody InstanceUpdatePropRequest req);
 
     /**
      * 虚拟机真机切换升级镜像
      */
     @PostMapping(value = "/armcloud-container/open/instance/virtualRealSwitchUpgradeImage")
     Result<List<InstanceUpgradeImageResponse>> virtualRealSwitchUpgradeImage(@RequestBody InstanceVirtualRealSwitchRequest req);
 
     /**
      * 数据备份
      */
     @PostMapping(value = "/armcloud-container/open/instance/backupData")
     Result<List<InstanceBackupDataResponse>> backupData(@RequestBody InstanceBackupDataRequest req);
 
     /**
      * 数据备份恢复
      */
     @PostMapping(value = "/armcloud-container/open/instance/restoreBackupData")
     Result<List<InstanceRestoreBackupDataResponse>> restoreBackupData(@RequestBody InstanceRestoreBackupDataRequest req);
 
     /**
      * 修改安卓改机属性并设置屏宽高等
      */
     @PostMapping(value = "/armcloud-container/open/instance/modifyProperties")
     Result<List<InstanceModifyPropertiesResponse>> modifyProperties(@RequestBody InstanceModifyPropertiesRequest req);
 
     /**
      * 真机切换虚拟机升级镜像
      */
     @PostMapping(value = "/armcloud-container/open/instance/realVirtualSwitchUpgradeImage")
     Result<List<InstanceUpgradeImageResponse>> virtualRealSwitchUpgradeImage(@RequestBody InstanceRealVirtualSwitchRequest req);
 
     /**
      * 切换真机adi模板
      */
     @PostMapping(value = "/armcloud-container/open/instance/replaceRealAdiTemplate")
     Result<List<InstanceUpgradeImageResponse>> replaceRealAdbTemplate(@RequestBody InstanceReplaceRealAdiTemplateRequest req);
 
 
     /**
      * 查询板卡状态
      */
     @PostMapping(value = "/armcloud-container/open/instance/lifecycle/status")
     Result<List<InstanceLifecycleStatusResponse>> instanceLifecycleStatus(@RequestBody InstanceLifecycleStatusRequest req);
 
 
 }