 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 @Data
 @ApiModel("LVM逻辑盘基础参数")
 public class BaseLvmDiskOpsParam {
 
     @ApiModelProperty("用于创建逻辑盘位的分区,板卡默认的data(userdata)分区(eg:/dev/mmcblk0p7(默认),/dev/sdb3)")
     private String targetDiskPart="/dev/mmcblk0p7";
 
     @ApiModelProperty("宿主机预留存储空间,单位(GB),多开限制情况下,可以为空,单开限制或者共享必须有值")
     private Long hostStorageSize;
 
     @ApiModelProperty("每个容器限制存储空间,单位(GB)")
     private Long containerPerStorageSize;
 
     @ApiModelProperty("容器多开的数量,容器并发数量")
     private Long containerConcurrentSize;
 
     @ApiModelProperty("磁盘是否隔离和限制(使用独立的磁盘)(默认false)")
     private Boolean isolateDisk = false;
 
 }