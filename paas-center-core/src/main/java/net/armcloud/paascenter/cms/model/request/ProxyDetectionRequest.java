 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 
 @Data
 public class ProxyDetectionRequest {
     @NotBlank(message = "proxyHost cannot null")
     private String proxyHost;
     @NotNull(message = "proxyPort cannot null")
     private Integer proxyPort;
     private String proxyAccount;
     private String proxyPwd;
 
     /**
      * 代理类型 1socks5 2http
      */
     private Integer proxyType;
 }