 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.enums.ContainerImageStatusEnum;
 import net.armcloud.paascenter.cms.model.BaseImageOpsParam;
 import net.armcloud.paascenter.cms.model.SysCmdOpsVO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-image", url = "placeholder")
 public interface ImageFeignClient {
 
     /**
      * 查询镜像拉取状态
      * code对应{@link ContainerImageStatusEnum}
      */
     @PostMapping(value = "/img/query/img/status")
     SysCmdOpsVO getPullStatus(URI host, @RequestBody BaseImageOpsParam dto);
 
     /**
      * 拉取镜像
      */
     @PostMapping(value = "/img/pull/img")
     SysCmdOpsVO pull(URI host, @RequestBody BaseImageOpsParam dto);
 }