 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 import java.util.Map;
 
 @Data
 @ApiModel("容器运行时资源配置参数")
 public class ContainerResourceOpsParam extends BaseContainerOpsParam {
 
     @ApiModelProperty("cpu核心限制数量,1000代表1核心,2000代表2核心")
     private Long cpuLimit;
 
     @ApiModelProperty("运行内存限制大小,单位MB,比如1024代表1024MG")
     private Long memoryLimit;
 
     @ApiModelProperty("存储隔离,单位:GB")
     private Long storageLimit;
 
     @ApiModelProperty("容器网络ip")
     private String ip;
 
     @ApiModelProperty("容器网络mac")
     private String mac;
 
     @ApiModelProperty("安卓dns1")
     private String dns1;
 
     @ApiModelProperty("安卓dns2")
     private String dns2;
 
     @ApiModelProperty("系统主机名称")
     private String hostname;
 
     @ApiModelProperty("镜像名称,镜像仓库")
     private String imageRepository;
 
     @ApiModelProperty("镜像标识")
     private String imageTag;
 
     @ApiModelProperty("磁盘是否隔离和限制(使用独立的磁盘)(默认true)")
     private Boolean isolateDisk = true;
 
     @ApiModelProperty("扩展标识id")
     private String extId;
 
     @ApiModelProperty("磁盘隔离参数")
     private BaseLvmDiskOpsParam diskOpsParam;
 
     @ApiModelProperty("容器运行逻辑编号,对应第几开(eg:1,2,...8)")
     private Long containerIndex;
 
     @ApiModelProperty("macvlan名字")
     private String macvlanName="";
 
     @ApiModelProperty( "宽度")
     private Integer width;
 
     @ApiModelProperty( "高度")
     private Integer height;
 
     @ApiModelProperty( "屏幕刷新率")
     private Integer fps;
 
     @ApiModelProperty("屏幕像素密度")
     private Integer dpi;
 
     @ApiModelProperty("GameServer的后端服务器访问地址")
     private String gameServerAddress;
 
     @Deprecated
     @ApiModelProperty("其他安卓改机属性(eg:k1=v1 k2=v2,[中间空格分隔])")
     private String otherAndroidProp;
 
     @ApiModelProperty("安卓设备改机属性")
     private Map<String, String> deviceAndroidProps;
 
     @ApiModelProperty("安卓证书数据")
     private String androidCertData;
 
     @ApiModelProperty("云真机模板信息,zip包下载地址")
     private String downloadUrlOfADI;
 
     @ApiModelProperty("云真机模板信息,zip包密码")
     private String passwordOfADI;
 
     // ro.a=1 ro.b=2 c.c=false
     // example:注意是一行,不同属性key value使用空格分隔,不需要换行
     //        // ro.build.fingerprint=google/raven/raven:13/TQ1A.230105.002/9325679:user/release-keys
     //        // ro.product.brand=google
     //        // ro.product.model=raven
     //        // ro.product.manufacturer=google
     //        // ro.product.device=raven
     //        // ro.product.name=raven
     //        // ro.build.version.incremental=9325679
 
 
 
     @ApiModelProperty("安卓是否清除数据")
     private Boolean clearContainerData = true;
 
 
     /**
      * 因需兼容历史数据添加此选项。待统一后，后续统一使用v2版本创建
      */
     private String createVersion;
 }