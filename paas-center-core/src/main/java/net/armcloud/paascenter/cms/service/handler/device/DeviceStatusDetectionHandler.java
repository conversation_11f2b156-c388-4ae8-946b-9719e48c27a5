 package net.armcloud.paascenter.cms.service.handler.device;
 
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.fastjson2.JSONArray;
 import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceStatusMQ;
 import net.armcloud.paascenter.common.redis.service.RedisService;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceStatusMQConfig;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Objects;
 import java.util.Optional;
 
 import static net.armcloud.paascenter.cms.constants.CacheKeyConstants.*;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_DEVICE_STATUS_DETECTION_LOCK_KEY;
 
 @Slf4j
 @Service
 public class DeviceStatusDetectionHandler extends AbstractDeviceTaskHandler {
     private final RedisService redisService;
     private final SystemManager systemManager;
     private final DeviceStatusMQConfig deviceStatusMQConfig;
     private final DefaultRocketMqProducerWrapper rocketMqProducerService;
     private final RedissonDistributedLock redissonDistributedLock;
 
     public void start() {
         super.asyncExecute(this::checkHealth);
     }
 
     public void checkHealth() {
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_DEVICE_STATUS_DETECTION_LOCK_KEY)) {
             log.debug("thread {} device status detection handler lock {} is held by current thread skip....", currentThreadId, SCHEDULED_DEVICE_STATUS_DETECTION_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_DEVICE_STATUS_DETECTION_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} device status detection handler not get lock key {}", currentThreadId, SCHEDULED_DEVICE_STATUS_DETECTION_LOCK_KEY);
             return;
         }
 
         try {
             Object serverIpsObj = redisService.getCacheObject(SERVER_IP_LIST);
             if (serverIpsObj == null) {
                 return;
             }
 
             List<String> serverIps = JSONArray.parseArray(serverIpsObj.toString(), String.class);
             if (CollectionUtils.isEmpty(serverIps)) {
                 log.debug("thread {} device status detection handler serverIps is empty", currentThreadId);
                 return;
             }
 
             serverIps.parallelStream().forEach(serverIp -> {
                 String devicesKey = SERVER_DEVICE_IP_LIST_PREFIX + serverIp;
                 if (Boolean.FALSE.equals(redisService.hasKey(devicesKey))) {
                     log.debug("thread {} device status detection handler devicesKey:{} not exist", currentThreadId, devicesKey);
                     return;
                 }
 
                 String serverIpDeviceObj = (String) Optional.ofNullable(redisService.getCacheObject(devicesKey)).orElse("");
                 List<String> deviceIps = JSONArray.parseArray(serverIpDeviceObj, String.class);
                 deviceIps.forEach(this::checkHealth);
             });
 
         } catch (Exception e) {
             log.error("DeviceStatusDetectionHandler error>>>>>",e);
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void checkHealth(String deviceIp) {
         String key = DEVICE_IP_PROPERTY_PREFIX + deviceIp;
         redisService.setCacheMapValue(key, "checkHealthTime", LocalDateTime.now().toString());
 
         boolean currentHealth = systemManager.health(deviceIp);
         redisService.setCacheMapValue(key, "health", String.valueOf(currentHealth));
         ContainerDeviceStatusMQ message = new ContainerDeviceStatusMQ();
         message.setDeviceIp(String.valueOf(deviceIp));
         message.setDeviceStatus(currentHealth ? 1 : 0);
         rocketMqProducerService.producerOrderlyMessage(deviceStatusMQConfig.getTopic(), deviceIp, JSON.toJSONString(message));
     }
 
     public DeviceStatusDetectionHandler(RedisService redisService, SystemManager systemManager,
                                         DeviceStatusMQConfig deviceStatusMQConfig, DefaultRocketMqProducerWrapper rocketMqProducerService,
                                         RedissonDistributedLock redissonDistributedLock, CmsTaskManager cmsTaskManager, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                         DeviceInstanceTaskMapper deviceInstanceTaskMapper, DeviceTaskStatusMQConfig deviceTaskStatusMQConfig,
                                         InstanceDetailImageSuccService instanceDetailImageSuccService, TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, rocketMqProducerService, cmsDeviceTaskMapper, deviceTaskStatusMQConfig, instanceDetailImageSuccService, taskRelInstanceDetailMapper);
         this.redisService = redisService;
         this.systemManager = systemManager;
         this.deviceStatusMQConfig = deviceStatusMQConfig;
         this.rocketMqProducerService = rocketMqProducerService;
         this.redissonDistributedLock = redissonDistributedLock;
     }
 }