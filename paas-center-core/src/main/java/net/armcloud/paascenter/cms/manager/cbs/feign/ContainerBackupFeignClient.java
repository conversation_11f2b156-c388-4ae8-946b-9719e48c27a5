 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.model.request.ContainerBackupReqParam;
 import net.armcloud.paascenter.cms.model.request.GetContainerBackupStatusReqParam;
 import net.armcloud.paascenter.cms.model.response.ContainerBackupRespVO;
 import net.armcloud.paascenter.cms.model.response.GetContainerBackupStatusRespVO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-backup", url = "placeholder")
 public interface ContainerBackupFeignClient {
 
     @PostMapping(value = "/backup/start")
     ContainerBackupRespVO start(URI deviceHost, @RequestBody ContainerBackupReqParam param);
 
     @PostMapping(value = "/backup/status")
     GetContainerBackupStatusRespVO status(URI deviceHost, @RequestBody GetContainerBackupStatusReqParam param);
 }