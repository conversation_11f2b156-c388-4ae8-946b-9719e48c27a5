 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import io.swagger.annotations.ApiParam;
 import lombok.Data;
 
 @Data
 @ApiModel("系统命令行执行参数")
 public class SysCmdParam {
     @ApiModelProperty("命令行字符串")
     private String cmd;
 
     @ApiModelProperty("任务是否异步执行")
     private Boolean async = true;
 
 }