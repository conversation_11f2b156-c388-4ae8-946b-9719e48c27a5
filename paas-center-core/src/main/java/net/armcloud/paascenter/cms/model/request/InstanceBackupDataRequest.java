 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class InstanceBackupDataRequest {
     @Valid
     @Size(min = 1, message = "instances cannot null")
     @NotNull(message = "instances cannot null")
     private List<Instance> instances;
 
     @Data
     public static class Instance {
         @NotBlank(message = "padCode cannot null")
         private String padCode;
 
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
     }
 }