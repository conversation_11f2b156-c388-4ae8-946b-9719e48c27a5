 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 import java.util.Map;
 
 @ApiModel("基础容器操作参数")
 @Data
 public class ContainerAndroidPropParam extends BaseContainerOpsParam{
 
     @ApiModelProperty("安卓设备改机属性")
     private Map<String, String> deviceAndroidProps;
 
     @ApiModelProperty("是否重启")
     private Boolean restart;
 }