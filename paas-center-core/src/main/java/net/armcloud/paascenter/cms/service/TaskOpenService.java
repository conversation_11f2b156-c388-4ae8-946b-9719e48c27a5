 package net.armcloud.paascenter.cms.service;
 
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.cms.model.request.TaskDetailRequest;
 import net.armcloud.paascenter.cms.model.response.DeviceTaskDetailResponse;
 import net.armcloud.paascenter.cms.model.response.InstanceTaskDetailResponse;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import org.springframework.stereotype.Service;
 
 import java.util.Objects;
 
 import static net.armcloud.paascenter.cms.exception.code.TaskExceptionCode.TASK_NOT_FOUND_EXCEPTION;
 
 @Service
 public class TaskOpenService {
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final InstanceTaskMapper instanceTaskMapper;
 
     public InstanceTaskDetailResponse instanceDetail(TaskDetailRequest request) {
         InstanceTask task = instanceTaskMapper.getById(request.getMasterTaskId());
         if (Objects.isNull(task)) {
             throw new BasicException(TASK_NOT_FOUND_EXCEPTION);
         }
 
         InstanceTaskDetailResponse response = new InstanceTaskDetailResponse();
         response.setMasterTaskId(task.getId());
         response.setMasterTaskStatus(task.getStatus());
         response.setPadCode(task.getInstanceName());
         response.setMsg(task.getMsg());
         return response;
     }
 
     public DeviceTaskDetailResponse deviceDetail(TaskDetailRequest request) {
         DeviceTask task = cmsDeviceTaskMapper.getById(request.getMasterTaskId());
         if (Objects.isNull(task)) {
             throw new BasicException(TASK_NOT_FOUND_EXCEPTION);
         }
 
         DeviceTaskDetailResponse response = new DeviceTaskDetailResponse();
         response.setMasterTaskId(task.getId());
         response.setMasterTaskStatus(task.getStatus());
         response.setDeviceIp(task.getIp());
         response.setMsg(task.getMsg());
         return response;
     }
 
     public TaskOpenService(InstanceTaskMapper instanceTaskMapper, CmsDeviceTaskMapper cmsDeviceTaskMapper) {
         this.instanceTaskMapper = instanceTaskMapper;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
     }
 
 }