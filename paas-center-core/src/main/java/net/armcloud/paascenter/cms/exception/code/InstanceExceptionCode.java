 package net.armcloud.paascenter.cms.exception.code;
 
 import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
 import lombok.AllArgsConstructor;
 import lombok.Getter;
 
 @Getter
 @AllArgsConstructor
 public enum InstanceExceptionCode implements ExceptionCode {
     NOT_FOUND_INSTANCE_CREATE_DATA_EXCEPTION(174000, "未找到实例创建数据"),
     INSTANCE_CREATE_FAIL_EXCEPTION(174001, "实例创建失败"),
     ;
 
     private final int status;
     private final String msg;
 }