 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.model.BaseLvmDiskOpsParam;
 import net.armcloud.paascenter.cms.model.SysCmdOpsVO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-disk", url = "placeholder")
 public interface DiskFeignClient {
 
     @PostMapping(value = "/disk/create/lvm/all")
     SysCmdOpsVO partition(URI host, @RequestBody BaseLvmDiskOpsParam dto);
 }