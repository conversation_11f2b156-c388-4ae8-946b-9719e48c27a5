 package net.armcloud.paascenter.cms.service.handler.instance.restart;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Objects;
 import java.util.concurrent.RejectedExecutionException;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX;
 
 @Slf4j
 @Service
 public class InstanceRestartTaskExecuteHandler extends AbstractInstanceTaskHandler {
     private final InstanceManager instanceManager;
     private final RedissonDistributedLock redissonDistributedLock;
 
     public void start(InstanceTask instanceTask) {
         try {
             super.asyncExecute(() -> tryStart(instanceTask));
         } catch (RejectedExecutionException e) {
             log.error("线程池:InstanceTaskPool,队列已满，存在任务丢失", e);
             DingTalkRobotClient.sendMessage("CMS服务-线程池:InstanceTaskPool,队列已满，存在任务丢失");
         }
     }
 
     public void tryStart(InstanceTask instanceTask) {
         String instanceTaskJson = JSON.toJSONString(instanceTask);
         log.info("InstanceRestartTaskHandler executeTask start handler instanceTask:{}", instanceTaskJson);
         String key = EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX + instanceTask.getInstanceName();
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, key);
             return;
         }
 
         RLock deviceTaskLock = null;
         try {
             // 云机任务与实例任务互斥，要执行任务则需要拿到板卡锁
             String deviceIp = instanceTask.getDeviceIp();
             String deviceTaskLockKey = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
             if (redissonDistributedLock.isHeldByCurrentThread(deviceTaskLockKey)) {
                 log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
                 return;
             }
 
             deviceTaskLock = redissonDistributedLock.tryLock(key, 0, 300);
             if (Objects.isNull(deviceTaskLock)) {
                 log.debug("thread {} not get lock {} skip....", currentThreadId, key);
                 return;
             }
             restartInstance(instanceTask);
         } catch (Exception e) {
             log.error("InstanceRestartTaskExecuteHandler>>>error", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
             if (deviceTaskLock != null) {
                 redissonDistributedLock.unlock(deviceTaskLock);
             }
         }
     }
 
     private void restartInstance(InstanceTask task) {
         try {
             boolean success = super.updateStatus(task, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
             String deviceIp = task.getDeviceIp();
             String instanceName = task.getInstanceName();
             boolean instanceExist = instanceManager.listAll(deviceIp).stream()
                     .anyMatch(instance -> instanceName.equals(instance.getName()));
             if (!instanceExist) {
                 super.updateStatus(task, FAIL_ALL, instanceName + " 未找到此实例", EXECUTING.getStatus());
                 return;
             }
             long startTime = System.currentTimeMillis();
             instanceManager.restart(deviceIp, instanceName);
             long endTime = System.currentTimeMillis();
             //耗时超过2秒，则进行日志打印
             if (endTime - startTime > 2000) {
                 log.info("instanceRestartTaskExecuteHandler.restartInstance【重启实例】，入参：deviceIp：{}，instanceName：{} 响应耗时: {} ms",deviceIp,instanceName, (endTime - startTime));
             }
         } catch (Exception e) {
             log.error("restartInstance instanceName:{} task:{} error>>>>", task.getInstanceName(), JSON.toJSONString(task), e);
             super.updateStatus(task, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         }
 
     }
 
     protected InstanceRestartTaskExecuteHandler(CmsTaskManager cmsTaskManager, RedissonDistributedLock redissonDistributedLock,
                                                 InstanceManager instanceManager, InstanceTaskMapper instanceTaskMapper,
                                                 InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                 DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                 InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                 TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceManager = instanceManager;
     }
 }