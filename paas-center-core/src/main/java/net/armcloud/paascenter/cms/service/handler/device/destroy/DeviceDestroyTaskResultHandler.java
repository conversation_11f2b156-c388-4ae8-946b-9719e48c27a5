 package net.armcloud.paascenter.cms.service.handler.device.destroy;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.apache.commons.collections.MapUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.DEVICE_DELETE;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_DEVICE_DESTROY_VERIFY_LOCK_KEY;
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_MACVLAN_MODE_FILE_DELETE_FAILED_EXCEPTION;
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_NETWORK_DELETION_FAILED_EXCEPTION;
 
 @Slf4j
 @Service
 public class DeviceDestroyTaskResultHandler extends AbstractDeviceTaskHandler {
     private final SystemManager systemManager;
     private final InstanceManager instanceManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final DeviceInstanceTaskMapper deviceInstanceTask;
     private final RedissonDistributedLock redissonDistributedLock;
 
     /**
      * 验证进行中的任务结果
      *
      * <p>
      * 任务成功规则：云机上没有任何实例
      */
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentThreadId = Thread.currentThread().getId();
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_DEVICE_DESTROY_VERIFY_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_DEVICE_DESTROY_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_DEVICE_DESTROY_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_DEVICE_DESTROY_VERIFY_LOCK_KEY);
             return;
         }
 
         try {
             List<DeviceTask> deviceTasks = cmsDeviceTaskMapper.listWaitVerifyResultTask(DCUtils.getDcId(), DEVICE_DELETE.getIntValue());
             if (CollectionUtils.isEmpty(deviceTasks)) {
                 return;
             }
 
             deviceTasks.forEach(deviceTask -> {
                 long deviceTaskId = deviceTask.getId();
                 String deviceIp = deviceTask.getIp();
 
                 Map<String, InstanceInfoResponse> padCodeInfoMap;
                 try {
                     padCodeInfoMap = instanceManager.listAll(deviceIp).stream()
                             .collect(Collectors.toMap(InstanceInfoResponse::getName, obj -> obj, (o1, o2) -> o1));
                 } catch (Exception e) {
                     log.error("listAll error>>>", e);
                     return;
                 }
 
                 List<DeviceInstanceTask> subTasks = deviceInstanceTask.listByMasterTaskId(deviceTaskId);
                 // 云机上没有任何实例则全重置成功
                 if (MapUtils.isEmpty(padCodeInfoMap)) {
                     subTasks.forEach(task -> super.updateStatus(task, SUCCESS, "", EXECUTING.getStatus()));
 
                     try {
                         deleteNetwork(deviceIp);
                         deleteMacvlanInCommonModeFile(deviceIp);
                         // 重启板卡预防cbs磁盘清理不成功
                         systemManager.execSyncCmd(deviceIp, "sudo reboot now");
                         super.updateStatus(deviceTask, SUCCESS, "", EXECUTING.getStatus());
                     } catch (Exception e) {
                         log.error("deviceTask:{} deleteNetwork error>>>>", JSON.toJSONString(deviceTask), e);
                         super.updateStatus(deviceTask, EXCEPTION, e.getMessage(), EXECUTING.getStatus());
                     }
 
                     return;
                 }
 
                 subTasks.forEach(deviceInstanceTask -> {
                     InstanceInfoResponse instanceInfoResponse = padCodeInfoMap.get(deviceInstanceTask.getInstanceName());
                     if (Objects.isNull(instanceInfoResponse)) {
                         super.updateStatus(deviceInstanceTask, SUCCESS, "", EXECUTING.getStatus());
                     }
                 });
             });
         } catch (Exception e) {
             log.error("DeviceDestroyTaskResultHandler error>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void deleteMacvlanInCommonModeFile(String deviceIp) {
         try {
             systemManager.execSyncCmd(deviceIp, "rm -rf /root/macvlanInCommonMode");
         } catch (Exception e) {
             log.error("execDeviceVirtualizeTask error>>>> deviceIp:{}", deviceIp, e);
             throw new BasicException(DEVICE_MACVLAN_MODE_FILE_DELETE_FAILED_EXCEPTION);
         }
     }
 
     private void deleteNetwork(String deviceIp) {
         try {
             systemManager.execSyncCmd(deviceIp, "docker network rm macvlan100");
         } catch (Exception e) {
             log.error("execDeviceVirtualizeTask error>>>> deviceIp:{}", deviceIp, e);
             throw new BasicException(DEVICE_NETWORK_DELETION_FAILED_EXCEPTION);
         }
     }
 
     protected DeviceDestroyTaskResultHandler(DeviceInstanceTaskMapper deviceInstanceTaskMapper, CmsTaskManager cmsTaskManager,
                                              InstanceManager instanceManager, RedissonDistributedLock redissonDistributedLock,
                                              DeviceInstanceTaskMapper deviceInstanceTask, DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper,
                                              DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                              SystemManager systemManager, InstanceDetailImageSuccService instanceDetailImageSuccService,
                                              TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, defaultRocketMqProducerWrapper, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceManager = instanceManager;
         this.redissonDistributedLock = redissonDistributedLock;
         this.deviceInstanceTask = deviceInstanceTask;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
         this.systemManager = systemManager;
     }
 }