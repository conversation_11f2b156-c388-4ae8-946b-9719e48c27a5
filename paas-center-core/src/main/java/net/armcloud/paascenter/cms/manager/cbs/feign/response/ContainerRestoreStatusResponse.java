 package net.armcloud.paascenter.cms.manager.cbs.feign.response;
 
 import net.armcloud.paascenter.cms.model.BaseVO;
 import lombok.Data;
 import lombok.EqualsAndHashCode;
 
 import java.util.Optional;
 
 @Data
 @EqualsAndHashCode(callSuper = true)
 public class ContainerRestoreStatusResponse extends BaseVO {
 
     public int getStatus() {
         return Optional.ofNullable(this.getDataPayload()).map(Integer::parseInt).orElse(0);
     }
 
     public boolean inProgressBackup() {
         return getStatus() == 1;
     }
 
     public boolean backupFailed() {
         return getStatus() == 2;
     }
 
     public boolean backupSucceed() {
         return getStatus() == 0;
     }
 }