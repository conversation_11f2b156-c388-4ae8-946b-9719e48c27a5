 package net.armcloud.paascenter.cms.service;
 
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.fastjson2.JSONObject;
 import net.armcloud.paascenter.cms.model.SysBuildInfo;
 import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.cms.model.request.*;
 import net.armcloud.paascenter.cms.model.response.*;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.model.SysSelfUpdateDTO;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import net.armcloud.paascenter.cms.utils.IdentificationCodeUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.beans.factory.annotation.Value;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 
 import java.util.ArrayList;
 import java.util.List;
 
 import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.GAME_SERVICE_INTERFACE_DOMAIN;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.WAIT_EXECUTE;
 
 /**
  * <AUTHOR>
  */
 @Slf4j
 @Service
 public class DeviceOpService {
     @Value("${android-prop.armcloud-server-addr}")
     private String armcloudServerAddr;
 
     private final SystemManager systemManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final ConfigurationMapper configurationMapper;
     private final DeviceInstanceTaskMapper deviceInstanceTaskMapper;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
 
     /**
      * 创建虚拟云机任务
      */
     @Transactional(rollbackFor = Exception.class)
     public List<DeviceVirtualizeResponse> addVirtualizeTask(VirtualizeDeviceRequest request) {
         List<DeviceVirtualizeResponse> responses = new ArrayList<>();
         request.getDevices().forEach(device -> {
             String deviceIp = device.getDeviceIp();
             long taskId = saveDeviceTask(device, TaskTypeEnum.DEVICE_CREATE);
             saveDeviceInstanceTask(taskId, TaskTypeEnum.DEVICE_CREATE, device);
 
             // builder result
             DeviceVirtualizeResponse virtualizeResponse = new DeviceVirtualizeResponse();
             virtualizeResponse.setDeviceIp(deviceIp);
             virtualizeResponse.setMasterTaskId(taskId);
             virtualizeResponse.setMasterTaskStatus(WAIT_EXECUTE.getStatus());
             responses.add(virtualizeResponse);
         });
 
         return responses;
     }
 
     private void saveDeviceInstanceTask(long taskId, TaskTypeEnum taskTypeEnum, VirtualizeDeviceRequest.Device device) {
         List<VirtualizeDeviceRequest.Device.Pad> pads = device.getPads();
         if (CollectionUtils.isEmpty(pads)) {
             return;
         }
 
         List<TaskRelInstanceDetail> taskRelInstanceDetails = new ArrayList<>(pads.size());
         for (int i = 0; i < pads.size(); i++) {
             VirtualizeDeviceRequest.Device.Pad pad = pads.get(i);
 
             String instanceName = pad.getPadCode();
             DeviceInstanceTask deviceInstanceTask = new DeviceInstanceTask();
             deviceInstanceTask.setMasterTaskId(taskId);
             deviceInstanceTask.setDeviceIp(device.getDeviceIp());
             deviceInstanceTask.setInstanceName(instanceName);
             deviceInstanceTask.setStatus(WAIT_EXECUTE.getStatus());
             deviceInstanceTaskMapper.insert(deviceInstanceTask);
 
             long subTaskId = deviceInstanceTask.getId();
             TaskRelInstanceDetail taskRelInstanceDetail = new TaskRelInstanceDetail();
             taskRelInstanceDetail.setTaskType(taskTypeEnum.getIntValue());
             taskRelInstanceDetail.setMasterTaskId(taskId);
             taskRelInstanceDetail.setSubTaskId(subTaskId);
             taskRelInstanceDetail.setInstanceName(instanceName);
             taskRelInstanceDetail.setIdentificationCode(IdentificationCodeUtils.genInstIdCode(taskId, subTaskId, instanceName));
             taskRelInstanceDetail.setContainerIndex(i + 1);
 
             if (pad.getAdi() != null) {
                 taskRelInstanceDetail.setAdiJson(JSON.toJSONString(pad.getAdi()));
             }
 
             if (pad.getDeviceAndroidProps() != null) {
                 taskRelInstanceDetail.setDeviceAndroidProp(JSON.toJSONString(pad.getDeviceAndroidProps()));
             }
 
             String gameServiceInterfaceDomain = configurationMapper.selectValueByKey(GAME_SERVICE_INTERFACE_DOMAIN);
             if (StringUtils.isNotBlank(gameServiceInterfaceDomain)) {
                 armcloudServerAddr = gameServiceInterfaceDomain;
             }
             taskRelInstanceDetail.setAndroidProp("ro.boot.armcloud_server_addr=" + armcloudServerAddr);
             if (StringUtils.isNotBlank(pad.getAndroidProp())) {
                 taskRelInstanceDetail.setAndroidProp(taskRelInstanceDetail.getAndroidProp() + " " + pad.getAndroidProp());
             }
 
             NetworkRequest networkRequest = pad.getNetwork();
             taskRelInstanceDetail.setIp(networkRequest.getIp());
             taskRelInstanceDetail.setMaxUplinkBandwidth(networkRequest.getMaxUplinkBandwidth());
             taskRelInstanceDetail.setMaxDownlinkBandwidth(networkRequest.getMaxUplinkBandwidth());
             taskRelInstanceDetail.setDns(networkRequest.getDns());
             taskRelInstanceDetail.setMac(networkRequest.getMac());
 
             ImageRequest imageRequest = pad.getImage();
             taskRelInstanceDetail.setImageTag(imageRequest.getTag());
             taskRelInstanceDetail.setImageId(imageRequest.getId());
 
             DisplayRequest displayRequest = pad.getDisplay();
             taskRelInstanceDetail.setWidth(displayRequest.getWidth());
             taskRelInstanceDetail.setHeight(displayRequest.getHeight());
             taskRelInstanceDetail.setDpi(displayRequest.getDpi());
             taskRelInstanceDetail.setFps(displayRequest.getFps());
 
             SpecRequest specRequest = pad.getSpec();
             taskRelInstanceDetail.setDisk(specRequest.getDisk());
             taskRelInstanceDetail.setCpu(specRequest.getCpu());
             taskRelInstanceDetail.setMemory(specRequest.getMemory());
             taskRelInstanceDetails.add(taskRelInstanceDetail);
         }
 
         taskRelInstanceDetailMapper.batchInsert(taskRelInstanceDetails);
     }
 
     private long saveDeviceTask(VirtualizeDeviceRequest.Device device, TaskTypeEnum taskTypeEnum) {
         DeviceTask deviceTask = new DeviceTask();
         deviceTask.setDcId(DCUtils.getDcId());
         deviceTask.setIp(device.getDeviceIp());
         deviceTask.setType(taskTypeEnum.getIntValue());
         deviceTask.setStatus(WAIT_EXECUTE.getStatus());
 
         VirtualizeDeviceRequest.Device.Pad pad = device.getPads().get(0);
         NetworkRequest network = pad.getNetwork();
         deviceTask.setSubnet(network.getSubnet());
         deviceTask.setIpRange(network.getIpRange());
         deviceTask.setGateway(network.getGateway());
         deviceTask.setNetworkDeviceName(network.getNetworkDeviceName());
 
         SpecRequest spec = pad.getSpec();
         VirtualizeDeviceRequest.InitInformation initInformation = device.getInitInformation();
         deviceTask.setIsolateDisk(spec.getIsolateDisk());
         deviceTask.setHostStorageSize(initInformation.getHostStorageSize());
         deviceTask.setContainerSize(initInformation.getContainerConcurrentSize());
 
         cmsDeviceTaskMapper.insert(deviceTask);
         return deviceTask.getId();
     }
 
     private long addDeviceTask(String deviceIp, TaskTypeEnum taskTypeEnum) {
         return addDeviceTask(deviceIp,taskTypeEnum,null);
     }
 
     private long addDeviceTask(String deviceIp, TaskTypeEnum taskTypeEnum,String reqParamJson) {
         DeviceTask deviceTask = new DeviceTask();
         deviceTask.setDcId(DCUtils.getDcId());
         deviceTask.setIp(deviceIp);
         deviceTask.setType(taskTypeEnum.getIntValue());
         deviceTask.setStatus(WAIT_EXECUTE.getStatus());
         deviceTask.setRequestParamJson(reqParamJson);
         cmsDeviceTaskMapper.insert(deviceTask);
         return deviceTask.getId();
     }
 
     /**
      * 添加重启云机任务
      */
     @Transactional(rollbackFor = Exception.class)
     public List<DeviceRestartResponse> addRestartTask(DeviceIpsRequest req) {
         List<DeviceRestartResponse> result = new ArrayList<>();
         req.getDeviceIps().forEach(deviceIp -> {
             long taskId = addDeviceTask(deviceIp, TaskTypeEnum.DEVICE_RESTART);
 
             DeviceRestartResponse deviceRestartResponse = new DeviceRestartResponse();
             deviceRestartResponse.setDeviceIp(deviceIp);
             deviceRestartResponse.setMasterTaskId(taskId);
             deviceRestartResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             result.add(deviceRestartResponse);
         });
 
         return result;
     }
 
     /**
      * 添加删除云机任务
      */
     @Transactional(rollbackFor = Exception.class)
     public List<DeviceDestroyResponse> addDestroyTask(DeviceIpsRequest req) {
         List<DeviceDestroyResponse> result = new ArrayList<>();
         req.getDeviceIps().forEach(deviceIp -> {
             long taskId = addDeviceTask(deviceIp, TaskTypeEnum.DEVICE_DELETE);
             DeviceDestroyResponse deviceDestroyResponse = new DeviceDestroyResponse();
             deviceDestroyResponse.setDeviceIp(deviceIp);
             deviceDestroyResponse.setMasterTaskId(taskId);
             deviceDestroyResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             result.add(deviceDestroyResponse);
         });
 
         return result;
     }
 
     public List<DeviceInfoResponse> info(DeviceIpsRequest req) {
         List<DeviceInfoResponse> deviceInfoResponses = new ArrayList<>(req.getDeviceIps().size());
         req.getDeviceIps().parallelStream().forEach(deviceIp -> {
             try {
                 SysBuildInfo sysBuildInfo = systemManager.getBuilderInfo(deviceIp);
                 log.info("deviceOpService.info-cbs返回参数:{}",JSON.toJSONString(sysBuildInfo));
                 if (sysBuildInfo == null) {
                     return;
                 }
 
                 DeviceInfoResponse response = new DeviceInfoResponse();
                 response.setDeviceIp(deviceIp);
                 response.setDebianSysInfo(sysBuildInfo.getDebianSysInfo());
                 response.setDebianBootInfo(sysBuildInfo.getDebianBootInfo());
                 response.setCbsInfo(sysBuildInfo.getCbsInfo());
                 response.setExtLifeTimeInfo(sysBuildInfo.getExtLifeTimeInfo());
                 deviceInfoResponses.add(response);
             } catch (Exception e) {
                 log.error("getBuilderInfo error>>> deviceIp:{},detailMessage:{}", deviceIp, e.getMessage());
             }
         });
 
         return deviceInfoResponses;
     }
 
     public List<SelfUpdateResponse> selfUpdate(SelfUpdateRequest request){
         List<SelfUpdateResponse> result = new ArrayList<>();
         request.getDeviceIps().forEach(deviceIp -> {
             SysSelfUpdateDTO sysSelfUpdateDTO = new SysSelfUpdateDTO();
             sysSelfUpdateDTO.setUrl(request.getCbsFileUrl());
             sysSelfUpdateDTO.setVersion(request.getCbsVersion());
             long taskId = addDeviceTask(deviceIp, TaskTypeEnum.DEVICE_CBS_SELF_UPDATE, JSONObject.toJSONString(sysSelfUpdateDTO));
 
             SelfUpdateResponse selfUpdateResponse = new SelfUpdateResponse();
             selfUpdateResponse.setDeviceIp(deviceIp);
             selfUpdateResponse.setMasterTaskId(taskId);
             selfUpdateResponse.setMasterTaskStatus(TaskStatusConstants.WAIT_EXECUTE.getStatus());
             result.add(selfUpdateResponse);
         });
 
         return result;
     }
 
     public DeviceOpService(TaskRelInstanceDetailMapper taskRelInstanceDetailMapper, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                            DeviceInstanceTaskMapper deviceInstanceTaskMapper, SystemManager systemManager,
                            ConfigurationMapper configurationMapper) {
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
         this.deviceInstanceTaskMapper = deviceInstanceTaskMapper;
         this.systemManager = systemManager;
         this.configurationMapper = configurationMapper;
     }
 }