 package net.armcloud.paascenter.cms.config;
 
 import lombok.Getter;
 import lombok.Setter;
 import org.springframework.boot.context.properties.ConfigurationProperties;
 import org.springframework.stereotype.Component;
 
 @Getter
 @Setter
 @Component
 @ConfigurationProperties(prefix = "harbor")
 public class HarborConfig {
     private String url;
     private String username;
     private String password;
     private String project;
 }