 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.constraints.Min;
 import javax.validation.constraints.NotNull;
 
 @Data
 public class SpecRequest {
     /**
      * 磁盘大小（单位：MB）
      */
     @Min(value = 1, message = "disk value is illegal")
     @NotNull(message = "disk cannot null")
     private Integer disk;
 
     /**
      * CPU（千分制，1000等于1核）
      */
     @Min(value = 1, message = "cpu value is illegal")
     @NotNull(message = "cpu cannot null")
     private Integer cpu;
 
     /**
      * 内存大小（单位:MB）
      */
     @Min(value = 1, message = "memory value is illegal")
     @NotNull(message = "memory cannot null")
     private Integer memory;
 
     /**
      * 磁盘是否隔离
      */
     private Boolean isolateDisk;
 }