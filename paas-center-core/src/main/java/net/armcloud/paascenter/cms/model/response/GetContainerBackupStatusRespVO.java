 package net.armcloud.paascenter.cms.model.response;
 
 import lombok.Data;
 import lombok.EqualsAndHashCode;
 import net.armcloud.paascenter.cms.model.BaseVO;
 
 @Data
 @EqualsAndHashCode(callSuper = true)
 public class GetContainerBackupStatusRespVO extends BaseVO {
     private Integer status;
 
     /**
      * 备份文件大小（单位：字节）
      */
     private Long filesize;
 
     public boolean inProgress() {
         return getStatus() == 1;
     }
 
     public boolean failed() {
         return getStatus() == 2;
     }
 
     public boolean succeed() {
         return getStatus() == 0;
     }
 
     public GetContainerBackupStatusRespVO builderInProgress() {
         super.buildSuccess();
         this.status = 1;
         return this;
     }
 
     public GetContainerBackupStatusRespVO builderFailed() {
         super.buildSuccess();
         this.status = 2;
         return this;
     }
 
     public GetContainerBackupStatusRespVO builderSucceed(long filesize) {
         super.buildSuccess();
         this.status = 0;
         this.filesize = filesize;
         return this;
     }
 }