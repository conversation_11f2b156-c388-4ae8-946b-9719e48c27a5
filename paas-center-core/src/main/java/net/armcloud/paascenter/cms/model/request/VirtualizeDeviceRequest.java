 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 import java.util.Map;
 
 @Data
 public class VirtualizeDeviceRequest {
     @Valid
     @NotNull(message = "devices cannot null")
     @Size(min = 1, message = "devices cannot null")
     private List<Device> devices;

     private Integer netStorageResFlag;
 
     @Data
     public static class Device {
         @NotNull(message = "initInformation cannot null")
         private InitInformation initInformation;
 
         /**
          * 云机所在ip
          */
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         /**
          * 实例列表
          */
         @Valid
         @NotNull(message = "pads cannot null")
         @Size(min = 1, message = "pads cannot null")
         private List<Pad> pads;
 
         @Data
         public static class Pad {
             private String containerIndex;
             /**
              * 实例标识
              */
             @NotNull(message = "padCode cannot null")
             private String padCode;
 
             /**
              * 镜像配置
              */
             @Valid
             @NotNull(message = "image cannot null")
             private ImageRequest image;
 
             /**
              * 展示配置
              */
             @Valid
             @NotNull(message = "display cannot null")
             private DisplayRequest display;
 
             /**
              * 规格配置
              */
             @Valid
             @NotNull(message = "spec cannot null")
             private SpecRequest spec;
 
             /**
              * 网络配置
              */
             @Valid
             @NotNull(message = "network cannot null")
             private NetworkRequest network;
 
             /**
              * 安卓属性
              */
             private String androidProp;
 
             /**
              * 安卓设备改机属性
              */
             private Map<String, String> deviceAndroidProps;
 
             /**
              * 云真机模板信息配置
              */
             private ADI adi;

             /**
              * 网存id
              */
             private String netStorageResId;

             /**
              * 是否网存标记 (默认本地存储0  1网络存储)
              */
             private Integer netStorageResFlag = 0;

         }
     }
 
     /**
      * 云真机模板
      */
     @Data
     public static class ADI{
         /**
          * 云真机模板地址
          */
         private String templateUrl;
 
         /**
          * 云真机模板密码
          */
         private String templatePassword;
 
         /**
          * 安卓证书数据
          */
         private String androidCertData;

         /**
          * adi模板Id
          */
         private Long realPhoneTemplateId;
     }
 
     @Data
     public static class InitInformation {
         /**
          * 容器存储空间,单位(GB)
          */
         private Integer hostStorageSize;
 
         /**
          * 容器多开的数量,容器并发数量
          */
         private Integer containerConcurrentSize;
     }
 }