 package net.armcloud.paascenter.cms.service.handler.instance.upgradeimage;
 
 import com.google.common.collect.Lists;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.InstanceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.mapper.InstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.instance.AbstractInstanceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.*;
 import java.util.concurrent.*;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum.*;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_INSTANCE_UPGRADE_IMAGE_VERIFY_LOCK_KEY;
 import static net.armcloud.paascenter.cms.utils.DCUtils.getDcId;
 
 @Slf4j
 @Service
 public class InstanceUpgradeImageTaskResultHandler extends AbstractInstanceTaskHandler {
     private final InstanceManager instanceManager;
     private final InstanceTaskMapper instanceTaskMapper;
     private final RedissonDistributedLock redissonDistributedLock;
 
     private static final int TIMEOUT_MINUTES = 5; // 任务最大允许执行时间（5分钟）
 
     // 线程池：最大32个线程，任务队列最大2000，拒绝策略：丢弃
     private final ThreadPoolExecutor DISCARD_POLICY_THREAD_POOL =
             new ThreadPoolExecutor(16, 128, 180, TimeUnit.SECONDS,
                     new LinkedBlockingQueue<>(2000),
                     new ThreadPoolExecutor.DiscardPolicy()) {
                 @Override
                 public ThreadFactory getThreadFactory() {
                     return new ThreadFactory() {
                         private int threadCount = 0;
 
                         @Override
                         public Thread newThread(Runnable runnable) {
                             String namePrefix = "InstanceUpgradeImageTask-Thread-";
                             return new Thread(runnable, namePrefix + threadCount++);
                         }
                     };
                 }
             };
 
 
     public void start() {
         super.asyncExecute(this::tryStart);
     }
 
     private void tryStart() {
         long currentTimeMillis = System.currentTimeMillis();
         long currentThreadId = Thread.currentThread().getId();
         log.debug("Thread {} start verifying running task result...", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_INSTANCE_UPGRADE_IMAGE_VERIFY_LOCK_KEY)) {
             log.debug("Thread {} holds lock {}. Skipping execution...", currentThreadId, SCHEDULED_INSTANCE_UPGRADE_IMAGE_VERIFY_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_INSTANCE_UPGRADE_IMAGE_VERIFY_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("Thread {} failed to acquire lock {}. Skipping execution...", currentThreadId, SCHEDULED_INSTANCE_UPGRADE_IMAGE_VERIFY_LOCK_KEY);
             return;
         }
 
         log.info("InstanceUpgradeImageTaskResultHandler.tryStart started at {}", System.currentTimeMillis());
 
         try {
             List<InstanceTask> tasks = instanceTaskMapper.listByStatusAndTargetTaskTypes(
                     getDcId(), EXECUTING.getStatus(),
                     Arrays.asList(INSTANCE_UPGRADE_IMAGE.getIntValue(), INSTANCE_REPLACE_PROP.getIntValue(),
                             INSTANCE_MODIFY_PROPERTIES_PROP.getIntValue(), INSTANCE_REPLACE_REAL_ADB.getIntValue()));
 
             if (CollectionUtils.isEmpty(tasks)) {
                 return;
             }
 
             List<List<InstanceTask>> partitionedTasks = Lists.partition(tasks, 50);
             CountDownLatch latch = new CountDownLatch(partitionedTasks.size());
             List<Future<?>> futures = new ArrayList<>();
             for (List<InstanceTask> instanceTaskList : partitionedTasks) {
                 Future<?> future = DISCARD_POLICY_THREAD_POOL.submit(() -> {
                     try {
                         processTaskBatch(instanceTaskList, currentTimeMillis);
                     } finally {
                         latch.countDown();
                     }
                 });
                 futures.add(future);
             }
 
             // 等待最多 5 分钟，超时则终止所有未完成任务
             if (!latch.await(TIMEOUT_MINUTES, TimeUnit.MINUTES)) {
                 log.warn("Execution timed out after {} minutes, canceling remaining tasks.", TIMEOUT_MINUTES);
                 futures.forEach(future -> future.cancel(true));
                 DISCARD_POLICY_THREAD_POOL.shutdownNow();
             }
 
         } catch (Exception e) {
             log.error("InstanceUpgradeImageTaskResultHandler execution failed", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
 
         log.info("InstanceUpgradeImageTaskResultHandler.tryStart ended at {}", System.currentTimeMillis());
     }
 
     /**
      * 处理一批 InstanceTask
      */
     private void processTaskBatch(List<InstanceTask> instanceTaskList, long currentTimeMillis) {
         instanceTaskList.parallelStream()
                 .collect(Collectors.groupingByConcurrent(InstanceTask::getDeviceIp))
                 .forEach((deviceIp, taskList) -> {
                     List<InstanceInfoResponse> instanceInfoResponses;
                     try {
                         log.info("Fetching instance info for deviceIp: {} at {}", deviceIp, System.currentTimeMillis());
                         instanceInfoResponses = instanceManager.listAll(deviceIp);
                         log.info("Fetched {} instances for deviceIp: {} at {}",
                                 instanceInfoResponses.size(), deviceIp, System.currentTimeMillis());
                     } catch (Exception e) {
                         log.error("Error fetching instance info for deviceIp: {} at {}. Exception: {}",
                                 deviceIp, System.currentTimeMillis(), e.getMessage());
                         return;
                     }
 
                     Map<String, InstanceInfoResponse> instanceInfoResponseMap = instanceInfoResponses.stream()
                             .collect(Collectors.toMap(InstanceInfoResponse::getName, obj -> obj, (o1, o2) -> o1));
 
                     taskList.forEach(task -> processTask(task, instanceInfoResponseMap, deviceIp, currentTimeMillis));
                 });
     }
 
     /**
      * 处理单个 InstanceTask
      */
     private void processTask(InstanceTask task, Map<String, InstanceInfoResponse> instanceInfoResponseMap,
                              String deviceIp, long currentTimeMillis) {
         String instanceName = task.getInstanceName();
         InstanceInfoResponse instanceInfoResponse = instanceInfoResponseMap.get(instanceName);
         if (Objects.isNull(instanceInfoResponse)) {
             return;
         }
 
         Date startUpTime = instanceInfoResponse.convertStartTimeToDate();
         if (Objects.isNull(startUpTime) || task.getStartTime().after(startUpTime)) {
             return;
         }
 
         log.info("Updating status for taskId: {} on deviceIp: {} at {}", task.getId(), deviceIp, System.currentTimeMillis());
         super.updateStatus(task, TaskStatusConstants.SUCCESS, "", EXECUTING.getStatus());
         log.info("Updated status for taskId: {} on deviceIp: {} at {}", task.getId(), deviceIp, System.currentTimeMillis());
     }
 
     protected InstanceUpgradeImageTaskResultHandler(CmsTaskManager cmsTaskManager,
                                                     RedissonDistributedLock redissonDistributedLock, InstanceManager instanceManager,
                                                     InstanceTaskMapper instanceTaskMapper,
                                                     InstanceTaskStatusMQConfig instanceTaskStatusMQConfig,
                                                     DefaultRocketMqProducerWrapper rocketMqProducerService,
                                                     InstanceDetailImageSuccService instanceDetailImageSuccService,
                                                     TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, instanceTaskMapper, instanceTaskStatusMQConfig, rocketMqProducerService,
                 instanceDetailImageSuccService, taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.instanceManager = instanceManager;
         this.instanceTaskMapper = instanceTaskMapper;
     }
 }