 package net.armcloud.paascenter.cms.manager.cbs;
 
 import com.alibaba.fastjson.JSONObject;
 import com.alibaba.fastjson2.JSON;
 import com.alibaba.fastjson2.JSONArray;
 import com.google.common.collect.Maps;
 import net.armcloud.paascenter.cms.model.*;
 import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.InstanceTask;
 import net.armcloud.paascenter.cms.manager.cbs.feign.ContainerInstanceFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.InstanceInfoResponse;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
 import net.armcloud.paascenter.cms.model.dto.BaseContainerOpsDTO;
 import feign.Request;
 import feign.RetryableException;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.stereotype.Component;
 
 import java.net.URI;
 import java.util.*;
 
 import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.GAME_SERVICE_INTERFACE_DOMAIN;
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_CONNECT_EXCEPTION;
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_GET_STATUS_FAIL_VIRTUALIZE_EXCEPTION;
 import static net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils.builderHost;
 
 @Slf4j
 @Component("cbsInstanceManager")
 public class InstanceManager {
     private final ConfigurationMapper configurationMapper;
     private final ContainerInstanceFeignClient containerInstanceFeignClient;
 
     public String create(CreatePadBO padBO) {
         ContainerResourceOpsParam containerResourceOpsParam = new ContainerResourceOpsParam();
         containerResourceOpsParam.setName(padBO.getName());
         containerResourceOpsParam.setCpuLimit(padBO.getCpuLimit());
         containerResourceOpsParam.setMemoryLimit(padBO.getMemoryLimit());
         containerResourceOpsParam.setStorageLimit(padBO.getStorageLimit());
         containerResourceOpsParam.setIp(padBO.getIp());
         containerResourceOpsParam.setHostname(padBO.getHostname());
         containerResourceOpsParam.setImageRepository(padBO.getImageRepository());
         containerResourceOpsParam.setImageTag(padBO.getImageTag());
         containerResourceOpsParam.setExtId(padBO.getExtId());
         containerResourceOpsParam.setAsync(padBO.getAsync());
         containerResourceOpsParam.setIsolateDisk(padBO.getIsolateDisk());
         containerResourceOpsParam.setContainerIndex(Long.valueOf(padBO.getContainerIndex()));
         containerResourceOpsParam.setWidth(padBO.getWidth());
         containerResourceOpsParam.setHeight(padBO.getHeight());
         containerResourceOpsParam.setFps(padBO.getFps());
         containerResourceOpsParam.setDpi(padBO.getDpi());
         containerResourceOpsParam.setOtherAndroidProp(padBO.getOtherAndroidProp());
         containerResourceOpsParam.setMacvlanName(padBO.getMacvlanName());
         containerResourceOpsParam.setDns1(padBO.getDns1());
         containerResourceOpsParam.setDns2(padBO.getDns2());
         containerResourceOpsParam.setMac(padBO.getMac());
         containerResourceOpsParam.setAndroidCertData(padBO.getAndroidCertData());
 
         if (StringUtils.isNotBlank(padBO.getDownloadUrlOfADI())) {
             containerResourceOpsParam.setDownloadUrlOfADI(padBO.getDownloadUrlOfADI());
         }
 
         if (StringUtils.isNotBlank(padBO.getPasswordOfADI())) {
             containerResourceOpsParam.setPasswordOfADI(padBO.getPasswordOfADI());
         }
         Map<String, String> deviceAndroidPropMap = Maps.newHashMap();
 //        每次重建实例,默认关闭adb
         deviceAndroidPropMap.put("persist.sys.cloud.madb_enable","0");
         if (StringUtils.isNotBlank(padBO.getDeviceAndroidProps())) {
             deviceAndroidPropMap.putAll(Optional.ofNullable(JSON.parseObject(padBO.getDeviceAndroidProps(), Map.class)).orElse(Maps.newHashMap()));
         }
         containerResourceOpsParam.setDeviceAndroidProps(deviceAndroidPropMap);
 //        if (StringUtils.isNotBlank(padBO.getDeviceAndroidProps())) {
 //            containerResourceOpsParam.setDeviceAndroidProps(JSON.parseObject(padBO.getDeviceAndroidProps(), Map.class));
 //        }
 
         BaseLvmDiskOpsParam baseLvmDiskOpsParam = new BaseLvmDiskOpsParam();
         baseLvmDiskOpsParam.setHostStorageSize(Optional.ofNullable(padBO.getHostStorageSize()).map(Long::valueOf).orElse(null));
         baseLvmDiskOpsParam.setContainerPerStorageSize(padBO.getStorageLimit());
         baseLvmDiskOpsParam.setContainerConcurrentSize(Optional.ofNullable(padBO.getContainerConcurrentSize()).map(Long::valueOf).orElse(null));
         baseLvmDiskOpsParam.setIsolateDisk(padBO.getIsolateDisk());
         containerResourceOpsParam.setDiskOpsParam(baseLvmDiskOpsParam);
         containerResourceOpsParam.setClearContainerData(Boolean.TRUE.equals(padBO.getClearContainerData()));
         String gameServiceInterfaceDomain = configurationMapper.selectValueByKey(GAME_SERVICE_INTERFACE_DOMAIN);
         containerResourceOpsParam.setGameServerAddress(gameServiceInterfaceDomain);
 
         URI deviceHost = builderHost(padBO.getDeviceIp());
         IBaseOpsVo iOpsVo;
         try {
             log.debug("InstanceManager create deviceIp {} >>> containerResourceOpsParam:{}",
                     padBO.getDeviceIp(), JSON.toJSONString(containerResourceOpsParam));
             iOpsVo = containerInstanceFeignClient.create(deviceHost, containerResourceOpsParam);
         } catch (RetryableException e) {
             String error = padBO.getDeviceIp() + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
         return iOpsVo.getDataPayload();
     }
 
     public List<DeviceInstanceTask> builderDeviceInstanceTask(long masterTaskId, String deviceIp) {
         List<DeviceInstanceTask> deviceInstanceTasks = new ArrayList<>();
         List<InstanceInfoResponse> instanceInfoResponses = listAll(deviceIp);
         instanceInfoResponses.forEach(instanceInfoResponse -> {
             DeviceInstanceTask deviceInstanceTask = new DeviceInstanceTask();
             deviceInstanceTask.setMasterTaskId(masterTaskId);
             deviceInstanceTask.setDeviceIp(deviceIp);
             deviceInstanceTask.setInstanceName(instanceInfoResponse.getName());
             deviceInstanceTask.setStatus(TaskStatusConstants.EXECUTING.getStatus());
             deviceInstanceTasks.add(deviceInstanceTask);
         });
 
         return deviceInstanceTasks;
     }
 
     /**
      * 查询云机实例信息
      */
     public List<InstanceInfoResponse> listAll(String deviceIp) {
         try {
             URI deviceHost = builderHost(deviceIp);
             IBaseOpsVo iOpsVo = containerInstanceFeignClient.listAll(deviceHost,new Request.Options(5000, 30000, true));
             BackendFeignUtils.versify(iOpsVo);
             String result = iOpsVo.getDataPayload();
             if (StringUtils.isBlank(result)) {
                 return new ArrayList<>(0);
             }
 
             return JSONArray.parseArray(result, InstanceInfoResponse.class);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         } catch (Exception e) {
             log.error("InstanceManager.listAll error . e:{}",e.getMessage());
             String msg = deviceIp + DEVICE_GET_STATUS_FAIL_VIRTUALIZE_EXCEPTION.getMsg() + " -> " + e.getMessage();
             throw new BasicException(DEVICE_GET_STATUS_FAIL_VIRTUALIZE_EXCEPTION.getStatus(), msg);
         }
     }
 
     public void restartAll(String deviceIp) {
         URI deviceHost = builderHost(deviceIp);
         IBaseOpsVo iOpsVo;
         try {
             iOpsVo = containerInstanceFeignClient.restartAll(deviceHost);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
         log.info("restartAll >> deviceIp:{} result:{}", deviceIp, iOpsVo.getDataPayload());
     }
 
     public String destroyAll(String deviceIp) {
         URI deviceHost = builderHost(deviceIp);
         IBaseOpsVo iOpsVo;
         try {
             // 因CBS接口只调用一次，有时会删不成功。临时处理调3次
             for (int i = 0; i < 3; i++) {
                 iOpsVo = containerInstanceFeignClient.destroyAll(deviceHost);
                 BackendFeignUtils.versify(iOpsVo);
             }
             return null;
         } catch (RetryableException e) {
             log.error("destroy device:{} error>>>", deviceIp, e);
             return deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
         }
     }
 
     public void restart(String deviceIp, String padCode) {
         BaseContainerOpsParam param = new BaseContainerOpsParam();
         param.setAsync(false);
         param.setName(padCode);
         IBaseOpsVo iOpsVo;
         try {
             iOpsVo = containerInstanceFeignClient.restart(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
     }
 
     public void reset(InstanceTask task,Integer taskType) {
         IBaseOpsVo iOpsVo;
         String deviceIp = task.getDeviceIp();
         BaseContainerOpsDTO param = new BaseContainerOpsDTO();
         param.setAsync(false);
         param.setName(task.getInstanceName());
         if(Objects.equals(taskType, TaskTypeEnum.INSTANCE_RESET.getIntValue())){
             Map<String,String> deviceAndroidProps = Maps.newHashMap();
             param.setDeviceAndroidProps(deviceAndroidProps);
             //每次重置默认关闭ADB
             deviceAndroidProps.put("persist.sys.cloud.madb_enable","0");
         }
         try {
             log.info("reset_debug:{}", JSONObject.toJSONString(param));
             iOpsVo = containerInstanceFeignClient.reset(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
     }
 
     public void remove(String deviceIp, String padCode, boolean clearData) {
         ContainerDeleteOpsParam param = new ContainerDeleteOpsParam();
         param.setAsync(false);
         param.setName(padCode);
         param.setResetContainerData(clearData);
         IBaseOpsVo iOpsVo;
         try {
             iOpsVo = containerInstanceFeignClient.remove(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
     }
 
     public void updateAndroidProp(String deviceIp, String instanceName, Map<String, String> deviceAndroidProps, Boolean restart) {
         IBaseOpsVo iOpsVo;
         try {
             ContainerAndroidPropParam param = new ContainerAndroidPropParam();
             param.setDeviceAndroidProps(deviceAndroidProps);
             param.setName(instanceName);
             param.setRestart(restart);
             log.debug("updateAndroidProp>>>param:{}", JSON.toJSONString(param));
             iOpsVo = containerInstanceFeignClient.updateAndroidPropAndRestart(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
     }
 
     public InstanceManager(ConfigurationMapper configurationMapper, ContainerInstanceFeignClient containerInstanceFeignClient) {
         this.configurationMapper = configurationMapper;
         this.containerInstanceFeignClient = containerInstanceFeignClient;
     }
 }