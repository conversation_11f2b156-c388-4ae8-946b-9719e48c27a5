 package net.armcloud.paascenter.cms.facade;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.model.request.ProxyDetectionRequest;
 import net.armcloud.paascenter.cms.model.request.SystemCmdRequest;
 import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 public interface SystemFacade {
     /**
      * 执行系统命令
      */
     @PostMapping(value = "/armcloud-container/open/system/cmd/sync")
     Result<String> execSyncCmd(@RequestBody SystemCmdRequest req);
 
     /**
      * 执行系统命令
      */
     @PostMapping(value = "/armcloud-container/open/system/proxy_detection")
     Result<ProxyDetectionResponse> proxyDetection(@RequestBody ProxyDetectionRequest req);
 }