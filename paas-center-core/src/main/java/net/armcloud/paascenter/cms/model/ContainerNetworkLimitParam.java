 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 @ApiModel("实例限速")
 @Data
 public class ContainerNetworkLimitParam {
 
     @ApiModelProperty("上传限速,字符串数字(单位: mbps,eg:1,10,100,[0为不限速,-1为禁止上传])")
     private String upload;
 
     @ApiModelProperty("下载限速,字符串数字(单位: mbps,eg:1,10,100,[0为不限速,-1为禁止下载]),")
     private String download;
 
 
     @ApiModelProperty("需要限速的容器名称,#containerName")
     private String name;
 }