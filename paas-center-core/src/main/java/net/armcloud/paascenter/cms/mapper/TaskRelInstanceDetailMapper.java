 package net.armcloud.paascenter.cms.mapper;
 
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.List;
 
 @Mapper
 public interface TaskRelInstanceDetailMapper {
     int batchInsert(List<TaskRelInstanceDetail> list);
 
     TaskRelInstanceDetail getByMasterTaskIdAndSubTaskId(@Param("taskType") int taskType, @Param("masterTaskId") long masterTaskId,
                                                         @Param("subTaskId") long subTaskId);

     void updateContainerProperty(@Param("id") long id, @Param("containerProperty") String containerProperty);
 
     TaskRelInstanceDetail getLatestByMasterTaskId(@Param("taskType") int taskType, @Param("masterTaskId") long masterTaskId);
 
     void insert(TaskRelInstanceDetail taskRelPadDetail);
 
     List<TaskRelInstanceDetail> getBySubTaskId(@Param("taskType") int taskType,@Param("subTaskId") long subTaskId);
 }