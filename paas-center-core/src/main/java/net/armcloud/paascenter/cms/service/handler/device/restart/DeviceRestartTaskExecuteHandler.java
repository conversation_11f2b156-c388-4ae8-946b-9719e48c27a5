 package net.armcloud.paascenter.cms.service.handler.device.restart;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.SystemManager;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 
 @Slf4j
 @Service
 public class DeviceRestartTaskExecuteHandler extends AbstractDeviceTaskHandler {
     private final SystemManager systemManager;
     private final RedissonDistributedLock redissonDistributedLock;
 
     public void start(DeviceTask deviceTask) {
         super.asyncExecute(() -> tryStart(deviceTask));
     }
 
     private void tryStart(DeviceTask deviceTask) {
         String dataJson = JSON.toJSONString(deviceTask);
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start exec device restart task:{}", currentThreadId, dataJson);
 
         String deviceIp = deviceTask.getIp();
         String key = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceIp;
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} exec device restart task:{} not get lock key {}", currentThreadId, dataJson, key);
             return;
         }
 
         try {
             restart(deviceTask);
         } catch (Exception e) {
             log.error("DeviceRestartTaskExecuteHandler error>>>", e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void restart(DeviceTask deviceTask) {
         String deviceIp = deviceTask.getIp();
         try {
             boolean success = super.updateStatus(deviceTask, TaskStatusConstants.EXECUTING, null, TaskStatusConstants.WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
 
             systemManager.execSyncCmd(deviceIp, "sudo reboot now");
         } catch (Exception e) {
             log.error("DeviceRestartTaskExecuteHandler error>>>> deviceTask:{}", JSON.toJSONString(deviceTask), e);
             super.updateStatus(deviceTask, TaskStatusConstants.FAIL_ALL, "执行重启命令失败", TaskStatusConstants.EXECUTING.getStatus());
         }
     }
 
     public DeviceRestartTaskExecuteHandler(RedissonDistributedLock redissonDistributedLock, DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                            SystemManager systemManager, CmsTaskManager cmsTaskManager, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                            DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper,
                                            DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, InstanceDetailImageSuccService instanceDetailImageSuccService,
                                            TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, defaultRocketMqProducerWrapper, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.systemManager = systemManager;
     }
 }