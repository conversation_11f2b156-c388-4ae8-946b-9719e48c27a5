 package net.armcloud.paascenter.cms.manager.cbs.model.bo.response;
 
 import com.alibaba.fastjson2.JSONObject;
 import com.alibaba.fastjson2.annotation.JSONField;
 import lombok.Data;
 import org.apache.commons.lang3.StringUtils;
 
 import java.time.LocalDateTime;
 import java.time.ZoneId;
 import java.time.format.DateTimeFormatter;
 import java.util.Date;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.utils.DateUtils.FORMART1;
 
 @Data
 public class InstanceInfoResponse {
     @JSONField(name = "Names")
     private String name;
 
     @JSONField(name = "ContainerStartTime")
     private String startTime;
 
     @JSONField(name = "Labels")
     private String labels;
 
     @JSONField(name = "State")
     private String state;
 
     @JSONField(name = "MountVersion")
     private String mountVersion;
 
     public Date convertStartTimeToDate() {
         LocalDateTime startDateTime = convertStartTimeToLocalDateTime();
         if (Objects.isNull(startDateTime)) {
             return null;
         }
 
         return Date.from(startDateTime.atZone(ZoneId.systemDefault()).toInstant());
     }
 
     public LocalDateTime convertStartTimeToLocalDateTime() {
         if (StringUtils.isBlank(startTime)) {
             return null;
         }
 
         return LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern(FORMART1));
     }
 
     public String getExtId() {
         return getLabelsJSONObject().getString("extId");
     }
 
     public JSONObject getLabelsJSONObject() {
         JSONObject jsonObject = new JSONObject();
         if (StringUtils.isBlank(labels)) {
             return jsonObject;
         }
 
         String[] pairs = labels.split(",");
         for (String pair : pairs) {
             String[] keyValue = pair.split("=");
             String key = keyValue[0];
             String value = keyValue[1];
             jsonObject.put(key, value);
         }
 
         return jsonObject;
     }
 }