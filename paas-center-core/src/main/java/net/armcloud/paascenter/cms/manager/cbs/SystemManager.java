 package net.armcloud.paascenter.cms.manager.cbs;
 
 import com.alibaba.fastjson2.JSONObject;
 import net.armcloud.paascenter.cms.model.SysBuildInfo;
 import net.armcloud.paascenter.cms.model.SysCmdOpsVO;
 import net.armcloud.paascenter.cms.model.SysCmdParam;
 import net.armcloud.paascenter.cms.manager.cbs.feign.SystemFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.response.CbsSelfUpdateStatusResponse;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import net.armcloud.paascenter.cms.model.SysSelfUpdateDTO;
 import lombok.extern.slf4j.Slf4j;
 import okhttp3.OkHttpClient;
 import org.springframework.stereotype.Component;
 
 import java.net.InetAddress;
 import java.util.concurrent.TimeUnit;
 
 @Slf4j
 @Component
 public class SystemManager {
     private final SystemFeignClient systemFeignClient;
     public static final int PORT = 18182;
 
     private static final OkHttpClient HTTP_CLIENT;
 
     static {
         HTTP_CLIENT = new OkHttpClient.Builder()
                 .connectTimeout(100, TimeUnit.MILLISECONDS)
                 .writeTimeout(100, TimeUnit.MILLISECONDS)
                 .readTimeout(100, TimeUnit.MILLISECONDS)
                 .build();
     }
 
     public String execAsyncCmd(String deviceIp, String cmd) {
         return execCmd(deviceIp, cmd, true);
     }
 
     public String execSyncCmd(String deviceIp, String cmd) {
         return execCmd(deviceIp, cmd, false);
     }
 
     public boolean health(String deviceIp) {
         try {
             InetAddress inet = InetAddress.getByName(deviceIp);
             boolean isReachable = inet.isReachable(200);
             if (!isReachable) {
                 return false;
             }
 
             systemFeignClient.health(BackendFeignUtils.builderHost(deviceIp));
             /*String fileUrl = BackendFeignUtils.builderHost(deviceIp) + "/actuator/health";
             Request request = new Request.Builder()
                     .url(fileUrl)
                     .build();
 
             Response response = HTTP_CLIENT.newCall(request).execute();
             if (!response.isSuccessful()){
                 return false;
             }*/
         } catch (Exception e) {
             log.error("deviceIp {} check health fail msg:{}", deviceIp, e.getMessage());
             return false;
         }
 
         return true;
     }
 
     public void restartDocker(String deviceIp) {
         SysCmdOpsVO sysCmdOpsVO = systemFeignClient.restartDocker(BackendFeignUtils.builderHost(deviceIp));
         BackendFeignUtils.versify(sysCmdOpsVO);
     }
 
     public String execCmd(String deviceIp, String cmd, boolean async) {
         SysCmdParam sysCmdParam = new SysCmdParam();
         sysCmdParam.setCmd(cmd);
         sysCmdParam.setAsync(async);
         SysCmdOpsVO systemCmdOpsVO;
         systemCmdOpsVO = systemFeignClient.execCmd(BackendFeignUtils.builderHost(deviceIp), sysCmdParam);
         BackendFeignUtils.versify(systemCmdOpsVO);
         return systemCmdOpsVO.getMsg();
     }
 
     public SysBuildInfo getBuilderInfo(String deviceIp) {
         return systemFeignClient.buildInfo(BackendFeignUtils.builderHost(deviceIp));
     }
 
     public String cbsUpdate(String deviceIp,SysSelfUpdateDTO sysSelfUpdateDTO) {
         SysCmdOpsVO systemCmdOpsVO = systemFeignClient.selfUpdate(BackendFeignUtils.builderHost(deviceIp),sysSelfUpdateDTO);
         BackendFeignUtils.versify(systemCmdOpsVO);
         return systemCmdOpsVO.getMsg();
     }
 
     /**
      * cbs更新状态查询
      * @param deviceIp
      * @return
      */
     public CbsSelfUpdateStatusResponse cbsUpdateStatus(String deviceIp) {
         CbsSelfUpdateStatusResponse cbsSelfUpdateStatusResponse = null;
         try{
             SysCmdOpsVO systemCmdOpsVO = systemFeignClient.selfUpdateStatus(BackendFeignUtils.builderHost(deviceIp));
             BackendFeignUtils.versify(systemCmdOpsVO);
             cbsSelfUpdateStatusResponse = JSONObject.parseObject(systemCmdOpsVO.getDataPayload(), CbsSelfUpdateStatusResponse.class);
         }catch (Exception e){
             //cbs更新的过程中 服务会中断 所以这里报错就不用处理 如果cbs确实启不起来了  就通过超时任务处理
             log.error("cbsUpdateStatus error deviceIp:{}",deviceIp,e);
         }
         return cbsSelfUpdateStatusResponse;
     }
 
     public SystemManager(SystemFeignClient systemFeignClient) {
         this.systemFeignClient = systemFeignClient;
     }
 }