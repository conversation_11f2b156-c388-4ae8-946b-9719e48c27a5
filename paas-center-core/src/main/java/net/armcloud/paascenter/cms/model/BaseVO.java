 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiParam;
 import lombok.AllArgsConstructor;
 import lombok.Data;
 import lombok.NoArgsConstructor;
 import net.armcloud.paascenter.cms.enums.ContainerOpsStatusEnum;
 
 @Data
 @AllArgsConstructor
 @NoArgsConstructor
 public class BaseVO implements IBaseOpsVo {
 
     @ApiParam("http调用通用返回编码值")
     private Integer code;
 
     @ApiParam("http调用通用返回消息")
     private String msg;
 
     @ApiParam("http调用通用返回内容(注意:异步调用无返回内容,返回空)")
     private String dataPayload;
 
     public BaseVO buildSuccess(String msgPayload) {
         this.code = ContainerOpsStatusEnum.SUCCESS.code;
         this.msg = ContainerOpsStatusEnum.SUCCESS.msg;
         this.dataPayload = msgPayload;
         return this;
     }
 
     public BaseVO buildSuccess() {
         this.code = ContainerOpsStatusEnum.SUCCESS.code;
         this.msg = ContainerOpsStatusEnum.SUCCESS.msg;
         this.dataPayload = null;
         return this;
     }
 
     public BaseVO buildFail(String msg) {
         this.code = ContainerOpsStatusEnum.FAIL.code;
         this.msg = msg;
         this.dataPayload = null;
         return this;
     }
 
 }