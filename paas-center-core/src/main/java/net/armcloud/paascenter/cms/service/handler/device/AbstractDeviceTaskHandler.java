 package net.armcloud.paascenter.cms.service.handler.device;
 
 import cn.hutool.core.bean.BeanUtil;
 import com.alibaba.fastjson2.JSON;
 import com.google.common.util.concurrent.ThreadFactoryBuilder;
 import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
 import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
 import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetailImageSucc;
 import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceTaskResultMQ;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Value;
 
 import javax.annotation.PostConstruct;
 import java.util.Date;
 import java.util.concurrent.ExecutorService;
 import java.util.concurrent.LinkedBlockingQueue;
 import java.util.concurrent.ThreadPoolExecutor;
 import java.util.concurrent.TimeUnit;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 
 @Slf4j
 public abstract class AbstractDeviceTaskHandler {
     private final CmsTaskManager cmsTaskManager;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
     private final DeviceTaskStatusMQConfig deviceTaskStatusMQConfig;
     private final DefaultRocketMqProducerWrapper rocketMqProducerService;
     private final DeviceInstanceTaskMapper deviceInstanceTaskMapper;
     private final TaskRelInstanceDetailMapper taskRelInstanceDetailMapper;
     private final InstanceDetailImageSuccService instanceDetailImageSuccService;
 
     @Value("${thread.device.core-size}")
     private Integer coreSize;
 
     @Value("${thread.device.max-size}")
     private Integer maxSize;
 
     private static final int CORE_POOL_SIZE;
 
     static {
         CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors();
     }
 
     private static final int MAXIMUM_POOL_SIZE = CORE_POOL_SIZE * 2 + 1;
     private static final long KEEP_ALIVE_TIME = 30L;
     private static final int LINKED_BLOCKING_QUEUE_CAPACITY = Integer.MAX_VALUE;
 
     private ExecutorService threadPool;
 
     protected void asyncExecute(Runnable command) {
         threadPool.execute(command);
     }
 
     protected boolean updateStatus(DeviceInstanceTask deviceInstanceTask, TaskStatusConstants status, String msg, Integer originStatus) {
         long taskId = deviceInstanceTask.getId();
 
         if (status == EXECUTING) {
             return deviceInstanceTaskMapper.updateStatusById(taskId, status.getStatus(), new Date(), null, msg, originStatus) > 0;
         }
 
         if (status == FAIL_ALL) {
             return deviceInstanceTaskMapper.updateStatusById(taskId, status.getStatus(), null, new Date(), msg, originStatus) > 0;
         }
 
         if (status == SUCCESS) {
             Boolean respStatus = deviceInstanceTaskMapper.updateStatusById(taskId, status.getStatus(), null, new Date(), msg, originStatus) > 0;
             //100,204任务执行成功 则插入task_rel_instance_detail_image_succ表
             DeviceTask deviceTask = cmsDeviceTaskMapper.getById(deviceInstanceTask.getMasterTaskId());
             if(deviceTask != null && TaskTypeEnum.DEVICE_CREATE.getIntValue().equals(deviceTask.getType())){
                 TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getByMasterTaskIdAndSubTaskId(deviceTask.getType(),deviceInstanceTask.getMasterTaskId(),deviceInstanceTask.getId());
                 if(taskRelInstanceDetail != null){
                     TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = BeanUtil.copyProperties(taskRelInstanceDetail,TaskRelInstanceDetailImageSucc.class);
                     instanceDetailImageSuccService.saveAndClear(taskRelInstanceDetailImageSucc);
                 }
             }
             return respStatus;
         }
 
         return false;
     }
 
     protected boolean updateStatus(DeviceTask task, TaskStatusConstants status, String msg, Integer originStatus) {
         return updateStatus(task,status,msg,originStatus,null);
     }
 
     protected boolean updateStatus(DeviceTask task, TaskStatusConstants status, String msg, Integer originStatus,String data) {
         boolean success = false;
         long taskId = task.getId();
 
         if (status == WAIT_EXECUTE) {
             success = cmsDeviceTaskMapper.updateStatusById(taskId, status.getStatus(), msg, null, null, null, originStatus) > 0;
         }
 
         if (status == EXECUTING) {
             Date timeoutTime = cmsTaskManager.getTimeoutTime(task.getType());
             success = cmsDeviceTaskMapper.updateStatusById(taskId, status.getStatus(), msg, new Date(), null, timeoutTime, originStatus) > 0;
         }
 
         if (status != WAIT_EXECUTE && status != EXECUTING) {
             success = cmsDeviceTaskMapper.updateStatusById(taskId, status.getStatus(), msg, null, new Date(), null, originStatus) > 0;
         }
 
         //100,204任务执行成功 则插入task_rel_instance_detail_image_succ表
         /*if(status == SUCCESS && (TaskTypeEnum.DEVICE_CREATE.getIntValue().equals(task.getType()) || TaskTypeEnum.INSTANCE_UPGRADE_IMAGE.getIntValue().equals(task.getType()))){
             TaskRelInstanceDetail taskRelInstanceDetail = taskRelInstanceDetailMapper.getByMasterTaskIdAndSubTaskId(task.getType(),task.getId(),task.getId());
             if(taskRelInstanceDetail != null){
                 TaskRelInstanceDetailImageSucc taskRelInstanceDetailImageSucc = BeanUtil.copyProperties(taskRelInstanceDetail,TaskRelInstanceDetailImageSucc.class);
                 instanceDetailImageSuccService.saveAndClear(taskRelInstanceDetailImageSucc);
             }
         }*/
 
         if (success) {
             ContainerDeviceTaskResultMQ message = new ContainerDeviceTaskResultMQ();
             message.setDeviceIp(task.getIp());
             message.setMasterTaskId(taskId);
             message.setMasterTaskStatus(status.getStatus());
             message.setMsg(msg);
             message.setData(data);
             rocketMqProducerService.producerNormalMessage(deviceTaskStatusMQConfig.getTopic(), JSON.toJSONString(message));
         }
 
         return success;
     }
 
     protected AbstractDeviceTaskHandler(CmsTaskManager cmsTaskManager, DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                         DefaultRocketMqProducerWrapper rocketMqProducerService, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                         DeviceTaskStatusMQConfig deviceTaskStatusMQConfig,
                                         InstanceDetailImageSuccService instanceDetailImageSuccService,
                                         TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         this.cmsTaskManager = cmsTaskManager;
         this.deviceInstanceTaskMapper = deviceInstanceTaskMapper;
         this.rocketMqProducerService = rocketMqProducerService;
         this.deviceTaskStatusMQConfig = deviceTaskStatusMQConfig;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
         this.instanceDetailImageSuccService = instanceDetailImageSuccService;
         this.taskRelInstanceDetailMapper = taskRelInstanceDetailMapper;
     }
 
     @PostConstruct
     private void initThreadPool() {
         threadPool = new ThreadPoolExecutor(coreSize, maxSize,
                 KEEP_ALIVE_TIME, TimeUnit.SECONDS, new LinkedBlockingQueue<>(LINKED_BLOCKING_QUEUE_CAPACITY), new ThreadFactoryBuilder()
                 .setNameFormat("DeviceTaskPool-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());
     }
 }