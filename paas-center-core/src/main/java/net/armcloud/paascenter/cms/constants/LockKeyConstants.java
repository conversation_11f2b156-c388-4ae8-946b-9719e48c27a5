 package net.armcloud.paascenter.cms.constants;
 
 public class LockKeyConstants {
     private static final String PROJECT_NAME_PREFIX = "armcloud-container-manage-service:";
     public static final String LOCK_PREFIX = PROJECT_NAME_PREFIX + "lock:";
 
     public static class InterfaceRequest {
         public static final String INTERFACE = "interface:";
         public static final String INTERFACE_DEVICE_OPERATIONAL_LOCK_KEY_PREFIX = LOCK_PREFIX + INTERFACE + "device-operational:";
         public static final String INTERFACE_PUSH_IMAGE_LOCK_KEY_PREFIX = LOCK_PREFIX + INTERFACE + "push-image:";
     }
 
     public static class Scheduled {
         public static final String SCHEDULED = "scheduled:";
 
         // device
         public static final String SCHEDULED_DEVICE_VIRTUALIZE_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-virtualize";
         public static final String SCHEDULED_DEVICE_VIRTUALIZE_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-virtualize-verify:";
 
         public static final String SCHEDULED_DEVICE_RESTART_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-restart";
         public static final String SCHEDULED_DEVICE_RESTART_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-restart-verify";
 
         public static final String SCHEDULED_DEVICE_DESTROY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-destroy";
         public static final String SCHEDULED_DEVICE_DESTROY_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-destroy-verify";
 
         public static final String SCHEDULED_DEVICE_STATUS_DETECTION_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "device-status-detection";
 
         // instance
         public static final String SCHEDULED_INSTANCE_RESTART_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-restart";
         public static final String SCHEDULED_INSTANCE_RESTART_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-restart-verify";
 
         public static final String SCHEDULED_INSTANCE_RESET_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-reset";
         public static final String SCHEDULED_INSTANCE_RESET_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-reset-verify";
 
         public static final String SCHEDULED_INSTANCE_UPGRADE_IMAGE_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-upgrade-image";
         public static final String SCHEDULED_INSTANCE_UPGRADE_IMAGE_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-upgrade-image-verify";
 
         public static final String SCHEDULED_IMAGE_PUSH_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "image-push";
 
         public static final String SCHEDULED_INSTANCE_DATA_BACKUP_VERIFY_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "instance-data-backup-verify";
 
         // timeout task
         public static final String SCHEDULED_HANDLER_DEVICE_TIMEOUT_TASK_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "handler-device-timeout-task";
         public static final String SCHEDULED_HANDLER_INSTANCE_TIMEOUT_TASK_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "handler-instance-timeout-task";
         public static final String SCHEDULED_HANDLER_IMAGE_TIMEOUT_TASK_LOCK_KEY = LOCK_PREFIX + SCHEDULED + "handler-image-timeout-task";
     }
 
     public static class ExecuteTask {
         public static final String EXECUTETASK = "execute-task:";
         public static final String EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX = LOCK_PREFIX + EXECUTETASK + "device:";
         public static final String EXECUTE_TASK_INSTANCE_LOCK_KEY_PREFIX = LOCK_PREFIX + EXECUTETASK + "instance:";
         public static final String EXECUTE_TASK_IMAGE_LOCK_KEY_PREFIX = LOCK_PREFIX + EXECUTETASK + "image:";
     }

     public static class NetStorageProcess{
         public static final String NET_STORAGE = "net-storage:";
         //操作用户存储余额的锁
         public static final String RES = "res:";
         //操作用户算力余额的锁
         public static final String NET_STORAGE_RES_LOCK_KEY_PREFIX = LOCK_PREFIX + NET_STORAGE+RES;

         public static final String COMPUTE_UNIT_QUEUE = "compute-unit-queue:";

         public static final String USE_COMPUTE_UNIT_ID = "use-compute-unit-id:";

         public static final String COMPUTE_IP_QUEUE = "compute-ip-queue:";

         /**
          * 修改网存资源分配锁
          */
         public static final String USED =  LOCK_PREFIX + NET_STORAGE+"used:";
     }

     public static class IpClashLock{
         public static final String CBS_DEVICE_IP_TO_USE = "OPEN:CBS_DEVICE_IP_TO_USE:";
         public static final String CBS_DEVICE_IP_TO_USE_TEMP = "OPEN:CBS_DEVICE_IP_TO_USE_TEMP:";
     }


 }