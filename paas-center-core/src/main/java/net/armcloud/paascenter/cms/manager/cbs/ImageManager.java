 package net.armcloud.paascenter.cms.manager.cbs;
 
 import net.armcloud.paascenter.cms.model.BaseImageOpsParam;
 import net.armcloud.paascenter.cms.manager.cbs.feign.ImageFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import org.springframework.stereotype.Component;
 
 import java.net.URI;
 
 import static net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils.builderHost;
 
 @Component
 public class ImageManager {
     private final ImageFeignClient imageFeignClient;
 
     public int getPullStatus(String deviceIp, String imageRepository, String imageTag) {
         BaseImageOpsParam baseImageOpsParam = new BaseImageOpsParam();
         baseImageOpsParam.setImageRepository(imageRepository);
         baseImageOpsParam.setImageTag(imageTag);
         baseImageOpsParam.setAsync(false);
         URI deviceHost = builderHost(deviceIp);
         return imageFeignClient.getPullStatus(deviceHost, baseImageOpsParam).getCode();
     }
 
     public void pull(String deviceIp, String imageRepository, String imageTag, boolean async) {
         BaseImageOpsParam baseImageOpsParam = new BaseImageOpsParam();
         baseImageOpsParam.setImageRepository(imageRepository);
         baseImageOpsParam.setImageTag(imageTag);
         baseImageOpsParam.setAsync(async);
         URI deviceHost = builderHost(deviceIp);
         BackendFeignUtils.versify(imageFeignClient.pull(deviceHost, baseImageOpsParam));
     }
 
     public ImageManager(ImageFeignClient imageFeignClient) {
         this.imageFeignClient = imageFeignClient;
     }
 
 
 }