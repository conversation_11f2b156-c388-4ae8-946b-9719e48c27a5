 package net.armcloud.paascenter.cms.model.request;
 
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class InstanceRestartRequest {
     @Valid
     @NotNull(message = "instances cannot null")
     @Size(min = 1, message = "instances cannot null")
     private List<Instance> instances;
 
     @Data
     public static class Instance{
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         @NotBlank(message = "padCode cannot null")
         private String padCode;

         //集群code
         private String clusterCode;
         /**
          * 网存Id
          */
         private String netStorageResId;

         @ApiModelProperty(value = "是否强制删除（会强制删除实例的全部数据，不做任何备份。）")
         private Boolean forceDelete = false;
     }
 
 }