 package net.armcloud.paascenter.cms.mapper;
 
 
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.Collection;
 import java.util.Date;
 import java.util.List;
 
 @Mapper
 public interface DeviceInstanceTaskMapper {
     int insert(DeviceInstanceTask record);
 
     int batchInsert(List<DeviceInstanceTask> list);
 
     List<String> listDeviceIpWithRunningTask(@Param("dcId") long dcId);
 
     List<DeviceInstanceTask> listWaitRunningTask(@Param("dcId") long dcId,
                                                  @Param("excludeDeviceIps") Collection<String> excludeDeviceIps,
                                                  @Param("handlerSize") int handlerSize);
 
     DeviceInstanceTask getById(@Param("subTaskId") long subTaskId);
 
     int updateStatusById(@Param("id") long id, @Param("status") int status, @Param("startTime") Date startTime,
                           @Param("endTime") Date endTime, @Param("msg") String msg, @Param("originStatus") Integer originStatus);
 
     void batchUpdateStatusById(@Param("ids") List<Long> ids, @Param("status") int status, @Param("startTime") Date startTime,
                                @Param("endTime") Date endTime, @Param("timeoutTime") Date timeoutTime, @Param("msg") String msg);
 
     List<DeviceInstanceTask> listWaitVerifyResultDeviceTask(Integer typeValue);
 
     List<DeviceInstanceTask> listByMasterTaskId(@Param("masterTaskId") long masterTaskId);
 
     void updateByMasterTaskId(@Param("masterTaskId") long masterTaskId, @Param("status") int status,
                               @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                               @Param("timeoutTime") Date timeoutTime, @Param("msg") String msg);
 
     List<DeviceInstanceTask> listTimeout(@Param("dcId") long dcId);
 
     DeviceInstanceTask getLatestCreateTask(@Param("dcId") long dcId, @Param("instanceName") String instanceName);
 }