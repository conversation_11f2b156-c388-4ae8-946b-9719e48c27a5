 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class DeviceIpsRequest {
     @NotNull(message = "deviceIps cannot null")
     @Size(min = 1, message = "deviceIps cannot null")
     private List<String> deviceIps;

     /**macVlan*/
     private String networkDeviceName;
 }