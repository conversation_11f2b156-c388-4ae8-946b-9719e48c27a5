 package net.armcloud.paascenter.cms.service.handler.device;
 
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.utils.DCUtils;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.List;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.TIMEOUT;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.Scheduled.SCHEDULED_HANDLER_DEVICE_TIMEOUT_TASK_LOCK_KEY;
 
 @Slf4j
 @Service
 public class DeviceTaskTimeoutHandler extends AbstractDeviceTaskHandler {
     private final RedissonDistributedLock redissonDistributedLock;
     private final CmsDeviceTaskMapper cmsDeviceTaskMapper;
 
     public void start() {
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start device task timeout handler....", currentThreadId);
 
         if (redissonDistributedLock.isHeldByCurrentThread(SCHEDULED_HANDLER_DEVICE_TIMEOUT_TASK_LOCK_KEY)) {
             log.debug("thread {} lock {} is held by current thread skip....", currentThreadId, SCHEDULED_HANDLER_DEVICE_TIMEOUT_TASK_LOCK_KEY);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(SCHEDULED_HANDLER_DEVICE_TIMEOUT_TASK_LOCK_KEY, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} not get lock {} skip....", currentThreadId, SCHEDULED_HANDLER_DEVICE_TIMEOUT_TASK_LOCK_KEY);
             return;
         }
 
         try {
             handlerDeviceTask();
         } catch (Exception e) {
             log.debug("thread {} start device task timeout handler error", currentThreadId, e);
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void handlerDeviceTask() {
         List<DeviceTask> deviceTasks = cmsDeviceTaskMapper.listTimeout(DCUtils.getDcId());
         if (CollectionUtils.isEmpty(deviceTasks)) {
             return;
         }
 
         deviceTasks.forEach(deviceTask -> super.updateStatus(deviceTask, TIMEOUT, "任务执行时间超过最大等待时间", EXECUTING.getStatus()));
     }
 
     public DeviceTaskTimeoutHandler(CmsTaskManager cmsTaskManager, DeviceInstanceTaskMapper deviceInstanceTaskMapper,
                                     RedissonDistributedLock redissonDistributedLock, DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper,
                                     DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                     InstanceDetailImageSuccService instanceDetailImageSuccService, TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, defaultRocketMqProducerWrapper, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.redissonDistributedLock = redissonDistributedLock;
         this.cmsDeviceTaskMapper = cmsDeviceTaskMapper;
     }
 }