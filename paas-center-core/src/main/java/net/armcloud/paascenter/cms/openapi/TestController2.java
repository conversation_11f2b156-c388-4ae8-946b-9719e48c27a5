 package net.armcloud.paascenter.cms.openapi;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.cms.model.dto.ProxyIpDTO;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.web.bind.annotation.RequestBody;
 import org.springframework.web.bind.annotation.RequestMapping;
 import org.springframework.web.bind.annotation.RequestMethod;
 import org.springframework.web.bind.annotation.RestController;
 
 import java.io.BufferedReader;
 import java.io.InputStreamReader;
 import java.net.*;
 
 /**
  * <AUTHOR>
  */
 @Slf4j
 @RestController
 public class TestController2 {
 
     @RequestMapping(value = "/proxyIp", method = RequestMethod.POST)
     public Result<?> proxyIp(@RequestBody ProxyIpDTO proxyIpDTO) {
         // 测试代理
         String testUrl = "http://api.ipify.org";
         Proxy.Type proxyType = null;
         if ("http".equals(proxyIpDTO.getProxyType())){
             proxyType = Proxy.Type.HTTP;
         } else if ("socks5".equals(proxyIpDTO.getProxyType())) {
             proxyType = Proxy.Type.SOCKS;
         }
         String result = testProxyWithExternalIP(proxyIpDTO.getProxyHost(), proxyIpDTO.getProxyPort(), proxyIpDTO.getProxyUsername(), proxyIpDTO.getProxyPassword(), proxyType, testUrl);
         return Result.ok(result);
     }
 
     /**
      * 测试代理是否可用，并获取通过代理访问的出口 IP 地址
      *
      * @param proxyHost     代理 IP 地址
      * @param proxyPort     代理端口
      * @param username      代理账号
      * @param password      代理密码
      * @param proxyType     代理类型（HTTP 或 SOCKS）
      * @param testUrl       测试目标 URL（返回出口 IP 地址）
      * @return              测试结果，包含代理状态及出口 IP
      */
     private String testProxyWithExternalIP(String proxyHost, int proxyPort, String username, String password, Proxy.Type proxyType, String testUrl) {
         try {
             // 设置代理身份认证
             Authenticator.setDefault(new Authenticator() {
                 @Override
                 protected PasswordAuthentication getPasswordAuthentication() {
                     return new PasswordAuthentication(username, password.toCharArray());
                 }
             });
 
             // 配置代理
             Proxy proxy = new Proxy(proxyType, new InetSocketAddress(proxyHost, proxyPort));
 
             // 创建 URL 对象
             URL url = new URL(testUrl);
 
             // 打开连接并设置代理
             HttpURLConnection connection = (HttpURLConnection) url.openConnection(proxy);
 
             // 设置超时时间(连接超时时间)
             connection.setConnectTimeout(2000);
             // 设置超时时间(读取超时时间)
             connection.setReadTimeout(2000);
 
             // 发起请求
             connection.setRequestMethod("GET");
             int responseCode = connection.getResponseCode();
 
             // 如果返回 200，代理可用
             if (responseCode == 200) {
                 BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                 StringBuilder response = new StringBuilder();
                 String line;
                 while ((line = in.readLine()) != null) {
                     response.append(line);
                 }
                 in.close();
 
                 return "代理可用: " + proxyHost + ":" + proxyPort + " (" + proxyType + ")\n出口 IP: " + response.toString();
             } else {
                 return "代理连接失败，HTTP 响应码: " + responseCode;
             }
         } catch (Exception e) {
             // 捕获异常，表示代理不可用
             return "代理连接失败: " + e.getMessage() + "\n代理地址: " + proxyHost + ":" + proxyPort + " (" + proxyType + ")";
         }
     }
 }