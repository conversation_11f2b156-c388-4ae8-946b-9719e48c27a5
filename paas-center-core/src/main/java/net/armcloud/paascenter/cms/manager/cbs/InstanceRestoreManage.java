 package net.armcloud.paascenter.cms.manager.cbs;
 
 import net.armcloud.paascenter.cms.model.request.ContainerRestoreReqParam;
 import net.armcloud.paascenter.cms.model.request.GetContainerRestoreStatusReqParam;
 import net.armcloud.paascenter.cms.model.response.ContainerRestoreRespVO;
 import net.armcloud.paascenter.cms.model.response.GetContainerRestoreStatusRespVO;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.cms.config.MinIOConfig;
 import net.armcloud.paascenter.cms.manager.cbs.feign.ContainerRestoreFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import feign.RetryableException;
 import org.springframework.stereotype.Component;
 
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_CONNECT_EXCEPTION;
 import static net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils.builderHost;
 
 @Component
 public class InstanceRestoreManage {
     private final MinIOConfig minIOConfig;
     private final ContainerRestoreFeignClient containerRestoreFeignClient;
 
     public void restore(String deviceIp, String instanceName, String storagePath) {
         ContainerRestoreRespVO response;
         try {
             ContainerRestoreReqParam param = new ContainerRestoreReqParam();
             param.setContainerName(instanceName);
             param.setMinioEndpoint(minIOConfig.getEndpoint());
             param.setMinioAccessKey(minIOConfig.getAccessKey());
             param.setMinioSecretKey(minIOConfig.getSecretKey());
             param.setMinioBucket(minIOConfig.getBucket());
             param.setMinioStorageDirectory(storagePath);
             response = containerRestoreFeignClient.start(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(response);
     }
 
     public GetContainerRestoreStatusRespVO status(String deviceIp, String instanceName, String storagePath) {
         GetContainerRestoreStatusRespVO response;
         try {
             GetContainerRestoreStatusReqParam param = new GetContainerRestoreStatusReqParam();
             param.setContainerName(instanceName);
             param.setMinioEndpoint(minIOConfig.getEndpoint());
             param.setMinioAccessKey(minIOConfig.getAccessKey());
             param.setMinioSecretKey(minIOConfig.getSecretKey());
             param.setMinioBucket(minIOConfig.getBucket());
             param.setMinioStorageDirectory(storagePath);
             response = containerRestoreFeignClient.status(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(response);
         return response;
     }
 
     public InstanceRestoreManage(MinIOConfig minIOConfig, ContainerRestoreFeignClient containerRestoreFeignClient) {
         this.minIOConfig = minIOConfig;
         this.containerRestoreFeignClient = containerRestoreFeignClient;
     }
 }