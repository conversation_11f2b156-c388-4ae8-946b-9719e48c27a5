 package net.armcloud.paascenter.cms.manager.cbs;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.cms.model.ContainerNetworkLimitParam;
 import net.armcloud.paascenter.cms.model.ContainerNetworkOpsParam;
 import net.armcloud.paascenter.cms.model.IBaseOpsVo;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.cms.manager.cbs.feign.NetworkFeignClient;
 import net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils;
 import feign.RetryableException;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Component;
 
 import java.net.URI;
 
 import static net.armcloud.paascenter.cms.exception.code.DeviceExceptionCode.DEVICE_CONNECT_EXCEPTION;
 import static net.armcloud.paascenter.cms.manager.cbs.utils.BackendFeignUtils.builderHost;
 
 @Slf4j
 @Component
 public class NetworkManager {
     private final NetworkFeignClient networkFeignClient;
 
     public void macVlanCreate(String deviceIp, String subnet, String ipRange, String gateway, String networkDeviceName) {
         ContainerNetworkOpsParam containerNetworkOpsParam = new ContainerNetworkOpsParam();
         containerNetworkOpsParam.setSubnet(subnet);
         containerNetworkOpsParam.setIpRange(ipRange);
         containerNetworkOpsParam.setGateway(gateway);
         containerNetworkOpsParam.setDeviceName(networkDeviceName);
         IBaseOpsVo iOpsVo;
         try {
             URI deviceHost = builderHost(deviceIp);
             String result = networkFeignClient.macVlanCreate(deviceHost, containerNetworkOpsParam);
             log.debug("macVlanCreate result:{} >>>", result);
             iOpsVo = JSON.parseObject(result, IBaseOpsVo.class);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
     }
 
     public void networkSpeedLimit(String deviceIp, String instanceName, String maxUplinkBandwidth, String maxDownlinkBandwidth) {
         ContainerNetworkLimitParam param = new ContainerNetworkLimitParam();
         param.setUpload(maxUplinkBandwidth);
         param.setDownload(maxDownlinkBandwidth);
         param.setName(instanceName);
         IBaseOpsVo iOpsVo;
         try {
             iOpsVo = networkFeignClient.networkSpeedLimit(builderHost(deviceIp), param);
         } catch (RetryableException e) {
             String error = deviceIp + DEVICE_CONNECT_EXCEPTION.getMsg() + e.getMessage();
             throw new BasicException(DEVICE_CONNECT_EXCEPTION.getStatus(), error);
         }
 
         BackendFeignUtils.versify(iOpsVo);
     }
 
     public NetworkManager(NetworkFeignClient networkFeignClient) {
         this.networkFeignClient = networkFeignClient;
     }
 }