 package net.armcloud.paascenter.cms.manager;
 
 import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
 import net.armcloud.paascenter.cms.mapper.ConfigurationMapper;
 import org.apache.commons.lang3.StringUtils;
 import org.springframework.stereotype.Component;
 
 import static net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants.INSTANCE_DEFAULT_DNS;
 
 @Component
 public class InstanceManager {
     private final ConfigurationMapper configurationMapper;
 
     public void setDefaultDNS(CreatePadBO createPadBO) {
         if (StringUtils.isNotBlank(createPadBO.getDns1()) || StringUtils.isNotBlank(createPadBO.getDns2())) {
             return;
         }
 
         String dns = configurationMapper.selectValueByKey(INSTANCE_DEFAULT_DNS);
         if (StringUtils.isBlank(dns)) {
             return;
         }
 
         String[] dnsArr = dns.split(",");
         if (dnsArr.length >= 1) {
             createPadBO.setDns1(dnsArr[0]);
         }
 
         if (dnsArr.length >= 2) {
             createPadBO.setDns2(dnsArr[1]);
         }
     }
 
     public InstanceManager(ConfigurationMapper configurationMapper) {
         this.configurationMapper = configurationMapper;
     }
 }