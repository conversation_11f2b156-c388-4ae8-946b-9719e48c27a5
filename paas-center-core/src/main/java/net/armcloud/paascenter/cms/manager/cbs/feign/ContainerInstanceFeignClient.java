 package net.armcloud.paascenter.cms.manager.cbs.feign;
 
 import net.armcloud.paascenter.cms.model.*;
 import net.armcloud.paascenter.cms.model.dto.BaseContainerOpsDTO;
 import feign.Request.Options;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;
 
 import java.net.URI;
 
 @FeignClient(name = "armcloud-container-backend", contextId = "armcloud-container-backend-instance", url = "placeholder" )
 public interface ContainerInstanceFeignClient {
 
     @PostMapping(value = "/container/lifecycle/status")
     ContainerResourceOpsVO listAll(URI host,Options options);
 
     @PostMapping(value = "/container/lifecycle/restartAll")
     ContainerResourceOpsVO restartAll(URI host);
 
     @PostMapping(value = "/container/lifecycle/destroyAll")
     ContainerResourceOpsVO destroyAll(URI deviceHost);
 
     @PostMapping(value = "/container/lifecycle/restart")
     ContainerResourceOpsVO restart(URI deviceHost, @RequestBody BaseContainerOpsParam param);
 
     @PostMapping(value = "/container/lifecycle/create")
     ContainerResourceOpsVO create(URI host, @RequestBody ContainerResourceOpsParam param);
 
     @PostMapping(value = "/container/lifecycle/reset")
     ContainerResourceOpsVO reset(URI deviceHost, @RequestBody BaseContainerOpsDTO param);
 
     @PostMapping(value = "/container/lifecycle/rm")
     ContainerResourceOpsVO remove(URI deviceHost, @RequestBody ContainerDeleteOpsParam param);
 
     @PostMapping(value = "/container/lifecycle/updateAndroidPropAndRestart")
     ContainerResourceOpsVO updateAndroidPropAndRestart(URI deviceHost, @RequestBody ContainerAndroidPropParam param);
 }