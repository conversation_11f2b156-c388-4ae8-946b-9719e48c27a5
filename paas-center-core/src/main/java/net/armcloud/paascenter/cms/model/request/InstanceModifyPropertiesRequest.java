 package net.armcloud.paascenter.cms.model.request;
 
 import lombok.Data;
 
 import javax.validation.Valid;
 import javax.validation.constraints.NotBlank;
 import javax.validation.constraints.NotNull;
 import javax.validation.constraints.Size;
 import java.util.List;
 
 @Data
 public class InstanceModifyPropertiesRequest {
     @Valid
     @NotNull(message = "instances cannot null")
     @Size(min = 1, message = "instances cannot null")
     private List<Instance> instances;
 
     @Data
     public static class Instance{
         @NotBlank(message = "deviceIp cannot null")
         private String deviceIp;
 
         @NotBlank(message = "padCode cannot null")
         private String padCode;
 
         /**
          * 安卓属性
          */
         private String deviceAndroidProps;
 
 
         /**
          * 屏幕宽
          */
         private Long screenWidth;
 
         /**
          * 高
          */
         private Long screenHigh;
 
         /**
          * 像素密度，dpi
          */
         private Long dpi;
 
         /**
          * 屏幕刷新率，fps
          */
         private Long fps;
 
         /**
          * dns信息
          */
         private String dns;

         private VirtualizeDeviceRequest.Device.Pad oldParam;
     }
 }