 package net.armcloud.paascenter.cms.service.handler.device.destroy;
 
 import com.alibaba.fastjson2.JSON;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask;
 import net.armcloud.paascenter.common.model.entity.container.DeviceTask;
 import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
 import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
 import net.armcloud.paascenter.cms.config.mq.DeviceTaskStatusMQConfig;
 import net.armcloud.paascenter.cms.manager.CmsTaskManager;
 import net.armcloud.paascenter.cms.manager.cbs.InstanceManager;
 import net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper;
 import net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper;
 import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
 import net.armcloud.paascenter.cms.service.handler.device.AbstractDeviceTaskHandler;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.commons.collections.CollectionUtils;
 import org.apache.commons.lang3.StringUtils;
 import org.redisson.api.RLock;
 import org.springframework.stereotype.Service;
 
 import java.util.List;
 import java.util.Objects;
 
 import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
 import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
 import static net.armcloud.paascenter.cms.constants.LockKeyConstants.ExecuteTask.EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX;
 
 @Slf4j
 @Service
 public class DeviceDestroyTaskExecuteHandler extends AbstractDeviceTaskHandler {
     private final InstanceManager instanceManager;
     private final RedissonDistributedLock redissonDistributedLock;
     private final DeviceInstanceTaskMapper deviceInstanceTaskMapper;
 
     public void start(DeviceTask deviceTask) {
         super.asyncExecute(() -> tryStart(deviceTask));
     }
 
     public void tryStart(DeviceTask deviceTask) {
         String dataJson = JSON.toJSONString(deviceTask);
         long currentThreadId = Thread.currentThread().getId();
         log.debug("thread {} start exec device destroy task task:{}", currentThreadId, dataJson);
 
         String key = EXECUTE_TASK_DEVICE_LOCK_KEY_PREFIX + deviceTask.getIp();
         if (redissonDistributedLock.isHeldByCurrentThread(key)) {
             log.debug("thread {} device destroy lock {} is held by current thread skip....", currentThreadId, key);
             return;
         }
 
         RLock lock = redissonDistributedLock.tryLock(key, 0, 300);
         if (Objects.isNull(lock)) {
             log.debug("thread {} exec device destroy task:{} not get lock key {}", currentThreadId, dataJson, key);
             return;
         }
 
         try {
             destroy(deviceTask);
         } catch (Exception e) {
             log.error("destroy error>>> task:{}", dataJson, e);
             throw new BasicException(PROCESSING_FAILED.getStatus(), e.getMessage());
         } finally {
             redissonDistributedLock.unlock(lock);
         }
     }
 
     private void destroy(DeviceTask deviceTask) {
         try {
             boolean success = super.updateStatus(deviceTask, EXECUTING, "", WAIT_EXECUTE.getStatus());
             if (!success) {
                 return;
             }
 
             String deviceIp = deviceTask.getIp();
             List<DeviceInstanceTask> deviceInstanceTasks = instanceManager.builderDeviceInstanceTask(deviceTask.getId(), deviceIp);
             if (CollectionUtils.isNotEmpty(deviceInstanceTasks)) {
                 deviceInstanceTaskMapper.batchInsert(deviceInstanceTasks);
             }
 
             String errorMsg = instanceManager.destroyAll(deviceIp);
             if (StringUtils.isNotBlank(errorMsg)) {
                 super.updateStatus(deviceTask, FAIL_ALL, errorMsg, EXECUTING.getStatus());
             }
 
         } catch (Exception e) {
             log.error("destroyDevice error >>> deviceTask:{}", JSON.toJSONString(deviceTask), e);
             super.updateStatus(deviceTask, FAIL_ALL, e.getMessage(), EXECUTING.getStatus());
         }
     }
 
 
     protected DeviceDestroyTaskExecuteHandler(DeviceInstanceTaskMapper deviceInstanceTaskMapper, CmsTaskManager cmsTaskManager,
                                               InstanceManager instanceManager, RedissonDistributedLock redissonDistributedLock,
                                               DefaultRocketMqProducerWrapper defaultRocketMqProducerWrapper, CmsDeviceTaskMapper cmsDeviceTaskMapper,
                                               DeviceTaskStatusMQConfig deviceTaskStatusMQConfig, InstanceDetailImageSuccService instanceDetailImageSuccService,
                                               TaskRelInstanceDetailMapper taskRelInstanceDetailMapper) {
         super(cmsTaskManager, deviceInstanceTaskMapper, defaultRocketMqProducerWrapper, cmsDeviceTaskMapper, deviceTaskStatusMQConfig,instanceDetailImageSuccService,taskRelInstanceDetailMapper);
         this.instanceManager = instanceManager;
         this.redissonDistributedLock = redissonDistributedLock;
         this.deviceInstanceTaskMapper = deviceInstanceTaskMapper;
     }
 }