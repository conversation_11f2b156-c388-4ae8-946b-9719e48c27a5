 package net.armcloud.paascenter.cms.model;
 
 import io.swagger.annotations.ApiModel;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
 
 @ApiModel("容器macvlan网络参数")
 @Data
 public class ContainerNetworkOpsParam  extends BaseContainerNetworkOpsParam{
 
     @ApiModelProperty("容器macvlan 网,eg(**********/16)")
     private String subnet;
 
     @ApiModelProperty("容器macvlan ip范围,eg(**********/17)")
     private String ipRange;
 
     @ApiModelProperty("容器macvlan网关,(**********)")
     private String gateway;
 
     @ApiModelProperty("容器macvlan deviceName(eth0)")
     private String deviceName;
 
 
 }