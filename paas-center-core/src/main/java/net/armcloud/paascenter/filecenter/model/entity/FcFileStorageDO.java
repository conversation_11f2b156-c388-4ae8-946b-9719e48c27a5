package net.armcloud.paascenter.filecenter.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 文件存储信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Getter
@Setter
@TableName("fc_file_storage")
public class FcFileStorageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件MD5哈希值,用于去重
     */
    private String fileMd5;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件扩展名
     */
    private String fileExt;

    /**
     * OSS上传状态
     */
    private String uploadStatus;

    /**
     * OSS存储路径
     */
    private String storagePath;

    /**
     * 公共访问URL
     */
    private String publicUrl;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 私有访问URL
     */
    private String privateUrl;
}
