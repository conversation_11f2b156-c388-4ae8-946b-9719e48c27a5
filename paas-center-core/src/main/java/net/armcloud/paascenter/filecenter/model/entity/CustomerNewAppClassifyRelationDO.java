package net.armcloud.paascenter.filecenter.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户应用分类应用关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Getter
@Setter
@TableName("customer_new_app_classify_relation")
public class CustomerNewAppClassifyRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long customerId;

    /**
     * 分类id
     */
    private Long newAppClassifyId;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 应用id
     */
    private Long appId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
