package net.armcloud.paascenter.filecenter.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import net.armcloud.paascenter.common.model.dto.api.TaskDetailsInfoDTO;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;
import net.armcloud.paascenter.common.model.vo.api.FileTaskInfoVo;

@Mapper
public interface FileUploadTaskMapper extends BaseMapper<FileUploadTask> {
    List<FileUploadTask> listByMasterTaskIdAndUniqueIds(@Param("masterTaskId") long masterTaskId, @Param("uniqueIds") List<String> uniqueIds);

    int updateNotEndFileStatus(@Param("id") long id, @Param("status") Integer status, @Param("endDate") Date endDate ,@Param("msg") String msg);

    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    List<FileUploadTask> listByTaskId(@Param("taskId") long taskId);

    int update(FileUploadTask fileUploadTask);

    FileUploadTask getById(@Param("id") long id);

    List<FileUploadTask> listWaitDownloadTaskByFileCustomerId(@Param("fileCustomerIds") List<Long> fileCustomerIds);

    int cancel(@Param("ids") List<Long> ids);

    List<FileTaskInfoVo> listVOByMasterUniqueId(@Param("taskBatchId") String taskBatchId);

    List<FileTaskInfoVo> listVOByCustomerTaskIdsAndCustomerId(TaskDetailsInfoDTO taskDetailsDTO);

} 
