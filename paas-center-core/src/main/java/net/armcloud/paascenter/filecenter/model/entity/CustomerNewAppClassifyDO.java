package net.armcloud.paascenter.filecenter.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户应用分类表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Getter
@Setter
@TableName("customer_new_app_classify")
public class CustomerNewAppClassifyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 应用数量
     */
    private Integer appNum;

    /**
     * 分类状态 是否启用(1：是；0：否) 默认1
     */
    private Integer enable;

    /**
     * 描述
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
