package net.armcloud.paascenter.filecenter.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.filecenter.config.ImageOssConfig;
import net.armcloud.paascenter.filecenter.utils.AliyunUploadInfoFetcher;
import net.armcloud.paascenter.filecenter.utils.AliyunUploadInfoFetcher.UploadInfo;
import net.armcloud.paascenter.openapi.utils.IdGenerateUtils;
import net.armcloud.paascenter.openapi.mapper.CustomerUploadImageMapper;

@Slf4j
@RestController
@RequestMapping("/openapi/filecenter")
public class AliyunImageController {
    
    @Autowired
    private ImageOssConfig imageOssConfig;

    private DefaultAcsClient createStsClient() {
        IClientProfile profile = DefaultProfile.getProfile(
                imageOssConfig.getRegion(),
                imageOssConfig.getAccessKey(),
                imageOssConfig.getSecretKey());
        return new DefaultAcsClient(profile);
    }

    @GetMapping("/image/oss/config")
    public Result<Map<String, String>> getImageOssConfig() {
        try {

            DefaultAcsClient stsClient = createStsClient();
            AssumeRoleRequest roleRequest = new AssumeRoleRequest();
            roleRequest.setRoleArn(imageOssConfig.getRoleArn());
            roleRequest.setRoleSessionName("upload-session");
            roleRequest.setDurationSeconds(43200L);

            AssumeRoleResponse roleResponse = stsClient.getAcsResponse(roleRequest);
            AssumeRoleResponse.Credentials credentials = roleResponse.getCredentials();

            Map<String, String> response = new HashMap<>();
            response.put("endpoint", imageOssConfig.getEndpoint());
            response.put("accessKeyId", credentials.getAccessKeyId());
            response.put("accessKeySecret", credentials.getAccessKeySecret());
            response.put("securityToken", credentials.getSecurityToken());
            response.put("bucketName", imageOssConfig.getBucketName());
            response.put("region", imageOssConfig.getRegion());
            response.put("expiration", String.valueOf(credentials.getExpiration()));

            return Result.ok(response);
        } catch (Exception e) {
            log.error("获取图片OSS配置失败: {}", e.getMessage(), e);
            return Result.fail(500, "获取图片OSS配置失败: " + e.getMessage());
        }
    }

    @Autowired
    private AliyunUploadInfoFetcher uploadInfoFetcher;

    @Autowired
    private CustomerUploadImageMapper customerUploadImageMapper;

    @GetMapping("/image/getToken")
    public Result<UploadInfo> getImageToken() {
        try {

            String repo = "armcloud/" + IdGenerateUtils.generateImageUploadUniqueId();

            if (customerUploadImageMapper.exists(new LambdaQueryWrapper<CustomerUploadImage>().eq(CustomerUploadImage::getUniqueId, repo))) {
                return Result.fail("当前获取镜像上传Token过于频繁，请稍后再试");
            }

            UploadInfo uploadInfo = uploadInfoFetcher.getUploadInfo(repo);
            return Result.ok(uploadInfo);
        } catch (Exception e) {
            log.error("获取镜像上传Token失败: {}", e.getMessage(), e);
            return Result.fail(500, "获取镜像上传Token失败: " + e.getMessage());
        }
    }
}
