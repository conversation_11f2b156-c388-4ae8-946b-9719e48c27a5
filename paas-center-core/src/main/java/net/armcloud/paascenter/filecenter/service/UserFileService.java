package net.armcloud.paascenter.filecenter.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;

import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.filecenter.model.dto.AppFileDetailDTO;
import net.armcloud.paascenter.filecenter.model.dto.AppFileListDTO;
import net.armcloud.paascenter.filecenter.model.dto.CacheAppDTO;
import net.armcloud.paascenter.filecenter.model.dto.CacheFileDTO;
import net.armcloud.paascenter.filecenter.model.dto.FileSaveDTO;
import net.armcloud.paascenter.filecenter.model.dto.FileStatusUpdateDTO;
import net.armcloud.paascenter.filecenter.model.dto.FileUploadRequestDTO;
import net.armcloud.paascenter.filecenter.model.dto.FileUploadResponseDTO;
import net.armcloud.paascenter.filecenter.model.dto.QueryFileListRequestDTO;

/**
 * User file service interface
 */
public interface UserFileService extends IService<UserFile> {
    
    /**
     * Initiate file upload
     *
     * @param request File upload request
     * @return File upload response with upload URL or existing file info
     */
    FileUploadResponseDTO initiateUpload(FileUploadRequestDTO request);
    
    
    /**
     * Check if file can be fast uploaded (already exists)
     *
     * @param request File upload request
     * @return true if file exists and can be fast uploaded, false otherwise
     */
    boolean isFastUpload(String fileMd5);
    
    /**
     * Get file storage ID by file MD5
     *
     * @param fileMd5 File MD5
     * @return file storage ID
     */
    Long getUploadedFileStorageIdByFileMd5(String fileMd5);

    /**
     * Update file status to valid
     *
     * @param fileId File ID
     * @param customerId Customer ID for permission check
     * @return true if updated successfully, false otherwise
     */
    List<String> batchSaveFile(List<FileSaveDTO> request);

    /**
     * Update file status to valid
     *
     * @param fileId File ID
     * @param customerId Customer ID for permission check
     * @return true if updated successfully, false otherwise
     */
    boolean updateFileStatus(FileStatusUpdateDTO request);

    /**
     * Batch delete files
     *
     * @param fileStorageIds List of file storage IDs to delete
     * @return true if all files were deleted successfully
     */
    List<String> batchDeleteFiles(List<String> fileIds);

    /**
     * Batch delete files
     *
     * @param fileStorageIds List of file storage IDs to delete
     * @return true if all files were deleted successfully
     */
    List<String> batchDeleteFilesByUniqueIds(List<String> fileUniqueIds);

    Page<Map<String, Object>> queryFileList(QueryFileListRequestDTO request);


    Page<Map<String, Object>> queryFileListForV1Api(QueryFileListRequestDTO request);


    /**
     * Cache file
     *
     * @param request Cache file request
     * @return true if cached successfully, false otherwise
     */
    Result<Map<String, Object>> cacheFile(CacheFileDTO request);

    /**
     * Cache app
     *
     * @param request Cache app request
     * @return true if cached successfully, false otherwise
     */
    Result<List<Map<String, Object>>> cacheApp(CacheAppDTO request);

    /**
     * App detail
     *
     * @param request App detail request
     * @return App detail
     */
    Map<String, Object> appDetail(AppFileDetailDTO request);

    /**
     * App list
     *
     * @param request App list request
     * @return App list
     */
    Page<Map<String, Object>> appList(AppFileListDTO request);


    /**
     * 
     * @param request
     * @return
     */
    Result<Object> aliyunfcCallback1(String requestBody) throws Exception;

} 