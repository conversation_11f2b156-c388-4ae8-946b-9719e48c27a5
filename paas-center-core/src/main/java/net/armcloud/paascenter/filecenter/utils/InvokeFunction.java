// This file is auto-generated, don't edit it. Thanks.
package net.armcloud.paascenter.filecenter.utils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.core.http.HttpHeaders;
import com.aliyun.sdk.service.fc20230330.AsyncClient;
import com.aliyun.sdk.service.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.sdk.service.fc20230330.models.InvokeFunctionResponse;
import com.google.gson.Gson;

import darabonba.core.RequestConfiguration;
import darabonba.core.client.ClientOverrideConfiguration;

public class InvokeFunction {



        public static void main(String[] args) throws Exception {

                // HttpClient Configuration
                /*
                 * HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
                 * .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout
                 * time, the default is 10 seconds
                 * .responseTimeout(Duration.ofSeconds(10)) // Set the response timeout time,
                 * the default is 20 seconds
                 * .maxConnections(128) // Set the connection pool size
                 * .maxIdleTimeOut(Duration.ofSeconds(50)) // Set the connection pool timeout,
                 * the default is 30 seconds
                 * // Configure the proxy
                 * .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new
                 * InetSocketAddress("<your-proxy-hostname>", 9001))
                 * .setCredentials("<your-proxy-username>", "<your-proxy-password>"))
                 * // If it is an https connection, you need to configure the certificate, or
                 * ignore the certificate(.ignoreSSL(true))
                 * .x509TrustManagers(new X509TrustManager[]{})
                 * .keyManagers(new KeyManager[]{})
                 * .ignoreSSL(false)
                 * .build();
                 */

                // Configure Credentials authentication information, including ak, secret, token
                StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                                // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and
                                // ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                                .accessKeyId("LTAI5t6ZtSJzCmuFtCNcRDk7")
                                .accessKeySecret("******************************")
                                // .securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS
                                // token
                                .build());

                // Configure the Client
                AsyncClient client = AsyncClient.builder()
                                .region("cn-hongkong") // Region ID
                                // .httpClient(httpClient) // Use the configured HttpClient, otherwise use the
                                // default HttpClient (Apache HttpClient)
                                .credentialsProvider(provider)
                                // .serviceConfiguration(Configuration.create()) // Service-level configuration
                                // Client-level configuration rewrite, can set Endpoint, Http request
                                // parameters, etc.
                                .overrideConfiguration(
                                                ClientOverrideConfiguration.create()
                                                                // Endpoint 请参考 https://api.aliyun.com/product/FC
                                                                .setEndpointOverride(
                                                                                "1191921788579688.cn-hongkong.fc.aliyuncs.com")
                                // .setConnectTimeout(Duration.ofSeconds(30))
                                )
                                .build();

                // Parameter settings for API request
                InputStream bodyStream = new ByteArrayInputStream("{\"key\":\"value\"}".getBytes("UTF-8"));
                // Create HTTP headers with async delay of 1 second
                HttpHeaders headers = new HttpHeaders();
                headers.set("x-fc-async-delay", "1");
                
                InvokeFunctionRequest invokeFunctionRequest = InvokeFunctionRequest.builder()
                                .xFcInvocationType("Async")
                                .xFcLogType("None")
                                .qualifier("LATEST")
                                .functionName("func-efbgebi1")
                                .body(bodyStream)
                                // Request-level configuration rewrite with headers including async delay
                                .requestConfiguration(RequestConfiguration.create().setHttpHeaders(headers))
                                .build();

                // Asynchronously get the return value of the API request
                CompletableFuture<InvokeFunctionResponse> response = client.invokeFunction(invokeFunctionRequest);
                // Synchronously get the return value of the API request
                InvokeFunctionResponse resp = response.get();
                System.out.println(new Gson().toJson(resp));
                // Asynchronous processing of return values
                /*
                 * response.thenAccept(resp -> {
                 * System.out.println(new Gson().toJson(resp));
                 * }).exceptionally(throwable -> { // Handling exceptions
                 * System.out.println(throwable.getMessage());
                 * return null;
                 * });
                 */

                // Finally, close the client
                client.close();
        }

}
