package net.armcloud.paascenter.filecenter.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

@Data
@Component
@ConfigurationProperties(prefix = "paas.filecenter")
public class FileCenterConfig {

    // 单用户同一时间最大上传文件数量
    private int maxUploadFileCount = 30;

    // 文件上传任务超时时间, 默认一个小时， 单位秒
    private int fileUploadTaskTimeout = 60 * 60;



}