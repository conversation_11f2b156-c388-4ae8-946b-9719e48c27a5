package net.armcloud.paascenter.filecenter.controller;

import static net.armcloud.paascenter.common.utils.http.RequestUtils.getCurrentRequest;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.filecenter.model.dto.QueryFileListRequestDTO;
import net.armcloud.paascenter.filecenter.service.UserFileService;

/**
 * 文件中心API控制器
 */
@Slf4j
@RestController
@RequestMapping("/openapi/filecenter")
public class FileCenterQueryController {

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserFileService userFileService;

    // 增加阿里云oss 上传回调接口
    @RequestMapping("/list")
    public Result<Page<Map<String, Object>>> listFile(@RequestBody QueryFileListRequestDTO request) {
        log.info("查询文件列表: {}", request);

        Long customerId = CustomerUtils.getAndVerifyUserId(getCurrentRequest());
        // 如果当前用户不是管理员，则设置客户ID
        if (!redisService.isAdmin(customerId)) {
            request.setCustomerId(customerId);
        }
        // 查询
        return Result.ok(userFileService.queryFileList(request));
    }

    
}