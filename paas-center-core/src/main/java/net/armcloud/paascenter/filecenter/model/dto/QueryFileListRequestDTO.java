package net.armcloud.paascenter.filecenter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

@Data
public class QueryFileListRequestDTO extends PageDTO {

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "包名")
    private String pkgName;

    @ApiModelProperty(value = "文件名")
    private String fileName;
    
    @ApiModelProperty(value = "文件类型", required = true)
    private String fileType;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;
}
