package net.armcloud.paascenter.filecenter.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;

import lombok.Data;
import net.armcloud.paascenter.filecenter.config.ImageOssConfig;

@Component
public class AliyunUploadInfoFetcher {
    // private String REPO = "armcloud/img-25021828327";
    private String TAG = "latest";

    @Autowired
    private ImageOssConfig imageOssConfig;

    


    public UploadInfo getUploadInfo(String repo) throws Exception {
        HttpURLConnection conn = (HttpURLConnection) new URL(imageOssConfig.getRegistryUrl() + "/v2/").openConnection();
        conn.setRequestMethod("GET");
        
        String cookie = conn.getHeaderField("Set-Cookie");
        String wwwAuthenticate = conn.getHeaderField("Www-Authenticate");
        
        Pattern pattern = Pattern.compile("realm=\"([^\"]+)\",service=\"([^\"]+)\"");
        Matcher matcher = pattern.matcher(wwwAuthenticate);
        
        if (matcher.find()) {
            String authUrl = matcher.group(1);
            String service = matcher.group(2);
            String tokenUrl = authUrl + "?service=" + service + "&scope=repository:" + repo + ":push,pull";
            
            HttpURLConnection tokenConn = (HttpURLConnection) new URL(tokenUrl).openConnection();
            tokenConn.setRequestMethod("GET");

            // 设置超时时间
            tokenConn.setConnectTimeout(5000); // 连接超时 5 秒
            tokenConn.setReadTimeout(10000);   // 读取超时 10 秒

            String authHeader = "Basic " + Base64.getEncoder().encodeToString((imageOssConfig.getRegistryUsername() + ":" + imageOssConfig.getRegistryPassword()).getBytes());
            tokenConn.setRequestProperty("Authorization", authHeader);
            if (cookie != null) {
                tokenConn.setRequestProperty("Cookie", cookie);
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(tokenConn.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            JSONObject jsonResponse = JSONObject.parseObject(response.toString());
            String token = jsonResponse.getString("token");
            String newCookie = tokenConn.getHeaderField("Set-Cookie");
            
            return new UploadInfo(imageOssConfig.getRegistryProvider(), imageOssConfig.getRegistryUrl(), token, newCookie, repo, TAG);
        } else {
            throw new Exception("无法解析认证信息");
        }
    }
    
    @Data
    public static class UploadInfo {
        String provider;
        String registryUrl;
        String token;
        String cookie;
        String repo;
        String tag;
        
        public UploadInfo(String provider, String registryUrl, String token, String cookie, String repo, String tag) {
            this.provider = provider;
            this.registryUrl = registryUrl;
            this.token = token;
            this.cookie = cookie;
            this.repo = repo;
            this.tag = tag;
        }
    }
}
