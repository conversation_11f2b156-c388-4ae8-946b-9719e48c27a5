package net.armcloud.paascenter.filecenter.controller;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.filecenter.FileStorage;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.filecenter.model.dto.*;
import net.armcloud.paascenter.filecenter.model.vo.AppParseInfoVO;
import net.armcloud.paascenter.filecenter.service.AppParseService;
import net.armcloud.paascenter.filecenter.service.FileStorageService;
import net.armcloud.paascenter.filecenter.service.UserFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static net.armcloud.paascenter.common.utils.http.RequestUtils.getCurrentRequest;

/**
 * 文件中心API控制器
 */
@Slf4j
@RestController
@RequestMapping("/openapi/filecenter")
public class FileCenterController {

    @Autowired
    private UserFileService userFileService;

    @Autowired
    private AppParseService appParseService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private RedisService redisService;

    /**
     * 秒传检测接口
     * 
     * @param fileMd5
     * @return
     */
    @GetMapping("/upload/check")
    public ResponseEntity<Map<String, Object>> checkUpload(@RequestParam("md5") String fileMd5) {
        try {
            FileStorage fileStorage = fileStorageService.findByMd5(fileMd5, "success");

            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("msg", "success");
            Map<String, Object> data = new HashMap<>();
            if(fileStorage == null){
                data.put("fileStorageId", null);
                data.put("storagePath", null);
                data.put("publicUrl", null);
            }else{
                data.put("fileStorageId", fileStorage.getId());
                data.put("storagePath", fileStorage.getStoragePath());
                data.put("publicUrl", fileStorage.getPublicUrl());
            }
            result.put("data", data);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("秒传检测失败: {}", e.getMessage(), e);

            Map<String, Object> result = new HashMap<>();
            result.put("code", 500);
            result.put("msg", "秒传检测失败: " + e.getMessage());
            result.put("data", null);

            return ResponseEntity.ok(result);
        }
    }

    /**
     * 初始化文件上传
     */
    @PostMapping("/upload/init")
    public Result<FileUploadResponseDTO> initiateUpload(@Valid @RequestBody FileUploadRequestDTO request) {
        try {
            FileUploadResponseDTO response = userFileService.initiateUpload(request);

            return Result.ok(response);
        } catch (Exception e) {
            log.error("初始化文件上传失败: {}", e.getMessage(), e);
            return Result.fail(500, "初始化文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 用户批量保存文件
     */
    @PostMapping("/upload/save/batch")
    public Result<List<String>> batchSaveFile(@Valid @RequestBody List<FileSaveDTO> request) {
        try {
            List<String> fileUniqueIds = userFileService.batchSaveFile(request);
            if (fileUniqueIds != null) {
                return Result.ok(fileUniqueIds);
            } else {
                return Result.fail(400, "更新文件状态失败");
            }
        } catch (Exception e) {
            log.error("更新文件状态失败: {}", e.getMessage(), e);

            return Result.fail(500, "更新文件状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件, 只能删除自己上传的文件
     * 
     * @param fileIds 文件唯一标识列表
     * @return 删除结果
     */
    @PostMapping("/delete/batch")
    public Result<List<String>> batchDeleteFiles(@RequestBody List<String> fileIds) {
        try {
            if (fileIds == null || fileIds.isEmpty()) {
                return Result.fail(400, "文件ID列表不能为空");
            }
            List<String> deletedFileIds = userFileService.batchDeleteFiles(fileIds);
            if (deletedFileIds != null && !deletedFileIds.isEmpty()) {
                return Result.ok(deletedFileIds);
            } else {
                return Result.fail(400, "批量删除文件失败");
            }
        } catch (Exception e) {
            log.error("批量删除文件失败: {}", e.getMessage(), e);
            return Result.fail(500, "批量删除文件失败: " + e.getMessage());
        }
    }

    /**
     * 根据fileStorageId获取App解析信息, 只需要AppFile表中的信息就行, 不需要返回id
     * 
     * @param fileStorageId
     * @return
     */
    @GetMapping("/app/parse-info")
    public Result<AppParseInfoVO> getAppParseInfo(@RequestParam("fileStorageId") Long fileStorageId) {
        try {
            AppParseInfoVO appParseInfo = appParseService.getAppParseInfo(fileStorageId);
            return Result.ok(appParseInfo);
        } catch (Exception e) {
            log.error("获取App解析信息失败: {}", e.getMessage(), e);
            return Result.fail(500, "获取App解析信息失败: " + e.getMessage());
        }
    }

    /**
     * 上报APP解析结果
     */
    @PostMapping("/app/parse-result")
    public Result<Object> reportAppParseResult(@Valid @RequestBody AppParseResultDTO request) {
        try {
            Long currenUid = CustomerUtils.getAndVerifyUserId(getCurrentRequest());
            if (!redisService.isAdmin(currenUid)) {
                request.setCustomerId(currenUid);
            }
            if (null == request.getCustomerId()) {
                request.setCustomerId(currenUid);
            }

            boolean success = appParseService.reportAppParseResult(request);

            if (success) {
                return Result.ok();
            } else {
                return Result.fail(400, "上报APP解析结果失败");
            }

        } catch (Exception e) {
            log.error("上报APP解析结果失败: {}", e.getMessage(), e);
            return Result.fail(500, "上报APP解析结果失败: " + e.getMessage());
        }
    }


    /**
     * 更新自定义应用信息
     */
    @PostMapping("/app/update-info")
    public Result<Boolean> updateInfo(@Valid @RequestBody AppUpdateInfoDTO dto) {
        Long currenUid = CustomerUtils.getAndVerifyUserId(getCurrentRequest());
        if (!currenUid.equals(dto.getCustomerId())) {
            throw new BasicException("只能编辑自己创建的应用信息");
        }
        appParseService.updateInfo(dto);
        return Result.ok();
    }


}