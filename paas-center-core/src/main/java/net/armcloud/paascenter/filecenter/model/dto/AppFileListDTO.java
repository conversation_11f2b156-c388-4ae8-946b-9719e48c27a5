package net.armcloud.paascenter.filecenter.model.dto;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppFileListDTO{
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @Size(max = 100, message = "apps长度不能超过100之间")
    @ApiModelProperty(value = "应用列表")
    private List<Long> appIds;

    @Min(value = 1, message = "offset最小值为1")
    @ApiModelProperty(value = "起始页,默认1")
    @NotNull(message = "page cannot null")
    private Integer page = 1;

    @Max(value = 100, message = "count最大值为100")
    @ApiModelProperty(value = "查询数量,默认10")
    @NotNull(message = "page cannot null")
    private Integer rows = 10;


    private String packageName;
}
