package net.armcloud.paascenter.filecenter.model.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.common.model.dto.PageDTO;

@Data
@EqualsAndHashCode(callSuper = true)
public class FileListDTO extends PageDTO {
    @ApiModelProperty(hidden = true)
    private Long customerId;


    /**
     * 包名
     */
    private String packageName;

    @ApiModelProperty(hidden = false)
    private List<Long> fileIdList;

    /**
     * 文件名
     */
    private String fileName;
}
