package net.armcloud.paascenter.filecenter.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * APP文件详细信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Getter
@Setter
@TableName("fc_app_files")
public class FcAppFilesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联file_storage表的ID
     */
    private Long fileStorageId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * Android包名
     */
    private String packageName;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 签名哈希值
     */
    private String signatureHash;

    /**
     * 开发者名称
     */
    private String developerName;

    /**
     * 应用图标存储路径
     */
    private String iconPath;

    /**
     * 应用图标公共访问URL
     */
    private String iconPublicUrl;

    /**
     * 支持的ABI架构,多个用逗号分隔
     */
    private String supportedAbis;

    /**
     * 最小安卓版本
     */
    private Integer minSdkVersion;

    /**
     * 目标安卓版本
     */
    private Integer targetSdkVersion;

    /**
     * APP安装包大小(字节)
     */
    private Long appSize;

    /**
     * 安装位置偏好
     */
    private String installLocation;

    /**
     * 是否系统应用
     */
    private Integer isSystemApp;

    /**
     * 是否调试应用
     */
    private Integer isDebugApp;

    /**
     * 主Activity类名
     */
    private String mainActivity;

    /**
     * 应用请求的权限,JSON格式
     */
    private String permissions;

    /**
     * 安装要求,JSON格式
     */
    private String installationRequirements;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 最后更新时间
     */
    private Date updatedTime;
}
