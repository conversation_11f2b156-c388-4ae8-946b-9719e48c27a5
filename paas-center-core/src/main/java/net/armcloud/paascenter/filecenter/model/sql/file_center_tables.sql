-- File Storage Table
CREATE TABLE fc_file_storage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_md5 VARCHAR(32) NOT NULL COMMENT '文件MD5哈希值,用于去重',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_ext VARCHAR(32) COMMENT '文件扩展名',
    upload_status ENUM('pending', 'uploading', 'success', 'failed') NOT NULL DEFAULT 'pending' COMMENT 'OSS上传状态',
    storage_path VARCHAR(1024) NOT NULL COMMENT 'OSS存储路径',
    public_url VARCHAR(1024) NOT NULL COMMENT '公共访问URL',
    private_url VARCHAR(1024) NOT NULL COMMENT '私有访问URL',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
		INDEX idx_upload_status (upload_status),
    UNIQUE KEY uk_file_md5 (file_md5)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件存储信息' AUTO_INCREMENT=1000000;


-- User Files Table
CREATE TABLE fc_user_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    file_storage_id BIGINT NOT NULL COMMENT '关联file_storage表的ID',
    file_unique_id VARCHAR(64) COMMENT '文件唯一标识',
    file_name VARCHAR(512) NOT NULL COMMENT '原始文件名',
    file_type ENUM('app', 'file') NOT NULL COMMENT '文件类型',
    file_status ENUM('invalid', 'valid') NOT NULL DEFAULT 'invalid' COMMENT '文件状态: invalid-无效, valid-有效',
    source_type ENUM('upload', 'url') NOT NULL DEFAULT 'upload' COMMENT '文件来源: upload-直接上传, url-URL下载',
    original_url VARCHAR(2048) COMMENT 'URL下载时的原始URL',
    file_comment VARCHAR(2048) COMMENT '文件备注',
    created_by BIGINT NOT NULL COMMENT '创建者用户ID',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '最后更新者用户ID',
    updated_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    INDEX idx_customer_id (customer_id),
    INDEX idx_file_storage_id (file_storage_id),
    INDEX idx_file_type (file_type),
    INDEX idx_file_unique_id (file_unique_id),
    INDEX idx_source_type (source_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户文件信息' AUTO_INCREMENT=1000000;

-- APP Files Table
CREATE TABLE fc_app_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_storage_id BIGINT NOT NULL COMMENT '关联file_storage表的ID',
    app_name VARCHAR(255) COMMENT '应用名称',
    package_name VARCHAR(255) COMMENT 'Android包名',
    version_name VARCHAR(100) COMMENT '版本名称',
    version_code INT COMMENT '版本号',
    signature_hash VARCHAR(256) COMMENT '签名哈希值',
    developer_name VARCHAR(255) COMMENT '开发者名称',
    icon_path VARCHAR(1024) COMMENT '应用图标存储路径',
    icon_public_url VARCHAR(1024) COMMENT '应用图标公共访问URL',
    supported_abis VARCHAR(255) COMMENT '支持的ABI架构,多个用逗号分隔',
    min_sdk_version INT COMMENT '最小安卓版本',
    target_sdk_version INT COMMENT '目标安卓版本',
    app_size BIGINT COMMENT 'APP安装包大小(字节)',
    install_location ENUM('auto', 'internalOnly', 'preferExternal') COMMENT '安装位置偏好',
    is_system_app TINYINT(1) DEFAULT 0 COMMENT '是否系统应用',
    is_debug_app TINYINT(1) DEFAULT 0 COMMENT '是否调试应用',
    main_activity VARCHAR(255) COMMENT '主Activity类名',
    permissions TEXT COMMENT '应用请求的权限,JSON格式',
    installation_requirements TEXT COMMENT '安装要求,JSON格式',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    INDEX idx_file_storage_id (file_storage_id),
    INDEX idx_package_version (package_name, version_code),
    INDEX idx_parse_status (parse_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP文件详细信息';

-- APP Parse Logs Table
CREATE TABLE fc_app_parse_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_storage_id BIGINT NOT NULL COMMENT '关联file_storage表的ID',
    parse_status ENUM('success', 'failed') NOT NULL COMMENT '解析状态',
    parse_content TEXT COMMENT '解析内容(JSON格式)',
    error_message TEXT COMMENT '错误信息',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_file_storage_id (file_storage_id) COMMENT '按文件查询解析记录',
    INDEX idx_created_time (created_time) COMMENT '按时间排序',
    INDEX idx_parse_status (parse_status) COMMENT '按状态查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP解析日志';

-- APP Parse Tasks Table
CREATE TABLE fc_app_parse_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_storage_id BIGINT NOT NULL COMMENT '关联file_storage表的ID',
    status ENUM('pending', 'processing', 'success', 'failed') NOT NULL COMMENT '解析状态',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    INDEX idx_file_storage_id (file_storage_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP解析任务';

-- FILE UPLOAD TASK TABLE
CREATE TABLE fc_file_upload_tasks (
    id bigint NOT NULL AUTO_INCREMENT,
    task_id bigint NOT NULL DEFAULT 0 COMMENT '任务表主键id',
    unique_id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '业务标识唯一id',
    file_type enum('app','file') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件类型',
    file_md5 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MD5哈希值,用于去重',
    file_size bigint NOT NULL COMMENT '文件大小(字节)',
    file_ext_info varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '文件扩展信息',
    file_storage_id bigint NOT NULL COMMENT '文件存储ID',
    file_id bigint NOT NULL COMMENT '用户文件ID',
    original_url varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始URL',
    customer_id bigint NOT NULL COMMENT '客户ID',
    status int NOT NULL DEFAULT 0 COMMENT '上传状态',
    delete_flag tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（1：已删除；0：未删除）',
    error_msg varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败错误消息',
    created_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    end_time timestamp NULL DEFAULT NULL,
    updated_time timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (id),
    INDEX idx_file_type (file_type),
    INDEX idx_status (status),
    UNIQUE INDEX UNI_ID (unique_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件上传任务' AUTO_INCREMENT=1000000;