package net.armcloud.paascenter.filecenter.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import net.armcloud.paascenter.common.model.entity.filecenter.AppFile;

/**
 * Mapper for fc_app_files table
 */
@Mapper
public interface AppFileMapper extends BaseMapper<AppFile> {
    
    /**
     * Find APP by package name and version code
     *
     * @param packageName Package name
     * @param versionCode Version code
     * @return AppFile or null if not found
     */
    AppFile findByPackageAndVersion(
            @Param("packageName") String packageName,
            @Param("versionCode") Integer versionCode
    );
} 