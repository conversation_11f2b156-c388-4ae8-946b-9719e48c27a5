package net.armcloud.paascenter.filecenter.service;

import net.armcloud.paascenter.common.model.entity.filecenter.FileStorage;

/**
 * File storage service interface
 */
public interface FileStorageService {
    
    /**
     * Find file storage by MD5
     *
     * @param fileMd5 File MD5 hash
     * @return FileStorage or null if not found
     */
    FileStorage findByMd5(String fileMd5, String uploadStatus);
    
    /**
     * Find file storage by ID
     * 
     * @param id File storage ID
     * @return FileStorage or null if not found
     */
    FileStorage findByStorageId(Long id);
    
    /**
     * Save or update file storage
     *
     * @param fileStorage File storage to save
     * @return Saved file storage with ID
     */
    FileStorage saveFileStorage(FileStorage fileStorage);
    
    /**
     * Delete file storage by ID
     *
     * @param id File storage ID to delete
     * @return true if deleted successfully, false otherwise
     */
    boolean deleteById(Long id);
}