package net.armcloud.paascenter.filecenter.model.vo;

import lombok.Data;

/**
 * APP parse information view object
 */
@Data
public class AppParseInfoVO {
    /**
     * File storage ID
     */
    private Long fileStorageId;

    /**
     * Application name
     */
    private String appName;
    private String appName2;

    /**
     * Android package name
     */
    private String packageName;
    
    /**
     * Version name
     */
    private String versionName;
    
    /**
     * Version code
     */
    private Integer versionCode;
    
    /**
     * Signature hash
     */
    private String signatureHash;
    
    /**
     * Developer name
     */
    private String developerName;
    
    /**
     * App icon public URL
     */
    private String iconPublicUrl;
    private String icon2PublicUrl;

    /**
     * Parse status
     */
    private String parseStatus;
    
    /**
     * Parse error message
     */
    private String parseError;
    
    /**
     * Supported ABIs
     */
    private String supportedAbis;
    
    /**
     * Minimum SDK version
     */
    private Integer minSdkVersion;
    
    /**
     * Target SDK version
     */
    private Integer targetSdkVersion;
    
    /**
     * APP size in bytes
     */
    private Long appSize;
    
    /**
     * Installation location
     */
    private String installLocation;
    
    /**
     * Is system app
     */
    private Boolean isSystemApp;
    
    /**
     * Is debug app
     */
    private Boolean isDebugApp;
    
    /**
     * Main activity
     */
    private String mainActivity;
    
    /**
     * Permissions
     */
    private String permissions;
    
    /**
     * Installation requirements
     */
    private String installationRequirements;
} 