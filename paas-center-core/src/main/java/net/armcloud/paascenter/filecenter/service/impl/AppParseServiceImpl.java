package net.armcloud.paascenter.filecenter.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.filecenter.*;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.filecenter.enums.ParseStatusEnum;
import net.armcloud.paascenter.filecenter.mapper.AppParseLogMapper;
import net.armcloud.paascenter.filecenter.mapper.AppParseTaskMapper;
import net.armcloud.paascenter.filecenter.mapper.UserAppFileMapper;
import net.armcloud.paascenter.filecenter.model.dto.AppParseResultDTO;
import net.armcloud.paascenter.filecenter.model.dto.AppUpdateInfoDTO;
import net.armcloud.paascenter.filecenter.model.vo.AppParseInfoVO;
import net.armcloud.paascenter.filecenter.service.AppParseService;
import net.armcloud.paascenter.filecenter.service.FileStorageService;
import net.armcloud.paascenter.filecenter.service.OssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Base64;
import java.util.Date;

import static net.armcloud.paascenter.common.utils.http.RequestUtils.getCurrentRequest;

/**
 * APP解析服务实现
 */
@Slf4j
@Service
public class AppParseServiceImpl implements AppParseService {

    @Autowired
    private net.armcloud.paascenter.filecenter.mapper.AppFileMapper appFileMapper;

    @Autowired
    private net.armcloud.paascenter.filecenter.mapper.UserFileMapper userFileMapper;

    @Autowired
    private AppParseLogMapper appParseLogMapper;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private OssService ossService;

    @Autowired
    private AppParseTaskMapper appParseTaskMapper;

    @Autowired
    private UserAppFileMapper userAppFileMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reportAppParseResult(AppParseResultDTO result) {
        try {
            log.info("reportAppParseResult: {}", JSON.toJSONString(result));

            String appName2 = result.getAppName2();
            String iconBase642 = result.getIconBase64_2();
            String iconBase64 = result.getIconBase64();
            Long customerId = result.getCustomerId();

            // 查询file_storage表
            FileStorage fileStorage = fileStorageService.findByStorageId(result.getFileStorageId());
            if (fileStorage == null) {
                log.error("文件不存在: {}", result.getFileStorageId());
                return false;
            }

            // 记录解析日志
            if (ParseStatusEnum.FAILED.getCode().equals(result.getParseStatus())) {
                recordParseLog(result.getFileStorageId(), "failed", null, result.getParseError());
                return true;
            } else {
                // APP 解析逻辑
                String parseContent = JSON.toJSONString(result);
                recordParseLog(result.getFileStorageId(), "success", parseContent, null);
            }

            // 如果解析成功，处理APP信息
            // 上传APP图标(如果有)
            String iconPath = null;
            String iconPublicUrl = null;
            if (StringUtils.hasText(iconBase64)) {
                if (iconBase64.contains("http://") || iconBase64.contains("https://")) {
                    iconPublicUrl = iconBase64;
                    iconBase64 = iconBase64.replaceAll("https://|http://", "");
                    iconPath = iconBase64.substring(iconBase64.indexOf("/") + 1);
                } else {
                    // base64解析
                    byte[] iconBytes = Base64.getDecoder().decode(iconBase64);
                    String iconMd5 = DigestUtils.md5DigestAsHex(iconBytes);
                    iconPath = ossService.getAppIconPath(result.getPackageName(), result.getVersionCode(), iconMd5);
                    iconPublicUrl = ossService.uploadBase64Image(iconPath, iconBase64, "image/png");
                }
            }

            String icon2Path = null;
            String icon2PublicUrl = null;
            if (StringUtils.hasText(iconBase642)) {
                if (iconBase642.contains("http://") || iconBase642.contains("https://")) {
                    icon2PublicUrl = iconBase642;
                    iconBase642 = iconBase642.replaceAll("https://|http://", "");
                    icon2Path = iconBase642.substring(iconBase642.indexOf("/") + 1);
                } else {
                    byte[] iconBytes = Base64.getDecoder().decode(iconBase642);
                    String iconMd5 = DigestUtils.md5DigestAsHex(iconBytes);
                    icon2Path = ossService.getAppIconPath(result.getPackageName(), result.getVersionCode(), iconMd5);
                    icon2PublicUrl = ossService.uploadBase64Image(icon2Path, iconBase642, "image/png");
                }
            }

            // 创建或更新APP文件记录
            AppFile appFile = getOrCreateAppFile(fileStorage.getId());
            appFile.setAppName(result.getAppName());
            appFile.setPackageName(result.getPackageName());
            appFile.setVersionName(result.getVersionName());
            appFile.setVersionCode(result.getVersionCode());
            appFile.setSignatureHash(result.getSignatureHash());
            appFile.setDeveloperName(result.getDeveloperName());
            appFile.setIconPath("/" + iconPath);
            appFile.setIconPublicUrl(iconPublicUrl);
            appFile.setSupportedAbis(result.getSupportedAbis());
            appFile.setMinSdkVersion(result.getMinSdkVersion());
            appFile.setTargetSdkVersion(result.getTargetSdkVersion());
            appFile.setAppSize(result.getAppSize());
            appFile.setMainActivity(result.getMainActivity());
            appFile.setIsSystemApp(result.getIsSystemApp());
            appFile.setIsDebugApp(result.getIsDebugApp());
            appFile.setPermissions(result.getPermissions());
            appFile.setInstallationRequirements(result.getInstallationRequirements());
            appFile.setUpdatedTime(new Date());

            if (appFile.getId() != null) {
                appFileMapper.updateById(appFile);
            } else {
                appFileMapper.insert(appFile);
            }

            if (StringUtils.hasText(appName2)) {
                UserAppFile userAppFile = new UserAppFile();
                userAppFile.setCustomerId(customerId);
                userAppFile.setFileUniqueId(result.getFileUniqueid());
                userAppFile.setAppName(appName2.trim());
                userAppFile.setIconPath(icon2Path);
                userAppFile.setIconPublicUrl(icon2PublicUrl);
                userAppFile.setCreatedTime(new Date());
                userAppFileMapper.insert(userAppFile);
                log.info("保存 userAppFile: {}", JSON.toJSONString(userAppFile));
            }

            return true;
        } catch (Exception e) {
            log.error("处理APP解析结果失败: {}", e.getMessage(), e);
            return false;
        }
    }

    public void updateAppFile(long fileStorageId, JSONObject appInfo) {
        try {
            recordParseLog(fileStorageId, "success", appInfo.toJSONString(), null);

            AppFile appFile = getOrCreateAppFile(fileStorageId);
            appFile.setAppName(appInfo.getString("app_name"));
            appFile.setPackageName(appInfo.getString("package_name"));
            appFile.setVersionName(appInfo.getString("version_name"));
            // 存在并且还得是数字
            if (appInfo.containsKey("version_code") && StringUtils.hasText(appInfo.getString("version_code"))) {
                try {
                    appFile.setVersionCode(Integer.parseInt(appInfo.getString("version_code")));
                } catch (Exception e) {
                    appFile.setVersionCode(1);
                    log.error("版本号格式错误: {}", appInfo.getString("version_code"), e);
                }
            } else { // 不存在默认为1
                appFile.setVersionCode(1);
            }
            appFile.setSignatureHash(appInfo.getString("signature_hash"));
            appFile.setDeveloperName(appInfo.getString("developer_name"));
            // 存在并且还得是数字
            if (appInfo.containsKey("min_sdk_version") && StringUtils.hasText(appInfo.getString("min_sdk_version"))) {
                appFile.setMinSdkVersion(Integer.parseInt(appInfo.getString("min_sdk_version")));
            }
            if (appInfo.containsKey("target_sdk_version") && StringUtils.hasText(appInfo.getString("target_sdk_version"))) {
                appFile.setTargetSdkVersion(Integer.parseInt(appInfo.getString("target_sdk_version")));
            }
            if (appInfo.containsKey("app_size")) {
                appFile.setAppSize(appInfo.getLong("app_size"));
            }
            if (appInfo.containsKey("supported_abis")) {
                appFile.setSupportedAbis(appInfo.getString("supported_abis"));
            }
            if (appInfo.containsKey("is_system_app")) {
                appFile.setIsSystemApp(appInfo.getBoolean("is_system_app"));
            }
            if (appInfo.containsKey("is_debug_app")) {
                appFile.setIsDebugApp(appInfo.getBoolean("is_debug_app"));
            }
            if (appInfo.containsKey("main_activity")) {
                appFile.setMainActivity(appInfo.getString("main_activity"));
            }
            if (appInfo.containsKey("permissions")) {
                appFile.setPermissions(appInfo.getString("permissions"));
            }
            if (appInfo.containsKey("installation_requirements")) {
                appFile.setInstallationRequirements(appInfo.getString("installation_requirements"));
            }
            if (appInfo.containsKey("install_location")) {
                appFile.setInstallLocation(appInfo.getString("install_location"));
            }
            appFile.setUpdatedTime(new Date());

            String iconOssPath = appInfo.getString("iconOssPath");
            if (StringUtils.hasText(iconOssPath)) {
                if (iconOssPath.startsWith("/")) {
                    iconOssPath = iconOssPath.substring(1);
                }
                appFile.setIconPath("/" + iconOssPath);
                appFile.setIconPublicUrl(ossService.getPublicUrl(iconOssPath));
            }

            if (appFile.getId() != null) {
                appFileMapper.updateById(appFile);
            } else {
                appFileMapper.insert(appFile);
            }
        } catch (Exception e) {
            log.error("处理APP解析结果失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取或创建APP文件记录
     */
    private AppFile getOrCreateAppFile(Long fileStorageId) {
        LambdaQueryWrapper<AppFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppFile::getFileStorageId, fileStorageId);

        AppFile appFile = appFileMapper.selectOne(wrapper);
        if (appFile == null) {
            appFile = new AppFile();
            appFile.setFileStorageId(fileStorageId);
            appFile.setCreatedTime(new Date());
        }

        return appFile;
    }

    @Override
    public AppParseInfoVO getAppParseInfo(Long fileStorageId) {
        AppFile appFile = appFileMapper.selectOne(
                new LambdaQueryWrapper<AppFile>()
                        .eq(AppFile::getFileStorageId, fileStorageId)
        );

        if (appFile == null) {
            return null;
        }

        // 转换为 VO
        AppParseInfoVO vo = new AppParseInfoVO();
        vo.setFileStorageId(appFile.getFileStorageId());
        vo.setAppName(appFile.getAppName());
        vo.setPackageName(appFile.getPackageName());
        vo.setVersionName(appFile.getVersionName());
        vo.setVersionCode(appFile.getVersionCode());
        vo.setSignatureHash(appFile.getSignatureHash());
        vo.setDeveloperName(appFile.getDeveloperName());
        vo.setIconPublicUrl(appFile.getIconPublicUrl());
        vo.setSupportedAbis(appFile.getSupportedAbis());
        vo.setMinSdkVersion(appFile.getMinSdkVersion());
        vo.setTargetSdkVersion(appFile.getTargetSdkVersion());
        vo.setAppSize(appFile.getAppSize());
        vo.setInstallLocation(appFile.getInstallLocation());
        vo.setIsSystemApp(appFile.getIsSystemApp());
        vo.setIsDebugApp(appFile.getIsDebugApp());
        vo.setMainActivity(appFile.getMainActivity());
        vo.setPermissions(appFile.getPermissions());
        vo.setInstallationRequirements(appFile.getInstallationRequirements());

        Long currenUid = CustomerUtils.getAndVerifyUserId(getCurrentRequest());
        UserAppFile userAppFile = userAppFileMapper.getByAppFileIdAndCustomerId(appFile.getId(), currenUid);
        if (userAppFile != null) {
            vo.setAppName2(userAppFile.getAppName());
            vo.setIcon2PublicUrl(userAppFile.getIconPublicUrl());
        }

        return vo;
    }

    @Override
    public void recordParseLog(Long fileStorageId, String parseStatus, String parseContent, String errorMessage) {
        try {
            AppParseLog log = new AppParseLog();
            log.setFileStorageId(fileStorageId);
            log.setParseStatus(parseStatus);
            log.setParseContent(parseContent);
            log.setErrorMessage(errorMessage);
            log.setCreatedTime(new Date());
            appParseLogMapper.insert(log);
        } catch (Exception e) {
            log.error("处理APP解析结果失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void createAppParseTask(Long fileStorageId) {
        // 创建APP解析任务
        // 1. 查询file_storage表
        FileStorage fileStorage = fileStorageService.findByStorageId(fileStorageId);
        if (fileStorage == null) {
            log.error("文件不存在: {}", fileStorageId);
            return;
        }

        // 2. 查询app_file表
        AppFile appFile = appFileMapper.selectOne(
                new LambdaQueryWrapper<AppFile>()
                        .eq(AppFile::getFileStorageId, fileStorageId)
        );
        // 如果app_file表中存在，就不需要解析了
        if (appFile != null) {
            return;
        }

        // 3. 创建APP解析任务

        // 如果已经存在解析任务,并且状态为pending或者processing，则不创建
        AppParseTask appParseTask = appParseTaskMapper.selectOne(
                new LambdaQueryWrapper<AppParseTask>()
                        .eq(AppParseTask::getFileStorageId, fileStorageId)
                        .in(AppParseTask::getStatus, Arrays.asList(ParseStatusEnum.PENDING.getCode(), ParseStatusEnum.PROCESSING.getCode()))
        );
        if (appParseTask != null) {
            return;
        }
        appParseTask = new AppParseTask();
        appParseTask.setFileStorageId(fileStorageId);
        appParseTask.setCreatedTime(new Date());
        appParseTask.setStatus(ParseStatusEnum.PENDING.getCode());
        appParseTaskMapper.insert(appParseTask);
    }

    @Override
    public void updateInfo(AppUpdateInfoDTO dto) {
        log.info("更新应用信息 dto: {}", JSON.toJSONString(dto));

        if(dto.getSortNum()!=null && dto.getSortNum() >10000){
            throw new BasicException("sorNum参数不能大于10000");
        }

        Long customerId = dto.getCustomerId();
        Long fileUniqueId = dto.getAppId();
        Long appFileId = dto.getAppFileId();
        String appName = dto.getAppName();
        String iconBase64 = dto.getIconBase64();

        AppFile appFile = appFileMapper.selectById(appFileId);
        if (null == appFile) {
            throw new BasicException("应用不存在");
        }

        String iconPath = null;
        String iconPublicUrl = null;
        if (StringUtils.hasText(iconBase64)) {
            byte[] iconBytes = Base64.getDecoder().decode(iconBase64);
            String iconMd5 = DigestUtils.md5DigestAsHex(iconBytes);
            iconPath = ossService.getAppIconPath(appFile.getPackageName(), appFile.getVersionCode(), iconMd5);
            iconPublicUrl = ossService.uploadBase64Image(iconPath, iconBase64, "image/png");
        }

        UserAppFile userAppFile = userAppFileMapper.getByAppFileIdAndCustomerId(fileUniqueId, customerId);
        if (userAppFile == null) {
            userAppFile = new UserAppFile();
            userAppFile.setCustomerId(customerId);
            userAppFile.setFileUniqueId(fileUniqueId);
            userAppFile.setCreatedTime(new Date());
            if (StrUtil.isNotBlank(appName)) {
                userAppFile.setAppName(appName.trim());
            }else{
                userAppFile.setAppName(appFile.getAppName());
            }
            userAppFile.setIconPath(iconPath);
            userAppFile.setIconPublicUrl(iconPublicUrl);
            userAppFileMapper.insert(userAppFile);
            log.info("保存 userAppFile: {}", JSON.toJSONString(userAppFile));
        } else {
            if(StrUtil.isNotBlank(appName)){
                userAppFile.setAppName(appName.trim());
            }
            if(StrUtil.isNotBlank(iconPath)){
                userAppFile.setIconPath(iconPath);

            }
            userAppFile.setIconPublicUrl(iconPublicUrl);
            userAppFileMapper.updateById(userAppFile);
            log.info("更新 userAppFile: {}", JSON.toJSONString(userAppFile));
        }

        // 更新sortNum及应用描述
        LambdaUpdateWrapper<UserFile> updateWrapper = new LambdaUpdateWrapper<UserFile>();
        if(dto.getSortNum()!=null){
            updateWrapper.set(UserFile::getSortNum, dto.getSortNum());
        }
        if(StrUtil.isNotBlank(dto.getDescription())){
            updateWrapper.set(UserFile::getFileComment, dto.getDescription());
        }
        updateWrapper.set(UserFile::getUpdatedTime, new Date())
                .eq(UserFile::getFileUniqueId, fileUniqueId.toString())
                .eq(UserFile::getCustomerId, customerId);

        userFileMapper.update(updateWrapper);
    }

}