package net.armcloud.paascenter.filecenter.model.dto;


import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "缓存应用结果")
public class CacheAppDTO extends BaseDTO {
    @NotNull(message = "parse cannot null")
    @ApiModelProperty(value = "是否缓存并解析（解析并缓存） 如解析则无需填包信息")
    private Boolean parse;

    @NotNull(message = "apps cannot null")
    @Size(min = 1, max = 20, message = "apps长度在1-20之间")
    @ApiModelProperty(value = "应用列表")
    private List<App> apps;

    /**暂时不对外暴露 只提供给有需要的客户*/
    @ApiModelProperty(value = "是否跳过校验")
    private Boolean skipCheck;

    @Data
    public static class App {
        @ApiModelProperty(value = "自定义应用ID，不填则默认生成")
        private Integer appId;

        @ApiModelProperty(value = "应用名称")
        private String appName;

        @ApiModelProperty(value = "应用下载地址")
        private String url;

        @ApiModelProperty(value = "包名")
        private String pkgName;

        @ApiModelProperty(value = "签名MD5")
        private String signMd5;

        @ApiModelProperty(value = "版本号")
        private Long versionNo;

        @ApiModelProperty(value = "版本名称")
        private String versionName;

        @ApiModelProperty(value = "应用描述")
        private String description;

        @ApiModelProperty(value = "应用唯一标识")
        private String md5sum;
    }


}
