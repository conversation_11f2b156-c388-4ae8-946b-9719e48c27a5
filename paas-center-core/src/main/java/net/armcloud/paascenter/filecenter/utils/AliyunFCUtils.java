package net.armcloud.paascenter.filecenter.utils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.core.http.HttpHeaders;
import com.aliyun.sdk.service.fc20230330.AsyncClient;
import com.aliyun.sdk.service.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.sdk.service.fc20230330.models.InvokeFunctionResponse;

import darabonba.core.RequestConfiguration;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.filecenter.config.OssConfig;

@Slf4j
@Component
public class AliyunFCUtils {

    @Autowired
    private OssConfig ossConfig;

    private volatile AsyncClient fcClient;
    private final Object lock = new Object();

    private AsyncClient getClient() {
        if (fcClient == null) {
            synchronized (lock) {
                if (fcClient == null) {
                    try {
                        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                                .accessKeyId(ossConfig.getAccessKey())
                                .accessKeySecret(ossConfig.getSecretKey())
                                .build());

                        fcClient = AsyncClient.builder()
                                .region("cn-hongkong")
                                .credentialsProvider(provider)
                                .overrideConfiguration(
                                        ClientOverrideConfiguration.create()
                                                .setEndpointOverride("1191921788579688.cn-hongkong.fc.aliyuncs.com"))
                                .build();
                    } catch (Exception e) {
                        log.error("Failed to initialize Aliyun FC client: ", e);
                        throw new RuntimeException("Failed to initialize Aliyun FC client", e);
                    }
                }
            }
        }
        return fcClient;
    }

    public String asyncInvokeFunction(String functionName, String body, long delaySeconds) {
        AsyncClient client = null;
        try {
            client = getClient();
            
            // Create HTTP headers with async delay
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-fc-async-delay", String.valueOf(delaySeconds));
            
            // Parameter settings for API request
            InputStream bodyStream = new ByteArrayInputStream(body.getBytes("UTF-8"));
            InvokeFunctionRequest invokeFunctionRequest = InvokeFunctionRequest.builder()
                    .xFcInvocationType("Async")
                    .xFcLogType("None")
                    .qualifier("LATEST")
                    .functionName(functionName)
                    .body(bodyStream)
                    .requestConfiguration(RequestConfiguration.create().setHttpHeaders(headers))
                    .build();

            // Asynchronously get the return value of the API request
            CompletableFuture<InvokeFunctionResponse> response = client.invokeFunction(invokeFunctionRequest);
            // Synchronously get the return value of the API request
            InvokeFunctionResponse resp = response.get();
            int statusCode = resp.getStatusCode();
            log.info("statusCode: {}", statusCode);
            
            Map<String, String> respHeaders = resp.getHeaders();
            // 获取requestId
            if (respHeaders != null) {
                // log.info("headers: {}", respHeaders);
                String requestId = respHeaders.get("X-Fc-Request-Id");
                log.info("requestId: {}", requestId);
                return requestId;
            }
        } catch (UnsupportedEncodingException e) {
            log.error("Encoding error: ", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Function execution interrupted: ", e);
        } catch (Exception e) {
            log.error("Function execution failed: ", e);
        }
        return null;
    }
}
