package net.armcloud.paascenter.filecenter.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * APP parse result DTO
 */
@Data
public class AppParseResultDTO {

    /**
     * File storage ID
     */
    @NotNull(message = "File storage ID cannot be null")
    private Long fileStorageId;
    private Long customerId;
    private Long fileUniqueid;

    /**
     * Application name
     */
    private String appName;

    /**
     * 自定义应用名
     */
    private String appName2;

    /**
     * Android package name
     */
    private String packageName;

    /**
     * Version name
     */
    private String versionName;

    /**
     * Version code
     */
    private Integer versionCode;

    /**
     * Signature hash
     */
    private String signatureHash;

    /**
     * Developer name
     */
    private String developerName;

    /**
     * App icon as Base64 encoded string
     */
    private String iconBase64;
    private String iconBase64_2;

    /**
     * Supported ABIs
     */
    private String supportedAbis;

    /**
     * Minimum SDK version
     */
    private Integer minSdkVersion;

    /**
     * Target SDK version
     */
    private Integer targetSdkVersion;

    /**
     * App size
     */
    private Long appSize;

    /**
     * Main activity
     */
    private String mainActivity;

    /**
     * Is system app
     */
    private Boolean isSystemApp;

    /**
     * Is debug app
     */
    private Boolean isDebugApp;

    /**
     * Parse status
     */
    private String parseStatus;

    /**
     * Parse error
     */
    private String parseError;

    /**
     * Installation location preference
     */
    private String installLocation;

    /**
     * Permissions (JSON format)
     */
    private String permissions;

    /**
     * Installation requirements (JSON format)
     */
    private String installationRequirements;

}