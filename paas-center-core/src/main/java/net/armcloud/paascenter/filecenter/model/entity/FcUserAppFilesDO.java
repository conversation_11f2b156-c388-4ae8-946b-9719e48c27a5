package net.armcloud.paascenter.filecenter.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户应用文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Getter
@Setter
@TableName("fc_user_app_files")
public class FcUserAppFilesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long fileUniqueId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 图标本地路径
     */
    private String iconPath;

    /**
     * 图标公网 URL
     */
    private String iconPublicUrl;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 最后更新时间
     */
    private Date updatedTime;
}
