package net.armcloud.paascenter.filecenter.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.filecenter.FileStorage;
import net.armcloud.paascenter.filecenter.mapper.FileStorageMapper;
import net.armcloud.paascenter.filecenter.service.FileStorageService;

/**
 * File storage service implementation
 */
@Slf4j
@Service
public class FileStorageServiceImpl extends ServiceImpl<FileStorageMapper, FileStorage> implements FileStorageService {
    
    @Override
    public FileStorage findByMd5(String fileMd5, String uploadStatus) {
        LambdaQueryWrapper<FileStorage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileStorage::getFileMd5, fileMd5);
        wrapper.eq(uploadStatus != null, FileStorage::getUploadStatus, uploadStatus);
        wrapper.last("limit 1");
        return getOne(wrapper);
    }
    
    @Override
    public FileStorage findByStorageId(Long id) {
        return getById(id);
    }
    
    @Override
    public FileStorage saveFileStorage(FileStorage fileStorage) {
        if (fileStorage.getId() != null) {
            updateById(fileStorage);
        } else {
            boolean saved = super.save(fileStorage);
            if (!saved) {
                log.error("Failed to save file storage: {}", fileStorage);
            }
        }
        return fileStorage;
    }
    
    @Override
    public boolean deleteById(Long id) {
        if (id == null) {
            log.warn("Cannot delete FileStorage with null id");
            return false;
        }
        
        try {
            boolean removed = removeById(id);
            if (removed) {
                log.info("Successfully deleted FileStorage with id: {}", id);
            } else {
                log.warn("Failed to delete FileStorage with id: {}, record may not exist", id);
            }
            return removed;
        } catch (Exception e) {
            log.error("Error deleting FileStorage with id: {}", id, e);
            return false;
        }
    }
} 