package net.armcloud.paascenter.filecenter.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * APP details view object
 */
@Data
public class AppDetailsVO {

    /**
     * APP ID
     */
    private Long id;
    private Long appFileId;

    /**
     * 文件存储ID
     */
    private Long fileStorageId;

    /**
     * 文件唯一ID
     */
    private String fileUniqueId;

    /**
     * Customer ID
     */
    private Long customerId;

    /**
     * Customer name
     */
    private String customerName;

    /**
     * Customer account
     */
    private String customerAccount;

    /**
     * Original file name
     */
    private String fileName;
    private String fileName2;

    /**
     * File type
     */
    private String fileType;

    /**
     * Source type
     */
    private String sourceType;

    /**
     * Original URL
     */
    private String originalUrl;

    /**
     * File comment
     */
    private String fileComment;

    /**
     * File size in bytes
     */
    private Long fileSize;

    /**
     * File MD5 hash
     */
    private String fileMd5;

    /**
     * Public URL for downloading
     */
    private String publicUrl;

    /**
     * Application name
     */
    private String appName;
    private String appName2;

    /**
     * Android package name
     */
    private String packageName;

    /**
     * Version name
     */
    private String versionName;

    /**
     * Version code
     */
    private Integer versionCode;

    /**
     * Signature hash
     */
    private String signatureHash;

    /**
     * Developer name
     */
    private String developerName;

    /**
     * App icon public URL
     */
    private String iconPublicUrl;
    private String icon2PublicUrl;

    /**
     * Supported ABIs
     */
    private String supportedAbis;

    /**
     * Minimum SDK version
     */
    private Integer minSdkVersion;

    /**
     * Target SDK version
     */
    private Integer targetSdkVersion;

    /**
     * APP size in bytes
     */
    private Long appSize;

    /**
     * Installation location
     */
    private String installLocation;

    /**
     * Is system app
     */
    private Boolean isSystemApp;

    /**
     * Is debug app
     */
    private Boolean isDebugApp;

    /**
     * Main activity
     */
    private String mainActivity;

    /**
     * Permissions
     */
    private String permissions;

    /**
     * Installation requirements
     */
    private String installationRequirements;

    /**
     * Create time
     */
    private Date createdTime;

    /**
     * 排序号
     */
    private Integer sortNum;
}