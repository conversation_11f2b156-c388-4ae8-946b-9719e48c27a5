package net.armcloud.paascenter.filecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * APP parse status enumeration
 */
@Getter
@AllArgsConstructor
public enum ParseStatusEnum {
    
    PENDING("pending", "Waiting for parsing"),
    PROCESSING("processing", "Parsing in progress"),
    SUCCESS("success", "Parsing completed successfully"),
    FAILED("failed", "Parsing failed");
    
    private final String code;
    private final String description;
    
    /**
     * Get enum by code
     */
    public static ParseStatusEnum getByCode(String code) {
        for (ParseStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING; // Default to pending
    }
} 