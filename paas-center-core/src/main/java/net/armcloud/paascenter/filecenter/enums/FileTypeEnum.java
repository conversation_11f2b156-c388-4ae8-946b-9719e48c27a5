package net.armcloud.paascenter.filecenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * File type enumeration
 */
@Getter
@AllArgsConstructor
public enum FileTypeEnum {
    
    APP("app", "Android Application Package"),
    IMAGE("image", "Image File"),
    ISO("iso", "Disk Image File"),
    FILE("file", "Other File Type");
    
    private final String code;
    private final String description;
    
    /**
     * Get enum by code
     */
    public static FileTypeEnum getByCode(String code) {
        for (FileTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return FILE; // Default to other
    }
} 