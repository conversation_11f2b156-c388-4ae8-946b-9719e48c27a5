package net.armcloud.paascenter.filecenter.model.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "缓存文件参数")
public class CacheFileDTO extends BaseDTO {

    @NotBlank(message = "fileName cannot null")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @NotBlank(message = "fileUrl cannot null")
    @ApiModelProperty(value = "文件下载地址")
    private String fileUrl;

    @NotBlank(message = "fileMd5 cannot null")
    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

}
