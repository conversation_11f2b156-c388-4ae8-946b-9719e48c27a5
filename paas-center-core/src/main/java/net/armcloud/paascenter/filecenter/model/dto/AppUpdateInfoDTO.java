package net.armcloud.paascenter.filecenter.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class AppUpdateInfoDTO {

    @NotNull
    private Long customerId;
    @NotNull
    private Long appFileId;
    @NotNull
    private Long appId;

    private String appName;
    private String iconBase64;

    /**
     * 文件描述，应用描述
     */
    private String description;
    /**
     * 排序号
     */
    private Integer sortNum;

}