package net.armcloud.paascenter.filecenter.service;

import com.alibaba.fastjson2.JSONObject;
import net.armcloud.paascenter.filecenter.model.dto.AppParseResultDTO;
import net.armcloud.paascenter.filecenter.model.dto.AppUpdateInfoDTO;
import net.armcloud.paascenter.filecenter.model.vo.AppParseInfoVO;

/**
 * APP解析服务接口
 */
public interface AppParseService {
    
    /**
     * 上报APP解析结果
     *
     * @param result APP解析结果
     * @return 处理是否成功
     */
    boolean reportAppParseResult(AppParseResultDTO result);
    

    /**
     * Get APP parse information
     *
     * @param fileStorageId File storage ID
     * @return APP parse information VO
     */
    AppParseInfoVO getAppParseInfo(Long fileStorageId);

    /**
     * Record parse log
     *
     * @param fileStorageId File storage ID
     * @param parseStatus Parse status
     * @param parseContent Parse content (can be null if failed)
     * @param errorMessage Error message (can be null if success)
     */
    void recordParseLog(Long fileStorageId, String parseStatus, String parseContent, String errorMessage);

    /**
     * Create APP parse task
     *
     * @param fileStorageId File storage ID
     */
    void createAppParseTask(Long fileStorageId);

    void updateAppFile(long fileStorageId, JSONObject appInfo);

    void updateInfo(AppUpdateInfoDTO dto);

}