package net.armcloud.paascenter.filecenter.model.dto;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppFileDetailDTO extends BaseDTO {
    @ApiModelProperty(value = "客户id", hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "自定义应用ID")
    @NotNull(message = "appId cannot null")
    private Long appId;
}
