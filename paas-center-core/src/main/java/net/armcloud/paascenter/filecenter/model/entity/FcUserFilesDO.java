package net.armcloud.paascenter.filecenter.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户文件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-13
 */
@Getter
@Setter
@TableName("fc_user_files")
public class FcUserFilesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 关联file_storage表的ID
     */
    private Long fileStorageId;

    /**
     * 文件唯一标识
     */
    private String fileUniqueId;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件状态: invalid-无效, valid-有效, del-删除
     */
    private String fileStatus;

    /**
     * 文件来源: upload-直接上传, url-URL下载
     */
    private String sourceType;

    /**
     * URL下载时的原始URL
     */
    private String originalUrl;

    /**
     * 文件备注
     */
    private String fileComment;

    /**
     * 创建者用户ID
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 最后更新者用户ID
     */
    private Long updatedBy;

    /**
     * 最后更新时间
     */
    private Date updatedTime;

    /**
     * 排序号
     */
    private Integer sortNum;
}
