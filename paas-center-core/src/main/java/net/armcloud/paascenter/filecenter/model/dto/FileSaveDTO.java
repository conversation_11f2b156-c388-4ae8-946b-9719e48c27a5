package net.armcloud.paascenter.filecenter.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * File status update DTO
 */
@Data
public class FileSaveDTO {
    
    /**
     * File storage ID
     */
    @NotNull(message = "File storage ID cannot be null")
    private Long fileStorageId;
    
    /**
     * File type (app, file)
     */
    private String fileType;

    /**
     * File name
     */
    private String fileName;

    /**
     * 客户定义分类
     */
    private String customerAppClassifyId;

    /**
     * 文件描述，应用描述
     */
    private String description;
    /**
     * 排序号
     */
    private Integer sortNum;

    
  
} 