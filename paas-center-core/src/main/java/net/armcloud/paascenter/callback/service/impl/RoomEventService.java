package net.armcloud.paascenter.callback.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.mapper.CallbackPadRoomMapper;
import net.armcloud.paascenter.callback.mapper.PadRtcStreamLogMapper;
import net.armcloud.paascenter.callback.service.IRoomEventService;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.model.dto.api.PadStreamStatusDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RtcRoomEventTimeDTO;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import net.armcloud.paascenter.common.model.entity.paas.PadRtcStreamLog;
import net.armcloud.paascenter.openapi.service.IPadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;

@Slf4j
@Service
public class RoomEventService implements IRoomEventService {
    @Resource
    private CallbackPadRoomMapper callbackPadRoomMapper;
    @Resource
    private PadRtcStreamLogMapper PadRtcStreamLogMapper;
    @Resource
    private IPadService padService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean roomCreateEventService(RtcRoomEventTimeDTO dto) {
        PadRoom padRoom = callbackPadRoomMapper.selectOne(new QueryWrapper<PadRoom>().eq("room_code", dto.getRoomId()));

        if (ObjectUtil.isNull(padRoom)) {
            return false;
        }
        //更新推流状态
        PadStreamStatusDTO padStreamStatusDTO = new PadStreamStatusDTO();
        padStreamStatusDTO.setPadCodes(Collections.singletonList(padRoom.getPadCode()));
        padStreamStatusDTO.setStreamStatus(NumberConsts.ONE);
        padService.updatePadStreamStatusService(padStreamStatusDTO.getPadCodes(), padStreamStatusDTO.getStreamStatus());
        // 插入日志
        PadRtcStreamLog streamLog = new PadRtcStreamLog();
        streamLog.setRoomCode(dto.getRoomId());
        streamLog.setPadCode(padRoom.getPadCode());
        streamLog.setStatus(NumberConsts.ONE);
        streamLog.setRoomCreateTime(dto.getTimestamp());

        return PadRtcStreamLogMapper.insert(streamLog) > NumberConsts.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean roomDestroyService(RtcRoomEventTimeDTO dto) {
        PadRoom padRoom = callbackPadRoomMapper.selectOne(new QueryWrapper<PadRoom>().eq("room_code", dto.getRoomId()));

        if (ObjectUtil.isNull(padRoom)) {
            return false;
        }
        //更新推流状态
        PadStreamStatusDTO padStreamStatusDTO = new PadStreamStatusDTO();
        padStreamStatusDTO.setPadCodes(Collections.singletonList(padRoom.getPadCode()));
        padStreamStatusDTO.setStreamStatus(NumberConsts.ZERO);
        padService.updatePadStreamStatusService(padStreamStatusDTO.getPadCodes(), padStreamStatusDTO.getStreamStatus());

        PadRtcStreamLog updateStreamLog = new PadRtcStreamLog();
        updateStreamLog.setRoomCode(dto.getRoomId());
        updateStreamLog.setRoomDestroyTime(dto.getTimestamp());
        updateStreamLog.setStatus(NumberConsts.TWO);

        return PadRtcStreamLogMapper.update(updateStreamLog, new QueryWrapper<PadRtcStreamLog>().eq("room_code", dto.getRoomId()).eq("status", NumberConsts.ONE)) > NumberConsts.ZERO;
    }
}
