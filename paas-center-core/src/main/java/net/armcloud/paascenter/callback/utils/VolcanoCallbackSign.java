package net.armcloud.paascenter.callback.utils;

import net.armcloud.paascenter.common.model.dto.rtc.RtcEventDTO;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class VolcanoCallbackSign {

    public static boolean checkEventSignature(RtcEventDTO event, String secretKey) {
        try {
            List<String> data = new ArrayList<>();
            data.add(event.getEventType());
            data.add(event.getEventData());
            data.add(event.getEventTime());
            data.add(event.getEventId());
            data.add(event.getAppId());
            data.add(event.getVersion());
            data.add(event.getNoce());
            data.add(secretKey);

            Collections.sort(data);

            final String payloadData = String.join("", data);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(payloadData.getBytes());
            String signature = byteToHexString(digest.digest());
            System.out.println(signature);

            if (signature.equals(event.getSignature())) {
                return true;
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static String byteToHexString(byte[] bytes) {
        return String.format("%064x", new BigInteger(1, bytes));
    }
}
