package net.armcloud.paascenter.callback.rocketmq;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.mq.file.DownloadFileResultMessage;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;

/**
 * topic:vcp_download_file_result
 * group:vcp_download_file_result_consumer_callback
 */
@Slf4j
@Service
@AliRocketMQMsgListener(topic = "${mq.download-file-result-message.topic}", consumerGroup = "${mq.download-file-result-message.group}")
public class VcpDownloadFileResultConsumer implements AliRocketMQListener<MessageView> {

    @Override
    public void onMessage(MessageView messageView) {
        log.debug("VcpDownloadFileResultConsumer messageId:{}>>>>>>>>>", messageView.getMessageId());
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        DownloadFileResultMessage message = JSON.parseObject(messageStr, DownloadFileResultMessage.class);
        if (Objects.isNull(message)) {
            log.error("message is null>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }

        log.debug("VcpDownloadFileResultConsumer message:{}>>>>>>>>>", message);
        // fileEventService.downloadResult(message);
    }
}
