package net.armcloud.paascenter.callback.service;

import net.armcloud.paascenter.common.model.dto.rtc.RtcRoomEventTimeDTO;

public interface IRoomEventService {

    /**
     * 房间创建事件
     *
     * @param dto 房间事件发生时间对象
     * @return Boolean
     */
    Boolean roomCreateEventService(RtcRoomEventTimeDTO dto);

    /**
     * 房间销毁事件
     *
     * @param dto 房间事件发生时间对象
     * @return Boolean
     */
    Boolean roomDestroyService(RtcRoomEventTimeDTO dto);
}
