package net.armcloud.paascenter.callback.rocketmq;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.service.strategy.task.TaskCallbackContext;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 *topic:vcp_pod_status
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${mq.task-status.group}", topic = "${mq.task-status.topic}")
public class VcpTaskStatusConsumer implements AliRocketMQListener<MessageView> {
    private final TaskCallbackContext callbackContentContext;

    @Override
    public void onMessage(MessageView messageView) {
        log.debug("VcpTaskStatusConsumer messageId:{}>>>>>>>>>", messageView.getMessageId());
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        PadStatusTaskMessageMQ padCmdResultMessage = JSON.parseObject(messageStr, PadStatusTaskMessageMQ.class);
        log.info("VcpTaskStatusConsumer.onMessage PadStatusTaskMessageMQ:{}", JSONObject.toJSONString(padCmdResultMessage));
        if (Objects.isNull(padCmdResultMessage)) {
            log.error("VcpTaskStatusConsumer is null>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }

        callbackContentContext.getInstance(padCmdResultMessage.getTaskBusinessType()).handler(padCmdResultMessage);
    }

    public VcpTaskStatusConsumer(TaskCallbackContext callbackContentContext) {
        this.callbackContentContext = callbackContentContext;
    }
}
