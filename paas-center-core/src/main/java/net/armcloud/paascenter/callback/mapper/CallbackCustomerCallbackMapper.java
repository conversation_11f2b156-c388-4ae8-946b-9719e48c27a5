package net.armcloud.paascenter.callback.mapper;

import net.armcloud.paascenter.callback.model.CustomerCallbackInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CallbackCustomerCallbackMapper {
    List<CustomerCallbackInfoVO> listByCustomerId(@Param("customerId") long customerId);

    /**
     * 获取所有支持回调的配置
     * @return
     */
    List<CustomerCallbackInfoVO> listAllTaskBusinessTypeNonEmpty();

}
