package net.armcloud.paascenter.callback.rocketmq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CallbackPadManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import net.armcloud.paascenter.common.model.mq.PadWSStatusMessage;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.commscenter.manager.PadCmdManager;
import net.armcloud.paascenter.commscenter.service.CommsceterPadService;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadStatusMapper;
import net.armcloud.paascenter.openapi.service.impl.PadStatusServiceImpl;
import net.armcloud.paascenter.task.service.PadStatusMonitor;
import net.armcloud.paascenter.task.service.impl.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.callback.constant.LockConstants.CONSUME_PAD_WS_STATUS_MESSAGE_PREFIX;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${mq.vcp-pad-ws-status.group}", topic = "${mq.vcp-pad-ws-status.topic}")
public class CallbackVcpPadWSStatusConsumer implements AliRocketMQListener<MessageView> {
	private final CallbackPadManager callbackPadManager;
	private final CallbackTaskManager callbackTaskManager;
	private final RedissonDistributedLock redissonDistributedLock;
	private final CommsceterPadService commsceterPadService;
	private final PadCmdManager padCmdManager;
	private final TaskService taskService;
	private final PadMapper padMapper;
	private final  PadStatusMapper padStatusMapper;
	private final PadStatusServiceImpl padStatusService;
	private final PadStatusMonitor padStatusMonitor;

	int availableProcessors = Runtime.getRuntime().availableProcessors();
	private final ExecutorService threadPool = new ThreadPoolExecutor(availableProcessors * 2, availableProcessors * 3,
			0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(2048), new ThreadFactoryBuilder()
			.setNameFormat("VcpPadWSStatusConsumerPool-%d").build(), new ThreadPoolExecutor.AbortPolicy());

	@Override
	public void onMessage(MessageView messageView) {
		String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
		PadWSStatusMessage message = JSON.parseObject(messageStr, PadWSStatusMessage.class);
		String key = CONSUME_PAD_WS_STATUS_MESSAGE_PREFIX + message.getPadCode();
		RLock lock = redissonDistributedLock.tryLock(key, 3, 30);
		try {
			formatImageId(message);
			// 先回调，再更新状态
			callbackOnline(message);
			updatePadOnline(message);
			// 与commscenter中的VcpPadWSStatusConsumer是消费同一个 这里合并
			padCmdManager.deleteCmdRunningLimit(message.getPadCode());
			if (Boolean.TRUE.equals(message.getConnected())) {
				updatePadRunningStatus(message);
				commsceterPadService.reportConnected(message);
				taskService.updateSpecialPadTaskStatus(message.getPadCode());
				padStatusMonitor.afterOnline(message.getPadCode());
			} else {
				commsceterPadService.reportDisconnected(message);
			}

			threadPool.execute(() -> asyncHandle(message));
		} catch (Exception e) {
			log.error("CallbackVcpPadWSStatusConsumer error :{}", e);
		} finally {
			redissonDistributedLock.unlock(lock);
		}
	}

	private void callbackOnline(PadWSStatusMessage message) {
		// log.info("实例连接状态变更回调准备 {}", JSONUtil.toJsonStr(message));
		if (StrUtil.isNotEmpty(message.getPadCode())) {
			LambdaQueryWrapper<Pad> queryWrapper = new LambdaQueryWrapper();
			queryWrapper.in(Pad::getPadCode, message.getPadCode());
			List<Pad> pads = padMapper.selectList(queryWrapper);
			if (CollUtil.isNotEmpty(pads)) {
				Pad pad = pads.get(0);
				Integer msgOnline = null;
				Boolean connected = message.getConnected();
				if (connected) {
					msgOnline = 1;
				} else {
					msgOnline = 0;
				}
				log.info("实例状态条件判断，入参：{}，pad在线状态：{}，入参在线状态：{}", JSONUtil.toJsonStr(message),pad.getOnline(),msgOnline);
				if (!pad.getOnline().equals(msgOnline)) {
					// log.info("实例连接状态回调满足条件 {}", JSONUtil.toJsonStr(message));
					List<String> padCodes = new ArrayList<String>();
					padCodes.add(message.getPadCode());
					List<PadStatus> padStatuseList = padStatusMapper.listByPadCodes(padCodes);
					if (CollUtil.isNotEmpty(padStatuseList)) {
						if(msgOnline==1){
							padStatusService.sendPadStatusService(pad.getCustomerId(), PadStatusConstant.RUNNING, msgOnline, padCodes);
						}else{
							PadStatus padStatus = padStatuseList.get(0);
							padStatusService.sendPadStatusService(pad.getCustomerId(), padStatus.getPadStatus(), msgOnline, padCodes);
						}
					}
				}
			}
		}
	}

	private void formatImageId(PadWSStatusMessage message) {
		String imageId = message.getImageId();
		if (StringUtils.isBlank(imageId)) {
			return;
		}

		if (!imageId.contains("/")) {
			return;
		}

		String[] parts = imageId.split("/");
		String lastPart = parts[parts.length - 1];
		int colonIndex = lastPart.indexOf(":");
		if (colonIndex != -1) {
			message.setImageId(lastPart.substring(0, colonIndex));
			return;
		}

		message.setImageId(lastPart);
	}

	private void asyncHandle(PadWSStatusMessage message) {
		updatePadTaskStatus(message);
		if (Boolean.TRUE.equals(message.getConnected())) {
			// 修改物理机状态
			updateDeviceTaskStatus(message);
		}
	}

	public void updateDeviceTaskStatus(PadWSStatusMessage message) {
		try {
			callbackTaskManager.updateDeviceTaskByWsConnected(message.getPadCode(), message.getConnected(), message.getBirth());
		} catch (Exception e) {
			log.error("updateDeviceTaskStatus error>>>>>message:{}", JSON.toJSONString(message), e);
		}
	}

	private void updatePadOnline(PadWSStatusMessage message) {
		try {
			String adbEnable = null;
			if (StrUtil.isNotEmpty(message.getAdbEnable())) {
				adbEnable = Objects.equals(message.getAdbEnable(), "1") ? "1" : "0";
			}
			callbackPadManager.updatePadOnline(message.getPadCode(), Boolean.TRUE.equals(message.getConnected()), message.getImageId(), message.getDataSize(),
					message.getDataSizeUsed(), message.getDataSizeAvailable(), message.getRtcVersionName(), message.getRtcVersionCode(), adbEnable);
			// 更新pad的任务模式
			if (StrUtil.isNotEmpty(message.getPadCode())) {
				Pad pad = padMapper.selectPadByPadCode(message.getPadCode());
				if (pad != null) {
					padMapper.updateTaskModeById(pad.getId(), (message.getPullMode() != null && message.getPullMode()) ? 1 : 0);
				}
			}
			// adb状态为空或者为0 表示关闭 为1表示开启
		} catch (Exception e) {
			log.error("updatePadOnline error>>>>>message:{}", JSON.toJSONString(message), e);
		}
	}

	private void updatePadTaskStatus(PadWSStatusMessage message) {
		try {
			Boolean birth = message.getBirth() != null ? Boolean.TRUE.equals(message.getBirth()) : null;
			callbackTaskManager.updatePadTaskByWsConnected(message.getPadCode(), message.getConnected(), birth, message.getTs(), message.getImageId());
		} catch (Exception e) {
			log.error("updatePadTaskStatus error>>>>>message:{}", JSON.toJSONString(message), e);
		}

	}

	private void updatePadRunningStatus(PadWSStatusMessage message) {
		String padCode = message.getPadCode();
		try {
			callbackPadManager.updatePadStatusRunning(padCode);
		} catch (Exception e) {
			log.info("updatePadRunningStatus error message:{}", JSON.toJSONString(message), e);
		}
	}

	public CallbackVcpPadWSStatusConsumer(CallbackTaskManager callbackTaskManager, CallbackPadManager callbackPadManager, RedissonDistributedLock redissonDistributedLock,
										  CommsceterPadService commsceterPadService, PadCmdManager padCmdManager, TaskService taskService, PadStatusMonitor padStatusMonitor
			, PadMapper padMapper,PadStatusMapper padStatusMapper,PadStatusServiceImpl padStatusService) {
		this.callbackTaskManager = callbackTaskManager;
		this.callbackPadManager = callbackPadManager;
		this.redissonDistributedLock = redissonDistributedLock;
		this.commsceterPadService = commsceterPadService;
		this.padCmdManager = padCmdManager;
		this.taskService = taskService;
		this.padMapper = padMapper;
		this.padStatusMapper = padStatusMapper;
		this.padStatusService = padStatusService;
		this.padStatusMonitor = padStatusMonitor;
	}
}
