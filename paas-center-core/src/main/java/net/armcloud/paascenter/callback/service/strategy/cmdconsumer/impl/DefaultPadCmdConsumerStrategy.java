package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.stereotype.Component;


@Component
public class DefaultPadCmdConsumerStrategy extends AbstractPadCmdConsumerStrategy{

    @Override
    public int getByCallbackType() {
        return -1;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        return null;
    }

    public DefaultPadCmdConsumerStrategy(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                         CustomerCallbackManager customerCallbackManager,
                                         PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
    }

}
