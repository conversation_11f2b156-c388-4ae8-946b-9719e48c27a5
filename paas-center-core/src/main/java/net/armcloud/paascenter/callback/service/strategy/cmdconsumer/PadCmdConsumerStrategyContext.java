package net.armcloud.paascenter.callback.service.strategy.cmdconsumer;

import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl.DefaultPadCmdConsumerStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Component
public class PadCmdConsumerStrategyContext {
    private static final Map<String, String> ROUTE_BEAN_NAME_MAP = new HashMap<>();
    private final ApplicationContext applicationContext;
    private final DefaultPadCmdConsumerStrategy defaultPadCmdConsumerStrategy;

    private final List<AbstractPadCmdConsumerStrategy> padCmdConsumerStrategyList;

    public PadCmdConsumerStrategyContext(ApplicationContext applicationContext, DefaultPadCmdConsumerStrategy defaultPadCmdConsumerStrategy,List<AbstractPadCmdConsumerStrategy> padCmdConsumerStrategyList) {
        this.applicationContext = applicationContext;
        this.defaultPadCmdConsumerStrategy = defaultPadCmdConsumerStrategy;
        this.padCmdConsumerStrategyList = padCmdConsumerStrategyList;
    }

    public static void putBeanName(String key, String value) {
        ROUTE_BEAN_NAME_MAP.put(key, value);
    }

    public IPadCmdConsumerStrategy getInstance(String route) {
        String beanName = ROUTE_BEAN_NAME_MAP.get(route);
        if (StringUtils.isBlank(beanName)) {
            return defaultPadCmdConsumerStrategy;
        }

        return (IPadCmdConsumerStrategy) applicationContext.getBean(beanName);
    }

    public AbstractPadCmdConsumerStrategy getInstanceByBusinessType(Integer taskBusinessType) {
        for (AbstractPadCmdConsumerStrategy strategy : padCmdConsumerStrategyList) {
            if(Objects.equals(taskBusinessType, strategy.getTaskBusinessType())){
                return strategy;
            }
        }
        //找不到,返回一个空的,这样方法就不会继续往下执行
        return null;

    }
}
