package net.armcloud.paascenter.callback.service.strategy.task.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.model.vo.PadResetTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.task.ITaskCallbackStrategy;
import net.armcloud.paascenter.callback.service.strategy.task.TaskCallbackContext;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.PAD_RESET_TASK_CALLBACK_TYPE;

@Slf4j
@Service
public class PadResetTaskCallbackStrategyImpl implements ApplicationRunner, ITaskCallbackStrategy {

    private final CustomerCallbackManager customerCallbackManager;

    @Override
    public void handler(PadStatusTaskMessageMQ message) {
        log.info("start handler reset task callback >>> message:{}", JSON.toJSONString(message));
        PadResetTaskCallbackVO callbackVO = new PadResetTaskCallbackVO();
        callbackVO.setPadCode(message.getPadCode());
        callbackVO.setTaskBusinessType(message.getTaskBusinessType());
        callbackVO.setTaskStatus(message.getTaskStatus());
        callbackVO.setTaskId(message.getTaskId());
        callbackVO.setTaskContent(message.getTaskContent());
        callbackVO.setEndTime(message.getEndTime());
        callbackVO.setTaskResult(message.getTaskResult());
        customerCallbackManager.callback(message.getCustomerId(), PAD_RESET_TASK_CALLBACK_TYPE, () -> callbackVO);
        log.info("end handler reset task callback >>> message:{} callbackVO:{}", JSON.toJSONString(message), JSON.toJSONString(callbackVO));
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        TaskCallbackContext.putBeanName(TaskTypeConstants.RESET.getType(), "padResetTaskCallbackStrategyImpl");
    }

    public PadResetTaskCallbackStrategyImpl(CustomerCallbackManager customerCallbackManager) {
        this.customerCallbackManager = customerCallbackManager;
    }
}
