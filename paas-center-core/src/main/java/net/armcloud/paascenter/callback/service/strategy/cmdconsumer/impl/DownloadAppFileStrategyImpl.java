package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadDownloadAppFileCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.command.PadDownloadAppFileCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.PAD_DOWNLOAD_APP_FILE_TASK_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.DOWNLOAD_FILE_APP_CMD;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.SUCCESS;

@Service
public class DownloadAppFileStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {

    private final CallbackTaskManager callbackTaskManager;

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(DOWNLOAD_FILE_APP_CMD.getCommand(), "downloadAppFileStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return PAD_DOWNLOAD_APP_FILE_TASK_CALLBACK_TYPE;
    }

    @Override
    public Integer getTaskBusinessType() {
        return TaskTypeConstants.DOWNLOAD_APP.getType();
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        PadDownloadAppFileCMDDTO cmdData = JSON.parseObject(message.getJsonData(), PadDownloadAppFileCMDDTO.class);
        PadDownloadAppFileCallbackVO result = new PadDownloadAppFileCallbackVO();
//        result.setOriginFileUrl(cmdData.getPath());
//        result.setFileId(cmdData.getFileId());
        result.setPadCode(message.getPadCode());
        result.setTaskBusinessType(TaskTypeConstants.DOWNLOAD_APP.getType());

        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        if (isNotEmpty(data) && isNotEmpty(data.getPadTask())) {
            result.setTaskId(data.getPadTask().getCustomerTaskId());
            result.setTaskStatus(data.getPadTask().getStatus());
            if (isNotEmpty(data.getFileCustomer())) {
                FileCustomer fileCustomer = data.getFileCustomer();
                PadDownloadAppFileCallbackVO.AppInfo appInfo = new PadDownloadAppFileCallbackVO.AppInfo();
                appInfo.setVersionName(fileCustomer.getAppVersionName());
                appInfo.setVersionCode(Objects.toString(fileCustomer.getAppVersionNo()));
                appInfo.setAppId(fileCustomer.getAppId());
                appInfo.setAppName(fileCustomer.getAppName());
                appInfo.setPkgName(fileCustomer.getAppPackageName());
                appInfo.setPadCode(message.getPadCode());
                appInfo.setResult(SUCCESS.getStatus().equals(data.getPadTask().getStatus()) ? true : false);
                appInfo.setFailMsg((appInfo.getResult() !=null && !appInfo.getResult())?message.getMsg():null);
                result.setApps(Collections.singletonList(appInfo));
            }
        }
        return result;
    }

    public DownloadAppFileStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                       CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
    }
}
