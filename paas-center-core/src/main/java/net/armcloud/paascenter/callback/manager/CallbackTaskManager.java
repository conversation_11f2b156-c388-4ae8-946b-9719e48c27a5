package net.armcloud.paascenter.callback.manager;

import org.springframework.stereotype.Component;

import net.armcloud.paascenter.common.client.internal.dto.UpdatePadTaskByWsDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.UpdateFileSubTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.UpdateSubTaskDTO;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.mq.file.DownloadFileResultMessage;
import net.armcloud.paascenter.task.service.IFileTaskService;
import net.armcloud.paascenter.task.service.IPadTaskService;
import net.armcloud.paascenter.task.service.ITaskService;

@Component
public class CallbackTaskManager {

    private final ITaskService taskService;
    private final IPadTaskService padTaskService;
    private final IFileTaskService fileTaskService;

    public void updateSubTaskStatus(long masterTaskId, long subTaskId, Integer subTaskStatus, String subTaskResult, String msg, String result) {
        UpdateSubTaskDTO updateSubTaskDTO = new UpdateSubTaskDTO();
        updateSubTaskDTO.setMasterTaskId(masterTaskId);
        updateSubTaskDTO.setSubTaskId(subTaskId);
        updateSubTaskDTO.setSubTaskStatus(subTaskStatus);
        updateSubTaskDTO.setSubTaskResult(subTaskResult);
        updateSubTaskDTO.setErrorMsg(msg);
        updateSubTaskDTO.setResult(result);
        taskService.updateSubTaskStatus(updateSubTaskDTO);
    }

    public void updateFileTaskStatus(DownloadFileResultMessage message) {
        UpdateFileSubTaskDTO updateSubTaskDTO = new UpdateFileSubTaskDTO();
        updateSubTaskDTO.setSubTaskId(message.getFileTaskId());
        updateSubTaskDTO.setSubTaskStatus(message.getTaskStatus());
        updateSubTaskDTO.setErrorMsg(message.getMsg());
        updateSubTaskDTO.setStartDate(message.getStartDate());
        updateSubTaskDTO.setEndDate(message.getEndDate());
        fileTaskService.updateSubTaskStatus(updateSubTaskDTO);
    }

    public void updateFileTaskStatus(FileUploadTask fileUploadTask) {
        UpdateFileSubTaskDTO updateSubTaskDTO = new UpdateFileSubTaskDTO();
        updateSubTaskDTO.setSubTaskId(fileUploadTask.getId());
        updateSubTaskDTO.setSubTaskStatus(fileUploadTask.getStatus());
        updateSubTaskDTO.setErrorMsg(fileUploadTask.getErrorMsg());
        updateSubTaskDTO.setStartDate(fileUploadTask.getCreatedTime());
        updateSubTaskDTO.setEndDate(fileUploadTask.getEndTime());
        fileTaskService.updateSubTaskStatus(updateSubTaskDTO);
    }

    public FileUploadTask getFileTaskById(long fileTaskId) {
        return fileTaskService.getById(fileTaskId);
    }

    public Task getById(long taskId) {
        return taskService.getById(taskId);
    }

    /**
     * 实例建立长连接更新任务信息
     */
    public void updatePadTaskByWsConnected(String padCode, Boolean connected, Boolean birth, Long startTime, String imageId) {
        UpdatePadTaskByWsDTO dto = new UpdatePadTaskByWsDTO();
        dto.setPadCode(padCode);
        dto.setConnected(connected);
        dto.setBirth(birth);
        dto.setStartTime(startTime);
        dto.setImageId(imageId);
        taskService.updatePadTaskByWsConnected(dto);
    }

    public CallbackTaskManager(ITaskService taskService, IPadTaskService padTaskService, IFileTaskService fileTaskService) {
        this.taskService = taskService;
        this.padTaskService = padTaskService;
        this.fileTaskService = fileTaskService;
    }

    public void updateDeviceTaskByWsConnected(String padCode, Boolean connected, Boolean birth) {
        UpdatePadTaskByWsDTO dto = new UpdatePadTaskByWsDTO();
        dto.setPadCode(padCode);
        dto.setConnected(connected);
        dto.setBirth(birth);
        taskService.updateDeviceTaskByWsConnected(dto);
    }

    public PadTaskVO getPadByUniqueId(String subTaskUniqueId) {
        return padTaskService.getPodTaskByUniqueId(subTaskUniqueId);
    }

    public PadTaskVO getPadBySubTaskId(Long subTaskId) {
        return padTaskService.getSubTaskId(subTaskId);
    }
}
