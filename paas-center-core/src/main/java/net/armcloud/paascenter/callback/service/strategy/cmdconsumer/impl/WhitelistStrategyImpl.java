package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.WhitelistCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.UPDATE_APP_WHITE_LIST_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UPDATE_WHITE_LIST;

@Service
public class WhitelistStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {

    private final CallbackTaskManager callbackTaskManager;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        PadCmdConsumerStrategyContext.putBeanName(UPDATE_WHITE_LIST.getCommand(), "whitelistStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return UPDATE_APP_WHITE_LIST_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId) {
        WhitelistCallbackVO result = new WhitelistCallbackVO();
        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        result.setPadCode(message.getPadCode());
        if(isNotEmpty(data) && isNotEmpty(data.getPadTask())){
            result.setTaskId(data.getPadTask().getCustomerTaskId());
            result.setTaskStatus(data.getPadTask().getStatus());
            result.setTaskBusinessType(TaskTypeConstants.APP_WHITE_LIST.getType());
        }
        return result;
    }

    public WhitelistStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                 CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
    }
}
