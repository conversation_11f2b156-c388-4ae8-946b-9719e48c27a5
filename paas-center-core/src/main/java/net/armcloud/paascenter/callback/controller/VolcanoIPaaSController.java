package net.armcloud.paascenter.callback.controller;

import com.alibaba.fastjson.JSON;
import net.armcloud.paascenter.callback.service.IRoomEventService;
import net.armcloud.paascenter.callback.utils.VolcanoCallbackSign;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.rtc.RtcEventDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RtcRoomEventTimeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static net.armcloud.paascenter.common.core.constant.RtcEventConsts.EventType.ROOM_CREATE;
import static net.armcloud.paascenter.common.core.constant.RtcEventConsts.EventType.ROOM_DESTROY;

@Slf4j
@RestController
@RequestMapping("/callback/open/volcano")
public class VolcanoIPaaSController {

    @Resource
    private IRoomEventService roomEventService;

    @Value("${volcano.event.rtcKey}")
    private String RTC_EVENT_KEY;

    @RequestMapping("roomStatusCallBlack")
    public Result<String> roomStatusCallBlack(@RequestBody RtcEventDTO dto) {
        log.info(">>>>>>>>>>>>>>>> roomStatusCallBlack >> dto={}", dto.toString());
        if (!VolcanoCallbackSign.checkEventSignature(dto, RTC_EVENT_KEY)) {
            log.info(">>>>>>>>>>>>>>>> roomStatusCallBlack  验签失败 >> dto={}", dto.toString());
            return Result.ok();
        }

        if (ROOM_CREATE.equals(dto.getEventType())) {

            RtcRoomEventTimeDTO eventData = JSON.parseObject(dto.getEventData(), RtcRoomEventTimeDTO.class);
            roomEventService.roomCreateEventService(eventData);

        } else if (ROOM_DESTROY.equals(dto.getEventType())) {

            RtcRoomEventTimeDTO eventData = JSON.parseObject(dto.getEventData(), RtcRoomEventTimeDTO.class);
            roomEventService.roomDestroyService(eventData);

        }

        return Result.ok();
    }

    @RequestMapping("armCallback")
    public Result<String> armCallback(@RequestBody Object dto) {
        log.info(">>>>>>>>>>>>>>>> armCallback>>dto={}", dto.toString());
        return Result.ok();
    }


}
