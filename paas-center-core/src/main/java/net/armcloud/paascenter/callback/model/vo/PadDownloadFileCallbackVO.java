package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PadDownloadFileCallbackVO extends BaseTaskCallbackVO{
//    private String originFileUrl;
    private String padCode;
    /**
     * 执行结果：true-成功，false-失败
     */
    private Boolean result;
    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 任务类型
     */
    private Integer taskBusinessType;
}
