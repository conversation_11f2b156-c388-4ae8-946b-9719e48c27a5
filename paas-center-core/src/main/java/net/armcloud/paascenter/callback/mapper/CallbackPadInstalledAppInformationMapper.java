package net.armcloud.paascenter.callback.mapper;

import net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface CallbackPadInstalledAppInformationMapper {
    int insert(PadInstalledAppInformation record);

    @Delete("delete from pad_installed_app_information where pad_code = #{padCode}")
    void deleteByPadCode(@Param("padCode") String padCode);
}