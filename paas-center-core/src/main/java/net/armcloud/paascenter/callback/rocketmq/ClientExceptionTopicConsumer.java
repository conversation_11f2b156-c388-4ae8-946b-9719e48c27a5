package net.armcloud.paascenter.callback.rocketmq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.service.IClientExceptionService;
import net.armcloud.paascenter.common.model.entity.paas.ClientExceptionInfo;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${mq.client_exception_info.group}", topic = "${mq.client_exception_info.topic}")
public class ClientExceptionTopicConsumer implements AliRocketMQListener<MessageView> {
    @Resource
    private IClientExceptionService clientExceptionService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("收到客户端异常消息:{}", str);
        ClientExceptionInfo clientExceptionInfo = JSON.parseObject(str, ClientExceptionInfo.class);
        if(StrUtil.isEmpty(clientExceptionInfo.getErrorJson())){
            clientExceptionInfo.setErrorJson(null);
        }
        clientExceptionService.save(clientExceptionInfo);
    }
}
