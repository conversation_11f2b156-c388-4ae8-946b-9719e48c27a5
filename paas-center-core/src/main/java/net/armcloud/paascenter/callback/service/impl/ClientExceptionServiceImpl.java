package net.armcloud.paascenter.callback.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.callback.mapper.ClientExceptionMapper;
import net.armcloud.paascenter.callback.service.IClientExceptionService;
import net.armcloud.paascenter.common.model.entity.paas.ClientExceptionInfo;
import org.springframework.stereotype.Service;

@Service
public class ClientExceptionServiceImpl extends ServiceImpl<ClientExceptionMapper, ClientExceptionInfo> implements IClientExceptionService{
}
