package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadAppStopCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.command.PadStopAppCMDDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.APP_START_OR_STOP_APP_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.STOP_APP_CMD;

@Service
public class AppStopStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    private final CallbackTaskManager callbackTaskManager;
    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(STOP_APP_CMD.getCommand(), "appStopStrategyImpl");
    }

    @Override
    public int getByCallbackType() {
        return APP_START_OR_STOP_APP_CALLBACK_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        PadStopAppCMDDTO cmdData = JSON.parseObject(message.getJsonData(), PadStopAppCMDDTO.class);
        PadAppStopCallbackVO result = new PadAppStopCallbackVO();
        PadTaskVO data = this.callbackTaskManager.getPadBySubTaskId(subTaskId);
        result.setPackageName(cmdData.getPackageName());
        result.setPadCode(message.getPadCode());
        if(isNotEmpty(data) && isNotEmpty(data.getPadTask())){
            result.setTaskId(data.getPadTask().getCustomerTaskId());
            result.setTaskStatus(data.getPadTask().getStatus());
            result.setTaskBusinessType(TaskTypeConstants.STOP_APP.getType());
        }
        return result;
    }

    public AppStopStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                               CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;

    }
}
