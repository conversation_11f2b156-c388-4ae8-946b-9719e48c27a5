package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PadAdbCallbackVO extends BaseTaskCallbackVO {

    /**
     * 实例编号
     */
    private String padCode;

    /**
     * 任务状态（-1：全失败；-2：部分失败；-3：取消；-4：超时；1：待执行；2：执行中；3：完成）
     */
    private Integer taskStatus;

    /**
     * 任务执行结束时间
     */
    private Long endTime;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 任务结果
     */
    private String taskResult;

    /**
     * 命令
     */
    private String cmd;

    /**
     * 命令结果
     */
    private String cmdResult;

    /**
     * 任务类型
     */
    private Integer taskBusinessType;
}
