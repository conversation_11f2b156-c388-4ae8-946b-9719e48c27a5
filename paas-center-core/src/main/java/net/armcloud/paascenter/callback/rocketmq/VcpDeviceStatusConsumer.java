package net.armcloud.paascenter.callback.rocketmq;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.model.vo.DeviceStatusCallbackVO;
import net.armcloud.paascenter.common.model.mq.callback.DeviceStatusMessageMQ;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.http.HttpClientUtils;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${mq.vcp-device-status.group}", topic = "${mq.vcp-device-status.topic}")
public class VcpDeviceStatusConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private CustomerCallbackManager customerCallbackManager;

    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("VcpDeviceStatusConsumer onMessage:{}", str);

        DeviceStatusMessageMQ dto = JSON.parseObject(str, DeviceStatusMessageMQ.class);

        //消息幂等
        String key = RedisKeyPrefix.VCP_DEVICE_STATUS_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.tryLock(key, 0, 5);
        try {
            DeviceStatusCallbackVO body = new DeviceStatusCallbackVO();
            body.setDeviceStatus(dto.getDeviceStatus());
            body.setDeviceCode(dto.getDeviceCode());
            HttpClientUtils.sendCallback(dto.getHost(), dto.getUrl(), JSON.toJSONString(body), dto.getSk());
        } finally {
            redissonDistributedLock.unlock(lock);
        }


    }
}
