package net.armcloud.paascenter.callback.rocketmq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.IPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.LIST_INSTALL_APP;

/**
 * topic:vcp_pod_cmd_result
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.vcp-pod-cmd-result}_consumer_callback", topic = "${producer-topic.vcp-pod-cmd-result}")
public class VcpPadCmdResultConsumer implements AliRocketMQListener<MessageView> {
    private final PadCmdConsumerStrategyContext padCmdConsumerStrategyContext;

    @Override
    public void onMessage(MessageView messageView) {
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        PadCmdResultMessage padCmdResultMessage = JSON.parseObject(messageStr, PadCmdResultMessage.class);
        log.info("VcpPadCmdResultConsumer.onMessage padCode:{},command:{},status:{},msg:{}",padCmdResultMessage.getPadCode(),padCmdResultMessage.getCommand(),padCmdResultMessage.getStatus(),padCmdResultMessage.getMsg());
        if (Objects.isNull(padCmdResultMessage)) {
            log.error("padCmdResultMessage is null>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }

        IPadCmdConsumerStrategy strategy = padCmdConsumerStrategyContext.getInstance(padCmdResultMessage.getCommand());
        if (Objects.isNull(strategy)) {
            log.warn("strategy not found>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }

        strategy.accept(padCmdResultMessage);
    }

    public VcpPadCmdResultConsumer(PadCmdConsumerStrategyContext padCmdConsumerStrategyContext) {
        this.padCmdConsumerStrategyContext = padCmdConsumerStrategyContext;
    }
}
