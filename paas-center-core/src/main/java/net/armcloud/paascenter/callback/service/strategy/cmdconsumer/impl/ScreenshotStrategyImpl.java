package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadScreenshotCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.LOCAL_SCREENSHOT;

@Slf4j
@Service
public class ScreenshotStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    @Override
    public int getByCallbackType() {
        return 0;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message,Long subTaskId) {
        PadScreenshotCallbackVO result = new PadScreenshotCallbackVO();
        result.setPadCode(message.getPadCode());
        return result;
    }

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(LOCAL_SCREENSHOT.getCommand(), "screenshotStrategyImpl");
    }

    public ScreenshotStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                  CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
    }

}
