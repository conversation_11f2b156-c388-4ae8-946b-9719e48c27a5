package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;

@Data
public class PadStatusTaskCallbackVO {
    /**
     * 任务业务类型
     */
    private Integer taskBusinessType;
    /**
     * 任务ID
     */
    private Integer taskId;
    /**
     * 子任务状态
     */
    private Integer taskStatus;
    /**
     * 云机编号
     */
    private String padCode;
    /**
     * 任务执行结束时间
     */
    private Long endTime;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 任务结果
     */
    private String taskResult;

}
