package net.armcloud.paascenter.callback.model.vo;

import net.armcloud.paascenter.common.model.dto.api.InstallAppTaskTimeOutMsgDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PadDownloadAppFileCallbackVO extends BaseTaskCallbackVO {

    /**
     * 云机编号
     */
    private String padCode;

    /**
     * 任务类型
     */
    private Integer taskBusinessType;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 任务执行结束时间
     */
    private Date endTime;

   List<AppInfo> apps;

   @Data
    public static class AppInfo {
        private Integer appId;
        private String appName;
        private String pkgName;
        private String padCode;
        private String versionName;
        private String versionCode;
        /**
         * 执行结果：true-成功，false-失败
         */
        private Boolean result;
       /**
        * 执行失败原因
        */
       private String failMsg;
    }

    public static PadDownloadAppFileCallbackVO build(InstallAppTaskTimeOutMsgDTO dto){
        PadDownloadAppFileCallbackVO callbackVO = new PadDownloadAppFileCallbackVO();
        callbackVO.setTaskStatus(dto.getTaskStatus());
        callbackVO.setTaskId(dto.getTaskId());
        callbackVO.setPadCode(dto.getPadCode());
        callbackVO.setTaskBusinessType(dto.getTaskBusinessType());
        callbackVO.setEndTime(new Date());
        List<AppInfo> appList = Lists.newArrayList();
        dto.getApps().forEach(app -> {
            AppInfo appInfo = new AppInfo();
            appInfo.setAppId(StringUtils.isEmpty(app.getAppId())?null:Integer.valueOf(app.getAppId()));
            appInfo.setAppName(app.getAppName());
            appInfo.setPkgName(app.getPackageName());
            appInfo.setPadCode(dto.getPadCode());
            appInfo.setVersionName(app.getVersionName());
            appInfo.setVersionCode(app.getVersionCode());
            appList.add(appInfo);
        });
        callbackVO.setApps(appList);
        return callbackVO;
    }
}
