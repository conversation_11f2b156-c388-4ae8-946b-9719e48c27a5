package net.armcloud.paascenter.callback.rocketmq.pullmode;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.model.entity.task.DeviceTask;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.openapi.manager.PadManager;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.mapper.DeviceTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.task.service.IDeviceTaskService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants.DEVICE_INIT;

/**
 * topic:vcp_pull_task_queue
 * 处理拉模式下 更新任务状态
 */
@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${mq.pull-task-queue.topic}_consumer_callback", topic = "${mq.pull-task-queue.topic}")
public class PullTaskQueueConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private TaskQueueManager taskQueueManager;
    @Resource
    private PadManager padManager;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private DeviceTaskMapper deviceTaskMapper;
    @Resource
    private TaskMapper taskMapper;
    @Resource
    private IDeviceTaskService deviceTaskService;

    @Override
    public void onMessage(MessageView messageView) {
        log.debug("PullTaskQueueConsumer messageId:{}>>>>>>>>>", messageView.getMessageId());
        String messageStr = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.debug("PullTaskQueueConsumer messageStr:{}", messageStr);
        Map<String,String> pullTaskQueueMap = JSON.parseObject(messageStr, Map.class);
        if (Objects.isNull(pullTaskQueueMap)) {
            log.error("PullTaskQueueConsumer pullTaskHealthDTO is null>>>>>messageView:{}", JSON.toJSONString(messageView));
            return;
        }
        String deviceType = pullTaskQueueMap.get("deviceType");
        String padTaskIdsJson = pullTaskQueueMap.get("padTaskIds");
        String taskStatus = pullTaskQueueMap.get("taskStatus");
        List<String> padTaskIds = JSON.parseObject(
                padTaskIdsJson,
                new TypeReference<List<String>>() {}.getType()
        );
        if(TaskChannelEnum.GAMESERVER.getCode().equals(deviceType)){
            for(String padTaskId : padTaskIds){
                Long padTaskIdLong = Long.parseLong(padTaskId);
                padManager.updatePadStatus(padTaskIdLong);
                taskQueueManager.updatePadTaskStatusRunPullMode(padTaskIdLong);
            }
        }else if(TaskChannelEnum.CBS.getCode().equals(deviceType)){
            for(String padTaskId : padTaskIds){
                //如果padTaskId后面带_device 则为cbs中的板卡任务
                if(padTaskId.contains("_device")){
                    String restorePadTaskId = padTaskId.substring(0,padTaskId.indexOf("_device"));
                    Long restorePadTaskIdLong = Long.parseLong(restorePadTaskId);
                    DeviceTask deviceTask = deviceTaskMapper.getById(restorePadTaskIdLong);
                    if(deviceTask == null){
                        continue;
                    }
                    if(TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(deviceTask.getStatus()) || TaskStatusConstants.EXECUTING.getStatus().equals(deviceTask.getStatus())){
                        //更新任务状态
                        int updateCount = deviceTaskService.updateDeviceTaskStatus(restorePadTaskIdLong, TaskStatusConstants.EXECUTING.getStatus());
                        if(updateCount == 0){
                            continue;
                        }
                        if(deviceTask != null){
                            Task task = taskMapper.selectById(deviceTask.getTaskId());
                            TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(task.getType());
                            if(taskTypeAndChannelEnum.getPadStatusConstant() != null){
                                //更新板卡分配状态
                                deviceService.updateAllocationStatus(deviceTask.getDeviceCode(),taskTypeAndChannelEnum.getPadStatusConstant());
                                if(!TaskTypeAndChannelEnum.CONTAINER_VIRTUALIZE.equals(taskTypeAndChannelEnum)){
                                    //更新板卡在线状态
                                    deviceService.updateDeviceStatusAndSendDeviceStatusCallback(Collections.singletonList(deviceTask.getDeviceCode()), DEVICE_INIT.getStatus(), null);
                                }
                            }
                        }
                    }
                }else{
                    Long padTaskIdLong = Long.parseLong(padTaskId);
                    padManager.updatePadStatus(padTaskIdLong);
                    taskQueueManager.updatePadTaskStatusRunPullMode(padTaskIdLong);
                }
            }
        }else if(TaskChannelEnum.BMC.getCode().equals(deviceType)){
            for(String padTaskId : padTaskIds){
                Long padTaskIdLong = null;
                if(padTaskId.contains("_arm")){
                    String restorePadTaskId = padTaskId.substring(0,padTaskId.indexOf("_arm"));
                    padTaskIdLong = Long.parseLong(restorePadTaskId);
                }else{
                    padTaskIdLong = Long.parseLong(padTaskId);
                }
                DeviceTask deviceTask = deviceTaskMapper.getById(padTaskIdLong);
                if(TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(deviceTask.getStatus()) || TaskStatusConstants.EXECUTING.getStatus().equals(deviceTask.getStatus())) {
                    //更新任务状态
                    int updateCount = deviceTaskService.updateDeviceTaskStatus(padTaskIdLong, TaskStatusConstants.EXECUTING.getStatus());
                    if (updateCount == 0) {
                        continue;
                    }
                }
            }
        }
    }
}
