package net.armcloud.paascenter.callback.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PadAppUninstallCallbackVO extends BaseTaskCallbackVO {
    List<PadDownloadAppFileCallbackVO.AppInfo> apps;

    @Data
    public static class AppInfo {
        private Integer appId;
        private String appName;
        private String pkgName;
        private String padCode;
        /**
         * 执行结果：true-成功，false-失败
         */
        private Boolean result;
    }
}
