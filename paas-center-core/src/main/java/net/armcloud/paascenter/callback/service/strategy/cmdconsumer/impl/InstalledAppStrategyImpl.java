package net.armcloud.paascenter.callback.service.strategy.cmdconsumer.impl;

import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.PAD_INSTALLED_APP_LIST_TASK_CALLBACK_TYPE;
import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.LIST_INSTALL_APP;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CallbackPadManager;
import net.armcloud.paascenter.callback.manager.CallbackTaskManager;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.mapper.CallbackPadMapper;
import net.armcloud.paascenter.callback.model.vo.BaseTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.InstalledAppCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.AbstractPadCmdConsumerStrategy;
import net.armcloud.paascenter.callback.service.strategy.cmdconsumer.PadCmdConsumerStrategyContext;
import net.armcloud.paascenter.common.client.internal.dto.GetCustomerFileIdDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.constant.comms.CommsConstant;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.mq.cmd.PadCmdResultMessage;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;

@Service
@Slf4j
public class InstalledAppStrategyImpl extends AbstractPadCmdConsumerStrategy implements ApplicationRunner {
    private final CallbackPadManager callbackPadManager;
    private final CallbackTaskManager callbackTaskManager;

    // private final FileCenterAppInternalFacade fileCenterAppInternalFacade;

    @Override
    public void run(ApplicationArguments args) {
        PadCmdConsumerStrategyContext.putBeanName(LIST_INSTALL_APP.getCommand(), "installedAppStrategyImpl");
    }

    @Override
    protected String getSubTaskResult(PadCmdResultMessage message) {
        // 已安装应用列表的数据在data字段中
        JSONObject jsonObject = JSONObject.parseObject(message.getJsonData());
        return jsonObject.getString(CommsConstant.DataField.APPS);
    }

    @Override
    protected void acceptBefore(PadCmdResultMessage message) {
        String jsonData = message.getJsonData();
        if (StringUtils.isBlank(jsonData)) {
            return;
        }

        // 已安装应用列表的数据在data字段中,赋值result字段入库
        JSONObject jsonObject = JSONObject.parseObject(message.getJsonData());
        Object apps = jsonObject.get(CommsConstant.DataField.APPS);
        jsonObject.put(CommsConstant.DataField.RESULT, apps);
        message.setJsonData(JSON.toJSONString(jsonObject));
        callbackPadManager.refreshPadInstalledAppInformation(message.getPadCode(), JSON.toJSONString(apps));
    }

    @Override
    public int getByCallbackType() {
        return PAD_INSTALLED_APP_LIST_TASK_CALLBACK_TYPE;
    }

    @Override
    protected BaseTaskCallbackVO getCallbackData(PadCmdResultMessage message, Long subTaskId) {
        InstalledAppCallbackVO result = JSON.parseObject(message.getJsonData(), InstalledAppCallbackVO.class);
        //查询任务状态
        PadTaskVO padTask = callbackTaskManager.getPadBySubTaskId(subTaskId);
        result.setPadCode(message.getPadCode());
        if (isNotEmpty(padTask) && isNotEmpty(padTask.getPadTask())) {
            result.setTaskId(padTask.getPadTask().getCustomerTaskId());
            result.setTaskStatus(padTask.getPadTask().getStatus());
            result.setTaskBusinessType(TaskTypeConstants.LIST_INSTALLED_APP.getType());
        }
        if(CollectionUtils.isEmpty(result.getApps())){
            return result;
        }
        //最大并行数量10个
        List<List<InstalledAppCallbackVO.APP>> partition = Lists.partition(result.getApps(), 10);
        partition.forEach(list->
            //并行填充安装中,下载中的版本号
            list.parallelStream().forEach(app -> {
                //安装中或者下载中,通过包名去获取app信息  这里通过查询去获取包名,是因为长链接项目(pass-comms)发版影响太大,尽量不要改动pass-comms那边的逻辑 暂时用这个方案,后续pass-comms优化后再重构这块逻辑
                if(Objects.nonNull(app) && (Objects.equals(2, app.getAppState())|| Objects.equals(1, app.getAppState()))) {
                    GetCustomerFileIdDTO dto = new GetCustomerFileIdDTO();
                    dto.setPkgName(app.getPackageName());
                    app.setVersionName("1.0.0");
                    app.setVersionCode("1");
                    // FileApp content = FeignUtils.getContent(fileCenterAppInternalFacade.getByFileIdAndPkgName(dto));
                    // if(Objects.nonNull(content)) {
                    //     app.setVersionName(content.getVersionName());
                    //     app.setVersionCode(String.valueOf(content.getVersionNo()));
                    // }
                }
            }));
        return result;
    }

    public InstalledAppStrategyImpl(CallbackTaskManager callbackTaskManager, CallbackPadMapper callbackPadMapper,
                                    CustomerCallbackManager customerCallbackManager, PadCommsDataService padCommsDataService,
                                    CallbackPadManager callbackPadManager) {
        super(callbackTaskManager, callbackPadMapper, customerCallbackManager, padCommsDataService);
        this.callbackTaskManager = callbackTaskManager;
        this.callbackPadManager = callbackPadManager;
        // this.fileCenterAppInternalFacade = fileCenterAppInternalFacade;
    }
}
