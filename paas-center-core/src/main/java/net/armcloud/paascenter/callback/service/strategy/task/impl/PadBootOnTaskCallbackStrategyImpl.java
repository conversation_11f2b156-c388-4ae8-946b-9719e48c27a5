package net.armcloud.paascenter.callback.service.strategy.task.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.callback.mapper.CallbackCustomerCallbackMapper;
import net.armcloud.paascenter.callback.model.CustomerCallbackInfoVO;
import net.armcloud.paascenter.callback.model.vo.PadBackupTaskCallbackVO;
import net.armcloud.paascenter.callback.model.vo.PadDefaultTaskCallbackVO;
import net.armcloud.paascenter.callback.service.strategy.task.ITaskCallbackStrategy;
import net.armcloud.paascenter.callback.service.strategy.task.TaskCallbackContext;
import net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadBootOnManager;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import org.redisson.api.RLock;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/07/15
 * @description 实例开机任务状态变更回调处理
 */
@Slf4j
@Service
@AllArgsConstructor
public class PadBootOnTaskCallbackStrategyImpl implements ApplicationRunner, ITaskCallbackStrategy {

    private final CustomerCallbackManager customerCallbackManager;

    private final CallbackCustomerCallbackMapper callbackCustomerCallbackMapper;

    private final NetPadBootOnManager netPadBootOnManager;

    private final PadMapper padMapper;
    private final TaskMapper taskMapper;
    private final RedissonDistributedLock redissonDistributedLock;

    @Override
    public void handler(PadStatusTaskMessageMQ message) {

        if (!TaskTypeConstants.isBootOnTask(message.getTaskBusinessType())) {
            log.error("PadBootOnTaskCallbackStrategyImpl handler message taskBusinessType is not boot on task! message:{}", JSON.toJSONString(message));
            return;
        }
        log.info("handler pad boot on task status callback message:{}", JSON.toJSONString(message));
        PadBackupTaskCallbackVO callbackVO = new PadBackupTaskCallbackVO();
        callbackVO.setPadCode(message.getPadCode());
        callbackVO.setTaskBusinessType(message.getTaskBusinessType());
        callbackVO.setTaskStatus(message.getTaskStatus());
        callbackVO.setTaskId(message.getTaskId());
        callbackVO.setTaskContent(message.getTaskContent());
        callbackVO.setEndTime(message.getEndTime());
        callbackVO.setTaskResult(message.getTaskResult());
        if(Objects.isNull(message.getTaskBusinessType())){
            log.warn("DefaultTaskCallbackStrategyImpl process task callback. but message_taskBusinessType_is_empty message:{}", JSON.toJSONString(message));
            return ;
        }

        // 判断非执行中
        if (!Objects.equals(TaskStatusConstants.EXECUTING.getStatus(), message.getTaskStatus())) {
            // 获取实例所属的集群
            Pad pad = padMapper.selectPadByPadCode(message.getPadCode());
            if (Objects.nonNull(pad) && StrUtil.isNotBlank(pad.getClusterCode())) {
                RLock lock = redissonDistributedLock.tryLock(lockKey(message.getPadCode()), 5, 10);
                if (Objects.nonNull(lock)) {
                    try {
                        if (netPadBootOnManager.isPadCodeBootOn(message.getPadCode())) {
                            log.info("PadBootOnTaskCallbackStrategyImpl handler message pad is boot on! message:{}", JSON.toJSONString(message));
                            netPadBootOnManager.decBootOnNum(pad.getClusterCode(), message.getPadCode());
                        }
                    } finally {
                        redissonDistributedLock.unlock(lock);
                    }
                }
            } else {
                log.warn("PadBootOnTaskCallbackStrategyImpl handler message pad is null or clusterCode is empty! message:{}, pad:{}", JSON.toJSONString(message), JSON.toJSONString(pad));
            }
        }

        List<CustomerCallbackInfoVO> list = callbackCustomerCallbackMapper.listAllTaskBusinessTypeNonEmpty();
        list.stream().filter(customerCallbackInfoVO -> Objects.equals(customerCallbackInfoVO.getTaskBusinessType(),message.getTaskBusinessType())).findFirst().ifPresent(customerCallbackInfoVO -> {
            customerCallbackManager.callback(message.getCustomerId(), customerCallbackInfoVO.getType(), () -> callbackVO);
        });
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 1.0 与 2.0 接口都使用这个类进行处理
        TaskCallbackContext.putBeanName(TaskTypeConstants.NET_PAD_ON.getType(), "padBootOnTaskCallbackStrategyImpl");
        TaskCallbackContext.putBeanName(TaskTypeConstants.CONTAINER_NET_STORAGE_ON.getType(), "padBootOnTaskCallbackStrategyImpl");
    }

    private String lockKey(String padCode) {
        return RedisKeyUtils.lockKey("pad_boot_on_lock", padCode);
    }
}
