package net.armcloud.paascenter.task.mapper;

import net.armcloud.paascenter.common.model.dto.api.DataDelDTO;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface PadBackupTaskInfoMapper {

    int countByPadCodesAndTaskStatusList(@Param("padCodes") List<String> padCodes, @Param("taskStatusList") List<Integer> taskStatusList);

    void batchInsert(@Param("list") List<PadBackupTaskInfo> list);

    List<PadBackupTaskInfo> listById(@Param("ids") List<Long> ids);

    PadBackupTaskInfo getCustomerLatestPadBackupData(@Param("customerId") Long customerId, @Param("backupId") Long backupId,
                                                     @Param("backupName") String backupName);

    void updateBackupSizeBySubTaskId(@Param("subTaskId") long subTaskId, @Param("backupSize") long backupSize);

    int delPadBackupData(@Param("customerId") Long customerId,@Param("dataDelDTOS") List<DataDelDTO> dataDelDTOS);

    /**
     * 检测参数是否存在
     * @param customerId
     * @param dataDelDTOS
     * @return
     */
    List<String> hasBackupName(@Param("customerId") Long customerId,@Param("dataDelDTOS") List<DataDelDTO> dataDelDTOS);
}