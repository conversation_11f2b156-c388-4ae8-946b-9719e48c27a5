package net.armcloud.paascenter.task.manager.executor.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceResetRequest;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Collections;
import java.util.List;

import static net.armcloud.paascenter.common.client.internal.utils.ContainerFeignUtils.builderHost;

/**
 * 实例重置
 */
@Slf4j
@Component
public class PadResetTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;

    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        URI host = builderHost(padEdgeClusterVO.getClusterPublicIp());
        InstanceResetRequest req = new InstanceResetRequest();

        InstanceResetRequest.Instance instance = new InstanceResetRequest.Instance();
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setPadCode(padCode);
        req.setInstances(Collections.singletonList(instance));
        return req;
    }

    public PadResetTaskExecutorStrategy(PadMapper padMapper) {
        this.padMapper = padMapper;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.RESET.getType(), "padResetTaskExecutorStrategy");
    }
}
