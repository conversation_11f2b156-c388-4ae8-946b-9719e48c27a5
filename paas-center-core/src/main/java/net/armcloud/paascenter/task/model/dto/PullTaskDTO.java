package net.armcloud.paascenter.task.model.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 拉取任务请求对象
 */
@Data
public class PullTaskDTO {
    /**集群编号*/
    private String clusterCode;

    /**设备类型 GS、CBS、BMC*/
    @NotEmpty(message = "设备类型不能为空")
    private String deviceType;

    /**设备ip*/
    private String deviceIp;
    /**设备编号*/
    private String deviceCode;

    /**拉取任务数量 默认1*/
    private Integer pullNum;
}
