package net.armcloud.paascenter.task.manager.executor.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceModifyPropertiesRequest;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.dto.api.ModifyPadPropertiesDTO;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;

import static net.armcloud.paascenter.task.manager.executor.impl.PadUpgradeImageTaskExecutorStrategy.buildPadOldParam;


/**
 * 修改实例属性
 */
@Slf4j
@Component
public class ModifyPadTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    private final InstanceDetailImageSuccService instanceDetailImageSuccService;

    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        PadEdgeClusterVO padEdgeClusterInfo = padMapper.getPadEdgeClusterInfoByPadCode(padCode);
        String deviceIp = padEdgeClusterInfo.getDeviceIp();
        ArrayList<InstanceModifyPropertiesRequest.Instance> list = Lists.newArrayList();
        InstanceModifyPropertiesRequest.Instance instance = new InstanceModifyPropertiesRequest.Instance();
        instance.setDeviceIp(deviceIp);
        instance.setPadCode(padEdgeClusterInfo.getPadCode());

        String taskContent = padTask.getContentJson();
        //添加安卓信息
        if(StringUtils.isNotEmpty(taskContent)){
            ModifyPadPropertiesDTO modifyPadInformationDTO = JSON.parseObject(taskContent, ModifyPadPropertiesDTO.class);
            if(Objects.nonNull(modifyPadInformationDTO.getScreenWidth())){
                instance.setScreenWidth(modifyPadInformationDTO.getScreenWidth());
            }
            if(Objects.nonNull(modifyPadInformationDTO.getScreenHigh())){
                instance.setScreenHigh(modifyPadInformationDTO.getScreenHigh());
            }
            if(Objects.nonNull(modifyPadInformationDTO.getDpi())){
                instance.setDpi(modifyPadInformationDTO.getDpi());

            }
            if(Objects.nonNull(modifyPadInformationDTO.getFps())){
                instance.setFps(modifyPadInformationDTO.getFps());

            }
            if(Objects.nonNull(modifyPadInformationDTO.getProps())){
                instance.setDeviceAndroidProps(modifyPadInformationDTO.getProps());
            }
            if(Objects.nonNull(modifyPadInformationDTO.getDns())){
                instance.setDns(modifyPadInformationDTO.getDns());
            }
        }

        TaskRelInstanceDetail taskRelInstanceDetail = instanceDetailImageSuccService.getLastInfo(padCode);
        instance.setOldParam(buildPadOldParam(taskRelInstanceDetail));

        list.add(instance);
        InstanceModifyPropertiesRequest request = new InstanceModifyPropertiesRequest();
        request.setInstances(list);
        return request;
    }


    public ModifyPadTaskExecutorStrategy(PadMapper padMapper,InstanceDetailImageSuccService instanceDetailImageSuccService) {
        this.padMapper = padMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.MODIFY_PROPERTIES_PAD.getType(), "modifyPadTaskExecutorStrategy");
    }
}
