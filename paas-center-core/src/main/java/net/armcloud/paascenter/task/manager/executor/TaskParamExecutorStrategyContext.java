package net.armcloud.paascenter.task.manager.executor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class TaskParamExecutorStrategyContext {
    private static final Map<Integer, String> TASK_TYPE_REF_BEAN_NAME_MAP = new HashMap<>();
    private final ApplicationContext applicationContext;

    public static void putBeanName(int taskType, String beanName) {
        TASK_TYPE_REF_BEAN_NAME_MAP.put(taskType, beanName);
    }

    public ITaskParamExecutorStrategy getInstance(int taskType) {
        String beanName = TASK_TYPE_REF_BEAN_NAME_MAP.get(taskType);
        if (StringUtils.isBlank(beanName)) {
            throw new UnsupportedOperationException("不支持的任务类型");
        }

        return (ITaskParamExecutorStrategy) applicationContext.getBean(beanName);
    }

    public TaskParamExecutorStrategyContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
}
