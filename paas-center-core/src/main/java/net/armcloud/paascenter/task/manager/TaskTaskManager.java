package net.armcloud.paascenter.task.manager;

import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
import static net.armcloud.paascenter.task.constants.LockConstants.TASK_TIMEOUT_CONFIG_PREFIX;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;

import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.model.entity.filecenter.FileUploadTask;
import net.armcloud.paascenter.common.model.entity.task.DeviceTask;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.filecenter.mapper.FileUploadTaskMapper;
import net.armcloud.paascenter.task.mapper.DeviceTaskMapper;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.task.mapper.TaskTimeoutConfigMapper;

@Component
public class TaskTaskManager {
    private final TaskMapper taskMapper;
    private final FileUploadTaskMapper fileUploadTaskMapper;
    private final PadTaskMapper padTaskMapper;
    private final RedisService redisService;
    private final TaskTimeoutConfigMapper taskTimeoutConfigMapper;
    private final DeviceTaskMapper deviceTaskMapper;

    public int refreshMasterTaskStatus(long masterTaskId) {
        long total = 0;
        long successCount = 0;
        long failCount = 0;
        long executingCount = 0;
        Task masterTask = taskMapper.selectById(masterTaskId);
        List<Integer> failStatusList = Arrays.asList(FAIL_ALL.getStatus(), TaskStatusConstants.TIMEOUT.getStatus(), TaskStatusConstants.CANCEL.getStatus());

        if (masterTask.belongsToFileTask()) {
            List<FileUploadTask> subTaskList = fileUploadTaskMapper.listByTaskId(masterTaskId);
            total = subTaskList.size();
            successCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), SUCCESS.getStatus())).count();
            executingCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), EXECUTING.getStatus())).count();
            failCount = subTaskList.stream().filter(dto -> failStatusList.contains(dto.getStatus())).count();
        }

        if (masterTask.belongsToPadTask()) {
            List<PadTask> subTaskList = padTaskMapper.listByTaskId(masterTaskId);
            total = subTaskList.size();
            successCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), SUCCESS.getStatus())).count();
            executingCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), EXECUTING.getStatus())).count();
            failCount = subTaskList.stream().filter(dto -> failStatusList.contains(dto.getStatus())).count();
        }

        if (masterTask.belongsToDeviceTask()) {
            List<DeviceTask> subTaskList = deviceTaskMapper.listByTaskId(masterTaskId);
            total = subTaskList.size();
            successCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), SUCCESS.getStatus())).count();
            executingCount = subTaskList.stream().filter(dto -> Objects.equals(dto.getStatus(), EXECUTING.getStatus())).count();
            failCount = subTaskList.stream().filter(dto -> failStatusList.contains(dto.getStatus())).count();
        }

        if (executingCount > 0) {
            return taskMapper.updateStatusIfChangedById(masterTaskId,EXECUTING.getStatus());
        }

        if (successCount == total) {
            return taskMapper.updateStatusIfChangedById(masterTaskId,SUCCESS.getStatus());
        }

        if (failCount > 0) {
            return taskMapper.updateStatusIfChangedById(masterTaskId,FAIL_PART.getStatus());
        }

        if (failCount == total) {
            return taskMapper.updateStatusIfChangedById(masterTaskId,FAIL_ALL.getStatus());
        }
        return 0;
    }

    public TaskTimeoutConfig getCacheTaskTimeoutConfigByType(int taskType) {
        String key = TASK_TIMEOUT_CONFIG_PREFIX + taskType;
        String jsonObj = redisService.getCacheObject(key);
        if (StringUtils.isNoneBlank(jsonObj)) {
            return JSON.parseObject(jsonObj, TaskTimeoutConfig.class);
        }

        TaskTimeoutConfig taskTimeoutConfig = taskTimeoutConfigMapper.getByTaskType(taskType);
        if (taskTimeoutConfig == null) {
            return null;
        }

        redisService.setCacheObject(key, JSON.toJSONString(taskTimeoutConfig), 1L, TimeUnit.HOURS);
        return taskTimeoutConfig;
    }

    public TaskTaskManager(TaskMapper taskMapper, FileUploadTaskMapper fileUploadTaskMapper, PadTaskMapper padTaskMapper, RedisService redisService, TaskTimeoutConfigMapper taskTimeoutConfigMapper, DeviceTaskMapper deviceTaskMapper) {
        this.taskMapper = taskMapper;
        this.fileUploadTaskMapper = fileUploadTaskMapper;
        this.padTaskMapper = padTaskMapper;
        this.redisService = redisService;
        this.taskTimeoutConfigMapper = taskTimeoutConfigMapper;
        this.deviceTaskMapper = deviceTaskMapper;
    }
}
