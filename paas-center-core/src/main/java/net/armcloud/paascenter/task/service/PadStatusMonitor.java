package net.armcloud.paascenter.task.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
import net.armcloud.paascenter.openapi.mapper.DevicePadMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.vo.DevicePadVO;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.CONTAINER_NET_STORAGE_OFF;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.NET_PAD_OFF;

/**
 * <AUTHOR>
 * @date 2025/08/04
 * @description 监控实例状态
 * <p>
 *     目前用于在实例关机成功后，仍然上报健康时。发送钉钉群预警排查问题。
 * </p>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PadStatusMonitor {

    private final RedisTemplate<String, String> redisTemplate;

    private final DevicePadMapper devicePadMapper;

    private final PadMapper padMapper;

    private final PadTaskMapper padTaskMapper;

    private final InstanceDetailImageSuccService instanceDetailImageSuccService;

    @Value("${spring.profiles.active:unknown}")
    private String springProfilesActive;

    public final static String PAD_ABNORMAL_ONLINE_DING_TEST = "https://oapi.dingtalk.com/robot/send?access_token=5426ab9d0cd8e81d3fb6018b11b4162f3925356f910ae38c210e63b50c3a0083";
    public final static String PAD_ABNORMAL_ONLINE_DING_PROD = "https://oapi.dingtalk.com/robot/send?access_token=5426ab9d0cd8e81d3fb6018b11b4162f3925356f910ae38c210e63b50c3a0083";

    private final static List<Integer> offTaskType = Arrays.asList(CONTAINER_NET_STORAGE_OFF.getType(), NET_PAD_OFF.getType());

    public void offSuccess(String padCode) {
        String key = offSuccessKey(padCode);
        redisTemplate.opsForValue().set(key, "1", 6, TimeUnit.HOURS);
    }

    public void afterOn(String padCode) {
        String key = offSuccessKey(padCode);
        redisTemplate.delete(key);
    }

    public void afterOnline(String padCode) {
        if (!isOffSuccess(padCode)) {
            return;
        }
        // 校验一下是否存在板卡关联。
        List<DevicePadVO> devicePadList = devicePadMapper.listByPadCode(Collections.singletonList(padCode));
        if (!devicePadList.isEmpty()) {
            return;
        }
        // 获取最后一次关机成功任务
        PadTask latestOffSuccTask = padTaskMapper.getLatestSuccTask(padCode, offTaskType);
        if (Objects.isNull(latestOffSuccTask)) {
            return;
        }
        // 获取设备最后一次关机所在的板卡
        TaskRelInstanceDetail lastInfo = instanceDetailImageSuccService.getLastInfo(padCode);
        if (Objects.isNull(lastInfo)) {
            return;
        }
        String containerProperty = lastInfo.getContainerProperty();
        if (StrUtil.isBlank(containerProperty)) {
            return;
        }
        JSONObject prop = JSONObject.parseObject(containerProperty);
        String deviceIp = prop.getString("deviceIp");
        if (StrUtil.isBlank(deviceIp)) {
            return;
        }
        List<Pad> padList = padMapper.listByPadCodes(Collections.singletonList(padCode));
        if (padList.isEmpty()) {
            return;
        }
        Pad pad = padList.get(0);

        // 发送钉钉预警
        try {
            String padStatus = Objects.equals(pad.getStatus(), -1) ? "已删" : "未删";
            String alertMessage = buildAlertMessage(padCode, latestOffSuccTask.getId(), deviceIp, padStatus);
            String title = "实例异常预警 - " + padCode;
            DingTalkRobotClient.sendMarkdownMessage(getDingAlertUrl(), springProfilesActive, title, alertMessage);
            log.info("实例关机后仍上报健康预警已发送，padCode: {}, taskId: {}", padCode, latestOffSuccTask.getId());
        } catch (Exception e) {
            log.error("发送钉钉预警失败，padCode: {}, taskId: {}", padCode, latestOffSuccTask.getId(), e);
        }
    }

    private boolean isOffSuccess(String padCode) {
        String key = offSuccessKey(padCode);
        return redisTemplate.hasKey(key);
    }

    private String offSuccessKey(String padCode) {
        return RedisKeyUtils.cacheKey("pad_off_success", padCode);
    }

    /**
     * 构建钉钉预警消息
     *
     * @param padCode    实例编号
     * @param taskId     关机任务ID
     * @param deviceIp   关机板卡IP
     * @param padStatus  实例状态（已删/未删）
     * @return 格式化的预警消息
     */
    private String buildAlertMessage(String padCode, Long taskId, String deviceIp, String padStatus) {

        return "## 🚨 实例异常预警\n\n" +
                "**检测到实例在关机成功后仍然上报健康状态，请及时排查！**\n\n" +
                "### 📋 异常详情\n\n" +
                "| 项目 | 值 |\n" +
                "|------|----|\n" +
                "| **实例编号** | `" + padCode + "` |\n" +
                "| **实例状态** | `" + padStatus + "` |\n" +
                "| **关机任务ID** | `" + taskId + "` |\n" +
                "| **关机板卡IP** | `" + deviceIp + "` |";
//                "| **实例IP** | `" + (StrUtil.isNotBlank(padIp) ? padIp : "未分配") + "` |\n" +
//                "| **设备类型** | `" + (StrUtil.isNotBlank(deviceType) ? deviceType : "未知") + "` |";
    }

    private String getDingAlertUrl() {
        if ("prod".equals(springProfilesActive)) {
            return PAD_ABNORMAL_ONLINE_DING_PROD;
        }
        return PAD_ABNORMAL_ONLINE_DING_TEST;
    }

}
