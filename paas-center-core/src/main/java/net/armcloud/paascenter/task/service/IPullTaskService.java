package net.armcloud.paascenter.task.service;

import net.armcloud.paascenter.task.model.dto.PullEdgeClusterConfigurationDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskHealthDTO;
import net.armcloud.paascenter.task.model.dto.PullTaskResultDTO;
import net.armcloud.paascenter.task.model.vo.PullTaskVO;

import java.util.Map;

public interface IPullTaskService {

    /**
     * 获取待执行的任务列表
     * @param dto
     * @return
     */
    PullTaskVO taskList(PullTaskDTO dto);

    /**
     * 上报任务结果
     * @param dto
     */
    void submitResult(PullTaskResultDTO dto);

    /**
     * 上报任务结果
     * @param dto
     */
    void submitResultDirect(PullTaskResultDTO dto);

    /**
     * 健康状态上报
     * @param dto
     */
    void pullHealth(PullTaskHealthDTO dto);

    /**
     * 获取集群配置
     * @param dto
     * @return
     */
    Map<String,String> edgeClusterConfiguration(PullEdgeClusterConfigurationDTO dto);
}
