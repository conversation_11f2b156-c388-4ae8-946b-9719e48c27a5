package net.armcloud.paascenter.task.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import net.armcloud.paascenter.common.client.internal.dto.DelPadBackupDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.GetLatestPadDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.InstructionPadCodeDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.DataDelDTO;
import net.armcloud.paascenter.common.model.dto.api.SelectPadCodeAndTaskIdDTO;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.task.manager.TaskFileManager;
import net.armcloud.paascenter.task.mapper.PadBackupTaskInfoMapper;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.task.model.vo.PadEdgeVO;
import net.armcloud.paascenter.task.service.IPadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.CommsCmdStatus.FAIL_COMMS_CMD_RECORD_STATUS;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@Service
public class PadTaskServiceImpl implements IPadTaskService {
    private final TaskFileManager taskFileManager;
    private final PadTaskMapper padTaskMapper;
    private final TaskMapper taskMapper;
    private final PadBackupTaskInfoMapper padBackupTaskInfoMapper;

    @Override
    public PadTaskVO getPodTaskByUniqueId(String uniqueId) {
        PadTaskVO data = new PadTaskVO();
        PadTask padTask = padTaskMapper.getPodTaskByUniqueId(uniqueId);
        data.setPadTask(padTask);
        if (isNotEmpty(padTask) && isNotEmpty(padTask.getCustomerFileId())) {
            List<FileCustomer> fileCustomers = taskFileManager.listCustomerFiles(Collections.singletonList(padTask.getCustomerFileId()));
            if (isNotEmpty(fileCustomers)) {
                data.setFileCustomer(fileCustomers.get(ZERO));
            }
        }
        return data;
    }

    @Override
    public PadTaskVO getSubTaskId(Long subTaskId) {
        PadTaskVO data = new PadTaskVO();
        PadTask padTask = padTaskMapper.getById(subTaskId);
        data.setPadTask(padTask);
        if (isNotEmpty(padTask) && isNotEmpty(padTask.getCustomerFileId())) {
            List<FileCustomer> fileCustomers = taskFileManager.listCustomerFiles(Collections.singletonList(padTask.getCustomerFileId()));
            if (isNotEmpty(fileCustomers)) {
                data.setFileCustomer(fileCustomers.get(ZERO));
            }
        }
        return data;
    }

    @Override
    public Object updateTaskMsg(List<CmdRecord> cmdRecords) {
        for (CmdRecord cmdRecord : cmdRecords) {
            if (StrUtil.isNotBlank(cmdRecord.getErrorMsg()) && cmdRecord.getStatus().equals(FAIL_COMMS_CMD_RECORD_STATUS)) {
                padTaskMapper.update(new UpdateWrapper<PadTask>().set("error_msg", cmdRecord.getErrorMsg()).eq("id", cmdRecord.getSubTaskId()));
            }
        }
        return null;
    }

    @Override
    public PadBackupTaskInfo getCustomerLatestPadBackupData(GetLatestPadDataDTO dto) {
        return padBackupTaskInfoMapper.getCustomerLatestPadBackupData(dto.getCustomerId(), dto.getBackupId(), dto.getBackupName());
    }

    @Override
    public List<DataDelDTO> delPadBackupData(DelPadBackupDataDTO dto) {
        //定义集合，保存不存在的数据进行返回
        List<DataDelDTO> notExistsData = new ArrayList<>();
        //先查询验证数据是否存在
        List<String> hasBackupNameList = padBackupTaskInfoMapper.hasBackupName(dto.getCustomerId(), dto.getDataDelDTOS());
        Set<String> hasBackupNameSet = new HashSet<>(hasBackupNameList);

        //便利筛选参入的参数是正确
        dto.getDataDelDTOS().removeIf(dataDelDTO -> {
            //如果传入的参数不存在，则返回给客户
            if (!hasBackupNameSet.contains(dataDelDTO.getBackupName())) {
                notExistsData.add(DataDelDTO.builder().padCode(dataDelDTO.getPadCode()).backupName(dataDelDTO.getBackupName()).build());
                return true;
            }
            return false;
        });
        if (CollectionUtil.isNotEmpty(dto.getDataDelDTOS())){
            padBackupTaskInfoMapper.delPadBackupData(dto.getCustomerId(), dto.getDataDelDTOS());
        }
        return notExistsData;
    }

    @Override
    public List<String> existInstructionPadCode(InstructionPadCodeDTO dto) {
        //根据单表拆分，先查询存在任务的padCode
        List<SelectPadCodeAndTaskIdDTO> selectPadCodeAndTaskIdDTOList = padTaskMapper.selectPadCodeAndTaskId(dto.getCustomerId(),dto.getPadCodes());
        if (CollectionUtil.isEmpty(selectPadCodeAndTaskIdDTOList)){
            return Collections.emptyList();
        }
        //转Map(K: taskId   V:padcode)
        Map<Long, String> padCodeRoomCodeMap = selectPadCodeAndTaskIdDTOList.stream().collect(Collectors.toMap(SelectPadCodeAndTaskIdDTO::getTaskId, SelectPadCodeAndTaskIdDTO::getPadCode, (o1, o2) -> o1));

        //获取主任务ID
        Set<Long> taskIdSet = selectPadCodeAndTaskIdDTOList.stream().map(SelectPadCodeAndTaskIdDTO::getTaskId).collect(Collectors.toSet());
        //根据主任务ID查询，查询主任务是否是匹配的类型
        List<Long> hasTaskIdList = taskMapper.hasTaskId(dto.getCustomerId(),dto.getTaskType(), Lists.newArrayList(taskIdSet));

        List<String> existPadCode = new ArrayList<>();
        hasTaskIdList.forEach(taskId -> {
            String padCode = padCodeRoomCodeMap.get(taskId);
            if (StringUtils.isNotBlank(padCode)) {
                existPadCode.add(padCode);
            }
        });
        return existPadCode;
    }


    public PadTaskServiceImpl(PadTaskMapper padTaskMapper, TaskFileManager taskFileManager, PadBackupTaskInfoMapper padBackupTaskInfoMapper, TaskMapper taskMapper) {
        this.padTaskMapper = padTaskMapper;
        this.taskFileManager = taskFileManager;
        this.padBackupTaskInfoMapper = padBackupTaskInfoMapper;
        this.taskMapper = taskMapper;
    }

    public PadTask getPadTaskByCustomerTaskId(Integer customerTaskId, Long customerId) {
        return padTaskMapper.selectOne(new QueryWrapper<PadTask>().eq("customer_task_id", customerTaskId).eq("customer_id", customerId));
    }

    @Override
    public List<PadEdgeVO> queryPadClusterInfo(List<String> padCodes) {
        return padTaskMapper.queryPadClusterInfo(padCodes);
    }
}
