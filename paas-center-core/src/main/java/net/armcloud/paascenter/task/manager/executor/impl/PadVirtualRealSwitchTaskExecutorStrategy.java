package net.armcloud.paascenter.task.manager.executor.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ImageRequest;
import net.armcloud.paascenter.cms.model.request.InstanceVirtualRealSwitchRequest;
import net.armcloud.paascenter.cms.model.request.VirtualizeDeviceRequest;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.bo.task.quque.PadUpgradeImageTaskQueueBO;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.AdiCertificateRepository;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.rtc.PadMacLog;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.openapi.manager.AdiCertificateManager;
import net.armcloud.paascenter.openapi.mapper.AdiCertificateRepositoryMapper;
import net.armcloud.paascenter.openapi.mapper.CustomerUploadImageMapper;
import net.armcloud.paascenter.openapi.mapper.PadMacLogMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static net.armcloud.paascenter.task.manager.executor.impl.PadUpgradeImageTaskExecutorStrategy.buildPadOldParam;

/**
 * 升级真机镜像
 */
@Slf4j
@Component
public class PadVirtualRealSwitchTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    private final PadTaskMapper padTaskMapper;
    private final PadMacLogMapper padMacLogMapper;
    private final InstanceDetailImageSuccService instanceDetailImageSuccService;
    private final ITaskService taskService;
    private final CustomerUploadImageMapper customerUploadImageMapper;
    private final AdiCertificateManager adiCertificateManager;
    private final AdiCertificateRepositoryMapper adiCertificateRepositoryMapper;

    /**
     * 2种方式
     * 1. 启朔设备发送指令
     * 2. 凌点设备通过容器
     *
     * @param taskQueue 实例任务
     */
    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadTask padTask = padTaskMapper.getById(taskQueue.getSubTaskId());
        ImageRequest image = new ImageRequest();
        image.setId(padTask.getImageId());
        // 暂不需要放开此功能，暂为固定值
        image.setTag("latest");

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        InstanceVirtualRealSwitchRequest.Instance instance = new InstanceVirtualRealSwitchRequest.Instance();
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setPadCode(padCode);
        instance.setImage(image);
        instance.setMac(getAndSetPadMac(padTask.getPadCode()));
        instance.setClearDiskData(padTask.getWipeData());
        InstanceVirtualRealSwitchRequest.ADI adi = new InstanceVirtualRealSwitchRequest.ADI();

        PadUpgradeImageTaskQueueBO padUpgradeImageTaskQueueBO = JSONUtil.toBean(taskQueue.getContentJson(), PadUpgradeImageTaskQueueBO.class);
        adi.setTemplateUrl(padUpgradeImageTaskQueueBO.getAdiUrl());
        adi.setTemplatePassword(padUpgradeImageTaskQueueBO.getAdiPassword());
        adi.setLayoutWidth(padUpgradeImageTaskQueueBO.getLayoutWidth());
        adi.setLayoutHigh(padUpgradeImageTaskQueueBO.getLayoutHigh());
        adi.setLayoutDpi(padUpgradeImageTaskQueueBO.getLayoutDpi());
        adi.setLayoutFps(padUpgradeImageTaskQueueBO.getLayoutFps());
        adi.setRealPhoneTemplateId(padUpgradeImageTaskQueueBO.getRealPhoneTemplateId());

        AdiCertificateRepository adiCertificateRepository = null;
        TaskRelInstanceDetail taskRelInstanceDetail = instanceDetailImageSuccService.getLastInfo(padCode);

        if (taskRelInstanceDetail != null && StringUtils.isNotBlank(taskRelInstanceDetail.getAdiJson())) {
            try {
                // 解析历史记录中的ADI信息
                VirtualizeDeviceRequest.ADI historicalAdi = JSON.parseObject(taskRelInstanceDetail.getAdiJson(), VirtualizeDeviceRequest.ADI.class);
                if (historicalAdi != null && historicalAdi.getRealPhoneTemplateId() != null) {
                    // 从数据库中获取历史证书
                    Integer certificateLeve = adiCertificateRepositoryMapper.selectCertificateLevelById(historicalAdi.getRealPhoneTemplateId());
                    if (certificateLeve != null && certificateLeve == 3) {
                        adiCertificateRepository = adiCertificateRepositoryMapper.selectById(historicalAdi.getRealPhoneTemplateId());
                        if(adiCertificateRepository != null){
                            log.info("升级真机镜像复用历史ADI证书,padCode:{}, certificateId:{}", padCode, adiCertificateRepository.getId());
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("解析历史ADI信息失败,padCode:{}, error:{}", padCode, e.getMessage());
            }
        }

        // 如果没有找到历史证书，则获取新证书
        if (adiCertificateRepository == null) {
            String imageParameter = customerUploadImageMapper.getImageParameterByImageUniqueId(padTask.getImageId());
            adiCertificateRepository = adiCertificateManager.useAdiCertificate(padCode, imageParameter);
            log.info("升级真机镜像获取新ADI证书,padCode:{}, certificateId:{}", padCode, adiCertificateRepository.getId());
        }

        // 设置安卓证书数据
        if (adiCertificateRepository != null) {
            adi.setAndroidCertData(adiCertificateRepository.getCertificate());
        }

        instance.setAdi(adi);
        instance.setOldParam(buildPadOldParam(taskRelInstanceDetail));

        InstanceVirtualRealSwitchRequest req = new InstanceVirtualRealSwitchRequest();
        req.setInstances(Collections.singletonList(instance));

        //记录task_rel_instance_detail表
        taskService.saveDeviceInstanceSingle(padTask.getTaskId(),padTask.getId(), TaskTypeAndChannelEnum.VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getCbsTaskTypeEnum(),taskRelInstanceDetail,req);
        return req;
    }


    private String getAndSetPadMac(String padCode) {
        Pad pad = padMapper.getByPadCode(padCode);
        String mac = pad.getMac();
        if (StringUtils.isNotBlank(mac)) {
            return mac;
        }

        do {
            mac = MACUtils.generateMacAddress();
            int updateSize = padMapper.updateMacById(pad.getId(), mac);
            if (updateSize <= 0) {
                continue;
            }

            PadMacLog padMacLog = new PadMacLog();
            padMacLog.setPadCode(pad.getPadCode());
            padMacLog.setMac(mac);
            padMacLogMapper.insert(padMacLog);
            return mac;
        } while (true);
    }

    public PadVirtualRealSwitchTaskExecutorStrategy(PadMapper padMapper, PadTaskMapper padTaskMapper, PadMacLogMapper padMacLogMapper,
                                                    InstanceDetailImageSuccService instanceDetailImageSuccService, ITaskService taskService,
                                                    CustomerUploadImageMapper customerUploadImageMapper, AdiCertificateManager adiCertificateManager,
                                                    AdiCertificateRepositoryMapper adiCertificateRepositoryMapper) {
        this.padMapper = padMapper;
        this.padTaskMapper = padTaskMapper;
        this.padMacLogMapper = padMacLogMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        this.taskService = taskService;
        this.customerUploadImageMapper = customerUploadImageMapper;
        this.adiCertificateManager = adiCertificateManager;
        this.adiCertificateRepositoryMapper = adiCertificateRepositoryMapper;

        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.VIRTUAL_REAL_SWITCH_UPGRADE_IMAGE.getType(), "padVirtualRealSwitchTaskExecutorStrategy");
    }
}
