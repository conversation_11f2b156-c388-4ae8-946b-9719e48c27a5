package net.armcloud.paascenter.task.enums;

import lombok.Getter;

@Getter
public enum TaskChannelEnum {
    GAMESERVER("GS", "gameserver",1),
    CBS("CBS", "cbs",2),
    BMC("BMC", "bmc",4),
    ;
    public final String code;
    public final String desc;
    /**权限*/
    public final Integer permission;

    TaskChannelEnum(String code, String desc,Integer permission) {
        this.code = code;
        this.desc = desc;
        this.permission = permission;
    }

    public static TaskChannelEnum fromCode(String code) {
        for (TaskChannelEnum taskChannel : TaskChannelEnum.values()) {
            if (taskChannel.getCode().equals(code)) {
                return taskChannel;
            }
        }
        return null;
    }
}