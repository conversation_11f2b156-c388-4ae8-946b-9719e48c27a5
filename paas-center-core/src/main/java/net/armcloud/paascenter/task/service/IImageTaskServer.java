package net.armcloud.paascenter.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.dto.api.AddImageTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.TaskImageUploadDTO;
import net.armcloud.paascenter.common.model.entity.task.ImageTask;
import net.armcloud.paascenter.common.model.vo.api.TaskImageUploadVo;

import java.util.List;

public interface IImageTaskServer extends IService<ImageTask> {
    /**
     * 添加任务
     * @param addImageTaskDTOList
     * @return
     */
    List<Integer> addImageTasks(List<AddImageTaskDTO> addImageTaskDTOList);

    /**
     * 根据任务id查询任务结果
     * @param taskDetailsDTO
     * @return
     */
    List<TaskImageUploadVo> taskList(TaskImageUploadDTO taskDetailsDTO);
}
