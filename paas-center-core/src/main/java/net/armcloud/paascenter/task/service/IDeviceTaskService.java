package net.armcloud.paascenter.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.client.internal.dto.CallbackDeviceTakes;
import net.armcloud.paascenter.common.model.entity.task.DeviceTask;

public interface IDeviceTaskService extends IService<DeviceTask> {

    /**
     * 修改云机任务状态
     * @param param
     */
    void callbackDeviceTakes(CallbackDeviceTakes param);

    /**
     * 更新板卡任务状态
     * @param subTaskId
     * @param status
     */
    int updateDeviceTaskStatus(Long subTaskId,Integer status);
}
