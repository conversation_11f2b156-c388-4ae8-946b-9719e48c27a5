package net.armcloud.paascenter.task.manager.executor.impl.netpadv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2Service;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025-07-07
 * @description 网存实例开机 V2版本
 */
@Slf4j
@Component
public class NetPadV2DelTaskExecuteHandler implements ITaskParamExecutorStrategy {

    @Resource
    public InstanceDetailImageSuccService instanceDetailImageSuccService;
    @Autowired
    private NetPadV2Service netPadV2Service;

    @Override
    public Object execute(TaskQueue padTask) {
        log.info("NetPadV2DelTaskExecuteHandler_execute_start, padTask:{}", JSONObject.toJSONString(padTask));
        JSONObject param = netPadV2Service.getTaskRequest(padTask.getKey(), TaskTypeConstants.NET_PAD_DEL);
        if(param == null){
            throw new BasicException("实例删除参数不存在");
        }
        log.info("NetPadV2DelTaskExecuteHandler_execute_end, result_param:{}", JSON.toJSONString(param));
        return param;
    }

    public NetPadV2DelTaskExecuteHandler() {
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.NET_PAD_DEL.getType(), "netPadV2DelTaskExecuteHandler");
    }
}
