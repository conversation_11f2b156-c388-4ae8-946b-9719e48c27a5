package net.armcloud.paascenter.task.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 拉模式配置
 */
@Slf4j
@Component
@RefreshScope
public class PullModeConfig {
    /**是否开启全局拉模式 如果开启 则所有的任务都是拉模式*/
    @Value("${pullModeOpen:false}")
    public Boolean pullModeOpen;
    /**拉模式 - 客户id 配置的客户id会走该模式*/
    @Value("#{'${pullModeCusIds:}'.split(',')}")
    public List<String> pullModeCusIds;
    /**拉模式 - 配置的实例编号会走该模式 对应gs任务和部分cbs任务*/
    @Value("#{'${pullModePadCodes:}'.split(',')}")
    public List<String> pullModePadCodes;
    /**拉模式 - 配置的板卡编号会走该模式 对应部分cbs任务和bmc任务*/
    @Value("#{'${pullModeDeviceCodes:}'.split(',')}")
    public List<String> pullModeDeviceCodes;

    @PostConstruct
    public void init() {
        PullModeConfigHolder.setPullModeConfig(this);
    }
}
