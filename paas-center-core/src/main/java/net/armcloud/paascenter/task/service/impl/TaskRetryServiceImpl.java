package net.armcloud.paascenter.task.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.task.TaskRetry;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.mapper.TaskRetryMapper;
import net.armcloud.paascenter.task.service.ITaskRetryService;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;

import com.alibaba.fastjson.JSON;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import org.springframework.beans.factory.annotation.Autowired;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.task.enums.RetryStatus;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 任务重试服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskRetryServiceImpl extends ServiceImpl<TaskRetryMapper, TaskRetry> implements ITaskRetryService {

    /**
     * 默认最大重试次数
     */
    private static final int DEFAULT_MAX_RETRY_COUNT = 3;

    @Autowired
    private TaskService taskService;

    @Autowired
    private PadTaskMapper padTaskMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    public TaskRetry createTaskRetry(TaskRetry taskRetry) {
        // 设置默认值
        if (taskRetry.getRetryCount() == null) {
            taskRetry.setRetryCount(0);
        }
        
        if (taskRetry.getMaxRetryCount() == null) {
            taskRetry.setMaxRetryCount(DEFAULT_MAX_RETRY_COUNT);
        }
        
        if (taskRetry.getRetryStatus() == null) {
            taskRetry.setRetryStatus(RetryStatus.PENDING.getCode());
        }
        
        // 计算首次重试时间（默认15秒后）
        if (taskRetry.getNextRetryTime() == null) {
            taskRetry.setNextRetryTime(calculateNextRetryTime(0));
        }
        
        // 保存记录
        boolean saved = this.save(taskRetry);
        if (saved) {
            log.info("Created task retry record: {}", taskRetry);
            return taskRetry;
        }
        return null;
    }

    @Override
    public int cancelPendingRetryByPadCodeAndTaskType(String padCode, Integer taskType) {
        LambdaUpdateWrapper<TaskRetry> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskRetry::getPadCode, padCode)
                .eq(TaskRetry::getTaskType, taskType)
                .eq(TaskRetry::getRetryStatus, RetryStatus.PENDING.getCode())
                .set(TaskRetry::getRetryStatus, RetryStatus.CANCELED.getCode());
                
        int count = this.baseMapper.update(null, updateWrapper);
        if (count > 0) {
            log.info("Canceled {} pending retry tasks for padCode: {}, taskType: {}", count, padCode, taskType);
        }
        return count;
    }

    @Override
    public List<TaskRetry> getPendingRetryTasks(int limit) {
        LambdaQueryWrapper<TaskRetry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskRetry::getRetryStatus, RetryStatus.PENDING.getCode())
                .le(TaskRetry::getNextRetryTime, new Date())
                .orderByAsc(TaskRetry::getNextRetryTime)
                .last("LIMIT " + limit);
        
        return this.list(queryWrapper);
    }

    @Override
    public boolean updateRetryStatus(Long id, Integer status) {
        LambdaUpdateWrapper<TaskRetry> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskRetry::getId, id)
                .set(TaskRetry::getRetryStatus, status);
        
        boolean result = this.update(updateWrapper);
        if (result) {
            log.info("Updated task retry status for ID {}: {}", id, status);
        }
        return result;
    }

    @Override
    public boolean updateRetryCountAndNextTime(Long id, Integer retryCount, Date nextRetryTime) {
        LambdaUpdateWrapper<TaskRetry> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TaskRetry::getId, id)
                .set(TaskRetry::getRetryCount, retryCount)
                .set(TaskRetry::getNextRetryTime, nextRetryTime);
        
        boolean result = this.update(updateWrapper);
        if (result) {
            log.info("Updated task retry count to {} and next time to {} for ID {}", retryCount, nextRetryTime, id);
        }
        return result;
    }

    @Override
    public TaskRetry hasPendingTask(String padCode,Integer taskType) {
        LambdaQueryWrapper<TaskRetry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskRetry::getPadCode, padCode)
                .eq(TaskRetry::getTaskType, taskType)
                .eq(TaskRetry::getRetryStatus, RetryStatus.PENDING.getCode())
                .last("limit 1");
        return this.getOne(queryWrapper);
    }
    
    /**
     * 计算下一次重试时间（指数退避策略）
     * 重试间隔为： 60 秒，120 秒，240 秒...
     *
     * @param retryCount 当前重试次数
     * @return 下一次重试时间
     */
    public Date calculateNextRetryTime(int retryCount) {
        Calendar calendar = Calendar.getInstance();
        
        // 基础延迟时间为60秒
        int baseDelaySeconds = 60;
        
        // 计算当前重试延迟（2^retryCount * baseDelaySeconds）
        int delaySeconds = (int) (Math.pow(2, retryCount) * baseDelaySeconds);
        
        // 最大延迟不超过10分钟
        delaySeconds = Math.min(delaySeconds, 600);
        
        calendar.add(Calendar.SECOND, delaySeconds);
        return calendar.getTime();
    }

    /**
     * 重试任务
     *
     * @param retry 任务重试记录
     * @return 是否重试成功
     */
    public boolean retryTask(TaskRetry retry) {
        try {
            Task masterTask = taskService.getById(retry.getMasterTaskId());
            PadTask padTask = padTaskMapper.getById(retry.getSubTaskId());
            
            if (masterTask == null || padTask == null) {
                log.warn("Original task not found for retry: masterTaskId={}, subTaskId={}", 
                        retry.getMasterTaskId(), retry.getSubTaskId());
                return false;
            }
            
            // 获取任务类型信息
            TaskTypeAndChannelEnum taskTypeEnum = TaskTypeAndChannelEnum.fromCode(retry.getTaskType());
            if (taskTypeEnum == null) {
                log.warn("Task type not found for retry: taskType={}", retry.getTaskType());
                return false;
            }
            
            try {
                // 从任务参数中解析出重试所需参数
                String taskParam = retry.getTaskParam();
                if (taskParam == null) {
                    log.warn("Task param is null for retry: taskId={}", retry.getSubTaskId());
                    return false;
                }
                
                JSONObject paramJson = JSON.parseObject(taskParam);
                
                // 获取通知参数
                String taskChannel = paramJson.getString("taskChannel");
                String clusterCode = paramJson.getString("clusterCode");
                String noticeKey = paramJson.getString("noticeKey");
                Object paramObj = paramJson.get("paramObj");
                
                // 如果通知参数中没有设置，则使用默认值
                if (taskChannel == null) {
                    taskChannel = taskTypeEnum.getChannel();
                }
                
                // 如果还是没有通知key，使用padCode
                if (noticeKey == null && retry.getPadCode() != null) {
                    noticeKey = retry.getPadCode();
                }

                TaskQueueManager taskQueueManager = applicationContext.getBean(TaskQueueManager.class);
                // 调用通知方法重试任务
                Boolean noticeStatus = taskQueueManager.edgeEventNotice(
                    taskChannel, 
                    clusterCode, 
                    noticeKey, 
                    paramObj
                );
                
                if (noticeStatus != null && noticeStatus) {
                    // 基于任务类型决定设置为成功或执行中
                    int newStatus;
                    
                    // 检查任务类型是否需要设置为执行中
                    if (Boolean.TRUE.equals(taskTypeEnum.getNeedWaitCallback())) {
                        // 需要等待回调的任务设置为执行中
                        newStatus = TaskStatusConstants.EXECUTING.getStatus();
                        log.info("Task retry succeeded and set to EXECUTING (waiting for callback): taskId={}", padTask.getId());
                    } else {
                        // 不需要等待回调的任务设置为成功
                        newStatus = TaskStatusConstants.SUCCESS.getStatus();
                        log.info("Task retry succeeded and set to SUCCESS: taskId={}", padTask.getId());
                    }
                    
                    // 更新任务状态
                    padTaskMapper.updateStatusAndTimeById(padTask.getId(), newStatus, new Date());
                    return true;
                } else {
                    // 重试失败，保持待执行状态
                    padTaskMapper.updateStatusAndTimeById(padTask.getId(), TaskStatusConstants.WAIT_EXECUTE.getStatus(), new Date());
                    return false;
                }
            } catch (Exception e) {
                log.error("Error parsing task param or notifying edge for task retry: taskId={}", retry.getSubTaskId(), e);
                return false;
            }
        } catch (Exception e) {
            log.error("Task retry execution failed for ID {}", retry.getId(), e);
            return false;
        }
    }


    /**
     * 处理待重试任务
     */
    @Override
    public void processRetryTasks() {
        log.info("Exec processRetryTasks current time:{}", System.currentTimeMillis());
        // 一次最多处理20个重试任务
        int batchSize = 20;
        List<TaskRetry> pendingRetries = this.getPendingRetryTasks(batchSize);
        
        if (pendingRetries.isEmpty()) {
//            log.info("No pending retry tasks found");
            return;
        }
        
        log.info("Found {} pending retry tasks", pendingRetries.size());
        
        // 按动作类型分组处理
        List<TaskRetry> notifyRetries = new ArrayList<>();
        List<TaskRetry> ackRetries = new ArrayList<>();
        
        // 将任务按action分组
        for (TaskRetry retry : pendingRetries) {
            String action = retry.getAction();
            if ("notify".equals(action)) {
                notifyRetries.add(retry);
            } else if ("ack".equals(action)) {
                ackRetries.add(retry);
            } else {
                // 未指定action
                notifyRetries.add(retry);
            }
        }
        
        // 处理notify类型的重试任务
        processNotifyRetryTasks(notifyRetries);
        
        // 处理ack类型的重试任务
        processAckRetryTasks(ackRetries);
    }
    
    /**
     * 处理notify类型的重试任务
     */
    private void processNotifyRetryTasks(List<TaskRetry> retries) {
        if (CollectionUtils.isEmpty(retries)) {
            return;
        }
        
        log.info("Processing {} notify retry tasks", retries.size());
        
        for (TaskRetry retry : retries) {
            try {
                // 检查是否超过最大重试次数
                if (retry.getRetryCount() >= retry.getMaxRetryCount()) {
                    log.info("Task retry {} exceeded max retry count: {}/{}", 
                            retry.getId(), retry.getRetryCount(), retry.getMaxRetryCount());
                    this.updateRetryStatus(retry.getId(), RetryStatus.FAILED.getCode());
                    continue;
                }
                
                // 执行重试
                boolean retrySuccess = this.retryTask(retry);
                
                if (retrySuccess) {
                    // 重试成功，更新状态
                    this.updateRetryStatus(retry.getId(), RetryStatus.SUCCESS.getCode());
                    log.info("Task retry succeeded: {}", retry.getId());
                    
                    // notify成功后，需要创建ack重试任务
                    createAckRetryTask(retry);
                } else {
                    // 重试失败，增加重试次数并计算下一次重试时间
                    int newRetryCount = retry.getRetryCount() + 1;
                    Date nextRetryTime = this.calculateNextRetryTime(newRetryCount);
                    
                    this.updateRetryCountAndNextTime(retry.getId(), newRetryCount, nextRetryTime);
                    log.info("Task retry failed, scheduled next retry at {}: {}", nextRetryTime, retry.getId());
                }
            } catch (Exception e) {
                log.error("Error processing task retry {}", retry.getId(), e);
            }
        }
    }
    
    /**
     * 处理ack类型的重试任务
     */
    private void processAckRetryTasks(List<TaskRetry> retries) {
        if (CollectionUtils.isEmpty(retries)) {
            return;
        }
        
        log.info("Processing {} ack retry tasks", retries.size());
        
        for (TaskRetry retry : retries) {
            try {
                // 检查是否超过最大重试次数
                if (retry.getRetryCount() >= retry.getMaxRetryCount()) {
                    log.info("Ack task retry {} exceeded max retry count: {}/{}", 
                            retry.getId(), retry.getRetryCount(), retry.getMaxRetryCount());
                    this.updateRetryStatus(retry.getId(), RetryStatus.FAILED.getCode());
                    continue;
                }
                
                // 对于ACK检查，需要查询原任务的状态
                Task masterTask = taskService.getById(retry.getMasterTaskId());
                
                // 检查是否有需要ACK的任务（类型为1206、状态为2、结果为空的任务）
                boolean needAck = masterTask != null &&
                        Objects.nonNull(padTaskMapper.getEmptyResultTaskByTypeAndStatus(retry.getSubTaskId(), 1206, TaskStatusConstants.EXECUTING.getStatus()));
                
                if (needAck) {
                    // 需要再次发送通知
                    boolean retrySuccess = this.retryTask(retry);
                    
                    if (retrySuccess) {
                        // ACK发送成功，等待下次检查结果
                        int newRetryCount = retry.getRetryCount() + 1;
                        Date nextRetryTime = this.calculateNextRetryTime(newRetryCount);
                        
                        this.updateRetryCountAndNextTime(retry.getId(), newRetryCount, nextRetryTime);
                        log.info("Ack task retry sent successfully, scheduled next check at {}: {}", nextRetryTime, retry.getId());
                    } else {
                        // 发送失败，增加重试次数并计算下一次重试时间
                        int newRetryCount = retry.getRetryCount() + 1;
                        Date nextRetryTime = this.calculateNextRetryTime(newRetryCount);
                        
                        this.updateRetryCountAndNextTime(retry.getId(), newRetryCount, nextRetryTime);
                        log.info("Ack task retry failed, scheduled next retry at {}: {}", nextRetryTime, retry.getId());
                    }
                } else {
                    // 任务已经有结果或状态已变更，ACK完成
                    this.updateRetryStatus(retry.getId(), RetryStatus.SUCCESS.getCode());
                    log.info("Ack task already completed or has result: {}", retry.getId());
                }
            } catch (Exception e) {
                log.error("Error processing ack task retry {}", retry.getId(), e);
            }
        }
    }
    
    /**
     * 为通知成功的任务创建ACK检查任务
     */
    private void createAckRetryTask(TaskRetry notifyRetry) {
        try {
            // 原始任务信息
            TaskRetry ackRetry = new TaskRetry();
            ackRetry.setMasterTaskId(notifyRetry.getMasterTaskId());
            ackRetry.setSubTaskId(notifyRetry.getSubTaskId());
            ackRetry.setPadCode(notifyRetry.getPadCode());
            ackRetry.setTaskType(notifyRetry.getTaskType());
            ackRetry.setTaskParam(notifyRetry.getTaskParam());
            ackRetry.setAction("ack");
            
            // 创建ACK检查任务
            this.createTaskRetry(ackRetry);
            log.info("Created ack check task for notify task: {}", notifyRetry.getId());
        } catch (Exception e) {
            log.error("Error creating ack retry task for notify task: {}", notifyRetry.getId(), e);
        }
    }
}