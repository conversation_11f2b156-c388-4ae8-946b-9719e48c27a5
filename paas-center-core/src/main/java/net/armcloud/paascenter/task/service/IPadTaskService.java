package net.armcloud.paascenter.task.service;

import net.armcloud.paascenter.common.client.internal.dto.DelPadBackupDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.GetLatestPadDataDTO;
import net.armcloud.paascenter.common.client.internal.dto.InstructionPadCodeDTO;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.DataDelDTO;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.task.model.vo.PadEdgeVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface IPadTaskService {

    /**
     * 根据唯一id获取实例任务
     * @param uniqueId
     * @return
     */
    PadTaskVO getPodTaskByUniqueId(String uniqueId);


    /**
     * 根据主键ID
     * @param subTaskId
     * @return
     */
    PadTaskVO getSubTaskId(Long subTaskId);

    /**
     * 添加错误信息
     * @param cmdRecords
     * @return
     */
    Object updateTaskMsg(List<CmdRecord> cmdRecords);

    PadBackupTaskInfo getCustomerLatestPadBackupData(GetLatestPadDataDTO dto);

    /**
     * 删除备份记录
     * @param dto
     * @return
     */
    List<DataDelDTO> delPadBackupData(DelPadBackupDataDTO dto);

    List<String>  existInstructionPadCode(@RequestBody InstructionPadCodeDTO dto);

    List<PadEdgeVO> queryPadClusterInfo(List<String> padCodes);
}
