package net.armcloud.paascenter.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务重试状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RetryStatus {
    
    /**
     * 待重试
     */
    PENDING(0, "待重试"),

    /**
     * 重试成功
     */
    SUCCESS(1, "重试成功"),

    /**
     * 重试失败
     */
    FAILED(2, "重试失败"),

    /**
     * 已取消
     */
    CANCELED(3, "已取消");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 通过状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举实例
     */
    public static RetryStatus fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RetryStatus status : RetryStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 