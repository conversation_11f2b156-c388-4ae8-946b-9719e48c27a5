package net.armcloud.paascenter.task.manager.executor.impl.netpadv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.manager.cbs.model.bo.CreatePadBO;
import net.armcloud.paascenter.cms.model.request.ContainerNetworkDTO;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.core.constant.container.TaskTypeEnum;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.paas.AdiCertificateRepository;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.PadBootOnRequestDTO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2Service;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-07
 * @description 网存实例开机 V2版本
 */
@Slf4j
@Component
public class NetPadV2BootOnTaskExecuteHandler implements ITaskParamExecutorStrategy {

    @Resource
    public InstanceDetailImageSuccService instanceDetailImageSuccService;
    @Autowired
    private NetPadV2Service netPadV2Service;

    @Override
    public Object execute(TaskQueue padTask) {
        log.info("NetPadV2BootOnTaskExecuteHandler_execute_start, padTask:{}", JSONObject.toJSONString(padTask));
        JSONObject param = netPadV2Service.getTaskRequest(padTask.getKey(), TaskTypeConstants.NET_PAD_ON);
        if(param == null){
            throw new BasicException("实例开机参数不存在");
        }
        log.info("NetPadV2BootOnTaskExecuteHandler_execute_end, result_param:{}", JSON.toJSONString(param));
        return param;
    }

    public NetPadV2BootOnTaskExecuteHandler() {
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.NET_PAD_ON.getType(), "netPadV2BootOnTaskExecuteHandler");
    }
}
