package net.armcloud.paascenter.task.manager.executor.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceRestoreBackupDataRequest;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.PadRestoreTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.job.mapper.PadTaskBackupMapper;
import net.armcloud.paascenter.job.mapper.PadTaskRestoreMapper;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 备份数据还原
 */
@Slf4j
@Component
public class PadRestoreTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final DeviceMapper deviceMapper;
    private final PadTaskBackupMapper padTaskBackupMapper;
    private final PadTaskRestoreMapper padTaskRestoreMapper;

    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        long subTaskId = taskQueue.getSubTaskId();
        PadRestoreTaskInfo padRestoreTaskInfo = padTaskRestoreMapper.getLatestByPadCodeAndSubTaskId(padCode, subTaskId);
        if (padRestoreTaskInfo == null) {
            return "not found pad restore task info";
        }
        String deviceIp = deviceMapper.selectDeviceIpByPadCode(padCode);
        PadBackupTaskInfo padBackupTaskInfo = padTaskBackupMapper.getById(padRestoreTaskInfo.getBackupId());

        InstanceRestoreBackupDataRequest req = new InstanceRestoreBackupDataRequest();
        InstanceRestoreBackupDataRequest.Instance instance = new InstanceRestoreBackupDataRequest.Instance();
        instance.setPadCode(padCode);
        instance.setDeviceIp(deviceIp);
        instance.setRestoreFilePath(padBackupTaskInfo.getPath());
        List<InstanceRestoreBackupDataRequest.Instance> instances = new ArrayList<>();
        instances.add(instance);
        req.setInstances(instances);
        return req;
    }

    public PadRestoreTaskExecutorStrategy(DeviceMapper deviceMapper, PadTaskRestoreMapper padTaskRestoreMapper, PadTaskBackupMapper padTaskBackupMapper) {
        this.deviceMapper = deviceMapper;
        this.padTaskRestoreMapper = padTaskRestoreMapper;
        this.padTaskBackupMapper = padTaskBackupMapper;

        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.RESTORE_PAD.getType(), "padRestoreTaskExecutorStrategy");
    }
}
