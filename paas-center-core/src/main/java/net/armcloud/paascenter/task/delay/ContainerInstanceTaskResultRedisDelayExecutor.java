package net.armcloud.paascenter.task.delay;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.dto.api.ContainerInstanceTaskResultDTO;
import net.armcloud.paascenter.common.model.mq.container.ContainerInstanceTaskResultMQ;
import net.armcloud.paascenter.common.redis.delay.RedisDelayTaskExecutor;
import net.armcloud.paascenter.task.service.ITaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 容器实例任务结果Redis延时执行器
 * 处理延时的容器实例任务结果更新
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Slf4j
@Component("containerInstanceTaskResultRedisDelayExecutor")
public class ContainerInstanceTaskResultRedisDelayExecutor implements RedisDelayTaskExecutor {
    
    @Resource
    private ITaskService taskService;
    
    @Override
    public void execute(String taskData) {
        try {
            log.info("ContainerInstanceTaskResultRedisDelayExecutor executing task: {}", taskData);
            
            // 解析任务数据
            ContainerInstanceTaskResultDTO updateDcImageStatusDTO = JSON.parseObject(taskData, ContainerInstanceTaskResultDTO.class);
            if (updateDcImageStatusDTO == null) {
                log.error("Failed to parse ContainerInstanceTaskResultMQ from taskData: {}", taskData);
                return;
            }
            
            // 执行任务结果更新
            taskService.updateContainerInstanceTaskResult(updateDcImageStatusDTO);
            
            log.info("ContainerInstanceTaskResultRedisDelayExecutor completed task for padCode: {}, masterTaskId: {}",
                    updateDcImageStatusDTO.getPadCode(), updateDcImageStatusDTO.getMasterTaskId());
                    
        } catch (Exception e) {
            log.error("ContainerInstanceTaskResultRedisDelayExecutor execution failed for taskData: {}", taskData, e);
            // 这里可以根据需要添加重试逻辑或者错误处理
        }
    }
    
    @Override
    public String getExecutorName() {
        return "containerInstanceTaskResultRedisDelayExecutor";
    }
}
