package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ContainerNetSyncDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.dto.api.NetPadSyncDTO;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.Objects;

/**
 * 网存同步备份
 * @Description: 实现网存同步备份任务执行器策略
 */
@Slf4j
@Component
public class PadNetSyncBackupTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        String contentJson = padTask.getContentJson();
        NetPadSyncDTO pad = JSON.parseObject(contentJson, NetPadSyncDTO.class);
        if(Objects.isNull(pad)){
            log.error("PadNetSyncBackupTaskExecutorStrategy.execute run pad is empty.");
            return StringUtils.EMPTY;
        }
        // 构建网存同步备份参数
        log.info("PadNetSyncBackupTaskExecutorStrategy.execute pad:{}", JSON.toJSONString(pad));
        ContainerNetSyncDTO containerNetSyncDTO = new ContainerNetSyncDTO();
        containerNetSyncDTO.setTaskId(padTask.getSubTaskId());
        containerNetSyncDTO.setTaskType(padTask.getTaskType());
        containerNetSyncDTO.setCreateTime(padTask.getCreateTime());
        ContainerNetSyncDTO.SyncTaskParam syncTaskParam = new ContainerNetSyncDTO.SyncTaskParam();
        syncTaskParam.setContainerName(padCode);
        syncTaskParam.setStorageId(pad.getNetStorageResId());
        containerNetSyncDTO.setTaskParam(syncTaskParam);
        log.info("PadNetSyncBackupTaskExecutorStrategy.execute ContainerNetSyncDTO:{}", JSON.toJSONString(containerNetSyncDTO));
        return containerNetSyncDTO;
    }

    public PadNetSyncBackupTaskExecutorStrategy() {
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.NET_SYNC_BACKUP.getType(), "padNetSyncBackupTaskExecutorStrategy");
    }
} 