package net.armcloud.paascenter.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.task.DeviceTask;
import net.armcloud.paascenter.task.annotation.CalculateTaskTimeout;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface DeviceTaskMapper extends BaseMapper<DeviceTask> {
    @CalculateTaskTimeout
    int insertBatch(@Param("deviceTasks") List<DeviceTask> deviceTasks);

    void batchUpdateUniqueId(@Param("list") List<DeviceTask> deviceTasks);

    DeviceTask getLastestTask(String padCode);

    List<DeviceTask> listByTaskId(long masterTaskId);

    int insertBatchDefaultTimeout(@Param("deviceTasks") List<DeviceTask> subTaskList);

    @CalculateTaskTimeout
    int updateSetTimeout(DeviceTask deviceTask);

    int updateDeviceTask(@Param("bmcTaskId") Long bmcTaskId,
                         @Param("customerTaskId") Long customerTaskId,
                         @Param("type") Integer type
    );

    DeviceTask getById(@Param("id") long id);

    int updateStatus(@Param("id") long id,@Param("status") Integer status,@Param("timeoutTime")Date timeoutTime);

    int pullModeUpdateTimeout(@Param("id") Long id, @Param("timeoutTime") Date timeoutTime, @Param("errorMsg") String errorMsg);

    List<String> countRunDeviceTaskByType(@Param("deviceCodes") List<String> deviceCodes,@Param("type") Integer type);

    List<Long> selectTheTypeDoingDeviceTask(@Param("padCode") String padCode,@Param("types") List<Integer> types);
}
