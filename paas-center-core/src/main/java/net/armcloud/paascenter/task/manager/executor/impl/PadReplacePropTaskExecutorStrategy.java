package net.armcloud.paascenter.task.manager.executor.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceReplacePropRequest;
import net.armcloud.paascenter.cms.service.InstanceDetailImageSuccService;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.bo.task.quque.PadUpdateAdiTaskQueueBO;
import net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.utils.MACUtils;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static net.armcloud.paascenter.task.manager.executor.impl.PadUpgradeImageTaskExecutorStrategy.buildPadOldParam;


/**
 * 一键新机
 */
@Component
@Slf4j
public class PadReplacePropTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    private final InstanceDetailImageSuccService instanceDetailImageSuccService;
    private final ITaskService taskService;

    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        PadEdgeClusterVO padEdgeClusterInfo = padMapper.getPadEdgeClusterInfoByPadCode(padCode);
        if (Objects.isNull(padEdgeClusterInfo)){
            return StringUtils.EMPTY;
        }
        String deviceIp = padEdgeClusterInfo.getDeviceIp();
        InstanceReplacePropRequest.Instance instance = new InstanceReplacePropRequest.Instance();
        instance.setDeviceIp(deviceIp);
        instance.setPadCode(padEdgeClusterInfo.getPadCode());
        String taskContent = padTask.getContentJson();

        PadUpdateAdiTaskQueueBO task = JSON.parseObject(taskContent, PadUpdateAdiTaskQueueBO.class);
        instance.setDeviceAndroidProps(task.getAndroidProp());
        //随机设置一个mac地址
        instance.setMac(MACUtils.generateMacAddress());
        InstanceReplacePropRequest.Instance.ADI adi = new InstanceReplacePropRequest.Instance.ADI();
        instance.setIsReal(task.getIsReal());
        adi.setTemplatePassword(task.getAdiPassword());
        adi.setAndroidCertData(task.getAdiCertificateRepository());
        adi.setTemplateUrl(task.getAdiUrl());
        adi.setLayoutDpi(task.getLayoutDpi());
        adi.setLayoutFps(task.getLayoutFps());
        adi.setLayoutHigh(task.getLayoutHigh());
        adi.setLayoutWidth(task.getLayoutWidth());
        adi.setRealPhoneTemplateId(task.getRealPhoneTemplateId());
        instance.setAdi(adi);

        TaskRelInstanceDetail taskRelInstanceDetail = instanceDetailImageSuccService.getLastInfo(padCode);
        instance.setOldParam(buildPadOldParam(taskRelInstanceDetail));

        InstanceReplacePropRequest request = new InstanceReplacePropRequest();
        request.setInstances(instance);

        //记录task_rel_instance_detail表
        taskService.saveDeviceInstanceSingle(padTask.getMasterTaskId(),padTask.getSubTaskId(), TaskTypeAndChannelEnum.REPLACE_PAD.getCbsTaskTypeEnum(),taskRelInstanceDetail,request);
        return request;
    }


    public PadReplacePropTaskExecutorStrategy(PadMapper padMapper,InstanceDetailImageSuccService instanceDetailImageSuccService,ITaskService taskService) {
        this.padMapper = padMapper;
        this.instanceDetailImageSuccService = instanceDetailImageSuccService;
        this.taskService = taskService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.REPLACE_PAD.getType(), "padReplacePropTaskExecutorStrategy");
    }
}
