package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ContainerNetShutdownDTO;
import net.armcloud.paascenter.cms.model.request.PadPowerOffForceDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/3/29 15:27
 * @Description:
 */
@Slf4j
@Component
public class PadNetWorkForcePowerOffTaskExecuteHandler implements ITaskParamExecutorStrategy {
    @Resource
    private PadMapper padMapper;
    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        Pad pad = padMapper.selectPadByPadCode(padCode);
        if (Objects.isNull(pad)) {
            log.error("PadNetWorkForcePowerOffTaskExecuteHandler_execute_pad is null, padCode:{}", padCode);
            return null;
        }
        PadPowerOffForceDTO powerOffForceDTO = new PadPowerOffForceDTO();
        powerOffForceDTO.setPadCode(padCode);
        powerOffForceDTO.setStorageId(pad.getNetStorageResId());
        return powerOffForceDTO;
    }
    public PadNetWorkForcePowerOffTaskExecuteHandler() {
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.FORCE_POWER_OFF.getType(), "padNetWorkForcePowerOffTaskExecuteHandler");
    }
}
