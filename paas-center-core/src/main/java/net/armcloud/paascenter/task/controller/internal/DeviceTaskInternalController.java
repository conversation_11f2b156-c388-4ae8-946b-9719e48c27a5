package net.armcloud.paascenter.task.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.CallbackDeviceTakes;
import net.armcloud.paascenter.common.client.internal.facade.DeviceTaskInternalFacade;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.task.service.IDeviceTaskService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class DeviceTaskInternalController implements DeviceTaskInternalFacade {
    @Resource
    private IDeviceTaskService deviceTaskService;

    @Override
    public Result<?> callbackDeviceTakes(CallbackDeviceTakes param) {
        deviceTaskService.callbackDeviceTakes(param);
        return Result.ok();
    }
}
