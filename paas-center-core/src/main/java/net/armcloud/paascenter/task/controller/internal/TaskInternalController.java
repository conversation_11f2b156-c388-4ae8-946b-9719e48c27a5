package net.armcloud.paascenter.task.controller.internal;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.facade.TaskInternalFacade;
import net.armcloud.paascenter.common.client.internal.vo.AddDeviceTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.AddPadTaskVO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import net.armcloud.paascenter.common.model.vo.api.*;
import net.armcloud.paascenter.common.model.vo.task.PadTaskCallbackVO;
import net.armcloud.paascenter.common.model.vo.task.UpdatePadResetAndRestartVO;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.INetSyncService;
import net.armcloud.paascenter.task.config.PullModeConfigHolder;
import net.armcloud.paascenter.task.config.PullModeImageConfig;
import net.armcloud.paascenter.task.enums.TaskChannelEnum;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.service.IPadTaskService;
import net.armcloud.paascenter.task.service.ITaskRetryService;
import net.armcloud.paascenter.task.service.ITaskService;
import net.armcloud.paascenter.task.service.impl.ImageTaskServerImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class TaskInternalController implements TaskInternalFacade {
    @Resource
    private ITaskService taskService;
    @Resource
    private ImageTaskServerImpl imageTaskServer;
    @Resource
    private IPadTaskService padTaskService;
    @Resource
    private PadMapper padMapper;
    @Resource
    private ITaskRetryService taskRetryService;
    @Resource
    private INetSyncService netSyncService;

    @Override
    public Result<List<AddPadTaskVO>> addPadTask(@Valid @RequestBody AddPadTaskDTO addTaskDTO) {
        List<AddPadTaskVO> list = new ArrayList<>();
        List<Pad> pads = padMapper.selectPadByPadCodes(addTaskDTO.getPadCodes());
        if(CollUtil.isNotEmpty(pads)){
            TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(addTaskDTO.getType());
            //只有gs任务区分任务类型
            Boolean isGsTask = taskTypeAndChannelEnum!=null && TaskChannelEnum.GAMESERVER.getCode().equals(taskTypeAndChannelEnum.getChannel());
            //拉任务 实例
            List<String> pullPadCodes = new ArrayList<>();
            //推任务 实例
            List<String> pushPadCodes = new ArrayList<>();
            for(Pad pad : pads){
                if((pad.getTaskMode() != null && pad.getTaskMode() == 1) || !isGsTask){
                    pullPadCodes.add(pad.getPadCode());
                }else{
                    pushPadCodes.add(pad.getPadCode());
                }
            }
            if(CollUtil.isNotEmpty(pullPadCodes)){
                addTaskDTO.setPadCodes(pullPadCodes);
                list = taskService.addPadTaskServicePullMode(addTaskDTO);
            }
            if(CollUtil.isNotEmpty(pushPadCodes)){
                //深拷贝
                AddPadTaskDTO pushAddPadTaskDTO = BeanUtil.copyProperties(addTaskDTO,AddPadTaskDTO.class);
                pushAddPadTaskDTO.setPadCodes(pushPadCodes);
                List<AddPadTaskVO> pushList = taskService.addPadTaskService(pushAddPadTaskDTO);
                if(CollUtil.isNotEmpty(pushList)){
                    list.addAll(pushList);
                }
            }
        }
        return Result.ok(list);
    }

    @Override
    public Result<List<AddDeviceTaskVO>> addDeviceTask(@Valid @RequestBody AddDeviceTaskDTO deviceTaskDTO) {
        return Result.ok(taskService.addDeviceTaskService(deviceTaskDTO));
    }

    @Override
    public Result<Boolean> updateTask(@Valid @RequestBody UpdateTaskDTO updateTaskDTO) {
        return Result.ok(taskService.updateTaskService(updateTaskDTO));
    }

    @Override
    public Result<Boolean> deleteTask(@Valid @RequestBody DeleteTaskDTO deleteTaskDTO) {
        return Result.ok(taskService.deleteTaskService(deleteTaskDTO));
    }

    @Override
    public Result<UpdatePadResetAndRestartVO> updatePadTask(@Valid @RequestBody UpdatePadTaskDTO updatePadTaskDTO) {
        return Result.ok(taskService.updatePadResetAndRestart(updatePadTaskDTO));
    }

    @Override
    public Result<?> updateSubTaskStatus(UpdateSubTaskDTO dto) {
        taskService.updateSubTaskStatus(dto);
        return Result.ok();
    }

    @Override
    public Result<List<TaskTimeoutConfig>> listTimeoutConfig() {
        return Result.ok(taskService.listTimeoutConfig());
    }

    @Override
    public Result<Task> getById(long id) {
        return Result.ok(taskService.getById(id));
    }

    @Override
    public Result<PadTaskCallbackVO> padTaskCallbackByCode(@Valid @RequestBody PadTaskDTO padTaskDTO) {
        return Result.ok(taskService.padTaskByCodeService(padTaskDTO.getPadCode()));
    }

    @Override
    public Result<?> updatePadTaskByWsConnected(UpdatePadTaskByWsDTO dto) {
        taskService.updatePadTaskByWsConnected(dto);
        return Result.ok();
    }

    @Override
    public Result<Object> updateDeviceTaskByWsConnected(UpdatePadTaskByWsDTO dto) {
        taskService.updateDeviceTaskByWsConnected(dto);
        return Result.ok();
    }

    @Override
    public Result<List<Integer>> addImageTasks(@Valid @RequestBody List<AddImageTaskDTO> addImageTaskDTOList) {
        return Result.ok(imageTaskServer.addImageTasks(addImageTaskDTOList));
    }

    @Override
    public Result<Task> updateContainerDeviceTaskResult(@RequestBody ContainerTaskResultVO dto) {
        return Result.ok(taskService.updateContainerDeviceTaskResult(dto));
    }

    @Override
    public Result<Boolean> updateDeviceTaskResult(@RequestBody List<AddDeviceTaskVO> deviceTasks) {
        return Result.ok(taskService.updateDeviceTaskResult(deviceTasks));
    }

    @Override
    public List<PadTask> selectTaskByTaskTypeAndTaskStatus(PadTaskAndStatusDTO padTaskAndStatusDTO) {
        return taskService.selectTaskByTaskTypeAndTaskStatus(padTaskAndStatusDTO.getPads(), padTaskAndStatusDTO.getStatus());
    }

    @Override
    public Result<Boolean> updateContainerInstanceTaskResult(ContainerInstanceTaskResultDTO dto) {
        return Result.ok(taskService.updateContainerInstanceTaskResult(dto));
    }

    @Override
    public void addDeviceTaskBmcTaskId(List<BmcTaskInfoVO> bmcTaskInfoVOs) {
        taskService.addDeviceTaskBmcTaskId(bmcTaskInfoVOs);
    }

    @Override
    public Result<List<PadTask>> addBackupTask(@RequestBody @Validated AddBackupTaskDTO param) {
        return Result.ok(taskService.addBackupTask(param));
    }

    @Override
    public Result<List<PadTask>> addRestoreTask(@RequestBody @Validated RestoreBackupTaskDTO param) {
        return Result.ok(taskService.addRestoreTask(param));
    }

    @Override
    public Result<PadBackupTaskInfo> getCustomerLatestPadBackupData(@RequestBody @Validated GetLatestPadDataDTO dto) {
        return Result.ok(padTaskService.getCustomerLatestPadBackupData(dto));
    }

    @Override
    public Result<List<DataDelDTO>> delPadBackupData(DelPadBackupDataDTO dto) {
        return Result.ok(padTaskService.delPadBackupData(dto));
    }

    @Override
    public Result<List<String>> existInstructionPadCode(InstructionPadCodeDTO dto) {
        return Result.ok(padTaskService.existInstructionPadCode(dto));
    }

    @Override
    public Result<Long> countDeviceTask(TaskTypeAndStatusDTO dto) {
        return Result.ok(taskService.countDeviceTask(dto));
    }

    @Override
    public Result<List<PadTaskViewVO>> padTaskDetailsService(TaskDetailsInfoDTO taskDetailsDTO) {
        return Result.ok(taskService.padTaskDetailsService(taskDetailsDTO));
    }

    @Override
    public Result<?> retryJob() {
        // 处理需要重试的任务
        taskRetryService.processRetryTasks();
        return Result.ok();
    }

    @Override
    public Result<?> pushTaskToEdge() {
        // 推送任务至Edge
        netSyncService.pushTaskToEdge();
        return Result.ok();
    }
}
