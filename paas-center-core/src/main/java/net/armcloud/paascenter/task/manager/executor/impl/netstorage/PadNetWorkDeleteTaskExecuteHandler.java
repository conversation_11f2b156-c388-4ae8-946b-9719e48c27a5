package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ContainerNetShutdownDTO;
import net.armcloud.paascenter.cms.model.request.InstanceRestartRequest;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 网存实例删除
 * <AUTHOR>
 * @Date 2025/3/29 15:27
 * @Description:
 */
@Slf4j
@Component
public class PadNetWorkDeleteTaskExecuteHandler implements ITaskParamExecutorStrategy {
    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        String contentJson = padTask.getContentJson();
        InstanceRestartRequest task = StringUtils.isBlank(contentJson) ? null
                : JSON.parseObject(contentJson, InstanceRestartRequest.class);

        Map<String, InstanceRestartRequest.Instance> padCodeToDeviceIpMap = task != null && task.getInstances() != null
                ? task.getInstances().stream()
                .collect(Collectors.toMap(
                        InstanceRestartRequest.Instance::getPadCode,
                        instance -> instance)) // 这里将值设为当前的 `instance`
                : Collections.emptyMap();
        ContainerNetShutdownDTO shutdownDTO = new ContainerNetShutdownDTO();
        shutdownDTO.setName(padCode);
        shutdownDTO.setStorageId(padCodeToDeviceIpMap.get(padCode).getNetStorageResId());
        shutdownDTO.setRemoveRBD(true);
        return shutdownDTO;
    }
    public PadNetWorkDeleteTaskExecuteHandler(NetStorageResUnitService netStorageResUnitService) {
//        this.netStorageResUnitService = netStorageResUnitService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CONTAINER_NET_STORAGE_DELETE.getType(), "padNetWorkDeleteTaskExecuteHandler");
    }
}
