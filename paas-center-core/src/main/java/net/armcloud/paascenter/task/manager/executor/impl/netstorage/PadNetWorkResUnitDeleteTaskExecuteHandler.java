package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ContainerNetShutdownDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.springframework.stereotype.Component;

/**
 * 网存存储删除
 * <AUTHOR>
 * @Date 2025/3/29 14:58
 * @Description:
 */
@Slf4j
@Component
public class PadNetWorkResUnitDeleteTaskExecuteHandler implements ITaskParamExecutorStrategy {
    private final NetStorageResUnitService netStorageResUnitService;

    @Override
    public Object execute(TaskQueue padTask) {
        String netResUnitCode = padTask.getKey();
        EdgeCluster edgeCluster = netStorageResUnitService.getEdgeClusterByCode(netResUnitCode);
        ContainerNetShutdownDTO containerNetShutdownDTO = new ContainerNetShutdownDTO();
        containerNetShutdownDTO.setRemoveRBD(true);
        containerNetShutdownDTO.setStorageId(netResUnitCode);
        return containerNetShutdownDTO;
    }
    public PadNetWorkResUnitDeleteTaskExecuteHandler(NetStorageResUnitService netStorageResUnitService) {
        this.netStorageResUnitService = netStorageResUnitService;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CONTAINER_NET_STORAGE_RES_UNIT_DELETE.getType(), "padNetWorkResUnitDeleteTaskExecuteHandler");
    }
}
