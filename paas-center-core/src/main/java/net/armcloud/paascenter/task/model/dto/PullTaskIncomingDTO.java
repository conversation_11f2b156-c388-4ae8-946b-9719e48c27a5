package net.armcloud.paascenter.task.model.dto;

import lombok.Data;

/**
 * 边缘机房时间通知请求对象
 */
@Data
public class PullTaskIncomingDTO {

    /**集群编号*/
    private String clusterCode;
    /**数据中心/机房编号*/
    private String dcCode;
    /**设备编号*/
    private String deviceCode;
    /**设备ip*/
    private String deviceIP;
    /**设备类型：GS、CBS、BMC*/
    private String deviceType;
    /**任务参数*/
    private Object data;
}
