package net.armcloud.paascenter.task.aop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.*;
import net.armcloud.paascenter.task.constants.TaskStatusConstants;
import net.armcloud.paascenter.task.manager.TaskTaskManager;
import net.armcloud.paascenter.task.mapper.DeviceTaskMapper;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.filecenter.mapper.FileUploadTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Aspect
@Component
public class CalculateTaskTimeoutAspect {
    private final TaskMapper taskMapper;
    private final Date defaultTimeOutTime;
    private final TaskTaskManager taskTaskManager;
    private final PadTaskMapper padTaskMapper;
    private final FileUploadTaskMapper fileUploadTaskMapper;
    private final DeviceTaskMapper deviceTaskMapper;

    @Pointcut(value = "@annotation(net.armcloud.paascenter.task.annotation.CalculateTaskTimeout)")
    private void aspectPointcut() {

    }

    @Before(value = "aspectPointcut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args.length < 1) {
            return;
        }

        Object obj = args[0];
        if ((obj instanceof FileTask) || (obj instanceof PadTask) || (obj instanceof DeviceTask)) {
            setTaskTimeoutTime(obj);
            return;
        }

        if ((obj instanceof Collection)) {
            Collection collection = (Collection) obj;
            collection.forEach(this::setTaskTimeoutTime);
        }
    }

    private void setTaskTimeoutTime(Object obj) {
        if ((obj instanceof FileTask)) {
            FileTask fileTask = (FileTask) obj;
            Long id = fileTask.getId();
            if (id == null) {
                fileTask.setTimeoutTime(getTimeoutTime(null, null));
                return;
            }

            long masterTaskId = fileUploadTaskMapper.getById(id).getTaskId();
            int taskType = taskMapper.selectById(masterTaskId).getType();
            fileTask.setTimeoutTime(getTimeoutTime(taskType, fileTask.getStatus()));
            return;
        }

        if ((obj instanceof PadTask)) {
            PadTask padTask = (PadTask) obj;
            Long id = padTask.getId();
            if (id == null) {
                if(padTask.getStatus() != null && TaskStatusConstants.EXECUTING.getStatus().equals(padTask.getStatus())){
                    return;
                }
                // 判断如果存在自定义超时时间则不更新超时时间
                if (Objects.nonNull(padTask.getCustomTimeout()) && Objects.nonNull(padTask.getTimeoutTime()) && TaskTypeConstants.isPadV2Task(padTask.getType())) {
                    // 网存实例2.0支持自定义超时时间
                    log.info("网存实例2.0支持自定义超时时间，不更新超时时间, padTask: {}", JSON.toJSONString(padTask));
                    return;
                }
                padTask.setTimeoutTime(getTimeoutTime(null, null));
                return;
            }
            PadTask padTaskFromDB = padTaskMapper.getById(id);
            long masterTaskId = padTaskFromDB.getTaskId();
            Task task = taskMapper.selectById(masterTaskId);
            int taskType = task.getType();
            if (Objects.nonNull(padTaskFromDB.getCustomTimeout()) && Objects.nonNull(padTaskFromDB.getTimeoutTime()) && TaskTypeConstants.isPadV2Task(taskType)) {
                // 网存实例2.0支持自定义超时时间
                log.info("网存实例2.0支持自定义超时时间，不更新超时时间, padTask: {}", JSON.toJSONString(padTask));
                return;
            }
            padTask.setTimeoutTime(getTimeoutTime(taskType, padTask.getStatus()));
            return;
        }

        if ((obj instanceof DeviceTask)) {
            DeviceTask deviceTask = (DeviceTask) obj;
            Long id = deviceTask.getId();
            if (id == null) {
                deviceTask.setTimeoutTime(getTimeoutTime(null, null));
                return;
            }

            long masterTaskId = deviceTaskMapper.getById(id).getTaskId();
            int taskType = taskMapper.selectById(masterTaskId).getType();
            deviceTask.setTimeoutTime(getTimeoutTime(taskType, deviceTask.getStatus()));
        }
    }

    private Date getTimeoutTime(Integer taskType, Integer status) {
        if (taskType == null) {
            return defaultTimeOutTime;
        }

        if (Objects.equals(TaskStatusConstants.WAIT_EXECUTE.getStatus(), status)) {
            return defaultTimeOutTime;
        }

        TaskTimeoutConfig taskTimeoutConfig = taskTaskManager.getCacheTaskTimeoutConfigByType(taskType);
        if (Objects.isNull(taskTimeoutConfig)) {
            return defaultTimeOutTime;
        }

        LocalDateTime timeoutTime = LocalDateTime.now().plusSeconds(taskTimeoutConfig.getTimeoutMillisecond() / 1000);
        return Date.from(timeoutTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public CalculateTaskTimeoutAspect(TaskTaskManager taskTaskManager, TaskMapper taskMapper, PadTaskMapper padTaskMapper, FileUploadTaskMapper fileUploadTaskMapper, DeviceTaskMapper deviceTaskMapper) {
        this.taskTaskManager = taskTaskManager;
        this.taskMapper = taskMapper;
        this.padTaskMapper = padTaskMapper;
        this.fileUploadTaskMapper = fileUploadTaskMapper;
        this.deviceTaskMapper = deviceTaskMapper;

        try {
            defaultTimeOutTime = new SimpleDateFormat("yyyy-MM-dd").parse("2038-01-01");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
