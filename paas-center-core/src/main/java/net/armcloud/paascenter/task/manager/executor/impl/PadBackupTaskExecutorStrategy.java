package net.armcloud.paascenter.task.manager.executor.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceBackupDataRequest;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO;
import net.armcloud.paascenter.job.mapper.PadTaskBackupMapper;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.mapper.EdgeClusterMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * 数据备份
 */
@Slf4j
@Component
public class PadBackupTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final DeviceMapper deviceMapper;
    private final EdgeClusterMapper edgeClusterMapper;
    private final PadTaskBackupMapper padTaskBackupMapper;

    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        log.info("runBackupData padCode:{}", padCode);
        List<String> padCodes = Collections.singletonList(padCode);
        List<EdgeClusterVO> clusterVOS = edgeClusterMapper.selectEdgeClusterByPadCodes(padCodes);
        if (CollectionUtils.isEmpty(clusterVOS)) {
            return "not found edge cluster";
        }

        long subTaskId = taskQueue.getSubTaskId();
        PadBackupTaskInfo padBackupTaskInfo = padTaskBackupMapper.getLatestByPadCodeAndSubTaskId(padCode, subTaskId);
        if (padBackupTaskInfo == null) {
            return "pad backup task info not found";
        }

        String deviceIp = deviceMapper.selectDeviceIpByPadCode(padCode);
        InstanceBackupDataRequest req = new InstanceBackupDataRequest();
        InstanceBackupDataRequest.Instance instance = new InstanceBackupDataRequest.Instance();
        instance.setPadCode(padCode);
        instance.setDeviceIp(deviceIp);
        List<InstanceBackupDataRequest.Instance> instances = new ArrayList<>();
        instances.add(instance);
        req.setInstances(instances);
        return req;
    }

    public PadBackupTaskExecutorStrategy(DeviceMapper deviceMapper, EdgeClusterMapper edgeClusterMapper, PadTaskBackupMapper padTaskBackupMapper) {
        this.deviceMapper = deviceMapper;
        this.edgeClusterMapper = edgeClusterMapper;
        this.padTaskBackupMapper = padTaskBackupMapper;

        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.BACKUP_PAD.getType(), "padBackupTaskExecutorStrategy");
    }
}
