package net.armcloud.paascenter.task.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.CallbackDeviceTakes;
import net.armcloud.paascenter.common.client.internal.dto.SendDeviceStatusDTO;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants;
import net.armcloud.paascenter.common.model.dto.api.DeviceNetworkDTO;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.entity.task.DeviceTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.task.manager.TaskTaskManager;
import net.armcloud.paascenter.task.mapper.DeviceTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import net.armcloud.paascenter.task.mapper.TaskTimeoutConfigMapper;
import net.armcloud.paascenter.task.service.IDeviceTaskService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.SET_GATEWAY;

@Slf4j
@Service
public class DeviceTaskServerImpl extends ServiceImpl<DeviceTaskMapper, DeviceTask> implements IDeviceTaskService {
    @Resource
    private TaskMapper taskMapper;
    @Resource
    private DeviceTaskMapper deviceTaskMapper;
    @Resource
    private TaskTaskManager taskTaskManager;
    @Resource
    private IDeviceService deviceService;
    @Resource
    private TaskTimeoutConfigMapper taskTimeoutConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callbackDeviceTakes(CallbackDeviceTakes param) {
        log.info(">>>>>>>>>>>>>>>>云机任务回调 param={}", param.toString());
        String taskId = param.getTaskId();
        Integer status = param.getStatus();
        // 查询执行中的任务
        DeviceTask deviceTask = deviceTaskMapper.selectOne(new QueryWrapper<DeviceTask>().eq("bmc_task_id", taskId).eq("status", EXECUTING.getStatus()));
        if (deviceTask == null) {
            log.warn(">>>>>>>>>>>>>>>>云机任务回调未找到对应任务 taskId={}", taskId);
            return;
        }

        log.info(">>>>>>>>>>>>>>>>云机任务 deviceTask={}", deviceTask.toString());
        // 跳过非执行中的任务
        if (!Objects.equals(deviceTask.getStatus(), EXECUTING.getStatus())) {
            return;
        }
        // 已过超时时间,由超时调度任务处理
        if (deviceTask.getTimeoutTime().before(new Date())) {
            return;
        }

        //修改物理机状态，发送物理机状态变更回调通知消息
        SendDeviceStatusDTO sendDeviceStatusDTO = new SendDeviceStatusDTO();
        sendDeviceStatusDTO.setDeviceCodes(Collections.singletonList(deviceTask.getDeviceCode()));
        sendDeviceStatusDTO.setDeviceStatus(NumberConsts.THREE.equals(status) ? DeviceStatusConstants.DEVICE_SUCCESS.getStatus() : status);
        sendDeviceStatusDTO.setCustomerId(deviceTask.getCustomerId());
        deviceService.updateDeviceStatusAndSendDeviceStatusCallback(sendDeviceStatusDTO.getDeviceCodes(), sendDeviceStatusDTO.getDeviceStatus(), sendDeviceStatusDTO.getCustomerId());

        setDeviceGateway(deviceTask);


        // 更新deviceTask状态
        deviceTask.setStatus(status);
        deviceTask.setEndTime(new Date());
        deviceTask.setUpdateTime(new Date());
        deviceTaskMapper.updateById(deviceTask);
        //刷新主任务状态
        taskTaskManager.refreshMasterTaskStatus(deviceTask.getTaskId());
        log.info(">>>>>>>>>>>>>>>>云机任务回调完成 taskId={}, status={}", taskId, status);
    }

    /**
     * 更新板卡任务状态
     * @param subTaskId 子任务id
     * @param status 任务状态
     */
    @Override
    public int updateDeviceTaskStatus(Long subTaskId, Integer status) {
        //获取当前任务的超时时间
        //查询主任务id
        DeviceTask deviceTask = deviceTaskMapper.selectById(subTaskId);
        if(deviceTask == null){
            return 0;
        }
        Task task = taskMapper.selectById(deviceTask.getTaskId());
        TaskTimeoutConfig taskTimeoutConfig = taskTimeoutConfigMapper.getByTaskType(task.getType());
        String nowTimeout = null;
        if(taskTimeoutConfig != null){
            nowTimeout = DateUtil.offsetSecond(DateUtil.date(), taskTimeoutConfig.getTimeoutMillisecond().intValue() / 1000).toString();
        }
        //更新deviceTask状态
        int count = deviceTaskMapper.updateStatus(subTaskId,status,DateUtil.parse(nowTimeout, "yyyy-MM-dd HH:mm:ss"));
        if(count > 0){
            //更新主任务状态
            taskMapper.updateStatusById(deviceTask.getTaskId(),status);
        }
        return count;
    }

    private void setDeviceGateway(DeviceTask deviceTask) {
        if (ObjectUtil.isNull(deviceTask.getTaskContent())) {
            return;
        }
        Task masterTask = taskMapper.selectById(deviceTask.getTaskId());
        if (!Objects.equals(SET_GATEWAY.getType(), masterTask.getType())) {
            return;
        }
        DeviceNetworkDTO deviceNetwork = JSON.parseObject(deviceTask.getTaskContent(), DeviceNetworkDTO.class);
        Device updateDevice = new Device();
        updateDevice.setGateway(deviceNetwork.getGateway());
        updateDevice.setDeviceCode(deviceTask.getDeviceCode());
        deviceService.updateDeviceByCode(updateDevice);
    }
}