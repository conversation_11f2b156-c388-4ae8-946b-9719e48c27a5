package net.armcloud.paascenter.task.manager.executor.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.commscenter.model.bo.CommsMessageBodyBO;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.SUB_TASK_ID;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.TASK_ID;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.DOWNLOAD_APP;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.DOWNLOAD_FILE;

/**
 * 构建gameserver任务参数
 */
@Slf4j
@Component
public class PadCmdTaskParamExecutorStrategy implements ITaskParamExecutorStrategy {

    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        PadCMDForwardDTO padCMDForwardDTO = JSON.parseObject(taskQueue.getContentJson(), PadCMDForwardDTO.class);

        // padInfos存了多个实例信息 只发送属于当前实例的数据
        PadCMDForwardDTO.PadInfoDTO padInfoDTO = padCMDForwardDTO.getPadInfos().stream()
                .filter(padInfo -> Objects.equals(padInfo.getPadCode(), padCode))
                .findFirst().orElse(new PadCMDForwardDTO.PadInfoDTO());
        padCMDForwardDTO.setPadInfos(Collections.singletonList(padInfoDTO));

        JSONObject dataJSONObject = JSONObject.from(padInfoDTO.getData());
        dataJSONObject.put(TASK_ID, taskQueue.getMasterTaskId());
        dataJSONObject.put(SUB_TASK_ID, taskQueue.getSubTaskId());
        padInfoDTO.setData(dataJSONObject);

        TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(taskQueue.getTaskType());
        CommsMessageBodyBO commsMessageBodyBO = new CommsMessageBodyBO();
        commsMessageBodyBO.setCommand(taskTypeAndChannelEnum.getCommsCommandEnum().getCommand());
        commsMessageBodyBO.setData(padInfoDTO.getData());
        return commsMessageBodyBO;
    }

    @PostConstruct
    public void addStrategyExecutor() {
        String thisName = "padCmdTaskParamExecutorStrategy";
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.EXECUTE_COMMAND.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(DOWNLOAD_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.UNINSTALL_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.STOP_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.RESTART_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.START_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.SCREENSHOT_LOCAL.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(DOWNLOAD_FILE.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.UPDATE_PAD_PROPERTIES.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.LIST_INSTALLED_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CLEAN_APP.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.APP_BLACK_LIST.getType(), thisName);
        //TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.LIMIT_BANDWIDTH.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GPS_INJECT_INFO.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.PAD_SET_NETWORK_PROXY.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GET_PAD_NETWORK_PROXY_INFO.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CHANGE_LANGUAGE.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CHANGE_TIME_ZONE.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.UPDATE_SIM.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CLEAR_APP_HOME.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.APP_WHITE_LIST.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.UPDATE_CONTACTS.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GS_PUSH_ARMCLOUD_FLOW.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GS_PUSH_VOLCANO_FLOW.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GS_JOIN_ARMCLOUD_SHARE_ROOM.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GS_JOIN_VOLCANO_SHARE_ROOM.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.GS_DESTROY_VOLCANO_ROOM.getType(), thisName);

        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.SIMULATE_TOUCH.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.SET_WIFI_LIST.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.ADD_PHONE_RECORD.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.SET_COMMIT_TEXT.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.RESET_GAID.getType(), thisName);
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.INJECTION_AUDIO.getType(), thisName);
    }
}