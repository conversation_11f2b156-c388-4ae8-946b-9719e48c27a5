package net.armcloud.paascenter.task.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskStatusConstants {
    /**
     * 全失败
     */
    FAIL_ALL( -1),

    /**
     * 部分失败
     */
    FAIL_PART( -2),

    /**
     * 取消
     */
    CANCEL( -3),

    /**
     * 超时
     */
    TIMEOUT( -4),

    /**
     * 异常
     */
    EXCEPTION( -5),

    /**
     * 等待执行
     */
    WAIT_EXECUTE( 1),

    /**
     * 执行中
     */
    EXECUTING( 2),

    /**
     * 完成
     */
    SUCCESS( 3);


    private final Integer status;
}
