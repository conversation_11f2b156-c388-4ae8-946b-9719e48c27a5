package net.armcloud.paascenter.task.config;


import lombok.extern.slf4j.Slf4j;

/**
 * 拉模式配置
 */
@Slf4j
public class PullModeConfigHolder {
    private static PullModeConfig pullModeConfig;

    public static void setPullModeConfig(PullModeConfig config) {
        pullModeConfig = config;
    }

    /**
     * 获取任务模式
     * @param customerId 客户ID
     * @param padCode 实例编号  实例任务就传实例编号
     * @param deviceCode 板卡编号  板卡编号就传板卡编号
     * @return
     */
    public static Boolean isPullMode(String customerId,String padCode,String deviceCode){
        if(pullModeConfig.pullModeOpen
                || pullModeConfig.pullModeCusIds.contains(customerId)
                || pullModeConfig.pullModePadCodes.contains(padCode)
                || pullModeConfig.pullModeDeviceCodes.contains(deviceCode)){
            return true;
        }
        return false;
    }
}
