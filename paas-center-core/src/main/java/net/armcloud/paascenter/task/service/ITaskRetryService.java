package net.armcloud.paascenter.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.task.TaskRetry;

import java.util.Date;
import java.util.List;

/**
 * 任务重试服务接口
 *
 * <AUTHOR>
 */
public interface ITaskRetryService extends IService<TaskRetry> {

    /**
     * 创建任务重试记录
     *
     * @param taskRetry 任务重试记录对象
     * @return 创建的任务重试记录
     */
    TaskRetry createTaskRetry(TaskRetry taskRetry);


    /**
     * 取消指定实例和任务类型的所有待重试任务
     *
     * @param padCode 实例编号
     * @param taskType 任务类型
     * @return 取消的任务数量
     */
    int cancelPendingRetryByPadCodeAndTaskType(String padCode, Integer taskType);

    /**
     * 获取指定数量的待重试任务
     *
     * @param limit 限制数量
     * @return 待重试任务列表
     */
    List<TaskRetry> getPendingRetryTasks(int limit);

    /**
     * 更新任务重试状态
     *
     * @param id 任务重试ID
     * @param status 新状态
     * @return 更新是否成功
     */
    boolean updateRetryStatus(Long id, Integer status);

    /**
     * 更新任务重试次数和下次重试时间
     *
     * @param id 任务重试ID
     * @param retryCount 新的重试次数
     * @param nextRetryTime 下次重试时间
     * @return 更新是否成功
     */
    boolean updateRetryCountAndNextTime(Long id, Integer retryCount, Date nextRetryTime);

    /**
     * 检查是否存在待重试的任务
     *
     * @param padCode 实例编号
     * @param taskType 任务类型
     * @return TaskRetry
     */
    TaskRetry hasPendingTask(String padCode, Integer taskType);
    
    /**
     * 处理所有待重试的任务
     * 查询待重试任务并执行重试逻辑
     */
    void processRetryTasks();
} 