package net.armcloud.paascenter.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.dto.api.TaskDTO;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.vo.api.TaskInfoVO;
import net.armcloud.paascenter.common.model.vo.task.PadTaskCallbackVO;
import net.armcloud.paascenter.job.dto.CleanTaskPendingDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface TaskMapper extends BaseMapper<Task> {

    /**
     * 新增任务
     *
     * @param task task
     * @return Task
     */
    int insertTask(Task task);

    void batchUpdateUniqueId(@Param("list") List<Task> list);

    Task getByUniqueIdAndCustomerId(@Param("uniqueId") String uniqueId, @Param("customerId") Long customerId);

    void batchInsert(@Param("list") List<Task> masters);

    /**
     * 根据padCode查询padTask 重启/重置 任务
     *
     * @param padCode padCode
     * @return PadTask
     */
    PadTaskCallbackVO selectPadTaskCallbackByCode(@Param("padCode") String padCode);

    Task getById(@Param("id") long id);

    /**
     * 查询任务ID是否为指定任务
     * @param customerId
     * @param taskType
     * @return
     */
    List<Long> hasTaskId(@Param("customerId") Long customerId,@Param("taskType") String taskType,@Param("ids") List<Long> ids);


    /**
     * 查询任务执行数量
     * @param taskType
     * @param taskStatusList
     * @return
     */
    long countDeviceTask(@Param("taskType") String taskType,@Param("taskStatusList") List<Integer> taskStatusList);

    int batchUpdateStatus(@Param("taskIds") List<Long> taskIds, @Param("status") int status);

    List<Task> listByIds(@Param("ids") List<Long> ids);

    @Update("update task set status = #{status} where id = #{id} ")
    void updateStatusById(@Param("id") long id, @Param("status") Integer status);

    int updateStatusIfChangedById(@Param("id") long id, @Param("status") Integer status);

    /**
     * 查询某个时间段之前的任务，只返回任务ID
     * @param date
     * @return
     */
    List<Long> selectIdByCreateTime(@Param("date") String date);

    /**
     * 根据ID批量删除任务
     * @param ids
     * @return
     */
    int delByIds(@Param("ids") List<Long> ids);


    List<CleanTaskPendingDTO>  getCleanTaskPending();
}
