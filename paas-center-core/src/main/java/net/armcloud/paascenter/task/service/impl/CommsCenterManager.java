package net.armcloud.paascenter.task.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.commscenter.service.CmdRecordService;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CommsCenterManager {
    private final CmdRecordService cmdRecordService;

    public void updateCommandRecord(long subTask, int status) {
        cmdRecordService.updateCommandRecordBySubTaskId(subTask, status);
    }

    public CommsCenterManager(CmdRecordService cmdRecordService) {
        this.cmdRecordService = cmdRecordService;
    }
}
