package net.armcloud.paascenter.task.manager;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import net.armcloud.paascenter.common.model.entity.file.FileCustomer;
import net.armcloud.paascenter.common.model.entity.filecenter.AppFile;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.filecenter.mapper.AppFileMapper;
import net.armcloud.paascenter.filecenter.mapper.UserFileMapper;

@Component
public class TaskFileManager {

    @Autowired
    private UserFileMapper userFileMapper;

    @Autowired
    private AppFileMapper appFileMapper;

    public List<FileCustomer> listCustomerFiles(List<Long> customerFileIds) {
        List<UserFile> userFiles = userFileMapper.selectBatchIds(customerFileIds);

        List<FileCustomer> fileCustomers = new ArrayList<>();
        for (UserFile userFile : userFiles) {
            FileCustomer fileCustomer = new FileCustomer();
            fileCustomer.setId(userFile.getId());
            fileCustomer.setFileId(userFile.getFileStorageId());
            fileCustomer.setCustomerId(userFile.getCustomerId());
            fileCustomer.setUniqueId(userFile.getFileUniqueId());
            if (userFile.getFileType().equals("app")) {
                fileCustomer.setFileType(2);
                fileCustomer.setAppId(Integer.parseInt(userFile.getFileUniqueId()));

                // query by fileStorageId
                AppFile appFile = appFileMapper.selectOne(new QueryWrapper<AppFile>().eq("file_storage_id", userFile.getFileStorageId()));
                if (null != appFile) {
                    fileCustomer.setAppPackageName(appFile.getPackageName());
                    fileCustomer.setAppName(appFile.getAppName());
                    fileCustomer.setAppVersionName(appFile.getVersionName());
                    fileCustomer.setAppVersionNo(appFile.getVersionCode() != null ? Long.valueOf(appFile.getVersionCode()) : null);
                    fileCustomer.setAppSignatureMd5(appFile.getSignatureHash());
                }
            } else {
                fileCustomer.setFileType(1);
            }
            fileCustomer.setDescription(userFile.getFileComment());
            fileCustomer.setFileName(userFile.getFileName());
            fileCustomer.setOriginUrl(userFile.getOriginalUrl());
            fileCustomers.add(fileCustomer);
        }
        return fileCustomers;
    }

}
