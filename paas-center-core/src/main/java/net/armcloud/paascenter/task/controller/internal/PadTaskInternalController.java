package net.armcloud.paascenter.task.controller.internal;

import net.armcloud.paascenter.common.client.internal.facade.PadTaskInternalFacade;
import net.armcloud.paascenter.common.client.internal.vo.PadTaskVO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.comms.CmdRecord;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.task.service.impl.PadTaskServiceImpl;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class PadTaskInternalController  implements PadTaskInternalFacade {
    private final PadTaskServiceImpl padTaskService;

    @Override
    public Result<PadTaskVO> getByUniqueId(String uniqueId) {
        return Result.ok(padTaskService.getPodTaskByUniqueId(uniqueId));
    }

    @Override
    public Result<PadTaskVO> getSubTaskId(Long subTaskId) {
        return Result.ok(padTaskService.getSubTaskId(subTaskId));
    }

    @Override
    public Result<PadTask> getPadTaskByCustomerTaskId(Integer customerTaskId,Long customerId) {
        return Result.ok(padTaskService.getPadTaskByCustomerTaskId(customerTaskId,customerId));
    }

    @Override
    public Result<?> updateTaskMsg(List<CmdRecord> cmdRecords) {
        return Result.ok(padTaskService.updateTaskMsg(cmdRecords));
    }

    public PadTaskInternalController(PadTaskServiceImpl padTaskService) {
        this.padTaskService = padTaskService;
    }
}
