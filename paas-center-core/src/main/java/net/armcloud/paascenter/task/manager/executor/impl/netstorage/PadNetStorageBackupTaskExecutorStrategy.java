package net.armcloud.paascenter.task.manager.executor.impl.netstorage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.ContainerNetWorkBackupDTO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 网存存储备份
 * <AUTHOR>
 * @Date 2025/3/29 15:41
 * @Description:
 */
@Slf4j
@Component
public class PadNetStorageBackupTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    @Override
    public Object execute(TaskQueue padTask) {
        String padCode = padTask.getKey();
        String contentJson = padTask.getContentJson();
        List<NetStorageResUnit> netStorageResUnitList = JSON.parseObject(contentJson, new TypeReference<List<NetStorageResUnit>>() {});
        if(CollectionUtils.isEmpty(netStorageResUnitList)){
            log.error("PadNetStorageBackupTaskExecutorStrategy.execute run netStorageResUnitList is empty.");
            return StringUtils.EMPTY;
        };
        Map<String, NetStorageResUnit> netStorageResUnitMap = netStorageResUnitList.stream()
                .collect(Collectors.toMap(NetStorageResUnit::getNetStorageResUnitCode, unit -> unit));
        NetStorageResUnit netStorageResUnit = netStorageResUnitMap.get(padCode);
        log.info("PadNetStorageBackupTaskExecutorStrategy.execute netStorageResUnit:{}",JSON.toJSONString(netStorageResUnit));
        ContainerNetWorkBackupDTO workBackupDTO = new ContainerNetWorkBackupDTO();
        workBackupDTO.setStorageId(netStorageResUnit.getTargetCode());
        workBackupDTO.setDistStorage(netStorageResUnit.getNetStorageResUnitCode());
        return workBackupDTO;
    }
    public PadNetStorageBackupTaskExecutorStrategy() {
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.CONTAINER_NET_STORAGE_BACKUP.getType(), "padNetStorageBackupTaskExecutorStrategy");
    }
}
