package net.armcloud.paascenter.task.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "pull-mode")
@RefreshScope
public class PullModeImageConfig {
    /**推模式镜像id  多个由英文逗号分隔*/
    private String pushModeImageIds;
}
