package net.armcloud.paascenter.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.dto.api.TaskImageUploadDTO;
import net.armcloud.paascenter.common.model.entity.task.ImageTask;
import net.armcloud.paascenter.common.model.vo.api.TaskImageUploadVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ImageTaskMapper extends BaseMapper<ImageTask> {
    List<TaskImageUploadVo> taskList(TaskImageUploadDTO taskDetailsDTO);
}
