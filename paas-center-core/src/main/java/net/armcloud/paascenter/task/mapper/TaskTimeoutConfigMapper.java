package net.armcloud.paascenter.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TaskTimeoutConfigMapper extends BaseMapper<TaskTimeoutConfig> {


    TaskTimeoutConfig getByTaskType(@Param("taskType") int taskType);

    List<TaskTimeoutConfig> listAll();

}
