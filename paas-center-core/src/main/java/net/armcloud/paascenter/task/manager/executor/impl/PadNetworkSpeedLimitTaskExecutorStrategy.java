package net.armcloud.paascenter.task.manager.executor.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.InstanceNetworkLimitRequest;
import net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.model.bo.task.quque.PadNetworkSpeedLimitBO;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.task.manager.executor.ITaskParamExecutorStrategy;
import net.armcloud.paascenter.task.manager.executor.TaskParamExecutorStrategyContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 实例限速
 */
@Slf4j
@Component
public class PadNetworkSpeedLimitTaskExecutorStrategy implements ITaskParamExecutorStrategy {
    private final PadMapper padMapper;
    @Override
    public Object execute(TaskQueue taskQueue) {
        String padCode = taskQueue.getKey();
        PadNetworkSpeedLimitBO networkLimitParam = JSONUtil.toBean(taskQueue.getContentJson(), PadNetworkSpeedLimitBO.class);
        List<PadEdgeClusterVO> padEdgeClusterVOS = padMapper.getPadEdgeClusterInfosByPadCodes(Collections.singletonList(padCode));
        if (CollectionUtils.isEmpty(padEdgeClusterVOS)) {
            return "not found cluster";
        }

        PadEdgeClusterVO padEdgeClusterVO = padEdgeClusterVOS.get(0);
        InstanceNetworkLimitRequest networkLimitRequest = new InstanceNetworkLimitRequest();
        InstanceNetworkLimitRequest.Instance instance = new InstanceNetworkLimitRequest.Instance();
        instance.setPadCode(padCode);
        instance.setDeviceIp(padEdgeClusterVO.getDeviceIp());
        instance.setMaxUplinkBandwidth(networkLimitParam.getUpBandwidth().toString());
        instance.setMaxDownlinkBandwidth(networkLimitParam.getDownBandwidth().toString());
        networkLimitRequest.setInstances(Collections.singletonList(instance));
        return networkLimitRequest;
    }

    public PadNetworkSpeedLimitTaskExecutorStrategy(PadMapper padMapper) {
        this.padMapper = padMapper;
        TaskParamExecutorStrategyContext.putBeanName(TaskTypeConstants.LIMIT_BANDWIDTH.getType(), "padNetworkSpeedLimitTaskExecutorStrategy");
    }
}
