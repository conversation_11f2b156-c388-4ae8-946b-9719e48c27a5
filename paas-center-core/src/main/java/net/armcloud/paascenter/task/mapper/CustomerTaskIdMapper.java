package net.armcloud.paascenter.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.CustomerTaskId;
import net.armcloud.paascenter.job.dto.CustomerTaskIdDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CustomerTaskIdMapper extends BaseMapper<CustomerTaskId> {

    /**
     * 客户 任务ID +1 自增
     *
     * @param customerId 客户ID
     */
    void updateByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据客户ID查询任务ID
     *
     * @param customerId 客户ID
     * @return taskId
     */
    Integer selectTaskIdByCustomerId(@Param("customerId") Long customerId);

    /**
     * 客户 任务ID +num 自增
     *
     * @param customerId 客户ID
     */
    int updateMultipleByCustomerId(@Param("customerId") Long customerId, @Param("num") int num);

    /**
     * 查询所有客户任务ID
     * @return
     */
    List<CustomerTaskIdDTO> selectCustomerTaskIds();
}
