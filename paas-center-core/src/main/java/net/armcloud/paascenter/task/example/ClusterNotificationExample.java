package net.armcloud.paascenter.task.example;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.task.manager.NoticeEdgeClusterManager;
import org.springframework.stereotype.Component;

/**
 * 集群通知使用示例
 * 
 * <AUTHOR>
 * @date 2025/08/05
 * @description 展示如何使用NoticeEdgeClusterManager进行集群通知失败计数
 */
@Slf4j
@Component
@AllArgsConstructor
public class ClusterNotificationExample {

    private final NoticeEdgeClusterManager noticeEdgeClusterManager;

    /**
     * 模拟集群通知方法
     * 
     * @param clusterAddress 集群地址
     * @param message 通知消息
     * @return 是否成功
     */
    public boolean sendNotificationToCluster(String clusterAddress, String message) {
        try {
            // 模拟发送通知到集群
            boolean success = performActualNotification(clusterAddress, message);
            
            if (success) {
                log.info("集群通知发送成功，地址: {}", clusterAddress);
                return true;
            } else {
                // 通知失败，记录失败计数
                int failureCount = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
                log.warn("集群通知发送失败，地址: {}, 当前分钟内失败次数: {}", clusterAddress, failureCount);
                return false;
            }
            
        } catch (Exception e) {
            // 异常情况也记录失败计数
            int failureCount = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
            log.error("集群通知发送异常，地址: {}, 当前分钟内失败次数: {}, 异常信息: {}", 
                     clusterAddress, failureCount, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量发送集群通知
     * 
     * @param clusterAddresses 集群地址列表
     * @param message 通知消息
     * @return 成功发送的数量
     */
    public int sendBatchNotifications(String[] clusterAddresses, String message) {
        int successCount = 0;
        
        for (String clusterAddress : clusterAddresses) {
            if (sendNotificationToCluster(clusterAddress, message)) {
                successCount++;
            }
        }
        
        log.info("批量集群通知完成，总数: {}, 成功: {}, 失败: {}", 
                clusterAddresses.length, successCount, clusterAddresses.length - successCount);
        
        return successCount;
    }

    /**
     * 获取集群通知失败统计
     * 
     * @param clusterAddress 集群地址
     * @return 当前分钟内的失败次数
     */
    public int getClusterNotificationFailureCount(String clusterAddress) {
        return noticeEdgeClusterManager.getFailureCount(clusterAddress);
    }

    /**
     * 检查集群通知健康状态
     * 
     * @param clusterAddress 集群地址
     * @return 是否健康（失败次数小于3次认为健康）
     */
    public boolean isClusterNotificationHealthy(String clusterAddress) {
        int failureCount = noticeEdgeClusterManager.getFailureCount(clusterAddress);
        boolean isHealthy = failureCount < 3;
        
        log.debug("集群通知健康检查，地址: {}, 失败次数: {}, 健康状态: {}", 
                 clusterAddress, failureCount, isHealthy ? "健康" : "异常");
        
        return isHealthy;
    }

    /**
     * 模拟实际的集群通知发送逻辑
     * 在实际项目中，这里应该是真正的HTTP请求或其他通信方式
     * 
     * @param clusterAddress 集群地址
     * @param message 消息内容
     * @return 是否成功
     */
    private boolean performActualNotification(String clusterAddress, String message) {
        // 模拟网络请求
        try {
            // 这里应该是实际的HTTP请求代码
            // 例如：RestTemplate、HttpClient、OkHttp等
            
            // 模拟随机成功/失败（用于测试）
            double random = Math.random();
            if (random < 0.7) { // 70%成功率
                Thread.sleep(100); // 模拟网络延迟
                return true;
            } else {
                // 模拟各种失败情况
                if (random < 0.8) {
                    throw new RuntimeException("连接超时");
                } else if (random < 0.9) {
                    throw new RuntimeException("目标服务不可用");
                } else {
                    return false; // 其他失败情况
                }
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 示例：在定时任务中使用
     */
    public void scheduledNotificationTask() {
        String[] clusters = {
            "http://cluster1.example.com:8080/api/notify",
            "http://cluster2.example.com:8080/api/notify",
            "http://cluster3.example.com:8080/api/notify"
        };
        
        String message = "定时健康检查通知";
        
        log.info("开始执行定时集群通知任务");
        int successCount = sendBatchNotifications(clusters, message);
        log.info("定时集群通知任务完成，成功通知 {} 个集群", successCount);
        
        // 检查各集群健康状态
        for (String cluster : clusters) {
            boolean healthy = isClusterNotificationHealthy(cluster);
            if (!healthy) {
                log.warn("集群通知状态异常，建议检查: {}", cluster);
            }
        }
    }
}
