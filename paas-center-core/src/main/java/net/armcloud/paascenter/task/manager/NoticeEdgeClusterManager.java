package net.armcloud.paascenter.task.manager;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/08/05
 * @description 通知边缘服务管理
 */
@Slf4j
@Service
public class NoticeEdgeClusterManager {

    @Resource
    private RedisService redisService;

    /**
     * 环境标识
     */
    @Value("${spring.profiles.active:unknown}")
    private String springProfilesActive;

    /**
     * 失败计数阈值
     */
    private static final int FAILURE_THRESHOLD = 5;

    /**
     * 计数器过期时间（分钟）
     */
    private static final long COUNTER_EXPIRE_MINUTES = 1L;

    /**
     * 预警发送间隔（分钟）
     */
    private static final long ALERT_INTERVAL_MINUTES = 5L;

    /**
     * 记录集群通知失败计数
     *
     * @param clusterAddress 集群通知地址
     * @return 当前分钟内的失败次数
     */
    public int recordNotificationFailure(String clusterAddress) {
        if (clusterAddress == null || clusterAddress.trim().isEmpty()) {
            log.warn("集群通知地址为空，无法记录失败计数");
            return 0;
        }

        try {
            // 生成当前分钟的计数器key
            String counterKey = generateCounterKey(clusterAddress);

            // Redis原子自增操作
            Integer currentCount = redisService.increment(counterKey);

            // 设置过期时间（如果是第一次设置）
            if (currentCount == 1) {
                redisService.expire(counterKey, COUNTER_EXPIRE_MINUTES, TimeUnit.MINUTES);
            }

            log.debug("集群通知失败计数记录成功，地址: {}, 当前分钟内失败次数: {}", clusterAddress, currentCount);

            // 检查是否需要发送预警
            if (currentCount >= FAILURE_THRESHOLD) {
                sendFailureAlert(clusterAddress, currentCount);
            }

            return currentCount;

        } catch (Exception e) {
            log.error("记录集群通知失败计数时发生异常，地址: {}", clusterAddress, e);
            return 0;
        }
    }

    /**
     * 获取指定集群地址当前分钟内的失败次数
     *
     * @param clusterAddress 集群通知地址
     * @return 当前分钟内的失败次数
     */
    public int getFailureCount(String clusterAddress) {
        if (clusterAddress == null || clusterAddress.trim().isEmpty()) {
            return 0;
        }

        try {
            String counterKey = generateCounterKey(clusterAddress);
            Object count = redisService.getCacheObject(counterKey);
            return count != null ? Integer.parseInt(count.toString()) : 0;
        } catch (Exception e) {
            log.error("获取集群通知失败计数时发生异常，地址: {}", clusterAddress, e);
            return 0;
        }
    }

    /**
     * 生成计数器Redis Key
     * 格式: paas-center-core:counter:cluster_notification_failure:{年月日时分}:{集群地址hash}
     *
     * @param clusterAddress 集群通知地址
     * @return Redis Key
     */
    private String generateCounterKey(String clusterAddress) {
        // 获取当前时间的年月日时分
        String timeKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));

        // 对集群地址进行hash处理，避免特殊字符问题
        String addressHash = String.valueOf(Math.abs(clusterAddress.hashCode()));

        return RedisKeyUtils.counterKey("cluster_notification_failure", timeKey, addressHash);
    }

    /**
     * 发送失败预警
     *
     * @param clusterAddress 集群通知地址
     * @param failureCount 失败次数
     */
    private void sendFailureAlert(String clusterAddress, int failureCount) {
        try {
            // 检查是否在预警间隔内已发送过预警
            String alertKey = generateAlertKey(clusterAddress);
            if (redisService.hasKey(alertKey)) {
                log.debug("预警间隔内已发送过预警，跳过发送，地址: {}", clusterAddress);
                return;
            }

            // 设置预警间隔锁
            redisService.setCacheObject(alertKey, "1", ALERT_INTERVAL_MINUTES, TimeUnit.MINUTES);

            // 构建预警消息
            String title = "🚨 集群通知失败预警";
            String markdownContent = buildAlertMarkdown(clusterAddress, failureCount);

            // 发送钉钉预警
            DingTalkRobotClient.sendMarkdownMessage(springProfilesActive, title, markdownContent);

            log.info("集群通知失败预警已发送，地址: {}, 失败次数: {}", clusterAddress, failureCount);

        } catch (Exception e) {
            log.error("发送集群通知失败预警时发生异常，地址: {}, 失败次数: {}", clusterAddress, failureCount, e);
        }
    }

    /**
     * 生成预警间隔锁Key
     *
     * @param clusterAddress 集群通知地址
     * @return Redis Key
     */
    private String generateAlertKey(String clusterAddress) {
        String addressHash = String.valueOf(Math.abs(clusterAddress.hashCode()));
        return RedisKeyUtils.cacheKey("cluster_notification_alert", addressHash);
    }

    /**
     * 构建预警Markdown消息
     *
     * @param clusterAddress 集群通知地址
     * @param failureCount 失败次数
     * @return Markdown格式的预警消息
     */
    private String buildAlertMarkdown(String clusterAddress, int failureCount) {

        return "## 🚨 集群通知失败预警\n\n" +
                "### 📊 预警详情\n" +
                "- **集群地址：** `" + clusterAddress + "`\n" +
                "- **失败次数：** " + failureCount + " 次\n" +
                "- **统计周期：** 1分钟内\n" +
                "- **预警阈值：** " + FAILURE_THRESHOLD + " 次\n" +
                "- **预警时间：** " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n";
    }
}
