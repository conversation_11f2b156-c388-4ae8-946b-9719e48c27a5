 package net.armcloud.paascenter.feign;

 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.feign.dto.DownloadAndPushDTO;
 import org.springframework.cloud.openfeign.FeignClient;
 import org.springframework.web.bind.annotation.PostMapping;
 import org.springframework.web.bind.annotation.RequestBody;

 @FeignClient(name = "paas-center-docker-push", contextId = "paas-center-docker-push-image")
 public interface DockerPushClient {
 

     @PostMapping(value = "/docker/downloadAndPush")
     Result<?> downloadAndPush(@RequestBody DownloadAndPushDTO dto);
 

 }