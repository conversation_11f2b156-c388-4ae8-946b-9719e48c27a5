 package net.armcloud.paascenter.bmc.service.impl;
 
 import com.alibaba.fastjson2.JSON;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 import net.armcloud.paascenter.common.redis.contstant.KeyPrefix;
 import net.armcloud.paascenter.common.redis.contstant.KeyTime;
 import net.armcloud.paascenter.bmc.utils.BmcRedisService;
 import net.armcloud.paascenter.bmc.constant.CardTaskStatus;
 import net.armcloud.paascenter.bmc.constant.CardTaskSubType;
 import net.armcloud.paascenter.bmc.mapper.CardTaskSubMapper;
 import net.armcloud.paascenter.bmc.service.ICardTaskSubService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 import org.springframework.util.ObjectUtils;
 
 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.concurrent.TimeUnit;
 
 import net.armcloud.paascenter.bmc.constant.Constants;
 import static net.armcloud.paascenter.bmc.constant.NumberConst.ZERO;
 
 @Slf4j
 @Service
 public class CardTaskSubServiceImpl extends ServiceImpl<CardTaskSubMapper, CardTaskSub> implements ICardTaskSubService {
     private final BmcRedisService bmcRedisService;
     private final CardTaskSubMapper cardTaskSubMapper;
 
 
     public CardTaskSubServiceImpl(BmcRedisService bmcRedisService, CardTaskSubMapper cardTaskSubMapper) {
         this.bmcRedisService = bmcRedisService;
         this.cardTaskSubMapper = cardTaskSubMapper;
     }
 
     @Override
     public CardTaskSub getCardSubTaskById(Long subId) {
         String key = KeyPrefix.CARD_TASK_SUB_INFO + subId;
         Object obj = bmcRedisService.getCacheObject(key);
         if (null != obj) {
             return JSON.parseObject(obj.toString(), CardTaskSub.class);
         }
         CardTaskSub cardTaskSub = cardTaskSubMapper.selectById(subId);
         if (ObjectUtils.isEmpty(cardTaskSub)) {
             return null;
         }
 
         bmcRedisService.setCacheObject(key, JSON.toJSONString(cardTaskSub), KeyTime.minute_10, TimeUnit.MINUTES);
         return cardTaskSub;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public CardTaskSub addCardSubTaskService(Long mainTaskId, String serverSn, Integer cardTaskSubType, LocalDateTime timeout) {
         String key = Constants.CAR_TASK_SUB + mainTaskId + "_" + cardTaskSubType;
         boolean rLock = bmcRedisService.acquireLock(key, 60);
         try {
             if (rLock) {
                 QueryWrapper<CardTaskSub> queryWrapper = new QueryWrapper<>();
                 queryWrapper.eq("main_task", mainTaskId).eq("type", cardTaskSubType).last("limit 1");
                 CardTaskSub cardTaskSub = cardTaskSubMapper.selectOne(queryWrapper);
                 if (!ObjectUtils.isEmpty(cardTaskSub)) {
                     return cardTaskSub;
                 }
 
                 CardTaskSub save = new CardTaskSub();
                 save.setMainTask(mainTaskId);
                 save.setType(cardTaskSubType);
                 save.setTimeout(timeout);
                 save.setServerSn(serverSn);
                 cardTaskSubMapper.insertCardTaskSub(save);
                 return save;
             }
         } finally {
             bmcRedisService.releaseLock(key);
         }
         return new CardTaskSub();
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public Boolean updateCardTaskSubService(Long id, Integer status) {
         CardTaskSub cardTasksub = new CardTaskSub();
         cardTasksub.setStatus(status);
         return cardTaskSubMapper.update(cardTasksub, new QueryWrapper<CardTaskSub>().eq("id", id).eq("status", CardTaskStatus.PROCESSING)) > ZERO;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public CardTaskSub addCardResetResultTaskService(CardTask mainTask) {
         String key = Constants.CAR_TASK_SUB + mainTask.getId() + "_" + CardTaskSubType.CARD_RESET_RESULT;
         boolean rLock = bmcRedisService.acquireLock(key, 60);
         try {
             if(rLock){
                 CardTaskSub save = new CardTaskSub();
                 save.setMainTask(mainTask.getId());
                 save.setType(CardTaskSubType.CARD_RESET_RESULT);
                 save.setTimeout(mainTask.getTimeout());
                 save.setServerSn(mainTask.getServerSn());
                 cardTaskSubMapper.insertCardTaskSub(save);
                 return save;
             }
         } finally {
             bmcRedisService.releaseLock(key);
         }
         return new CardTaskSub();
     }
 
     @Override
     public List<String> getExecutedServerSn(Integer subType) {
         return cardTaskSubMapper.selectExecutedServerSn(subType);
     }
 }