 package net.armcloud.paascenter.bmc.service;
 
 import com.baomidou.mybatisplus.extension.service.IService;
 import net.armcloud.paascenter.common.model.entity.bmc.Server;
 import net.armcloud.paascenter.bmc.model.dto.DeleteArmServerDTO;
 import net.armcloud.paascenter.bmc.model.dto.DeleteImagesDTO;
 import net.armcloud.paascenter.bmc.model.dto.InitArmServerDTO;
 import net.armcloud.paascenter.bmc.model.dto.UploadImagesDTO;
 import net.armcloud.paascenter.bmc.model.vo.ArmServerInitVO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 
 import java.util.List;
 
 public interface IServerService extends IService<Server> {
 
     /**
      * 获取节点服务器信息
      *
      * @return ArmServerInfoVO
      */
     List<Server> getServerList();
 
     /**
      * 初始化节点服务器
      *
      * @return Boolean
      */
     ArmServerInitVO addArmServerAndCardInfo(InitArmServerDTO parma);
 
     /**
      * 拉取增量板卡信息
      * @param parma
      * @return
      */
     ArmServerInitVO pullArmServerAndCardInfo(InitArmServerDTO parma);
 
     /**
      * arm 心跳检测
      */
     void checkArmHeartbeatService();
 
     /**
      * 根据socApiUrl获取服务器信息
      *
      * @param socApiUrl
      * @return
      */
     Server getServerBySocUrl(String socApiUrl);
 
     /**
      * 删除 arm server 服务器
      *
      * @param parma
      * @return
      */
     Boolean deleteArmServerService(DeleteArmServerDTO parma);
 
     /**
      * 添加镜像上传任务
      *
      * @param parma 上传镜像请求参数
      * @return 任务信息
      */
     TaskVO uploadImagesService(UploadImagesDTO parma);
 
     /**
      * 获取镜像上传结果
      */
     void handleUploadImagesResult();
 
 
     /**
      * 删除镜像
      *
      * @param parma 上传镜像请求参数
      * @return 任务信息
      */
     Boolean deleteImagesService(DeleteImagesDTO parma);
 }