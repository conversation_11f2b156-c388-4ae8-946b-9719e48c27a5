 package net.armcloud.paascenter.bmc.model.dto;
 
 import lombok.Data;
 import lombok.EqualsAndHashCode;
 
 import javax.validation.constraints.NotNull;
 
 @Data
 @EqualsAndHashCode(callSuper = true)
 public class InitArmServerDTO extends BaseRequestDTO {
 
     /**
      * ip段 例：**********/128
      */
     private String cardIps;
 
     /**
      * netmask
      */
     @NotNull(message = "netmask cannot null")
     private String netmask;
     /**
      * gateway
      */
     @NotNull(message = "gateway cannot null")
     private String gateway;
     /**
      * dns
      */
     @NotNull(message = "dns cannot null")
     private String dns;
     /**
      * ArmServer类型 1-凌点 2-启朔
      */
     private Integer armServerType;
 
 
 }