 package net.armcloud.paascenter.bmc.service.impl;
 
 import com.alibaba.fastjson2.JSON;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import net.armcloud.paascenter.bmc.constant.Constants;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.utils.IpUtils;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmCardVO;
 import net.armcloud.paascenter.common.model.entity.bmc.Card;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTaskSub;
 import net.armcloud.paascenter.common.model.entity.bmc.Server;
 import net.armcloud.paascenter.common.redis.contstant.KeyPrefix;
 import net.armcloud.paascenter.common.redis.contstant.KeyTime;
 import net.armcloud.paascenter.bmc.constant.CardTaskStatus;
 import net.armcloud.paascenter.bmc.constant.CardTaskSubType;
 import net.armcloud.paascenter.bmc.exception.ManageExceptionCode;
 import net.armcloud.paascenter.bmc.mapper.CardMapper;
 import net.armcloud.paascenter.bmc.mapper.CardTaskMapper;
 import net.armcloud.paascenter.bmc.mapper.CardTaskSubMapper;
 import net.armcloud.paascenter.bmc.model.dto.*;
 import net.armcloud.paascenter.bmc.model.vo.CardTaskInfoVO;
 import net.armcloud.paascenter.bmc.model.vo.PowerRestartTaskVO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 import net.armcloud.paascenter.bmc.service.ICardService;
 import net.armcloud.paascenter.bmc.service.ICardTaskService;
 import net.armcloud.paascenter.bmc.service.callback.CardTaskCallbackService;
 import net.armcloud.paascenter.bmc.utils.BmcRedisService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.aop.framework.AopContext;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 import org.springframework.util.CollectionUtils;
 import org.springframework.util.ObjectUtils;
 
 import java.time.LocalDateTime;
 import java.util.ArrayList;
 import java.util.List;
 import java.util.concurrent.TimeUnit;
 
 import static net.armcloud.paascenter.bmc.constant.Constants.*;
 import static net.armcloud.paascenter.bmc.constant.NumberConst.*;
 import static net.armcloud.paascenter.common.utils.DateUtils.plusMinutesDateTime;
 import static net.armcloud.paascenter.common.redis.contstant.KeyPrefix.*;
 import static net.armcloud.paascenter.bmc.configure.ArmCloudApiUrls.DEVICE_STATUS_CALLBACK;
 import static net.armcloud.paascenter.bmc.configure.ArmCloudApiUrls.DEVICE_TASK_CALLBACK;
 import static net.armcloud.paascenter.bmc.constant.CardTaskType.*;
 import static net.armcloud.paascenter.bmc.exception.ManageExceptionCode.*;
 
 
 @Slf4j
 @Service
 public class CardTaskServiceImpl extends ServiceImpl<CardTaskMapper, CardTask> implements ICardTaskService {
     private final CardMapper cardMapper;
     private final CardTaskMapper cardTaskMapper;
     private final CardTaskSubMapper cardTaskSubMapper;
     private final BmcRedisService bmcRedisService;
     private final ICardService cardService;
 
     private final CardTaskCallbackService cardTaskCallbackService;
 
     public CardTaskServiceImpl(CardMapper cardMapper, CardTaskMapper cardTaskMapper, CardTaskSubMapper cardTaskSubMapper, BmcRedisService bmcRedisService, ICardService cardService, CardTaskCallbackService cardTaskCallbackService) {
         this.cardMapper = cardMapper;
         this.cardTaskMapper = cardTaskMapper;
         this.cardTaskSubMapper = cardTaskSubMapper;
         this.bmcRedisService = bmcRedisService;
 
         this.cardService = cardService;
         this.cardTaskCallbackService = cardTaskCallbackService;
     }
 
     @Override
     public List<String> checkCardRunningTask(List<String> cardIds) {
         List<String> failCardIds = new ArrayList<>();
         cardIds.forEach(cardId -> {
             Long count = cardTaskMapper.selectCount(new QueryWrapper<CardTask>().eq("card_id", cardId).eq("status", CardTaskStatus.PROCESSING));
             if (count > ZERO) {
                 failCardIds.add(cardId);
             }
         });
         return failCardIds;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public List<PowerRestartTaskVO> addCardPowerRestartTaskService(List<PowerRestartTaskDTO> tasks, Integer timeOut) {
         List<PowerRestartTaskVO> taskVOS = new ArrayList<>();
         List<CardTask> cardTasks = new ArrayList<>();
         tasks.forEach(task -> {
             Card card = cardService.getCardByCardId(task.getCardId());
             if (ObjectUtils.isEmpty(card)) {
                 throw new BasicException(ManageExceptionCode.NODE_ID_DOES_NOT_EXIST);
             }
             String key = CARD_POWER_RESTART + task.getCardId();
             //是否存在断电重启任务
             if (bmcRedisService.hasKey(key)) {
                 throw new BasicException(NODE_POWER_RESTART_TASK_ALREADY_EXISTS);
             }
             CardTask cardTask = new CardTask();
             cardTask.setTaskId(task.getTaskId());
             cardTask.setServerSn(card.getServerSn());
             cardTask.setCardId(task.getCardId());
             cardTask.setType(POWER_RESTART);
             cardTask.setTimeout(plusMinutesDateTime(timeOut));
             cardTasks.add(cardTask);
         });
         cardTaskMapper.insertCardTaskBatch(cardTasks);
 
         //添加节点下电执行任务
         List<CardTaskSub> cardTaskSubs = new ArrayList<>();
         cardTasks.parallelStream().forEach(cardTask -> {
             CardTaskSub cardTaskSub = new CardTaskSub();
             cardTaskSub.setMainTask(cardTask.getId());
             cardTaskSub.setType(CardTaskSubType.POWER_OFF);
             cardTaskSub.setTimeout(cardTask.getTimeout());
             cardTaskSub.setServerSn(cardTask.getServerSn());
             cardTaskSubs.add(cardTaskSub);
             taskVOS.add(new PowerRestartTaskVO(cardTask.getId(), cardTask.getTaskId()));
 
         });
         cardTaskSubMapper.insertBatch(cardTaskSubs);
         return taskVOS;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public Boolean updateCardTaskService(Long id, Integer status, Boolean syncFlag) {
         CardTask cardTask = new CardTask();
         cardTask.setStatus(status);
         cardTask.setSyncStatus(syncFlag ? 1 : 0);
         return cardTaskMapper.update(cardTask, new QueryWrapper<CardTask>().eq("id", id).eq("status", CardTaskStatus.PROCESSING)) > ZERO;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public CardTaskSub addCardPowerOnTaskService(CardTaskSub cardTasksub) {
         String key = Constants.CAR_TASK_SUB + cardTasksub.getMainTask() + "_" + cardTasksub.getType();
         boolean rLock = bmcRedisService.acquireLock(key, 60);
         try{
             if(rLock){
                 QueryWrapper<CardTaskSub> queryWrapper = new QueryWrapper<>();
                 queryWrapper.eq("main_task", cardTasksub.getMainTask()).eq("type", CardTaskSubType.POWER_ON);
                 CardTaskSub cardTaskSub = cardTaskSubMapper.selectOne(queryWrapper);
                 if (!ObjectUtils.isEmpty(cardTaskSub)) {
                     return cardTaskSub;
                 }
 
                 CardTask mainTask = getCardTaskById(cardTasksub.getMainTask());
 
                 CardTaskSub save = new CardTaskSub();
                 save.setMainTask(mainTask.getId());
                 save.setType(CardTaskSubType.POWER_ON);
                 save.setTimeout(mainTask.getTimeout());
                 cardTaskSubMapper.insertCardTaskSub(save);
                 return save;
             }
         }finally {
             bmcRedisService.releaseLock(key);
         }
         return new CardTaskSub();
 
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public Boolean updateCardPowerRestartTaskService(CardTask mainTask, CardTaskSub cardTasksub, Boolean syncFlag) {
         this.updateCardTaskSubService(cardTasksub.getId(), CardTaskStatus.SUCCESS);
         //删除断电重启任务缓存
         String key = CARD_POWER_RESTART + mainTask.getCardId();
         bmcRedisService.deleteObject(key);
 
         return this.updateCardTaskService(mainTask.getId(), CardTaskStatus.SUCCESS, syncFlag);
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public Boolean updateCardTaskSubService(Long id, Integer status) {
         CardTaskSub cardTasksub = new CardTaskSub();
         cardTasksub.setStatus(status);
         return cardTaskSubMapper.update(cardTasksub, new QueryWrapper<CardTaskSub>().eq("id", id).eq("status", CardTaskStatus.PROCESSING)) > ZERO;
     }
 
     @Override
     public List<TaskVO> addSetCardNetWorkTaskService(List<CardNetworkDTO> cardNetworkInfos) {
         log.info("initArmServer start addSetCardNetWorkTaskService time:{}", LocalDateTime.now());
         List<TaskVO> taskVOS = new ArrayList<>();
         List<CardTask> cardTasks = new ArrayList<>();
         cardNetworkInfos.forEach(cardNet -> {
             String sn = null;
             if (ObjectUtils.isEmpty(cardNet.getServerSn())) {
                 Card cardVO = cardService.getCardByCardId(cardNet.getCardId());
                 sn = cardVO.getServerSn();
             } else {
                 sn = cardNet.getServerSn();
             }
 
             CardTask cardTask = new CardTask();
             cardTask.setServerSn(sn);
             cardTask.setCardId(cardNet.getCardId());
             cardTask.setType(SET_NETWORK);
             cardTask.setTaskContent(JSON.toJSONString(cardNet));
             cardTask.setTimeout(plusMinutesDateTime(cardNet.getTimeOut()));
             cardTasks.add(cardTask);
 
         });
         cardTaskMapper.insertCardTaskBatch(cardTasks);
 
         List<CardTaskSub> cardTaskSubs = new ArrayList<>();
         cardTasks.parallelStream().forEach(cardTask -> {
             //添加设置IP子任务
             CardTaskSub cardTaskSub = new CardTaskSub();
             cardTaskSub.setServerSn(cardTask.getServerSn());
             cardTaskSub.setMainTask(cardTask.getId());
             cardTaskSub.setType(CardTaskSubType.SET_NETWORK_IP);
             cardTaskSub.setServerSn(cardTask.getServerSn());
             cardTaskSub.setTimeout(cardTask.getTimeout());
             cardTaskSubs.add(cardTaskSub);
             taskVOS.add(new TaskVO(cardTask.getId(), cardTask.getCardId()));
         });
         cardTaskSubMapper.insertBatch(cardTaskSubs);
         log.info("initArmServer end addSetCardNetWorkTaskService time:{}", LocalDateTime.now());
         return taskVOS;
     }
 
     @Override
     public CardTaskInfoVO getTaskInfoService(String taskId) {
         CardTask cardTask = cardTaskMapper.selectOne(new QueryWrapper<CardTask>().eq("id", Long.valueOf(taskId)).last("limit 1"));
         if (ObjectUtils.isEmpty(cardTask)) {
             throw new BasicException(ManageExceptionCode.THE_TASK_DOES_NOT_EXIST_IN_BMC);
         }
         CardTaskInfoVO cardTaskInfoVO = new CardTaskInfoVO();
         cardTaskInfoVO.setTaskId(cardTask.getId());
         cardTaskInfoVO.setCardId(cardTask.getCardId());
         cardTaskInfoVO.setStatus(cardTask.getStatus());
         cardTaskInfoVO.setResultMsg(cardTask.getTaskContent());
         return cardTaskInfoVO;
     }
 
     @Override
     public List<TaskVO> addCardResetTaskService(List<CardTaskDTO> cardTasks, Integer timeOut) {
         List<TaskVO> taskVOS = new ArrayList<>();
         List<CardTaskSub> cardTaskSubs = new ArrayList<>();
         cardTasks.forEach(dto -> {
             Card card = cardService.getCardByCardId(dto.getCardId());
 
             CardTask cardTask = new CardTask();
             cardTask.setTaskId(dto.getTaskId());
             cardTask.setServerSn(card.getServerSn());
             cardTask.setCardId(dto.getCardId());
             cardTask.setType(RESET);
             cardTask.setTimeout(plusMinutesDateTime(timeOut));
             cardTaskMapper.insertCardTask(cardTask);
 
             //添加节点重置任务
             CardTaskSub cardTaskSub = new CardTaskSub();
             cardTaskSub.setServerSn(card.getServerSn());
             cardTaskSub.setMainTask(cardTask.getId());
             cardTaskSub.setType(CardTaskSubType.CARD_RESET);
             cardTaskSub.setTimeout(cardTask.getTimeout());
             cardTaskSub.setServerSn(cardTask.getServerSn());
             cardTaskSubs.add(cardTaskSub);
             taskVOS.add(new TaskVO(cardTask.getId(), cardTask.getCardId()));
         });
         cardTaskSubMapper.insertBatch(cardTaskSubs);
         return taskVOS;
     }
 
     @Override
     public CardTask getCardTaskById(Long mainId) {
         String key = KeyPrefix.CARD_TASK_INFO + mainId;
         Object obj = bmcRedisService.getCacheObject(key);
         if (null != obj) {
             return JSON.parseObject(obj.toString(), CardTask.class);
         }
 
         CardTask cardTask = cardTaskMapper.selectById(mainId);
         if (ObjectUtils.isEmpty(cardTask)) {
             return null;
         }
         bmcRedisService.setCacheObject(key, JSON.toJSONString(cardTask), KeyTime.minute_10, TimeUnit.MINUTES);
         return cardTask;
     }
 
     @Override
     public CardTask getCardTaskBySubId(Long subId) {
         String key = KeyPrefix.CARD_TASK_INFO_SUB_ID + subId;
         Object obj = bmcRedisService.getCacheObject(key);
         if (null != obj) {
             return JSON.parseObject(obj.toString(), CardTask.class);
         }
         CardTaskSub cardTaskSub = getCardTaskSubBySubId(subId);
         if (ObjectUtils.isEmpty(cardTaskSub)) {
             return null;
         }
         CardTask cardTask = getCardTaskById(cardTaskSub.getMainTask());
         if (ObjectUtils.isEmpty(cardTaskSub)) {
             return null;
         }
         bmcRedisService.setCacheObject(key, JSON.toJSONString(cardTask), KeyTime.minute_10, TimeUnit.MINUTES);
         return cardTask;
     }
 
     @Override
     public CardTaskSub getCardTaskSubBySubId(Long subId) {
         String key = KeyPrefix.CARD_TASK_SUB_INFO + subId;
         Object obj = bmcRedisService.getCacheObject(key);
         if (null != obj) {
             return JSON.parseObject(obj.toString(), CardTaskSub.class);
         }
         log.info("getCardTaskSubBySubId:{}", subId);
         CardTaskSub cardTaskSub = cardTaskSubMapper.selectById(subId);
         if (ObjectUtils.isEmpty(cardTaskSub)) {
             return null;
         }
 
         bmcRedisService.setCacheObject(key, JSON.toJSONString(cardTaskSub), KeyTime.minute_10, TimeUnit.MINUTES);
         return cardTaskSub;
     }
 
     @Override
     public List<TaskVO> addInitCardTaskService(InitArmServerDTO parma, List<ArmCardVO> armCardVOS, Long serverId, String sn) {
         List<Card> saveCards = new ArrayList<>();
         List<CardNetworkDTO> cardNetworkInfos = new ArrayList<>();
 
         //添加板卡信息
         List<String> ipList = IpUtils.getAvailableIPs(parma.getCardIps(), armCardVOS.size());
         for (int i = 0; i < armCardVOS.size(); i++) {
             ArmCardVO armCardVO = armCardVOS.get(i);
             String[] positionArr = armCardVO.getPosition().split("-");
 
 
             Card save = new Card();
             save.setCardId(armCardVO.getId());
             save.setIp(ipList.get(i));
             save.setNetmask(parma.getNetmask());
             save.setGateway(parma.getGateway());
             save.setDns(parma.getDns());
 
             save.setSn(armCardVO.getId());
             save.setMac(armCardVO.getMac());
             save.setServerId(serverId);
             save.setServerSn(sn);
             save.setNodeId(positionArr[0]);
             save.setPosition(positionArr[1]);
             save.setCreateBy("bmc-manage");
             saveCards.add(save);
         }
 
         //删除节点信息
         cardMapper.delete(new QueryWrapper<Card>().eq("server_id", serverId));
 
         cardMapper.insertBatch(saveCards);
         saveCards.forEach(card -> {
             String key = KeyPrefix.BMC_CARD_LIST + card.getCardId();
             bmcRedisService.deleteObject(key);
 
             CardNetworkDTO cardNetworkDTO = new CardNetworkDTO();
             cardNetworkDTO.setCardId(card.getCardId());
             cardNetworkDTO.setIp(card.getIp());
             cardNetworkDTO.setNetmask(card.getNetmask());
             cardNetworkDTO.setGateway(card.getGateway());
             cardNetworkDTO.setDns(card.getDns());
             cardNetworkDTO.setTimeOut(parma.getTimeOut());
             cardNetworkDTO.setServerSn(card.getServerSn());
             cardNetworkInfos.add(cardNetworkDTO);
         });
         //添加设置节点网络信息任务
         return addSetCardNetWorkTaskService(cardNetworkInfos);
     }
 
     @Override
     public List<TaskVO> pullInitCardTaskService(InitArmServerDTO parma, List<ArmCardVO> armCardVOS, Long serverId, String sn) {
         List<Card> saveCards = new ArrayList<>();
         //添加板卡信息
         List<String> ipList = IpUtils.getAvailableIPs(parma.getCardIps(), armCardVOS.size());
         for (int i = 0; i < armCardVOS.size(); i++) {
             ArmCardVO armCardVO = armCardVOS.get(i);
             String[] positionArr = armCardVO.getPosition().split("-");
 
             Card save = new Card();
             save.setCardId(armCardVO.getId());
             save.setIp(ipList.get(i));
             save.setNetmask(parma.getNetmask());
             save.setGateway(parma.getGateway());
             save.setDns(parma.getDns());
 
             save.setSn(armCardVO.getId());
             save.setMac(armCardVO.getMac());
             save.setServerId(serverId);
             save.setServerSn(sn);
             save.setNodeId(positionArr[0]);
             save.setPosition(positionArr[1]);
             save.setCreateBy("bmc-manage");
             saveCards.add(save);
         }
 
         log.info("过滤已存在后的板卡数量size:{},saveCards:{}",saveCards.size(),JSON.toJSONString(saveCards));
         cardMapper.insertBatch(saveCards);
         List<CardNetworkDTO> cardNetworkInfos = new ArrayList<>(saveCards.size());
         saveCards.forEach(card -> {
             String key = KeyPrefix.BMC_CARD_LIST + card.getCardId();
             bmcRedisService.deleteObject(key);
             CardNetworkDTO cardNetworkDTO = new CardNetworkDTO();
             cardNetworkDTO.setCardId(card.getCardId());
             cardNetworkDTO.setIp(card.getIp());
             cardNetworkDTO.setNetmask(card.getNetmask());
             cardNetworkDTO.setGateway(card.getGateway());
             cardNetworkDTO.setDns(card.getDns());
             cardNetworkDTO.setTimeOut(parma.getTimeOut());
             cardNetworkDTO.setServerSn(card.getServerSn());
             cardNetworkInfos.add(cardNetworkDTO);
         });
         //添加设置节点网络信息任务
         return addSetCardNetWorkTaskService(cardNetworkInfos);
     }
 
     @Override
     public Boolean handleTimeoutTasksService(CardTaskSub cardTaskSub) {
         CardTask cardTask = getCardTaskById(cardTaskSub.getMainTask());
 
         return ((ICardTaskService) AopContext.currentProxy()).handleTimeoutTaskDatabase(cardTask, cardTaskSub);
     }
 
     @Override
     public Boolean handleTimeoutTaskDatabase(CardTask cardTask, CardTaskSub cardTaskSub) {
         Boolean syncFlag;
         if (!ObjectUtils.isEmpty(cardTask.getTaskId()) && !cardTask.getTaskId().startsWith(BMC_TASK_PREFIX)) {
             syncFlag = cardTaskCallbackService.sendCardTaskCallback(DEVICE_TASK_CALLBACK, cardTask.getId().toString(), cardTask.getType(), CardTaskStatus.FAIL, REQUEST_TIMEOUT);
         } else {
             syncFlag = cardTaskCallbackService.sendCardStatusCallback(DEVICE_STATUS_CALLBACK, cardTask.getCardId(), null, ZERO);
         }
 
         //修改任务状态
         CardTaskSub updateSub = new CardTaskSub();
         updateSub.setStatus(CardTaskStatus.FAIL);
         updateSub.setUpdateBy("timeoutTask");
         cardTaskSubMapper.update(updateSub, new QueryWrapper<CardTaskSub>().eq("id", cardTaskSub.getId()).eq("status", CardTaskStatus.PROCESSING));
 
         //回调任务状态
         CardTask updateTask = new CardTask();
         updateTask.setStatus(CardTaskStatus.FAIL);
         updateTask.setSyncStatus(syncFlag ? 1 : 0);
         updateTask.setUpdateBy("timeoutTask");
         cardTaskMapper.update(updateTask, new QueryWrapper<CardTask>().eq("id", cardTask.getId()).eq("status", CardTaskStatus.PROCESSING));
         return true;
     }
 
 
     @Override
     public List<TaskVO> addReCardInitTaskService(Server server, List<ArmCardVO> armCardVOS, Integer timeOut) {
         if (CollectionUtils.isEmpty(armCardVOS)) {
             throw new BasicException(FAILED_TO_OBTAIN_CARD_INFORMATION);
         }
 
         List<TaskVO> taskVOList = new ArrayList<>();
         List<CardTask> cardTasks = new ArrayList<>();
         List<CardTaskSub> CardTaskSubs = new ArrayList<>();
 
         armCardVOS.forEach(card -> {
             CardTask cardTask = new CardTask();
             cardTask.setServerSn(server.getSn());
             cardTask.setCardId(card.getId());
             cardTask.setType(REINIT);
             cardTask.setTimeout(plusMinutesDateTime(timeOut));
             cardTasks.add(cardTask);
         });
 
         if (CollectionUtils.isEmpty(cardTasks)) {
             throw new BasicException(NODE_RESET_FAILED);
         }
 
         cardTaskMapper.insertCardTaskBatch(cardTasks);
 
         cardTasks.parallelStream().forEach(cardTask -> {
             CardTaskSub cardTaskSub = new CardTaskSub();
             cardTaskSub.setMainTask(cardTask.getId());
             cardTaskSub.setType(CardTaskSubType.CARD_REINIT);
             cardTaskSub.setTimeout(cardTask.getTimeout());
             cardTaskSub.setServerSn(cardTask.getServerSn());
             CardTaskSubs.add(cardTaskSub);
 
             TaskVO taskVO = new TaskVO(cardTask.getId(), cardTask.getCardId());
             taskVOList.add(taskVO);
         });
         cardTaskSubMapper.insertBatch(CardTaskSubs);
         return taskVOList;
     }
 
     @Override
     public List<String> getExecutedServerSn(Integer type) {
         return cardTaskMapper.selectExecutedServerSn(type);
     }
 
     @Override
     public List<String> getExecutedServerByTypes(List<Integer> types) {
         return cardTaskMapper.selectExecutedServerByTypes(types);
     }
 
     @Override
     public Boolean updateCardTaskIdToNullService(Long id) {
         return cardTaskMapper.updateCardTaskIdToNull(id) > ZERO;
     }
 
     @Override
     public Boolean syncCardInitTaskService() {
         QueryWrapper<CardTask> qw = new QueryWrapper<CardTask>().eq("type", SET_NETWORK).eq("status", CardTaskStatus.SUCCESS).eq("sync_status", ZERO);
         List<CardTask> networkCardTasks = cardTaskMapper.selectList(qw);
         log.info("syncCardInitTaskService 待同步板卡初始网络任务数量={}", networkCardTasks.size());
         networkCardTasks.forEach(cardTask -> {
             //回调云机信息
             Boolean syncFlag = false;
             Card card = cardService.getCardByCardId(cardTask.getCardId());
             if (!ObjectUtils.isEmpty(card)) {
                 syncFlag = cardTaskCallbackService.sendCardStatusCallback(DEVICE_STATUS_CALLBACK, card.getCardId(), card.getIp(), ONE);
             }
             log.info("syncCardInitTaskService 回调初始板卡网络信息 cardTaskId={} cardId={} ip={} syncFlag={}", cardTask.getId(), cardTask.getCardId(), card.getIp(), syncFlag);
             if (syncFlag) {
                 CardTask updateTask = new CardTask();
                 updateTask.setId(cardTask.getId());
                 updateTask.setSyncStatus(ONE);
                 updateTask.setUpdateBy("syncCardTaskIdService");
                 cardTaskMapper.updateById(updateTask);
             }
         });
 
         QueryWrapper<CardTask> task_qw = new QueryWrapper<CardTask>().in("status", CardTaskStatus.SUCCESS, CardTaskStatus.FAIL).eq("sync_status", ZERO);
         List<CardTask> cardTasks = cardTaskMapper.selectList(task_qw);
         log.info("syncCardInitTaskService 待同步板卡任务数量={}", cardTasks.size());
 
         cardTasks.forEach(cardTask -> {
             //回调任务结果
             Boolean syncFlag = cardTaskCallbackService.sendCardTaskCallback(DEVICE_TASK_CALLBACK, cardTask.getId().toString(), cardTask.getType(), cardTask.getStatus(), "");
             if (syncFlag) {
                 CardTask updateTask = new CardTask();
                 updateTask.setId(cardTask.getId());
                 updateTask.setSyncStatus(ONE);
                 updateTask.setUpdateBy("syncCardTaskIdService");
                 cardTaskMapper.updateById(updateTask);
             }
             log.info("syncCardInitTaskService 回调板卡任务信息 cardTaskId={}  syncFlag={}", cardTask.getId(), syncFlag);
         });
         return true;
     }
 
     @Override
     @Transactional(rollbackFor = Exception.class)
     public List<TaskVO> addReinstallTaskService(ReinstallDTO parma) {
 
         if (CollectionUtils.isEmpty(parma.getCardIds())) {
             throw new BasicException(FAILED_TO_OBTAIN_CARD_INFORMATION);
         }
 
         List<TaskVO> taskVOs = new ArrayList<>();
         List<CardTask> cardTasks = new ArrayList<>();
         List<CardTaskSub> CardTaskSubs = new ArrayList<>();
 
         parma.getCardIds().forEach(cardId -> {
             Card card = cardService.getCardByCardId(cardId);
             if (ObjectUtils.isEmpty(card)) {
                 throw new BasicException(FAILED_TO_OBTAIN_CARD_INFORMATION);
             }
             CardTask cardTask = new CardTask();
             cardTask.setServerSn(card.getServerSn());
             cardTask.setCardId(cardId);
             if (parma.getType().equals(ONE)) {
                 cardTask.setType(REINSTALL);
             } else if (parma.getType().equals(TWO)) {
                 cardTask.setType(FLASH_BOOT);
             }
             cardTask.setTimeout(plusMinutesDateTime(parma.getTimeOut()));
             cardTask.setTaskContent(parma.getName());
             cardTasks.add(cardTask);
         });
 
         if (CollectionUtils.isEmpty(cardTasks)) {
             throw new BasicException(FAILED_TO_ADD_NODE_FLASHING_TASK);
         }
         cardTaskMapper.insertCardTaskBatch(cardTasks);
 
         cardTasks.forEach(cardTask -> {
             CardTaskSub cardTaskSub = new CardTaskSub();
             cardTaskSub.setMainTask(cardTask.getId());
             if (parma.getType().equals(ONE)) {
                 cardTaskSub.setType(CardTaskSubType.REINSTALL);
             } else if (parma.getType().equals(TWO)) {
                 cardTaskSub.setType(CardTaskSubType.FLASH_BOOT);
             }
 
             cardTaskSub.setTimeout(cardTask.getTimeout());
             cardTaskSub.setServerSn(cardTask.getServerSn());
             CardTaskSubs.add(cardTaskSub);
 
             TaskVO taskVO = new TaskVO(cardTask.getId(), cardTask.getCardId());
             taskVOs.add(taskVO);
         });
         cardTaskSubMapper.insertBatch(CardTaskSubs);
         return taskVOs;
     }
 
     @Override
     public List<PowerRestartTaskVO> addPaasSetCardNetworkTaskService(List<CardNetworkDTO> cardNetworkInfos) {
         log.info("initArmServer start addSetCardNetWorkTaskService time:{}", LocalDateTime.now());
         List<PowerRestartTaskVO> taskVOS = new ArrayList<>();
         List<CardTask> cardTasks = new ArrayList<>();
         cardNetworkInfos.forEach(cardNet -> {
             String sn = null;
             if (ObjectUtils.isEmpty(cardNet.getServerSn())) {
                 Card cardVO = cardService.getCardByCardId(cardNet.getCardId());
                 sn = cardVO.getServerSn();
             } else {
                 sn = cardNet.getServerSn();
             }
 
             CardTask cardTask = new CardTask();
             cardTask.setTaskId(cardNet.getOutTaskId());
             cardTask.setServerSn(sn);
             cardTask.setCardId(cardNet.getCardId());
             cardTask.setType(SET_NETWORK);
             cardTask.setTaskContent(JSON.toJSONString(cardNet));
             cardTask.setTimeout(plusMinutesDateTime(cardNet.getTimeOut()));
             cardTasks.add(cardTask);
 
         });
         cardTaskMapper.insertCardTaskBatch(cardTasks);
 
         List<CardTaskSub> cardTaskSubs = new ArrayList<>();
         cardTasks.forEach(cardTask -> {
             //添加设置IP子任务
             CardTaskSub cardTaskSub = new CardTaskSub();
             cardTaskSub.setServerSn(cardTask.getServerSn());
             cardTaskSub.setMainTask(cardTask.getId());
             cardTaskSub.setType(CardTaskSubType.SET_NETWORK_IP);
             cardTaskSub.setServerSn(cardTask.getServerSn());
             cardTaskSub.setTimeout(cardTask.getTimeout());
             cardTaskSubs.add(cardTaskSub);
             taskVOS.add(new PowerRestartTaskVO(cardTask.getId(), cardTask.getTaskId()));
         });
         cardTaskSubMapper.insertBatch(cardTaskSubs);
         log.info("initArmServer end addSetCardNetWorkTaskService time:{}", LocalDateTime.now());
         return taskVOS;
     }
 }