 package net.armcloud.paascenter.bmc.utils;
 
 
 import com.alibaba.fastjson2.JSON;
 import lombok.extern.slf4j.Slf4j;
 import org.apache.http.Header;
 import org.apache.http.client.config.RequestConfig;
 import org.apache.http.client.methods.*;
 import org.apache.http.conn.ssl.NoopHostnameVerifier;
 import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
 import org.apache.http.conn.ssl.TrustAllStrategy;
 import org.apache.http.entity.ContentType;
 import org.apache.http.entity.StringEntity;
 import org.apache.http.impl.client.CloseableHttpClient;
 import org.apache.http.impl.client.HttpClients;
 import org.apache.http.message.BasicHeader;
 import org.apache.http.ssl.SSLContextBuilder;
 import org.apache.http.util.EntityUtils;
 
 import java.util.Map;
 
 @Slf4j
 public class HttpClientUtils {
     private final static Integer TIMEOUT = 15000;
 
 
     /**
      * 获取请求配置参数
      *
      * @return RequestConfig
      */
     private static RequestConfig getRequestConfig() {
         // 创建请求配置，设置超时时间
         return RequestConfig.custom().setConnectTimeout(TIMEOUT)  // 设置连接超时
                 .setConnectionRequestTimeout(TIMEOUT)  // 设置连接请求超时
                 .build();
     }
 
     private static Header[] getHeaders(Map<String, String> headers) {
         return headers.entrySet().stream().map(entry -> new BasicHeader(entry.getKey(), entry.getValue())).toArray(Header[]::new);
     }
 
     /**
      * 创建模拟客户端（针对 https 客户端禁用 SSL 验证）
      *
      * @return
      * @throws Exception
      */
     public static CloseableHttpClient createHttpClientWithNoSsl() throws Exception {
         SSLContextBuilder builder = SSLContextBuilder.create();
         builder.loadTrustMaterial(new TrustAllStrategy());
         SSLConnectionSocketFactory ssl_sf = new SSLConnectionSocketFactory(builder.build(), NoopHostnameVerifier.INSTANCE);
         return HttpClients.custom().setSSLSocketFactory(ssl_sf).setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).build();
     }
 
     /**
      * get请求
      *
      * @param url 请求地址
      * @return String
      */
     public static String doGet(String url, Map<String, String> headers) {
         try (CloseableHttpClient httpClient = createHttpClientWithNoSsl()) {
             HttpGet get = new HttpGet(url);
             get.setConfig(getRequestConfig());
 
             if (null != headers) {
                 get.setHeaders(getHeaders(headers));
             }
             try (CloseableHttpResponse response = httpClient.execute(get)) {
                 return EntityUtils.toString(response.getEntity());
             }
         } catch (Exception e) {
             log.error("httpClient doGet error,url:{}", url, e);
         }
         return null;
     }
 
     /**
      * post请求
      *
      * @param url  请求地址
      * @param body 请求参数
      * @return String
      */
     public static String doPost(String url, Object body, Map<String, String> headers) {
         try (CloseableHttpClient httpClient = createHttpClientWithNoSsl()) {
             HttpPost post = new HttpPost(url);
             post.setConfig(getRequestConfig());
 
             if (null != headers) {
                 post.setHeaders(getHeaders(headers));
             }
             if (null != body) {
                 post.setEntity(new StringEntity(JSON.toJSONString(body), ContentType.APPLICATION_JSON));
             }
 
             try (CloseableHttpResponse response = httpClient.execute(post)) {
                 return EntityUtils.toString(response.getEntity());
             }
         } catch (Exception e) {
             log.error("httpClient doPost error,url:{},body:{}", url, JSON.toJSON(body), e);
         }
         return null;
     }
 
     /**
      * put请求
      *
      * @param url  请求地址
      * @param body 请求参数
      * @return String
      */
     public static String doPut(String url, String body,Map<String, String> headers) {
         try (CloseableHttpClient httpClient = createHttpClientWithNoSsl()) {
             HttpPut put = new HttpPut(url);
             put.setConfig(getRequestConfig());
 
             if (null != headers) {
                 put.setHeaders(getHeaders(headers));
             }
             if (null != body) {
                 put.setEntity(new StringEntity(JSON.toJSONString(body), ContentType.APPLICATION_JSON));
             }
 
             try (CloseableHttpResponse response = httpClient.execute(put)) {
                 return EntityUtils.toString(response.getEntity());
 
             }
         } catch (Exception e) {
             log.error("httpClient doPut error,url:{},body:{}", url, JSON.toJSON(body), e);
         }
         return null;
     }
 
     /**
      * delete请求
      *
      * @param url 请求地址
      * @return String
      */
     public static String doDelete(String url, Map<String, String> headers) {
         try (CloseableHttpClient httpClient = createHttpClientWithNoSsl()) {
             HttpDelete delete = new HttpDelete(url);
             delete.setConfig(getRequestConfig());
 
             if (null != headers) {
                 delete.setHeaders(getHeaders(headers));
             }
             try (CloseableHttpResponse response = httpClient.execute(delete)) {
                 return EntityUtils.toString(response.getEntity());
             }
         } catch (Exception e) {
             log.error("httpClient doDelete error,url:{}", url, e);
         }
         return null;
     }
 
 }