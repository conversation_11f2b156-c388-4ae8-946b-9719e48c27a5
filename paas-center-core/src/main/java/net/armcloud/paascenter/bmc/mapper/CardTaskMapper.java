 package net.armcloud.paascenter.bmc.mapper;
 
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
 import net.armcloud.paascenter.common.model.entity.bmc.CardTask;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.List;
 
 @Mapper
 public interface CardTaskMapper extends BaseMapper<CardTask> {
     /**
      * 插入任务
      *
      * @param cardTask 节点任务
      * @return CardTask
      */
     int insertCardTask(CardTask cardTask);
 
 
     /**
      * 插入任务
      *
      * @param cardTask 节点任务
      * @return CardTask
      */
     int insertCardTaskBatch(@Param("list") List<CardTask> cardTask);
 
     /**
      * 查询待执行任务的服务器列表
      *
      * @return
      */
     List<String> selectExecutedServerSn(@Param("type") Integer type);
 
     /**
      * @param types
      * @return
      */
     List<String> selectExecutedServerByTypes(@Param("types") List<Integer> types);
 
     /**
      * 任务ID更新null
      *
      * @param id
      * @return
      */
     int updateCardTaskIdToNull(@Param("id") Long id);
 }