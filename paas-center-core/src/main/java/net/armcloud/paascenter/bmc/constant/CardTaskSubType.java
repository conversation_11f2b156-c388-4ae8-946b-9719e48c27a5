 package net.armcloud.paascenter.bmc.constant;
 
 /**
  * 节点子任务类型
  */
 public class CardTaskSubType {
     /**
      * 下电
      */
     public static final int POWER_OFF = 1;
     /**
      * 下电结果查询
      */
     public static final int POWER_OFF_QUERY = 2;
     /**
      * 上电
      */
     public static final int POWER_ON = 3;
     /**
      * 上电结果查询
      */
     public static final int POWER_ON_QUERY = 4;
     /**
      * 设置IP
      */
     public static final int SET_NETWORK_IP = 5;
     /**
      * 节点重置
      */
     public static final int CARD_RESET = 6;
     /**
      * 节点重置结果
      */
     public static final int CARD_RESET_RESULT = 7;
 
     /**
      * 节点恢复出厂设置
      */
     public static final int CARD_REINIT = 8;
     /**
      * 上传镜像
      */
     public static final int UPLOAD_IMAGES = 9;
 
     /**
      * 板卡刷 Debian 系统
      */
     public static final int REINSTALL = 10;
 
     /**
      * 板卡刷 boot(内核)
      */
     public static final int FLASH_BOOT = 11;
 
 }