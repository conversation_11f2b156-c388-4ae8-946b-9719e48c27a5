 package net.armcloud.paascenter.bmc.controller;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmServerInfoVO;
 import net.armcloud.paascenter.common.lingdian.service.ILinDianApiService;
 import net.armcloud.paascenter.bmc.model.dto.DeleteArmServerDTO;
 import net.armcloud.paascenter.bmc.model.dto.DeleteImagesDTO;
 import net.armcloud.paascenter.bmc.model.dto.InitArmServerDTO;
 import net.armcloud.paascenter.bmc.model.dto.UploadImagesDTO;
 import net.armcloud.paascenter.bmc.model.vo.ArmServerInitVO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 import net.armcloud.paascenter.bmc.service.IServerService;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.validation.annotation.Validated;
 import org.springframework.web.bind.annotation.*;
 
 import javax.annotation.Resource;
 import javax.validation.Valid;
 import java.time.LocalDateTime;
 
 import static net.armcloud.paascenter.bmc.constant.ArmServerType.LINGDIAN;
 
 @Slf4j
 @RequestMapping("/armcloud-bmc-manage/server/")
 @RestController
 @Validated
 public class BmcArmServerController {
 
     @Resource
     private IServerService serverManageService;
 
     @Resource
     private ILinDianApiService iLinDianApiService;
 
     @PostMapping("init")
     public Result<ArmServerInitVO> initArmServer(@Valid @RequestBody InitArmServerDTO parma) {
         log.info("initArmServer start time:{}", LocalDateTime.now());
         ArmServerInitVO result = null;
         if (LINGDIAN.equals(parma.getArmServerType())) {
             result = serverManageService.addArmServerAndCardInfo(parma);
         }
         log.info("initArmServer end time:{}", LocalDateTime.now());
         return Result.ok(result);
     }
 
     @PostMapping("pull/increment")
     public Result<ArmServerInitVO> pullIncrement(@Valid @RequestBody InitArmServerDTO parma) {
         ArmServerInitVO result = null;
         //目前只使用凌点
         if (LINGDIAN.equals(parma.getArmServerType())) {
             result = serverManageService.pullArmServerAndCardInfo(parma);
         }
         return Result.ok(result);
     }
 
     @PostMapping("delete")
     public Result<ArmServerInitVO> deleteArmServer(@Valid @RequestBody DeleteArmServerDTO parma) {
         log.info("deleteArmServer start time:{}", LocalDateTime.now());
         serverManageService.deleteArmServerService(parma);
         log.info("deleteArmServer end time:{}", LocalDateTime.now());
         return Result.ok();
     }
 
     /**
      * 上传镜像
      *
      * @return
      */
     @PostMapping("uploadImages")
     public Result<TaskVO> uploadImages(@Valid @RequestBody UploadImagesDTO parma) {
         log.info("uploadImages start time:{}", LocalDateTime.now());
         TaskVO taskVO = serverManageService.uploadImagesService(parma);
         log.info("uploadImages end time:{}", LocalDateTime.now());
         return Result.ok(taskVO);
     }
 
     /**
      * 删除镜像
      *
      * @return
      */
     @PostMapping("deleteImages")
     public Result<TaskVO> deleteImages(@Valid @RequestBody DeleteImagesDTO parma) {
         log.info("deleteImages start time:{}", LocalDateTime.now());
         Boolean flag = serverManageService.deleteImagesService(parma);
         log.info("deleteImages end time:{}", LocalDateTime.now());
         return flag ? Result.ok() : Result.fail();
     }
 
 
     @GetMapping("detail")
     public Result<ArmServerInfoVO> getDetail(@RequestParam("socApiUrl") String socApiUrl) {
         ArmServerInfoVO serverInfoService = iLinDianApiService.getServerInfoService(socApiUrl);
         return Result.ok(serverInfoService);
 
     }
 
 
 }