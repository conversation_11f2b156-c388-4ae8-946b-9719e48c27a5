 package net.armcloud.paascenter.bmc.mapper;
 
 import com.baomidou.mybatisplus.core.mapper.BaseMapper;
 import net.armcloud.paascenter.common.model.entity.bmc.Card;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
 
 import java.util.List;
 
 @Mapper
 public interface CardMapper extends BaseMapper<Card> {
     /**
      * 批量插入任务
      *
      * @param list
      * @return
      */
     int insertBatch(@Param("list") List<Card> list);
 
     List<Card> selectCardByServerId(@Param("serverId") Long serverId);
 }