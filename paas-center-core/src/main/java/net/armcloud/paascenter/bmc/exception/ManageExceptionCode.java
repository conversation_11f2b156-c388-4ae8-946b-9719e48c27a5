 package net.armcloud.paascenter.bmc.exception;
 
 import lombok.AllArgsConstructor;
 import lombok.Getter;
 import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
 
 /**
  * 10 -用户
  * <p>
  * 20-节点
  */
 
 @Getter
 @AllArgsConstructor
 public enum ManageExceptionCode implements ExceptionCode {
     SUCCESS(200, "成功"),
     SYSTEM_EXCEPTION(500, "系统异常"),
     USER_DOES_NOT_EXIST(1100, "用户不存在"),
     PASSWORD_ERROR(1101, "密码错误"),
 
     TOKEN_IS_INVALID(1000, "token无效，请重新登录"),
     FAILED_TO_OBTAIN_CARD_INFORMATION(1001, "获取板卡信息失败"),
     FAILED_TO_OBTAIN_SERVER_INFORMATION(1002, "获取服务器信息失败"),
     FAILED_TO_INCOMPLETE_NODE_INFORMATION(1003, "获取节点信息不全，请检查SOC节点是否正常"),
     ARM_SERVER_DOES_NOT_EXIST(1004, "删除失败，ARM服务器不存在"),
     ARM_SERVER_NOT_EXIST(1005, "ARM服务器不存在"),
 
     NODE_POWER_RESTART_TASK_ALREADY_EXISTS(2000, "节点断电重启任务已存在"),
     FAILED_TO_ADD_POWER_OUTAGE_TASK(2001, "添加节点断电重启任务失败，请重试"),
     NODE_POWER_OFF_TASK_ALREADY_EXISTS(2002, "节点下电任务已存在"),
     NODE_POWER_OFF_FAILURE(2003, "节点下电失败，请重试"),
     THE_TASK_DOES_NOT_EXIST_IN_BMC(2004, "bmc不存在该任务，请确认任务id是否正确"),
     FAILED_TO_ADD_NODE_RESET_TASK(2005, "添加节点重置任务失败,请重试"),
     NODE_ID_DOES_NOT_EXIST(2006, "添加断电重启任务失败，节点ID不存在，请检查后重试"),
     BMC_FAILED_TO_OBTAIN_TOKEN(2007, "bmc获取token失败，请重试"),
     NODE_RESET_FAILED(2008, "节点恢复出厂设置失败"),
     FAILED_TO_ADD_NETWORK_TASK_SETTINGS(2009, "添加设置网络任务失败，请重试"),
     NODE_SET_NETWORK_TASK_ALREADY_EXISTS(2010, "节点断电重启任务已存在"),
     FAILED_TO_ADD_IMAGE_UPLOAD_TASK(2011, "添加镜像上传任务失败,请重试"),
     FAILED_TO_ADD_NODE_FLASHING_TASK(2012, "添加节点刷机任务失败"),
     FAILED_TO_DELETE_IMAGE(2013, "删除镜像失败"),
     ;
 
 
     private final int status;
     private final String msg;
 }