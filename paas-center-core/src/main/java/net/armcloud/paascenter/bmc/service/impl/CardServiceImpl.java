 package net.armcloud.paascenter.bmc.service.impl;
 
 import com.alibaba.fastjson2.JSON;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import net.armcloud.paascenter.bmc.mapper.CardMapper;
 import net.armcloud.paascenter.common.model.entity.bmc.Card;
 import net.armcloud.paascenter.common.redis.contstant.KeyPrefix;
 import net.armcloud.paascenter.common.redis.contstant.KeyTime;
 import net.armcloud.paascenter.bmc.utils.BmcRedisService;
 import net.armcloud.paascenter.bmc.service.ICardService;
 import org.springframework.stereotype.Service;
 import org.springframework.util.ObjectUtils;
 
 import java.util.concurrent.TimeUnit;
 
 
 @Service
 public class CardServiceImpl extends ServiceImpl<CardMapper, Card> implements ICardService {
     private final BmcRedisService bmcRedisService;
     private final CardMapper cardMapper;
 
     public CardServiceImpl(BmcRedisService bmcRedisService, CardMapper cardMapper) {
         this.bmcRedisService = bmcRedisService;
         this.cardMapper = cardMapper;
     }
 
     @Override
     public Card getCardByCardId(String cardId) {
         String key = KeyPrefix.BMC_CARD_LIST + cardId;
         Object cacheObject = bmcRedisService.getCacheObject(key);
         if (cacheObject != null) {
             return JSON.parseObject(cacheObject.toString(), Card.class);
         }
         Card card = cardMapper.selectOne(new QueryWrapper<Card>().eq("card_id", cardId).last("limit 1"));
         if (ObjectUtils.isEmpty(card)) {
             return null;
         }
         String jsonString = JSON.toJSONString(card);
         bmcRedisService.setCacheObject(key, jsonString, KeyTime.minute_10, TimeUnit.MINUTES);
         return card;
 
     }
 }