 package net.armcloud.paascenter.bmc.configure;
 
 import org.springframework.context.annotation.Bean;
 import org.springframework.context.annotation.Configuration;
 
 import java.util.concurrent.Executors;
 import java.util.concurrent.ScheduledExecutorService;
 
 @Configuration
 public class ScheduledThreadPoolConfig {
     @Bean
     public ScheduledExecutorService scheduledExecutorService() {
         return Executors.newScheduledThreadPool(5);
     }
 
     public static int calculateOptimalThreadPoolSize() {
         int corePoolSize = Runtime.getRuntime().availableProcessors();
         double targetCpuUtilization = 0.8;
         double waitTimeRatio = 0.8;
 
         return (int) (corePoolSize * targetCpuUtilization / (1 - waitTimeRatio));
 
     }
 }