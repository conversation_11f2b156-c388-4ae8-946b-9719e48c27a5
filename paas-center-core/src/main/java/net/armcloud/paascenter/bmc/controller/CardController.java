 package net.armcloud.paascenter.bmc.controller;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import net.armcloud.paascenter.common.core.exception.BasicException;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmCardStatusVO;
 import net.armcloud.paascenter.common.lingdian.model.vo.ArmCardVO;
 import net.armcloud.paascenter.common.model.entity.bmc.Server;
 import net.armcloud.paascenter.common.redis.contstant.KeyPrefix;
 import net.armcloud.paascenter.common.redis.contstant.KeyTime;
 import net.armcloud.paascenter.bmc.utils.BmcRedisService;
 import net.armcloud.paascenter.bmc.model.dto.*;
 import net.armcloud.paascenter.bmc.model.vo.PowerRestartTaskVO;
 import net.armcloud.paascenter.bmc.model.vo.TaskVO;
 import net.armcloud.paascenter.bmc.service.ICardManageService;
 import net.armcloud.paascenter.bmc.service.ICardTaskService;
 import net.armcloud.paascenter.bmc.service.IServerService;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.util.CollectionUtils;
 import org.springframework.util.ObjectUtils;
 import org.springframework.validation.annotation.Validated;
 import org.springframework.web.bind.annotation.*;
 
 import javax.annotation.Resource;
 import javax.validation.Valid;
 import java.util.ArrayList;
 import java.util.List;
 import java.util.concurrent.TimeUnit;
 import java.util.stream.Collectors;
 
 import static net.armcloud.paascenter.bmc.exception.ManageExceptionCode.FAILED_TO_ADD_NETWORK_TASK_SETTINGS;
 import static net.armcloud.paascenter.bmc.exception.ManageExceptionCode.FAILED_TO_ADD_POWER_OUTAGE_TASK;
 
 @RequestMapping("/armcloud-bmc-manage/card/")
 @RestController
 @Validated
 public class CardController {
     @Resource
     private BmcRedisService bmcRedisService;
     @Resource
     private ICardManageService cardManageService;
     @Autowired
     private IServerService serverService;
     @Resource
     private ICardTaskService cardTaskService;
 
     /**
      * 添加所有节点恢复出厂任务
      *
      * @param parma
      * @return
      */
     @PostMapping("reCardInitAll")
     public Result<?> reCardInit(@Valid @RequestBody ReCardInitDTO parma) {
         Server server = serverService.getServerBySocUrl(parma.getSocApiUrl());
         List<ArmCardVO> cardVOList = cardManageService.getCardsInfoService(parma.getSocApiUrl());
         List<TaskVO> taskVOS = cardTaskService.addReCardInitTaskService(server, cardVOList, parma.getTimeOut());
         return Result.ok(taskVOS);
     }
 
     /**
      * 添加单节点恢复出厂任务
      *
      * @param parma
      * @return
      */
     @PostMapping("reCardInit")
     public Result<?> getCardList(@Valid @RequestBody ReCardInitDTO parma) {
         Server server = serverService.getServerBySocUrl(parma.getSocApiUrl());
         List<ArmCardVO> cardVOList = new ArrayList<>();
         parma.getCardIds().forEach(cardId -> {
             ArmCardVO cardVO = new ArmCardVO();
             cardVO.setId(cardId);
             cardVOList.add(cardVO);
         });
         List<TaskVO> taskVOS = cardTaskService.addReCardInitTaskService(server, cardVOList, parma.getTimeOut());
         return Result.ok(taskVOS);
     }
 
     @GetMapping("infos")
     public Result<?> getCardList(@Valid @RequestBody UserLoginDTO parma) {
         List<ArmCardVO> cardVOList = cardManageService.getCardsInfoService(parma.getSocApiUrl());
         return Result.ok(cardVOList);
     }
 
     /**
      * 设置节点与soc链接对应关系
      *
      * @param socApiUrl socApiUrl
      * @param cardIds   节点id
      */
     private void setSocUrlAndCardId(String socApiUrl, List<String> cardIds) {
         if (!CollectionUtils.isEmpty(cardIds)) {
             cardIds.forEach(cardId -> {
                 String nodeSocKey = KeyPrefix.LING_DIAN_NODE_SOC_URL + cardId;
                 bmcRedisService.setCacheObject(nodeSocKey, socApiUrl, KeyTime.day_1, TimeUnit.DAYS);
             });
         }
     }
 
     @PostMapping("powerRestart")
     public Result<?> powerRestart(@Valid @RequestBody PowerRestartDTO parma) {
         if (CollectionUtils.isEmpty(parma.getTasks())) {
             return Result.fail();
         }
         List<String> cardIds = parma.getTasks().stream().map(PowerRestartTaskDTO::getCardId).collect(Collectors.toList());
         this.setSocUrlAndCardId(parma.getSocApiUrl(), cardIds);
 
         List<String> failCardIds = cardTaskService.checkCardRunningTask(cardIds);
         if (!CollectionUtils.isEmpty(failCardIds)) {
             return Result.fail(failCardIds, "Node is executing task, please try again later");
         }
 
         //添加节点断电重启任务
         List<PowerRestartTaskVO> taskVOS = cardTaskService.addCardPowerRestartTaskService(parma.getTasks(), parma.getTimeOut());
         if (ObjectUtils.isEmpty(taskVOS)) {
             throw new BasicException(FAILED_TO_ADD_POWER_OUTAGE_TASK);
         }
         return Result.ok(taskVOS);
     }
 
     @PostMapping("setNetwork")
     public Result<?> setCardNetwork(@Valid @RequestBody SetCardNetworkDTO parma) {
         List<String> cardIds = parma.getCardNetworkInfos().stream().map(CardNetworkDTO::getCardId).collect(Collectors.toList());
         this.setSocUrlAndCardId(parma.getSocApiUrl(), cardIds);
 
         List<String> failCardIds = cardTaskService.checkCardRunningTask(cardIds);
         if (!CollectionUtils.isEmpty(failCardIds)) {
             return Result.fail(failCardIds, "Node is executing task, please try again later");
         }
 
         List<PowerRestartTaskVO> taskVOS = cardTaskService.addPaasSetCardNetworkTaskService(parma.getCardNetworkInfos());
         if (CollectionUtils.isEmpty(taskVOS)) {
             throw new BasicException(FAILED_TO_ADD_NETWORK_TASK_SETTINGS);
         }
         return Result.ok(taskVOS);
     }
 
     @PostMapping("reset")
     public Result<?> cardReset(@Valid @RequestBody ResetCardDTO parma) {
         List<String> cardIds = parma.getCardTasks().stream().map(CardTaskDTO::getCardId).collect(Collectors.toList());
         List<String> failCardIds = cardTaskService.checkCardRunningTask(cardIds);
         if (!CollectionUtils.isEmpty(failCardIds)) {
             return Result.fail(failCardIds, "Node is executing task, please try again later");
         }
 
         return Result.ok(cardManageService.addCardResetService(parma));
     }
 
     @PostMapping("getNetwork")
     public Result<?> getCardNetwork(@Valid @RequestBody BaseRequestDTO parma) {
         return Result.ok(cardManageService.getArmCardNetworkService(parma.getSocApiUrl()));
     }
 
     @PostMapping("taskInfo")
     public Result<?> getCardTask(@Valid @RequestBody CardTaskInfoDTO parma) {
         return Result.ok(cardTaskService.getTaskInfoService(parma.getTaskId()));
     }
 
     @PostMapping("nodeInfo")
     public Result<?> nodeInfo(@Valid @RequestBody BaseRequestDTO parma) {
         return Result.ok(cardManageService.getNodesInfoService(parma.getSocApiUrl(), "0"));
     }
 
     @PostMapping("reinstall")
     public Result<?> reinstall(@Valid @RequestBody ReinstallDTO parma) {
         return Result.ok(cardTaskService.addReinstallTaskService(parma));
     }
 
     @PostMapping("networkInfo")
     public Result<?> uploadImages(@Valid @RequestBody CardNetworkInfoDTO parma) {
         return Result.ok(cardManageService.getCardNetworkInfo(parma));
     }
 
     @PostMapping("status")
     public Result<?> getCardStatusList(@Valid @RequestBody CardNetworkInfoDTO parma) {
         List<ArmCardStatusVO> cardVOList = cardManageService.getCardStatusInfo(parma);
         return Result.ok(cardVOList);
     }
 }