 package net.armcloud.paascenter.bmc.controller;
 
 import net.armcloud.paascenter.common.core.domain.Result;
 import org.springframework.web.bind.annotation.GetMapping;
 import org.springframework.web.bind.annotation.RequestMapping;
 import org.springframework.web.bind.annotation.RestController;
 
 @RequestMapping("/armcloud-bmc-manage/heartbeat/")
 @RestController
 public class HeartbeatController {
     @GetMapping("status")
     public Result<?> heartbeat() {
         return Result.ok();
     }
 }