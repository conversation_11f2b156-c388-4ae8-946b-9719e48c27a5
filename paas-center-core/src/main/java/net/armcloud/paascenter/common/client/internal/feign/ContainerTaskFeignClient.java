package net.armcloud.paascenter.common.client.internal.feign;

import net.armcloud.paascenter.cms.model.request.TaskDetailRequest;
import net.armcloud.paascenter.cms.model.response.DeviceTaskDetailResponse;
import net.armcloud.paascenter.cms.model.response.InstanceTaskDetailResponse;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.net.URI;

@FeignClient(name = "armcloud-container-task", url = "placeholder")
public interface ContainerTaskFeignClient {

    /**
     * 实例任务详情
     */
    @PostMapping(value = "/armcloud-container/open/task/instance/detail")
    Result<InstanceTaskDetailResponse> instanceDetail(URI host, TaskDetailRequest request);

    /**
     * 云机任务详情
     */
    @PostMapping(value = "/armcloud-container/open/task/device/detail")
    Result<DeviceTaskDetailResponse> deviceDetail(URI host, TaskDetailRequest request);
}
