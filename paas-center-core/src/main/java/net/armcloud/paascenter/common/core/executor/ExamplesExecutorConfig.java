//package net.armcloud.paascenter.common.core.executor;
//
//import lombok.extern.slf4j.Slf4j;
//import net.armcloud.paascenter.common.core.trace.TraceIdTaskDecorator;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.scheduling.annotation.AsyncConfigurer;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
//import java.util.concurrent.Executor;
//import java.util.concurrent.ThreadPoolExecutor;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@Configuration
//public class ExamplesExecutorConfig implements AsyncConfigurer {
//
//    @Bean(name = "examples")
//    public Executor examples() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(2);
//        executor.setMaxPoolSize(5);
//        executor.setQueueCapacity(10);
//        executor.setKeepAliveSeconds(5 * 60);
//        executor.setThreadNamePrefix("examples-");
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
//        executor.setTaskDecorator(new TraceIdTaskDecorator());
//        return executor;
//    }
//
//
//}
