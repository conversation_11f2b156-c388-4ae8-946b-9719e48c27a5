package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
@Accessors(chain = true)
public class JoinArmcloudShareRoomCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "roomCode cannot null")
    private String roomCode;

    @NotNull(message = "userId cannot null")
    private String userId;

    @NotBlank(message = "roomToken cannot null")
    private String roomToken;

    @NotBlank(message = "signalServer cannot null")
    private String signalServer;
}
