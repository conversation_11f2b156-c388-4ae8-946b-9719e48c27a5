package net.armcloud.paascenter.common.redis.delay;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Redis延时任务工具类
 * 提供便捷的延时任务提交方法
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Slf4j
public class RedisDelayTaskUtils {
    
    /**
     * 提交延时任务
     * 
     * @param executorName 执行器名称
     * @param taskData 任务数据
     * @param delayTimeMs 延时时间（毫秒）
     * @return 任务ID
     */
    public static String submitDelayTask(String executorName, String taskData, long delayTimeMs) {
        try {
            RedisDelayTaskManager manager = SpringUtil.getBean(RedisDelayTaskManager.class);
            return manager.submitDelayTask(executorName, taskData, delayTimeMs);
        } catch (Exception e) {
            log.error("Failed to submit redis delay task: executor={}, delay={}ms", executorName, delayTimeMs, e);
            return null;
        }
    }
    
    /**
     * 提交延时任务（秒为单位）
     * 
     * @param executorName 执行器名称
     * @param taskData 任务数据
     * @param delayTimeSeconds 延时时间（秒）
     * @return 任务ID
     */
    public static String submitDelayTaskSeconds(String executorName, String taskData, long delayTimeSeconds) {
        return submitDelayTask(executorName, taskData, delayTimeSeconds * 1000);
    }
    
    /**
     * 取消延时任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public static boolean cancelDelayTask(String taskId) {
        try {
            RedisDelayTaskManager manager = SpringUtil.getBean(RedisDelayTaskManager.class);
            return manager.cancelDelayTask(taskId);
        } catch (Exception e) {
            log.error("Failed to cancel redis delay task: taskId={}", taskId, e);
            return false;
        }
    }
}
