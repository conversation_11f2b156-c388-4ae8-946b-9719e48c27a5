package net.armcloud.paascenter.common.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CustomerSelectCallbackVO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 回调类型type
     */
    @ApiModelProperty(value = "回调类型type")
    private String callbackType;

    /**
     * 回调类型名称
     */
    @ApiModelProperty(value = "回调类型名称")
    private String callbackName;


}
