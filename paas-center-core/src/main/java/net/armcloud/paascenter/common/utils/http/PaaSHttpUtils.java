package net.armcloud.paascenter.common.utils.http;

import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class PaaSHttpUtils {
    /**
     * 获取所有请求头
     *
     * @param request ServerHttpRequest
     * @return Map<String, String>
     */
    public static Map<String, String> getServerHttpRequestAllHeaders(ServerHttpRequest request) {
        HttpHeaders headerNames = request.getHeaders();
        Map<String, String> headerMap = new HashMap<>();
        headerNames.forEach((key, values) -> headerMap.put(key, values.get(0)));
        return headerMap;
    }

    /**
     * 获取所有请求头
     *
     * @param request HttpServletRequest
     * @return Map<String, String>
     */
    public static Map<String, String> getAllHeaders(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames == null) {
            return Collections.emptyMap();
        }
        return Collections.list(headerNames).stream().collect(Collectors.toMap(name -> name, request::getHeader));
    }

    /**
     * 获取请求体
     *
     * @param request HttpServletRequest
     * @return byte[] body数组
     *
     */
    public static byte[] getRequestBody(HttpServletRequest request) {
        try {
            InputStream inputStream = request.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }
}
