package net.armcloud.paascenter.common.core.constant.callback;


import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * 回调订阅类型
 */
public class CallbackTypeConstants {

    /**
     * 实例升级镜像
     */
    public static final Integer PAD_UPGRADE_IMAGES_CALLBACK_TYPE = 3;

    /**
     * 文件上传任务
     */
    public static final Integer FILE_UPLOAD_TASK_CALLBACK_TYPE = 4;

    /**
     * 文件上传实例任务
     */
    public static final Integer PAD_DOWNLOAD_FILE_TASK_CALLBACK_TYPE = 5;

    /**
     * 应用同步任务
     */
    public static final Integer CACHE_APP_TASK_CALLBACK_TYPE = 6;

    /**
     * 应用安装任务
     */
    public static final Integer PAD_DOWNLOAD_APP_FILE_TASK_CALLBACK_TYPE = 7;

    /**
     * 应用卸载任务
     */
    public static final Integer PAD_UNINSTALL_APP_TASK_CALLBACK_TYPE = 8;

    /**
     * 异步执行ADB任务
     */
    public static final Integer PAD_EXECUTE_ADB_TASK_CALLBACK_TYPE = 9;

    /**
     * 修改实例属性任务
     */
    public static final Integer UPDATE_PAD_PROPERTY_TASK_CALLBACK_TYPE = 10;


    /**
     * 实例重启任务
     */
    public static final Integer PAD_RESTART_TASK_CALLBACK_TYPE = 11;

    /**
     * 实例重置任务
     */
    public static final Integer PAD_RESET_TASK_CALLBACK_TYPE = 12;

    /**
     * 实例升级镜像任务
     */
    public static final Integer PAD_UPGRADE_IMAGES_TASK_CALLBACK_TYPE = 13;

    /**
     * 获取实例已安装应用任务
     */
    public static final Integer PAD_INSTALLED_APP_LIST_TASK_CALLBACK_TYPE = 14;

    /**
     * 应用启停任务
     */
    public static final Integer APP_START_OR_STOP_APP_CALLBACK_TYPE = 15;

    /**
     * 删除文件
     */
    public static final Integer FILE_CENTER_FILE_DELETE_CALLBACK_TYPE = 16;

    /**
     * 云机状态
     */
    public static final Integer DEVICE_RESTART_CALLBACK_TYPE = 18;

    /**
     * 实例状态
     */
    public static final Integer PAD_STATUS_CALLBACK_TYPE = 19;

    /**
     * 应用黑名单任务
     */
    public static final Integer UPDATE_APP_BLACK_LIST_TYPE = 20;


    /**
     * 备份实例
     */
    public static final Integer PAD_BACKUP_TYPE = 21;

    /**
     * 还原备份数据
     */
    public static final Integer PAD_RESTORE_TYPE = 22;

    /**
     * 一键新机
     */
    public static final Integer REPLACE_PAD_TYPE = 23;
    /**
     * 应用白名单任务
     */
    public static final Integer UPDATE_APP_WHITE_LIST_TYPE = 24;

    /**
     * 镜像DOWNLOAD AND PUSH
     */
    public static final Integer IMAGE_DOWNLOAD_AND_PUSH = 30;

}
