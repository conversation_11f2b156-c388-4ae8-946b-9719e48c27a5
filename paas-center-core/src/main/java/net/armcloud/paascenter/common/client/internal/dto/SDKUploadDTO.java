package net.armcloud.paascenter.common.client.internal.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SDKUploadDTO {
    @NotBlank(message = "path cannot null")
    private String path;

    @NotBlank(message = "fileMd5 cannot null")
    private String fileMd5;

    @NotBlank(message = "token cannot null")
    private String token;

    @NotNull(message = "dcId cannot null")
    private Long dcId;

    @NotNull(message = "fileSize cannot null")
    public Long fileSize;

    private String uuid;

    @NotBlank(message = "fileName cannot null")
    private String fileName;

    private String iconPath;

    @NotBlank(message = "padCode cannot null")
    private String padCode;

    /**
     * 文件存储路径
     */
    private String targetDirectory;

    private App appInfo;

    /**
     * 操作人
     */
    private String oprBy;

    @Data
    public static class App {
        private String appName;
        private String packageName;
        private String signatureMd5;
        private Long versionNo;
        private String versionName;
    }

}
