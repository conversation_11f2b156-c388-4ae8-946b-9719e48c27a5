package net.armcloud.paascenter.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils {
    //匹配IPv4地址的正则表达式
    private static final String REGEX_IP = "(?:https?://)?(\\d{1,3}\\.){3}\\d{1,3}";

    /**
     * 获取socIp
     *
     * @param str
     * @return
     */
    public static String getSocIp(String str) {

        Pattern pattern = Pattern.compile(REGEX_IP);
        Matcher matcher = pattern.matcher(str);

        if (matcher.find()) {
            return matcher.group().replaceAll("https?://", "");
        }
        return null;
    }

}
