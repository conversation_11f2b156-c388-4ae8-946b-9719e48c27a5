package net.armcloud.paascenter.common.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

public class IpUtils {

/*    public static void main(String[] args) {
        String cidr = "**********/16";
        int requiredCount = 128;  // 指定需要的数量
        List<String> availableIPs = getAvailableIPs(cidr, requiredCount);

        for (String ip : availableIPs) {
            System.out.println(ip);
        }
    }*/

    public static List<String> getAvailableIPs(String cidr, int count) {
        List<String> ipList = new ArrayList<>();
        String[] parts = cidr.split("/");
        String ip = parts[0];
        int prefix;
        try {
            prefix = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid CIDR prefix");
        }

        // Calculate the start and end IP addresses
        long startIP = ipToLong(ip);
        long endIP = startIP + (1L << (32 - prefix)) - 1;

        // Exclude network address and broadcast address
        for (long i = startIP + 1; i < endIP && ipList.size() < count; i++) {
            String currentIp = longToIp(i);
            if (!currentIp.endsWith(".0")) {
                ipList.add(currentIp);
            }
        }

        return ipList;
    }

    private static long ipToLong(String ipAddress) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            byte[] octets = inetAddress.getAddress();
            long result = 0;
            for (byte octet : octets) {
                result <<= 8;
                result |= octet & 0xFF;
            }
            return result;
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("Invalid IP address");
        }
    }

    private static String longToIp(long ip) {
        return ((ip >> 24) & 0xFF) + "." +
                ((ip >> 16) & 0xFF) + "." +
                ((ip >> 8) & 0xFF) + "." +
                (ip & 0xFF);
    }
}
