package net.armcloud.paascenter.common.volcipaas.service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.volcipaas.rtcutils.AccessToken;
import net.armcloud.paascenter.common.volcipaas.rtcutils.Utils;
import org.springframework.stereotype.Service;

import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class RtcSdkService {

    /**
     * 获取rtc token
     *
     * @param appId             rtc appid
     * @param appKey            rtc appKey
     * @param roomCode          房间编号
     * @param userId            用户id
     * @param expire            有效使劲按
     * @param publishStreamFlag 是否具备发布流权限
     * @return token
     */

    public String getRtcTokenService(String appId, String appKey, String roomCode, String userId, Integer expire, Boolean publishStreamFlag) {
        AccessToken token = new AccessToken(appId, appKey, roomCode, userId);
        token.ExpireTime(Utils.getTimestamp() + expire);
        //// 添加订阅流权限
        token.AddPrivilege(AccessToken.Privileges.PrivSubscribeStream, Utils.getTimestamp() + expire);
        if (publishStreamFlag) {
            // 添加发布流权限
            token.AddPrivilege(AccessToken.Privileges.PrivPublishStream, Utils.getTimestamp() + expire);
        }
        return token.Serialize();
    }

    public String getPublicPushStreamToken(String appId, String appKey, long exp, String padCode) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("version", "1.0");
        payload.put("appID", appId);
        // 过期时间 精确到秒
        payload.put("exp", System.currentTimeMillis() / 1000 + exp);
        payload.put("action", "pub");
        payload.put("streamID", padCode);
        payload.put("enableSubAuth", false);

        Map<String, Object> header = new HashMap<>();
        header.put("alg", "HS256");
        header.put("typ", "JWT");

        byte[] keyBytes = appKey.getBytes();
        Key key = new SecretKeySpec(keyBytes, SignatureAlgorithm.HS256.getJcaName());
        return Jwts.builder()
                .setHeader(header)
                .setClaims(payload)
                .signWith(SignatureAlgorithm.HS256, key)
                .compact();
    }
}
