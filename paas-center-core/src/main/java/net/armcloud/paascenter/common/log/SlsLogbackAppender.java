package net.armcloud.paascenter.common.log;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import cn.hutool.core.util.StrUtil;
import net.armcloud.paascenter.common.service.SlsLogService;
import net.armcloud.paascenter.common.utils.TraceIdHelper;
import org.slf4j.MDC;
import org.springframework.context.ApplicationContext;

import java.util.HashMap;
import java.util.Map;

/**
 * SLS Logback Appender
 * 自定义Logback Appender，将日志发送到阿里云SLS
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
public class SlsLogbackAppender extends AppenderBase<ILoggingEvent> {

    private SlsLogService slsLogService;

    @Override
    protected void append(ILoggingEvent event) {
        // 尝试从多个途径获取SLS服务
        // 方法1: 从SlsServiceHolder获取
        slsLogService = SlsServiceHolder.getSlsLogService();

        // 如果SLS服务不可用，静默忽略，不影响正常日志记录
        if (slsLogService == null) {
            return;
        }

        try {
            // 获取基础信息
            String level = event.getLevel().toString();
            String logger = event.getLoggerName();
            String message = event.getFormattedMessage();
            String traceId = event.getMDCPropertyMap().get(TraceIdHelper.TRACE_ID());
            if (StrUtil.isBlank(traceId)) {
                traceId = "-";
            }

            // 构建精简的额外字段（不包含基础字段，避免重复）
            Map<String, String> extraFields = new HashMap<>();

            // 只添加非基础字段
            extraFields.put("thread", event.getThreadName());

            // 添加MDC信息（去除mdc_前缀，直接使用原key）
            Map<String, String> mdcPropertyMap = event.getMDCPropertyMap();
            if (mdcPropertyMap != null && !mdcPropertyMap.isEmpty()) {
                mdcPropertyMap.forEach((key, value) -> {
                    if (!"traceId".equals(key) && value != null && !value.isEmpty()) {
                        extraFields.put(key, value);
                    }
                });
            }

            // 添加完整的异常信息和堆栈
            if (event.getThrowableProxy() != null) {
                extraFields.put("exception", event.getThrowableProxy().getClassName());

                // 构建完整的异常信息（包含异常消息和堆栈）
                StringBuilder exceptionInfo = new StringBuilder();

                // 添加异常消息
                if (event.getThrowableProxy().getMessage() != null) {
                    exceptionInfo.append(event.getThrowableProxy().getClassName())
                                 .append(": ")
                                 .append(event.getThrowableProxy().getMessage())
                                 .append("\n");
                }

                // 添加堆栈信息（限制堆栈深度，避免过长）
                if (event.getThrowableProxy().getStackTraceElementProxyArray() != null) {
                    int maxStackLines = Math.min(20, event.getThrowableProxy().getStackTraceElementProxyArray().length);
                    for (int i = 0; i < maxStackLines; i++) {
                        exceptionInfo.append("\tat ")
                                     .append(event.getThrowableProxy().getStackTraceElementProxyArray()[i].toString())
                                     .append("\n");
                    }
                    // 如果堆栈被截断，添加提示
                    if (event.getThrowableProxy().getStackTraceElementProxyArray().length > maxStackLines) {
                        exceptionInfo.append("\t... ")
                                     .append(event.getThrowableProxy().getStackTraceElementProxyArray().length - maxStackLines)
                                     .append(" more\n");
                    }
                }

                // 添加Caused by信息
                if (event.getThrowableProxy().getCause() != null) {
                    appendCauseInfo(exceptionInfo, event.getThrowableProxy().getCause());
                }

                // 将完整异常信息作为一个字段（限制总长度）
                if (exceptionInfo.length() > 0) {
                    String stackTraceStr = exceptionInfo.toString().trim();
                    // 限制异常信息总长度，避免SLS字段过大
                    if (stackTraceStr.length() > 8000) {
                        stackTraceStr = stackTraceStr.substring(0, 8000) + "\n... [truncated]";
                    }
                    extraFields.put("stackTrace", stackTraceStr);
                }
            }

            // 异步发送到SLS（使用事件的原始时间戳）
            slsLogService.sendLogAsync(level, logger, message, traceId, extraFields, event.getTimeStamp());

        } catch (Exception e) {
            // 避免日志记录本身出错影响应用
            addError("发送日志到SLS失败", e);
        }
    }

    /**
     * 递归添加Caused by异常信息
     */
    private void appendCauseInfo(StringBuilder sb, ch.qos.logback.classic.spi.IThrowableProxy cause) {
        if (cause == null) {
            return;
        }

        sb.append("Caused by: ")
          .append(cause.getClassName());

        if (cause.getMessage() != null) {
            sb.append(": ").append(cause.getMessage());
        }
        sb.append("\n");

        // 添加Caused by的堆栈信息（限制深度）
        if (cause.getStackTraceElementProxyArray() != null) {
            int maxStackLines = Math.min(15, cause.getStackTraceElementProxyArray().length);
            for (int i = 0; i < maxStackLines; i++) {
                sb.append("\t ")
                  .append(cause.getStackTraceElementProxyArray()[i].toString())
                  .append("\n");
            }
            // 如果堆栈被截断，添加提示
            if (cause.getStackTraceElementProxyArray().length > maxStackLines) {
                sb.append("\t... ")
                  .append(cause.getStackTraceElementProxyArray().length - maxStackLines)
                  .append(" more\n");
            }
        }

        // 递归处理更深层的Caused by
        if (cause.getCause() != null) {
            appendCauseInfo(sb, cause.getCause());
        }
    }
}
