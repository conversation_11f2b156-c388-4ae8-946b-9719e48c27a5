package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UPDATE_CONTACTS;


@Getter
@Setter
@Accessors(chain = true)
public class PadContactsCMDDTO extends BasePadCMDDTO {
    private String importFile;
    private Object info;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPDATE_CONTACTS);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}
