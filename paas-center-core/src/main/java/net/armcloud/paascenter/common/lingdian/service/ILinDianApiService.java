package net.armcloud.paascenter.common.lingdian.service;

import net.armcloud.paascenter.common.lingdian.model.dto.ArmCardVlanConfigDTO;
import net.armcloud.paascenter.common.lingdian.model.dto.ImageDTO;
import net.armcloud.paascenter.common.lingdian.model.dto.UpImageDTO;
import net.armcloud.paascenter.common.lingdian.model.vo.*;

import java.util.List;

public interface ILinDianApiService {

    /**
     * 登录
     *
     * @param apiUrl soc Api地址
     * @return 登录token
     */
    String loginService(String apiUrl);

    /**
     * 获取服务器信息
     *
     * @param apiUrl soc Api地址
     * @return ArmServerInfoVO
     */
    ArmServerInfoVO getServerInfoService(String apiUrl);

    /**
     * 查询全部节点信息
     *
     * @param apiUrl soc Api地址
     * @return List<ArmCardVO>
     */
    List<ArmCardVO> getCardsInfoService(String apiUrl);

    /**
     * 查询全部节点状态
     *
     * @param apiUrl soc Api地址
     * @return List<ArmCardStatusVO>
     */
    List<ArmCardStatusVO> getCardsStatusService(String apiUrl);

    /**
     * 查询单个节点状态
     *
     * @param apiUrl    soc Api地址
     * @param armCardId 节点ID
     * @return ArmCardStatusVO
     */
    ArmCardStatusVO getCardStatusService(String apiUrl, String armCardId);

    /**
     * 节点上电
     *
     * @param apiUrl    soc Api地址
     * @param armCardId 节点ID
     * @return Boolean
     */
    Boolean armCardPowerOnService(String apiUrl, String armCardId);

    /**
     * 节点下电
     *
     * @param apiUrl    soc Api地址
     * @param armCardId 节点ID
     * @return Boolean
     */
    Boolean armCardPowerOffService(String apiUrl, String armCardId);

    /**
     * 配置节点IP
     *
     * @param apiUrl    soc Api地址
     * @param armCardId 板卡ID
     * @param ip        板卡IP
     * @param netmask   子网掩码
     * @param gateway   网关
     * @return Boolean
     */
    Boolean armCardIpConfigService(String apiUrl, String armCardId, String ip, String netmask, String gateway, String dns);

    /**
     * 配置节点 dns
     *
     * @param apiUrl    soc Api地址
     * @param armCardId 节点ID
     * @param dns       dns配置参数
     * @return Boolean
     */
    Boolean armCardDnsConfigService(String apiUrl, String armCardId, String dns);

    /**
     * 配置节点 Vlan
     *
     * @param apiUrl    soc Api地址
     * @param armCardId 节点ID
     * @param param     Vlan配置参数
     * @return Boolean
     */
    Boolean armCardVlanConfigService(String apiUrl, String armCardId, ArmCardVlanConfigDTO param);

    /**
     * 查询全部节点网络配置
     *
     * @param apiUrl soc Api地址
     * @return List<ArmCardNetworkVO>
     */
    List<LdCardNetworkVO> getArmCardNetworkService(String apiUrl);

    /**
     * 查询节点网络配置
     *
     * @param apiUrl soc Api地址
     * @return ArmCardNetworkVO
     */
    ArmCardNetworkVO getArmCardNetworkById(String apiUrl, String armCardId);

    /**
     * 重新初始化节点
     *
     * @param apiUrl    soc Api地址
     * @param armCardId node id
     * @return Boolean
     */
    Boolean reInitCardService(String apiUrl, String armCardId);

    /**
     * 节点重置任务
     *
     * @param apiUrl    soc Api地址
     * @param armCardId node id
     * @return Boolean
     */
    JobVO reInitCardJobService(String apiUrl, String armCardId);

    /**
     * 获取任务执行结果
     *
     * @param apiUrl soc Api地址
     * @param jobId
     * @return
     */
    String getJobInfoService(String apiUrl, String jobId);

    /**
     * 上传镜像
     *
     * @param apiUrl soc Api地址
     * @return
     */
    Boolean uploadImagesService(String apiUrl, UpImageDTO params);

    /**
     * 获取镜像信息
     *
     * @param apiUrl  soc Api地址
     * @param romName 镜像名称
     * @return ImageDTO
     */
    ImageDTO getImagesByRoomNameService(String apiUrl, String romName);

    /**
     * 删除镜像
     *
     * @param apiUrl  soc Api地址
     * @param romName 镜像名称
     * @return
     */
    Boolean deleteImagesService(String apiUrl, String romName);

    /**
     * 节点刷机任务
     *
     * @param apiUrl    soc Api地址
     * @param armCardId node id
     * @return Boolean
     */
    JobVO reinstallJobService(String apiUrl, String armCardId, String roomName);

    /**
     * 获取 刀片soc节点信息
     *
     * @param apiUrl soc Api地址
     * @return
     */
    List<NodeInfoVO> getNodesInfoService(String apiUrl, String node);

}
