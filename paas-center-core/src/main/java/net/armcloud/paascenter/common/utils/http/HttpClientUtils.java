package net.armcloud.paascenter.common.utils.http;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.DateUtils;
import net.armcloud.paascenter.common.utils.sign.PaasSignUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class HttpClientUtils {
    private final static Integer TIMEOUT = 3000;
    private final static Integer DEFAULT_TIMEOUT = 15 * 1000;
    private final static String contentType = "application/json";
    private final static String signedHeaders = "content-type;host;x-content-sha256;x-date";

    /**
     * 创建模拟客户端（针对 https 客户端禁用 SSL 验证）
     *
     * @return
     * @throws Exception
     */
    public static CloseableHttpClient createHttpClientWithNoSsl() throws Exception {
        SSLContextBuilder builder = SSLContextBuilder.create();
        builder.loadTrustMaterial(new TrustAllStrategy());
        SSLConnectionSocketFactory ssl_sf = new SSLConnectionSocketFactory(builder.build(), NoopHostnameVerifier.INSTANCE);
        return HttpClients.custom().setSSLSocketFactory(ssl_sf).build();
    }


    public static String doPost(String url, String params,Integer timeOut) throws Exception {

        try (CloseableHttpClient httpClient = createHttpClientWithNoSsl()) {

            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Accept", "application/json");
            httpPost.setHeader("Content-Type", "application/json");
            String charSet = "UTF-8";
            StringEntity entity = new StringEntity(params, charSet);
            httpPost.setEntity(entity);


            // 设置超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(timeOut)
                    .setSocketTimeout(timeOut)
                    .setConnectionRequestTimeout(timeOut)
                    .build();
            httpPost.setConfig(requestConfig);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return EntityUtils.toString(response.getEntity());
            }
        } catch (Exception e) {
            log.error("httpClient doPost error,url:{},body:{}", url, params, e);
            throw e;
        }
    }


    public static String doPost(String url, String params) throws Exception {
        return doPost(url, params, DEFAULT_TIMEOUT);
    }



    /**
     * @param host 访问域名
     * @param url  回调地址
     * @param body body
     * @param sk   客户密钥
     * @return String
     */
    public static String sendCallback(String host, String url, String body, String sk) throws Exception {

        String xDate = DateUtils.DateToUTC(LocalDateTime.now());

        HttpPost httpPost = new HttpPost(url);

        httpPost.setHeader("Accept", contentType);
        httpPost.setHeader("Content-Type", contentType);
        if(!StringUtils.isBlank(host)){
            httpPost.setHeader("host", host);
        }
        httpPost.setHeader("x-date", xDate);
        httpPost.setHeader("signedHeaders", signedHeaders);

        String signature = PaasSignUtils.signature(contentType, signedHeaders, host, xDate, sk, body.getBytes());
        httpPost.setHeader("signature", signature);

        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(body, charSet);
        httpPost.setEntity(entity);

        // 设置超时时间
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(TIMEOUT) // 连接超时时间为2秒
                .setSocketTimeout(TIMEOUT) // Socket超时时间为2秒
                .setConnectionRequestTimeout(TIMEOUT) // 请求超时时间为2秒
                .build();
        httpPost.setConfig(requestConfig);

        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = httpclient.execute(httpPost);
        HttpEntity responseEntity = response.getEntity();
        StatusLine statusLine = response.getStatusLine();
        String result = EntityUtils.toString(responseEntity);
        log.info("sendCallback host:{} url:{} body:{} sk:{} result:{}", host, url, body, sk, result);
        return result;
    }


    /**
     * 带返回code码的http请求响应方法
     * @param host
     * @param url
     * @param body
     * @param sk
     * @return
     * @throws Exception
     */

    public static Map<String, Object> sendHttpCallbackRequest(String host, String url, String body, String sk) throws Exception {
        String xDate = DateUtils.DateToUTC(LocalDateTime.now());
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Accept", contentType);
        httpPost.setHeader("Content-Type", contentType);
        if (!StringUtils.isBlank(host)) {
            httpPost.setHeader("host", host);
        }
        httpPost.setHeader("x-date", xDate);
        httpPost.setHeader("signedHeaders", signedHeaders);

        String signature = PaasSignUtils.signature(contentType, signedHeaders, host, xDate, sk, body.getBytes());
        httpPost.setHeader("signature", signature);

        StringEntity entity = new StringEntity(body, "UTF-8");
        httpPost.setEntity(entity);

        // 设置超时时间
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(TIMEOUT)
                .setSocketTimeout(TIMEOUT)
                .setConnectionRequestTimeout(TIMEOUT)
                .build();
        httpPost.setConfig(requestConfig);

        try (CloseableHttpClient httpclient = HttpClients.createDefault();
             CloseableHttpResponse response = httpclient.execute(httpPost)) {

            int statusCode = response.getStatusLine().getStatusCode();
            String result = EntityUtils.toString(response.getEntity());

            log.info("sendCallback host:{} url:{} body:{} sk:{} statusCode:{} result:{}", host, url, body, sk, statusCode, result);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("statusCode", statusCode);
            responseMap.put("body", result);
            return responseMap;
        }
    }

}