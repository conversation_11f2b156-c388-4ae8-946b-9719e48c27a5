package net.armcloud.paascenter.common.core.constant.paas;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PadOperBusinessType {
    /**
     * 其它
     */
    OTHER(0),

    /**
     * 新增
     */
    ADD(1),

    /**
     * 修改
     */
    MODIFY(2),

    /**
     * 删除
     */
    DELETE(3),

    /**
     * 授权
     */
    AUTHORIZE(4),

    /**
     * 导出
     */
    EXPORT(5),

    /**
     * 导入
     */
    IMPORT(6),

    /**
     * 强退
     */
    FORCE_LOGOUT(7),

    /**
     * 生成代码
     */
    GENERATE_CODE(8),

    /**
     * 清空数据
     */
    CLEAR_DATA(9);

    private final int code;
}
