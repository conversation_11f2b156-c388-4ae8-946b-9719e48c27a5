package net.armcloud.paascenter.common.lingdian.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import net.armcloud.paascenter.bmc.constant.NumberConst;
import net.armcloud.paascenter.bmc.utils.HttpClientUtils;
import net.armcloud.paascenter.common.lingdian.configure.LinDianApiUrls;
import net.armcloud.paascenter.common.lingdian.configure.LinDianConfig;
import net.armcloud.paascenter.common.lingdian.model.dto.*;
import net.armcloud.paascenter.common.lingdian.model.vo.*;
import net.armcloud.paascenter.common.redis.contstant.KeyTime;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.core.constant.Constants.REQUEST_FAIL;
import static net.armcloud.paascenter.common.core.constant.Constants.REQUEST_SUCCESS;
import static net.armcloud.paascenter.common.lingdian.constant.LingDianConstants.*;

@Slf4j
@Service
public class LinDianApiServiceImpl implements ILinDianApiService {
    private final static String AUTH_TOKEN = "Auth-Token";
    private final static String ARM_CARD_ID = "{{arm_card_id}}";
    private final static String JOB_ID = "{{job_id}}";


    @Resource
    private LinDianConfig linDianConfig;
    @Resource
    private RedisService redisService;


    @Override
    public String loginService(String socUrl) {
        try {
            String url = socUrl + LinDianApiUrls.LOGIN;

            LoginDTO loginDTO = new LoginDTO();
            loginDTO.setUser(linDianConfig.getUserName());
            loginDTO.setPassword(linDianConfig.getPassword());

            String result = HttpClientUtils.doPost(url, loginDTO, null);
            log.debug("linDianApiService method:{}  result={}", "loginService", result);
            if (null != result) {
                LoginLinDianVO<LingDianLoginVO> resultLinDianVO = JSON.parseObject(result, new TypeReference<LoginLinDianVO<LingDianLoginVO>>() {
                });

                if (LOGIN_SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getData().getAuthToken();
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method={} e:", "loginService", e);
        }
        return null;
    }

    /**
     * 根据 apiUrl 获取 authToken
     *
     * @param apiUrl soc api url
     * @return token
     */
    private String getAuthToken(String apiUrl) {
        String authToken = redisService.getCacheObject(apiUrl);
        if (ObjectUtils.isEmpty(authToken)) {
            authToken = loginService(apiUrl);
            if (ObjectUtils.isEmpty(authToken)) {
                throw new RuntimeException("login failed");
            }
            redisService.setCacheObject(apiUrl, authToken, KeyTime.minute_30, TimeUnit.MINUTES);
            return authToken;
        }
        return authToken;
    }

    /**
     * token 失效删除缓存
     *
     * @param apiUrl soc api url
     * @param result 调用接口返回结果
     */
    private void deleteToken(String apiUrl, String result) {
        if (result.equals("Unauthorized")) {
            redisService.deleteObject(apiUrl);
        }
    }

    @Override
    public ArmServerInfoVO getServerInfoService(String apiUrl) {
        try {
//            String url = apiUrl + LinDianApiUrls.ARM_SERVER_INFO;
//            Map<String, String> headers = new HashMap<>();
//            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));
//            String result = HttpClientUtils.doGet(url, headers);
//            if (null != result) {
//                deleteToken(apiUrl, result);
//                ResultLinDianVO<ArmServerInfoVO> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<ArmServerInfoVO>>() {
//                });
//
//                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
//                    return resultLinDianVO.getContent();
//                }
//            }
        } catch (Exception e) {
            log.error("linDianApiService method={} e:", "getServerInfoService", e);
        }
        return null;
    }

    @Override
    public List<ArmCardVO> getCardsInfoService(String apiUrl) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_INFO;
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug("linDianApiService method:{}  result={}", "getCardsInfoService", result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<List<ArmCardVO>> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<List<ArmCardVO>>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getContent();
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method={} e:", "getCardsInfoService", e);
        }
        return null;
    }

    @Override
    public List<ArmCardStatusVO> getCardsStatusService(String apiUrl) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_STATUS;
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug("linDianApiService method:{}  result={}", "getCardsStatusService", result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<List<ArmCardStatusVO>> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<List<ArmCardStatusVO>>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getContent();
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method={} e:", "getCardsStatusService", e);
        }
        return null;
    }

    @Override
    public ArmCardStatusVO getCardStatusService(String apiUrl, String armCardId) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARD_SINGLE_STATUS.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug("linDianApiService method:{}  result={}", "getCardStatusService", result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<List<ArmCardStatusVO>> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<List<ArmCardStatusVO>>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode() && resultLinDianVO.getContent().size() > NumberConst.ZERO) {
                    return resultLinDianVO.getContent().get(0);
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method={} e:", "getCardStatusService", e);
        }
        return null;
    }

    @Override
    public Boolean armCardPowerOnService(String apiUrl, String armCardId) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_POWER_ON.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doPut(url, null, headers);
            log.debug("linDianApiService method:{} cardId={} result={}", "armCardPowerOnService", armCardId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId={} e:", "armCardPowerOnService", armCardId, e);
        }
        return false;
    }

    @Override
    public Boolean armCardPowerOffService(String apiUrl, String armCardId) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_POWER_OFF.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doPut(url, null, headers);
            log.debug("linDianApiService method:{} cardId={} result={}", "armCardPowerOffService", armCardId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId={} e:", "armCardPowerOffService", armCardId, e);
        }
        return false;
    }

    @Override
    public Boolean armCardIpConfigService(String apiUrl, String armCardId, String ip, String netmask, String gateway, String dns) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARD_IP_CONFIG.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            ArmCardIpConfigDTO body = new ArmCardIpConfigDTO();
            body.setIp(ip);
            body.setDns(dns);
            body.setNetmask(netmask);
            body.setGateway(gateway);

            log.info("进入方法中armCardIpConfigService,url:{},body:{},headers:{}",url,JSON.toJSONString(body),JSON.toJSONString(headers));
            String result = HttpClientUtils.doPost(url, body, headers);
            log.info("linDianApiService method={} armCardId={} param={},result={}", "armCardIpConfigService", armCardId, body, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId={} e:", "armCardIpConfigService", armCardId, e);
        }
        return false;
    }

    @Override
    public Boolean armCardDnsConfigService(String apiUrl, String armCardId, String dns) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARD_DNS_CONFIG.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            ArmCardDnsConfigDTO body = new ArmCardDnsConfigDTO();
            body.setDns(dns);

            String result = HttpClientUtils.doPost(url, body, headers);
            log.debug("linDianApiService method{} armCardId={} param={},result={}", "armCardDnsConfigService", armCardId, body, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId={} e:", "armCardDnsConfigService", armCardId, e);
        }
        return false;
    }

    @Override
    public Boolean armCardVlanConfigService(String apiUrl, String armCardId, ArmCardVlanConfigDTO param) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARD_VLAN_CONFIG.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doPost(url, param, headers);
            log.debug("linDianApiService method{} armCardId={} param={},result={}", "armCardVlanConfigService", armCardId, param, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId={} e:", "armCardVlanConfigService", armCardId, e);
        }
        return false;
    }

    @Override
    public List<LdCardNetworkVO> getArmCardNetworkService(String apiUrl) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_NETWORK_INFO;
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug("linDianApiService method{} result={}", "getArmCardNetworkService", result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<List<LdCardNetworkVO>> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<List<LdCardNetworkVO>>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getContent();
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} e:", "getArmCardNetworkService", e);
        }
        return null;
    }

    @Override
    public ArmCardNetworkVO getArmCardNetworkById(String apiUrl, String armCardId) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARD_NETWORK_INFO.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug("linDianApiService method:{} cardId:{} result={}", "getArmCardNetworkById", armCardId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<List<LdCardNetworkVO>> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<List<LdCardNetworkVO>>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    if (!resultLinDianVO.getContent().isEmpty()) {
                        return resultLinDianVO.getContent().get(0).getEth0();
                    }
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId:{} e:", "getArmCardNetworkById", armCardId, e);
        }
        return null;
    }

    @Override
    public Boolean reInitCardService(String apiUrl, String armCardId) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_RE_INIT.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doPost(url, null, headers);
            log.debug("linDianApiService method:{} cardId:{} result={}", "reInitCardService", armCardId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId:{} e:", "reInitCardService", armCardId, e);
        }
        return false;
    }

    @Override
    public JobVO reInitCardJobService(String apiUrl, String armCardId) {
        try {
            String url = apiUrl + LinDianApiUrls.ARM_CARDS_RE_INIT.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doPost(url, null, headers);
            log.debug("linDianApiService method:{} cardId:{} result={}", "reInitCardJobService", armCardId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<JobVO> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<JobVO>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getContent();
                }
            }
        } catch (Exception e) {
            log.error("linDianApiService method:{} cardId:{} e:", "reInitCardJobService", armCardId, e);
        }
        return null;
    }


    @Override
    public String getJobInfoService(String apiUrl, String jobId) {
        try {
            String url = apiUrl + LinDianApiUrls.JOB_INFO.replace(JOB_ID, jobId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug(" Requesting LingDian BMC Info method={} jobId={} result={}", "getJobInfoService", jobId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<JobInfoVO> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<JobInfoVO>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    log.info(" Requesting LingDian BMC Info method={} jobId={} msg={}", "getJobInfoService", jobId, resultLinDianVO.getContent());
                    if (resultLinDianVO.getContent().getState().equals("success")) {
                        return REQUEST_SUCCESS;
                    }
                } else if (FAIL_CODE == resultLinDianVO.getCode()) {
                    if(JSONObject.toJSONString(resultLinDianVO.getContent()).contains("reinstall")){
                        return "running";
                    }
                    return REQUEST_FAIL;
                }
            }
        } catch (Exception e) {
            log.error(" Requesting LingDian BMC Error method={} jobId={}", "getJobInfoService", jobId, e);
        }
        return "running";
    }

    @Override
    public Boolean uploadImagesService(String apiUrl, UpImageDTO params) {
        try {
            String url = apiUrl + LinDianApiUrls.UPLOAD_IMAGES;
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doPost(url, params, headers);
            log.debug(" Requesting LingDian BMC Error method={} result={}", "uploadImagesService", result);
            if (null != result) {
                ResultLinDianVO<String> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<String>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error(" Requesting LingDian BMC Error method={} params={}", "uploadImagesService", params, e);
        }
        return false;
    }

    @Override
    public ImageDTO getImagesByRoomNameService(String apiUrl, String romName) {
        try {
            String url = apiUrl + LinDianApiUrls.IMAGES_INFO.replace("{{rom_name}}", romName);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug(" Requesting LingDian BMC Info method={} roomName={} result={}", "getImagesByRoomNameService", romName, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<UpImageDTO> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<UpImageDTO>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    log.info(" Requesting LingDian BMC Info method={} roomName={} msg={}", "getImagesByRoomNameService", romName, resultLinDianVO.getContent());
                    if (ObjectUtils.isEmpty(resultLinDianVO.getContent())) {
                        return null;
                    }
                    return resultLinDianVO.getContent().getImage();
                }
            }
        } catch (Exception e) {
            log.error(" Requesting LingDian BMC Error method={} roomName={}", "getImagesByRoomNameService", romName, e);
        }
        return null;
    }

    @Override
    public Boolean deleteImagesService(String apiUrl, String romName) {
        try {
            String url = apiUrl + LinDianApiUrls.DELETE_IMAGES.replace("{{rom_name}}", romName);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doDelete(url, headers);
            log.debug(" Requesting LingDian BMC Info method={} roomName={} result={}", "deleteImagesService", romName, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<?> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<?>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error(" Requesting LingDian BMC Error method={} roomName={}", "deleteImagesService", romName, e);
        }
        return false;
    }

    @Override
    public JobVO reinstallJobService(String apiUrl, String armCardId, String roomName) {
        try {
            String url = apiUrl + LinDianApiUrls.REINSTALL.replace(ARM_CARD_ID, armCardId);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            ReinstallDTO body = new ReinstallDTO();
            body.setName(roomName);

            String result = HttpClientUtils.doPost(url, body, headers);
            log.debug(" Requesting LingDian BMC Info method={} armCardId={} result={}", "reinstallJobService", armCardId, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<JobVO> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<JobVO>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getContent();
                }
            }
        } catch (Exception e) {
            log.error(" Requesting LingDian BMC Error method={} armCardId={}", "reinstallJobService", armCardId, e);
        }
        return null;
    }

    @Override
    public List<NodeInfoVO> getNodesInfoService(String apiUrl, String node) {
        try {
            String url = apiUrl + LinDianApiUrls.NODE_INFO.replace("{{node}}", node);
            Map<String, String> headers = new HashMap<>();
            headers.put(AUTH_TOKEN, getAuthToken(apiUrl));

            String result = HttpClientUtils.doGet(url, headers);
            log.debug(" Requesting LingDian BMC Info method={} node={} result={}", "getNodesInfoService", node, result);
            if (null != result) {
                deleteToken(apiUrl, result);
                ResultLinDianVO<List<NodeInfoVO>> resultLinDianVO = JSON.parseObject(result, new TypeReference<ResultLinDianVO<List<NodeInfoVO>>>() {
                });

                if (SUCCESS_CODE == resultLinDianVO.getCode()) {
                    return resultLinDianVO.getContent();
                }
            }
        } catch (Exception e) {
            log.error(" Requesting LingDian BMC Error method={} node={}", "getNodesInfoService", node, e);
        }
        return null;
    }
}
