package net.armcloud.paascenter.common.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.DOWNLOAD_FILE_CMD;

@Getter
@Setter
@Accessors(chain = true)
public class PadDownloadFileCMDDTO extends BasePadCMDDTO {
    @NotBlank(message = "path cannot null")
    private String path;

    @NotBlank(message = "fileName cannot null")
    private String fileName;

    @NotNull(message = "install cannot null")
    private Boolean install;

    private String iconUrl;

    private String packageName;

    private String url;

    private String md5;

    /**
     * 是否授权应用
     */
    private Boolean isAuthorization;

    private Long customerFileId;

    /**
     * 文件存储路径，非必传，需以/开头
     */
    private String targetDirectory;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, String oprBy) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(DOWNLOAD_FILE_CMD);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        padCMDForwardDTO.setOprBy(oprBy);

        return padCMDForwardDTO;
    }
}
