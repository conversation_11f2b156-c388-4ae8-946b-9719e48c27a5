package net.armcloud.paascenter.common.client.internal.dto;

import lombok.Data;

@Data
public class UpdatePadTaskByWsDTO {
    private String padCode;
    /**
     * 是否连接
     * <p>
     * true：已连接
     * false：断开
     */
    private Boolean connected;

    /**
     * 是否是恢复出厂设置状态
     */
    private Boolean birth;

    /**
     * 任务开始时间
     */
    private Long startTime;

    /**
     * 镜像id
     */
    String imageId;

    /**
     * 存储总容量
     */
    private Long dataSize;

    /**
     * 存储已使用容量
     */
    private Long dataSizeUsed;
}
