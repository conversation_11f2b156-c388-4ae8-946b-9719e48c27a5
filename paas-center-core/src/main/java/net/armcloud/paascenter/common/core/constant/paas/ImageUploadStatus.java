package net.armcloud.paascenter.common.core.constant.paas;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 镜像上传状态定义
 * <p>
 */
@Getter
@AllArgsConstructor
public enum ImageUploadStatus {

    /**
     * 预热中
     */
    UPLOAD_EXECUTE( 1),

    /**
     * 预热完成
     */
    SUCCESS( 2),

    /**
     * 预热失败
     */
    FAIL( -1),

    /**
     * 初始化
     */
    INITIALIZE(10),

    /**
     * 下载上传中
     */
    DOWNLOAD_AND_PUSH(11),

    ;


    private final Integer status;
}
