package net.armcloud.paascenter.common.volcipaas.rtcutils;

import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Map;
import java.util.TreeMap;

public class ByteBufWrapper {
    ByteBuffer buffer = ByteBuffer.allocate(1024).order(ByteOrder.LITTLE_ENDIAN);

    public ByteBufWrapper() {
    }

    public ByteBufWrapper(byte[] bytes) {
        this.buffer = ByteBuffer.wrap(bytes).order(ByteOrder.LITTLE_ENDIAN);
    }

    public byte[] asBytes() {
        byte[] out = new byte[buffer.position()];
        ((<PERSON>uffer) buffer).rewind(); // 显式调用 Buffer 的 rewind 方法
        buffer.get(out, 0, out.length);
        return out;
    }


    // packUint16
    public ByteBufWrapper put(short v) {
        buffer.putShort(v);
        return this;
    }

    public ByteBufWrapper put(byte[] v) {
        put((short)v.length);
        buffer.put(v);
        return this;
    }

    // packUint32
    public ByteBufWrapper put(int v) {
        buffer.putInt(v);
        return this;
    }

    public ByteBufWrapper put(long v) {
        buffer.putLong(v);
        return this;
    }

    public ByteBufWrapper put(String v) {
        return put(v.getBytes());
    }

    public ByteBufWrapper put(TreeMap<Short, String> extra) {
        put((short)extra.size());

        for (Map.Entry<Short, String> pair : extra.entrySet()) {
            put(pair.getKey());
            put(pair.getValue());
        }

        return this;
    }

    public ByteBufWrapper putIntMap(TreeMap<Short, Integer> extra) {
        put((short)extra.size());

        for (Map.Entry<Short, Integer> pair : extra.entrySet()) {
            put(pair.getKey());
            put(pair.getValue());
        }

        return this;
    }

    public Short readInteger() {
        return buffer.getShort();
    }


    public int readInt() {
        return buffer.getInt();
    }

    public byte[] readBytes() {
        Short length = readInteger();
        byte[] bytes = new byte[length];
        buffer.get(bytes);
        return bytes;
    }

    public String readString() {
        byte[] bytes = readBytes();
        return new String(bytes);
    }

    public TreeMap readMap() {
        TreeMap<Short, String> map = new TreeMap<>();

        Short length = readInteger();

        for (short i = 0; i < length; ++i) {
            Short k = readInteger();
            String v = readString();
            map.put(k, v);
        }

        return map;
    }

    public TreeMap<Short, Integer> readIntMap() {
        TreeMap<Short, Integer> map = new TreeMap<>();

        Short length = readInteger();

        for (short i = 0; i < length; ++i) {
            Short k = readInteger();
            Integer v = readInt();
            map.put(k, v);
        }

        return map;
    }
}