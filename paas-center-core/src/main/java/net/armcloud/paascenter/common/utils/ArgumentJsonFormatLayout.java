package net.armcloud.paascenter.common.utils;

import ch.qos.logback.classic.pattern.MessageConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.helpers.MessageFormatter;

import java.util.stream.Stream;

public class ArgumentJsonFormatLayout extends MessageConverter {

  public static String toJSONString(Object object) {
    return StringUtils.left(toJSONStr(object), 8192);
  }

  private static String toJSONStr(Object object) {
    if (object == null) {
      return null;
    }

    if (object instanceof CharSequence) {
      return object.toString();
    }

    if (object instanceof Number) {
      return object.toString();
    }

    if ((object == boolean.class
        || object == byte.class
        || object == char.class
        || object == short.class
        || object == int.class
        || object == long.class
        || object == float.class
        || object == double.class)) {
      return object.toString();
    }

    try {
      return JSON.toJSONString(object);
    } catch (Throwable e) {
      return object.toString();
    }
  }

  public String convert(ILoggingEvent event) {
    if (event.getArgumentArray() == null) {
      return event.getFormattedMessage();
    }

    try {
      return MessageFormatter.arrayFormat(
              event.getMessage(),
              Stream.of(event.getArgumentArray())
                  .map(ArgumentJsonFormatLayout::toJSONString)
                  .toArray())
          .getMessage();
    } catch (Exception e) {
      return event.getFormattedMessage();
    }
  }
}
