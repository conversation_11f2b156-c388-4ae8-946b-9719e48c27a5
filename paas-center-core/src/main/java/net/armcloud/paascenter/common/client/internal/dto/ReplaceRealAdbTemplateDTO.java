package net.armcloud.paascenter.common.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Set;

@Data
public class ReplaceRealAdbTemplateDTO extends BaseDTO implements Serializable {
    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1,max = 200,message = "实例数量不多于200个")
    private Set<String> padCodes;

    @ApiModelProperty(value = "是否清除实例数据(data分区), true清除，false不清除", required = true)
    @NotNull(message = "wipeData cannot null")
    private Boolean wipeData;

    @ApiModelProperty(value = "真机模板ID", required = true)
    @NotNull(message = "realPhoneTemplateId cannot null")
    private Long realPhoneTemplateId;


    @ApiModelProperty(value = "设置安卓属性")
    private String  androidProp;

    /**
     * 任务来源
     */
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    private String oprBy;

}
