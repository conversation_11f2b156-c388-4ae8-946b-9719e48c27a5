package net.armcloud.paascenter.common.utils;

import java.util.UUID;

public class UuidUtils {
    /**
     * 生成一个随机UUID。
     *
     * @return 随机UUID的字符串表示形式
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成一个不带连字符的UUID。
     *
     * @return 随机UUID的不带连字符的字符串表示形式
     */
    public static String generateUUIDWithoutHyphens() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
