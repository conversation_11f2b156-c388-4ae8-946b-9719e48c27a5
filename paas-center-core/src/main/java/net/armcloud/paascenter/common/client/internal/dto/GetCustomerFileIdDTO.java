package net.armcloud.paascenter.common.client.internal.dto;

import lombok.Data;

@Data
public class GetCustomerFileIdDTO {

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 文件ID
     */
//    @NotNull(message = "fileId cannot null")
    private String fileId;


    /**
     * 包名
     */
    private String pkgName;

    public GetCustomerFileIdDTO(Long customerId, String fileId, String pkgName) {
        this.customerId = customerId;
        this.fileId = fileId;
        this.pkgName = pkgName;
    }

    public GetCustomerFileIdDTO(){
        super();
    }

}
