package net.armcloud.paascenter.common.client.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.command.PadCMDForwardDTO;
import net.armcloud.paascenter.common.client.internal.vo.AddPadTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.CommsTransmissionResultVO;
import net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.dto.api.AddPadTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.DeleteTaskDTO;
import net.armcloud.paascenter.commscenter.service.PadCommsDataService;
import net.armcloud.paascenter.task.service.ITaskService;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.SUB_TASK_ID;
import static net.armcloud.paascenter.common.core.constant.comms.CommsConstant.DataField.TASK_ID;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.EXECUTING;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.WAIT_EXECUTE;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.UPGRADE_IMAGE;

/**
 * 舍弃此工具类
 * 使用{ net.armcloud.paascenter.client.component.PadCommandComponent}代替
 */
@Slf4j
@Deprecated
public class PadCommandUtils {
    @Resource
    private PadCommsDataService padCommsDataService;
    @Resource
    private ITaskService taskService;

    /**
     * 发送命令
     */
    public List<CommsTransmissionResultVO> sendPadCommand(PadCMDForwardDTO padCMDForwardDTO, ExceptionCode exceptionCode) {
        padCMDForwardDTO.setSourceCode(SourceTargetEnum.PAAS);

        //下载文件指令加入队列
        if (CommsCommandEnum.DOWNLOAD_FILE_CMD.equals(padCMDForwardDTO.getCommand())
                || CommsCommandEnum.DOWNLOAD_FILE_APP_CMD.equals(padCMDForwardDTO.getCommand())) {
            return padCommsDataService.asyncForward(padCMDForwardDTO);
        }

        return padCommsDataService.forward(padCMDForwardDTO);
    }

    public PadTaskBO executeTaskCMD(Long customerId, TaskTypeConstants taskType, PadCMDForwardDTO padCMDForwardDTO,
                                    ExceptionCode exceptionCode) {
        return executeTaskCMD(customerId, taskType, () -> padCMDForwardDTO, exceptionCode);
    }

    /**
     * 发送命令并生成任务id
     * <p>
     * 生成的任务为执行中
     *
     * @return 任务id
     */
    public PadTaskBO executeTaskCMD(Long customerId, TaskTypeConstants taskType, Supplier<PadCMDForwardDTO> cmdSupplier,
                                    ExceptionCode exceptionCode) {
        List<String> padCodes = cmdSupplier.get().getPadInfos().stream()
                .map(PadCMDForwardDTO.PadInfoDTO::getPadCode).collect(Collectors.toList());
        Consumer<AddPadTaskDTO> dtoConsumer = task -> {
            task.setPadCodes(padCodes);
            task.setType(taskType.getType());
            if (taskType.getType().equals(UPGRADE_IMAGE.getType())) {
                task.setStatus(WAIT_EXECUTE.getStatus());
            } else {
                task.setStatus(EXECUTING.getStatus());
            }
            task.setCustomerId(customerId);
            task.setSourceCode(cmdSupplier.get().getSourceCode().getCode());
            task.setCustomerFileId(cmdSupplier.get().getCustomerFileId());
            task.setImageId(cmdSupplier.get().getImageId());
            task.setWipeData(cmdSupplier.get().getWipeData());
            task.setTaskContent(cmdSupplier.get().getTaskContent());
            task.setCreateBy(cmdSupplier.get().getOprBy());
        };

        return executeTaskCMD(cmdSupplier, exceptionCode, dtoConsumer);
    }

    /**
     * 发送命令并生成任务id
     *
     * @return 任务id
     */
    public PadTaskBO executeTaskCMD(Supplier<PadCMDForwardDTO> cmdSupplier, ExceptionCode exceptionCode,
                                    Consumer<AddPadTaskDTO> dtoConsumer) {
        AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
        dtoConsumer.accept(addTaskDTO);
        Long masterTaskId = null;
        List<AddPadTaskVO> padTasks;
        PadCMDForwardDTO padCMDForwardDTO = cmdSupplier.get();
        List<String> failPads;
        try {
            // 生成任务
            padTasks = taskService.addPadTaskService(addTaskDTO);
            masterTaskId = padTasks.get(0).getMasterTaskId();
            List<AddPadTaskVO> finalPadTasks = padTasks;
            padCMDForwardDTO.getPadInfos().forEach(padInfoDTO -> {
                String padCode = padInfoDTO.getPadCode();
                AddPadTaskVO padTask = finalPadTasks.stream()
                        .filter(task -> task.getPadCode().equals(padCode)).findFirst()
                        .orElse(new AddPadTaskVO());
                JSONObject jsonObject = JSONObject.from(padInfoDTO.getData());
                jsonObject.put(TASK_ID, padTask.getMasterTaskId());
                jsonObject.put(SUB_TASK_ID, padTask.getSubTaskId());
                padInfoDTO.setData(jsonObject);
            });

            // 发送指令
            if (addTaskDTO.getType().equals(UPGRADE_IMAGE.getType())) {
                failPads = new ArrayList<>();
            } else {
                List<CommsTransmissionResultVO> cmdResult = sendPadCommand(padCMDForwardDTO, exceptionCode);
                failPads = cmdResult.stream()
                        .filter(r -> Boolean.FALSE.equals(r.getSendSuccess()))
                        .map(CommsTransmissionResultVO::getPadCode).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(failPads) && failPads.size() >= padCMDForwardDTO.getPadInfos().size()) {
                    throw new BasicException(exceptionCode);
                }
            }
        } catch (Exception e) {
            log.error("executeTaskCMD error>>>> padCMDForwardDTO:{}", JSON.toJSONString(padCMDForwardDTO), e);
            if (Objects.nonNull(masterTaskId)) {
                taskService.deleteTaskService(new DeleteTaskDTO(masterTaskId));
            }

            throw new BasicException(exceptionCode);
        }

        List<PadTaskBO.PadSubTaskBO> subTaskBo = new ArrayList<>();
        for (AddPadTaskVO padTask : padTasks) {
            String padCode = padTask.getPadCode();
            PadTaskBO.PadSubTaskBO subTaskBO = new PadTaskBO.PadSubTaskBO();
            subTaskBO.setPadCode(padCode);
            subTaskBO.setSendCmdSuccess(!failPads.contains(padCode));
            subTaskBO.setSubTaskStatus(padTask.getSubTaskStatus());
            subTaskBO.setMasterTaskId(padTask.getMasterTaskId());
            subTaskBO.setMasterTaskUniqueId(padTask.getMasterUniqueId());
            subTaskBO.setSubTaskId(padTask.getSubTaskId());
            subTaskBO.setSubTaskUniqueId(padTask.getSubTaskUniqueId());
            subTaskBO.setCustomerTaskId(padTask.getCustomerTaskId());

            subTaskBo.add(subTaskBO);
        }

        PadTaskBO masterTask = new PadTaskBO();
        masterTask.setMasterTaskId(padTasks.get(0).getMasterTaskId());
        masterTask.setMasterTaskUniqueId(padTasks.get(0).getMasterUniqueId());
        masterTask.setSubTasks(subTaskBo);
        return masterTask;
    }
}
