package net.armcloud.paascenter.common.utils.http;

import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

public class HttpUrlUtils {


    /**
     * 如果fileName 不带文件后缀将url路径拼接到fleName作为后缀
     * @param fileName
     * @param url
     * @return
     */
    public static String getFileSuffix(String fileName,String url) {
        if (url == null || url.isEmpty()) {
            return fileName;
        }
        if (StringUtils.isBlank(fileName)) {
            fileName = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20);
        }
        // 查找最后一个 '.' 和 '/' 的位置
        int lastDotIndex = url.lastIndexOf('.');
        int lastSlashIndex = url.lastIndexOf('/');

        // 判断 '.' 是否在 '/' 之后，确保是文件后缀
        if (lastDotIndex > lastSlashIndex && lastDotIndex != -1) {
            // 截取并返回文件后缀
            return fileName+"."+url.substring(lastDotIndex + 1);
        }

        // 如果没有后缀名，返回默认值
        return fileName;
    }

}
