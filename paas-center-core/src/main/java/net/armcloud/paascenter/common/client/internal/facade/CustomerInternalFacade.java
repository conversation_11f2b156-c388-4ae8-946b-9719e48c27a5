package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.client.internal.dto.CheckSdkTokenPadDTO;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.client.internal.vo.SDKCustomerVO;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface CustomerInternalFacade {

    @PostMapping(value = "/openapi/internal/customer/sdk/verifyAndGetInfo")
    Result<SDKCustomerVO> verifyAndGetInfo(@RequestBody VerifyAndGetSDKCustomerDTO dto);

    @PostMapping(value = "/openapi/internal/customer/sdk/bindCheckUuid")
    Result<Boolean> sdkTokenBindUuid(@RequestBody VerifyAndGetSDKCustomerDTO dto);

    @PostMapping(value = "/openapi/internal/customer/sdk/checkSdkTokenBindCustomerAndPad")
    Result<Boolean> checkSdkTokenBindCustomerAndPad(@RequestBody CheckSdkTokenPadDTO dto);

    @PostMapping(value = "/openapi/internal/customer/sdk/checkSdkTokenBindCustomerAndPadV2")
    Result<Boolean> checkSdkTokenBindCustomerAndPadV2(@RequestBody CheckSdkTokenPadDTO dto);
}
