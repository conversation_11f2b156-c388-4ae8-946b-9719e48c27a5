package net.armcloud.paascenter.common.log;

import lombok.Getter;
import lombok.Setter;
import net.armcloud.paascenter.common.service.SlsLogService;

/**
 * SLS服务持有者
 * 用于在Logback Appender中获取SLS服务，解决Spring容器启动顺序问题
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
public class SlsServiceHolder {

    /**
     * -- SETTER --
     *  设置SLS服务实例
     * -- GETTER --
     *  获取SLS服务实例

     */
    @Getter
    @Setter
    private static volatile SlsLogService slsLogService;

}
