package net.armcloud.paascenter.common.rocketmq.concurrent;

import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024-03-26 09:37
 */
public class CommonThreadPoolExecutors {
    private static final int corePoolSize = Runtime.getRuntime().availableProcessors();
    private static final int maxPoolSize =  Runtime.getRuntime().availableProcessors()*3/2;
    private static final byte[] threadPoolInitLock = new byte[0];
    private static volatile ThreadPoolExecutor threadPoolExecutorSingleton;
    private static final int maxBlockedSize = Integer.MAX_VALUE;
    private static final long keepAliveSeconds = 30L;

    public static ThreadPoolExecutor getDefaultSingletonThreadPool() {
        if (threadPoolExecutorSingleton == null) {
            synchronized (threadPoolInitLock) {
                if (threadPoolExecutorSingleton == null) {
                    threadPoolExecutorSingleton = newDefaultThreadPoolExecutor();
                }
            }
        }
        return threadPoolExecutorSingleton;
    }


    private static ThreadPoolExecutor newDefaultThreadPoolExecutor() {
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize,
                keepAliveSeconds, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(maxBlockedSize),
                new CommonThreadFactory(),new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static ThreadPoolExecutor newThreadPoolExecutor(int corePoolSize, int maxPoolSiz, String threadPoolNamePrefix) {
        return new ThreadPoolExecutor(corePoolSize, maxPoolSiz,
                keepAliveSeconds, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(maxBlockedSize),
                new CommonThreadFactory(threadPoolNamePrefix),new ThreadPoolExecutor.CallerRunsPolicy());
    }


    /**
     * 直接丢弃的拒绝策略线程池
     * @param corePoolSize
     * @param maxPoolSiz
     * @param threadPoolNamePrefix
     * @return
     */
    public static ThreadPoolExecutor newDiscardThreadPoolExecutor(int corePoolSize, int maxPoolSiz, String threadPoolNamePrefix) {
        return new ThreadPoolExecutor(corePoolSize, maxPoolSiz,
                keepAliveSeconds, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(maxBlockedSize),
                new CommonThreadFactory(threadPoolNamePrefix), new ThreadPoolExecutor.DiscardPolicy());
    }


    private static class CommonThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;
        private static final String defaultPrefixName = "armcloudAsync-";
        private static final String threadNameFlag = "-t-";
        private static final String nameSeparator = "-";

        public CommonThreadFactory() {
            this("");
        }

        public CommonThreadFactory(String poolNamePrefix) {
            String prefix = null;
            if (StringUtils.isNotBlank(poolNamePrefix)) {
                prefix = poolNamePrefix;
            } else {
                prefix = defaultPrefixName;
            }
            if (!prefix.endsWith(nameSeparator)) {
                prefix = prefix + nameSeparator;
            }
            namePrefix = prefix + poolNumber.getAndIncrement() + threadNameFlag;
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
        }

        @Override
        public Thread newThread(Runnable runnable) {
            Thread workThread = new Thread(group, runnable, namePrefix + threadNumber.getAndIncrement(), 0);
            if (workThread.isDaemon()) {
                workThread.setDaemon(false);
            }
            if (workThread.getPriority() != Thread.NORM_PRIORITY) {
                workThread.setPriority(Thread.NORM_PRIORITY);
            }
            return workThread;
        }
    }

    public static void main(String[] args) {
        System.out.println(corePoolSize);
        System.out.println(maxPoolSize);
    }
}
