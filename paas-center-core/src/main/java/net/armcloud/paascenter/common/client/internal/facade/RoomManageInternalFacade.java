package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.rtc.ManageApplyShareTokenDTO;
import net.armcloud.paascenter.common.model.dto.rtc.RoomTokenConsoleDTO;
import net.armcloud.paascenter.common.model.vo.rtc.RoomTokenVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;


public interface RoomManageInternalFacade {

    /**
     * 获取房间token
     */
    @PostMapping(value = "/openapi/internal/rtc/getRoomToken")
    Result<RoomTokenVO> getRoomToken(@Valid @RequestBody RoomTokenConsoleDTO param);

    /**
     * 获取共享连接实例共享房间TOKEN
     */
    @PostMapping("/openapi/internal/rtc/share/applyToken")
    Result<Object> applyShareToken(@Valid @RequestBody ManageApplyShareTokenDTO dto);
}
