package net.armcloud.paascenter.common.client.internal.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UpdateInfoDTO {

    /**
     * 此字段已废弃,使用customerFileId代替
     */
    @Deprecated
    @NotNull(message = "fileId cannot null")
    private Long fileId;

    @NotNull(message = "customerFileId cannot null")
    private Long customerFileId;

    @NotNull(message = "name cannot null")
    private String name;

    @NotBlank(message = "fileMd5 cannot null")
    private String fileMd5;

    private String path;

    private Long size;

    private App appInfo;

    private Long dcId;

    private String iconPath;

    private String originUrl;

    @Data
    public static class App {
        private String appName;
        private String packageName;
        private String signatureMd5;
        private Long versionNo;
        private String versionName;
    }
}
