package net.armcloud.paascenter.common.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/19 20:39
 * @Description:
 */

@Data
public class DeviceLevelDTO {

    @ApiModelProperty(value = "板卡code列表")
    @NotNull(message = "deviceCodes cannot null")
    @Size(min = 1, max = 128, message = "设置板卡数量请勿超过128")
    private List<String> deviceCodes;

    @ApiModelProperty(value = "板卡规格")
    private String  deviceLevel;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;
    @ApiModelProperty(value = "所属机房code")
    private String dcCode;
    @ApiModelProperty(value = "用户ID")
    private Long  customerId;

}
