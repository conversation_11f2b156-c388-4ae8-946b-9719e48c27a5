package net.armcloud.paascenter.common.core.constant.comms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommsCommandEnum {
    /**
     * 通知Pad进入火山RTC房间推流
     */
    @JsonProperty("push_volcano_flow")
    PUSH_VOLCANO_FLOW("push_volcano_flow"),

    @JsonProperty("destroy_volcano_room")
    DESTROY_VOLCANO_ROOM("destroy_volcano_room"),

    /**
     * 本地截图
     */
    @JsonProperty("local_screenshot")
    LOCAL_SCREENSHOT("local_screenshot"),

    /**
     * 上传预览图
     */
    @JsonProperty("upload_preview")
    UPLOAD_PREVIEW("upload_preview"),

    /**
     * 执行ADB命令
     */
    @JsonProperty("adb_cmd")
    ADB_CMD("adb_cmd"),

    /**
     * 重启云机
     */
    @JsonProperty("restart")
    RESTART("restart"),

    /**
     * 重置云机
     */
    @JsonProperty("reset")
    RESET("reset"),

    /**
     * 升级镜像
     */
    @JsonProperty("upgrade_image")
    UPGRADE_IMAGE("upgrade_image"),

    /**
     * 下载文件
     */
    @JsonProperty("download_file")
    DOWNLOAD_FILE_CMD("download_file"),

    /**
     * 下载应用文件
     */
    @JsonProperty("download_file_app")
    DOWNLOAD_FILE_APP_CMD("download_file_app"),


    /**
     * 卸载应用
     */
    @JsonProperty("uninstall")
    UNINSTALL_APP_CMD("uninstall"),

    /**
     * 启动应用
     */
    @JsonProperty("start_app")
    START_APP_CMD("start_app"),

    /**
     * 停止应用
     */
    @JsonProperty("stop_app")
    STOP_APP_CMD("stop_app"),

    /**
     * 重启应用
     */
    @JsonProperty("restart_app")
    RESTART_APP_CMD("restart_app"),

    /**
     * 已安装应用列表
     */
    @JsonProperty("list_install_app")
    LIST_INSTALL_APP("list_install_app"),

    /**
     * 修改实例属性
     */
    @JsonProperty("update_properties")
    UPDATE_PROPERTIES("update_properties"),

    /**
     * pad准备就绪
     */
    @JsonProperty("ready")
    PAD_READY("ready"),

    /**
     * 应用黑名单
     */
    @JsonProperty("update_black_list")
    UPDATE_BLACK_LIST("update_black_list"),

    /**
     * 设置WIFI列表属性
     */
    @JsonProperty("set_wifi_list")
    SET_WIFI_LIST("set_wifi_list"),

    /**
     * 推送armcloud流
     */
    @JsonProperty("push_armcloud_flow")
    PUSH_ARMCLOUD_FLOW("push_armcloud_flow"),

    /**
     * 加入火山共享房间
     */
    @JsonProperty("join_volcano_share_room")
    JOIN_VOLCANO_SHARE_ROOM("join_volcano_share_room"),

    /**
     * 加入Armcloud共享房间
     */
    @JsonProperty("join_armcloud_share_room")
    JOIN_ARMCLOUD_SHARE_ROOM("join_armcloud_share_room"),

    /**
     * 设置实例带宽
     */
    @JsonProperty("speed_limit")
    SPEED_LIMIT("speed_limit"),

    /**
     * 设置实例代理
     */
    @JsonProperty("proxy_configure")
    PROXY_CONFIGURE("proxy_configure"),

    /**
     * 查询实例代理信息
     */
    @JsonProperty("proxy_info")
    PROXY_INFO("proxy_info"),

    /**
     * 修改语言
     */
    @JsonProperty("change_language")
    CHANGE_LANGUAGE("change_language"),

    /**
     * 修改时区
     */
    @JsonProperty("change_time_zone")
    CHANGE_TIME_ZONE("change_time_zone"),

    /**
     * 修改SIM卡信息
     */
    @JsonProperty("update_sim")
    UPDATE_SIM("update_sim"),

    /**
     * 更新 gameService
     */
    @JsonProperty("upgrade_gameserver_version")
    UPGRADE_GAMESERVER_VERSION("upgrade_gameserver_version"),

    /**
     * 杀死所有的app并返回云机首页
     */
    @JsonProperty("clear_app_home")
    CLEAR_APP_HOME("clear_app_home"),

    /**
     * 设置经纬度
     */
    @JsonProperty("gps_inject_info")
    GPS_INJECT_INFO("gps_inject_info"),

    /**
     * 更新通讯录
     */
    @JsonProperty("update_contacts")
    UPDATE_CONTACTS("update_contacts"),

    @JsonProperty("update_white_list")
    UPDATE_WHITE_LIST("update_white_list"),

    /**
     * 模拟触控
     */
    @JsonProperty("simulate_touch")
    SIMULATE_TOUCH("simulate_touch"),

    /**
     * 云机文本信息输入
     */
    @JsonProperty("set_commit_text")
    SET_COMMIT_TEXT("set_commit_text"),

    /**
     * 添加通话记录
     */
    @JsonProperty("add_phone_record")
    ADD_PHONE_RECORD("add_phone_record"),

    /**
     * 网存同步备份
     */
    @JsonProperty("net_sync_backup")
    NET_SYNC_BACKUP("net_sync_backup"),

    @JsonProperty("reset_gms_id")
    RESET_GAID("reset_gms_id"), // 重置GAID

    /**
     * 添加通话记录
     */
    @JsonProperty("injection_audio")
    INJECTION_AUDIO("injection_audio"),

    ;

    private final String command;
}
