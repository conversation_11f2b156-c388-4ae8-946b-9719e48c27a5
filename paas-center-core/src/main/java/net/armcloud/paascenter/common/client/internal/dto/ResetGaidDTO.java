package net.armcloud.paascenter.common.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class ResetGaidDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "实例组ID")
    private List<Integer> groupIds;

    @ApiModelProperty(value = "重置gms类型, 可选：GAID")
    @NotNull(message = "resetGmsType cannot null")
    private String resetGmsType;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    @ApiModelProperty(value = "操作者")
    private String oprBy;

}