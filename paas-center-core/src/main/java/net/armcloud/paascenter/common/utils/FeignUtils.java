package net.armcloud.paascenter.common.utils;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.SYSTEM_EXCEPTION;

@Slf4j
public class FeignUtils {

    public static <T> T getContent(Result<T> result) {
        return getContent(result, SYSTEM_EXCEPTION, null);
    }

    public static <T> T getContent(Result<T> result, Object param) {
        return getContent(result, SYSTEM_EXCEPTION, param);
    }

    public static <T> T getContent(Result<T> result, ExceptionCode exceptionCode, Object param) {
        if (Objects.isNull(result)) {
            log.error("feign exception>>>>>result is null  param:{}", JSON.toJSONString(param));
            throw new BasicException(exceptionCode);
        }

        if (Result.ok().getCode() != result.getCode()) {
            log.error("feign exception>>>>>result:{} not success  param:{}", JSON.toJSONString(result), JSON.toJSONString(param));
            throw new BasicException(result.getCode(), result.getMsg());
        }

        return result.getData();
    }

}
