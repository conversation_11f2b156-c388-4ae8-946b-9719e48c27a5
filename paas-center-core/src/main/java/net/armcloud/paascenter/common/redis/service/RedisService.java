package net.armcloud.paascenter.common.redis.service;

import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
import net.armcloud.paascenter.common.utils.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * spring redis 工具类
 *
 * <AUTHOR>
 **/
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Slf4j
@Component
public class RedisService {

    /** 管理员角色权限标识 */
    private static final String SUPER_ADMIN = "is_admin";
    @Autowired
    public RedisTemplate redisTemplate;

    /**
     * 用于获取用户权限信息
     * 判断 value 是否存在于 key 对应的 Set 中
     * @param key   Redis 中的 key
     * @return 如果 value 存在于 key 对应的 Set 中，返回 true；否则返回 false
     */
    private boolean isAdmin(String key) {
        // 1. 使用 redisTemplate 读取 Hash 字段值
        String isAdmin = (String) redisTemplate.opsForHash().get(key, SUPER_ADMIN);
        if (isAdmin != null) {
            return "1".equals(isAdmin);
        }
        return false;
    }

    public boolean isAdmin(Long customerId) {
        if (customerId == null) {
            return false;
        }
        if (customerId == 0) {
            return true;
        }
        return isAdmin(RedisKeyPrefix.USER_ROLES_PREFIX + customerId);
    }

    public void cacheCustomerRole(Integer customerId, Integer isAdmin) {
        redisTemplate.opsForHash().put(RedisKeyPrefix.USER_ROLES_PREFIX + customerId, "is_admin", isAdmin);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key   缓存的键值
     * @param value 缓存的值
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     *
     * @param key      缓存的键值
     * @param value    缓存的值
     * @param timeout  时间
     * @param timeUnit 时间颗粒度
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    public <T> void setBatchCacheObject(final Map<String, String> keys, final long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().multiSet(keys);

        // todo 需批量设置
        keys.forEach((key, value) -> redisTemplate.expire(key, timeout, timeUnit));
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout) {
        return expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置有效时间
     *
     * @param key     Redis键
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return true=设置成功；false=设置失败
     */
    public boolean expire(final String key, final long timeout, final TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取有效时间
     *
     * @param key Redis键
     * @return 有效时间
     */
    public long getExpire(final String key) {
        return redisTemplate.getExpire(key);
    }

    /**
     * 判断 key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 获得缓存的基本对象。
     *
     * @param key 缓存键值
     * @return 缓存键值对应的数据
     */
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }

    /**
     * 批量获取缓存对象
     *
     * @param keys 缓存键值列表
     * @return 键值对映射
     */
    public Map<String, Object> multiGet(final List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Object> values = redisTemplate.opsForValue().multiGet(keys);
        Map<String, Object> result = new HashMap<>();

        for (int i = 0; i < keys.size(); i++) {
            Object value = values.get(i);
            if (value != null) {
                result.put(keys.get(i), value);
            }
        }

        return result;
    }

    /**
     * 删除单个对象
     *
     * @param key
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     *
     * @param collection 多个对象
     * @return
     */
    public boolean deleteObject(final Collection collection) {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 缓存List数据
     *
     * @param key      缓存的键值
     * @param dataList 待缓存的List数据
     * @return 缓存的对象
     */
    public <T> long setCacheList(final String key, final List<T> dataList) {
        Long count = redisTemplate.opsForList().rightPushAll(key, dataList);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     *
     * @param key 缓存的键值
     * @return 缓存键值对应的数据
     */
    public <T> List<T> getCacheList(final String key) {
        return redisTemplate.opsForList().range(key, 0, -1);
    }

    /**
     * 缓存Set
     *
     * @param key     缓存键值
     * @param dataSet 缓存的数据
     * @return 缓存数据的对象
     */
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> dataSet) {
        BoundSetOperations<String, T> setOperation = redisTemplate.boundSetOps(key);
        Iterator<T> it = dataSet.iterator();
        while (it.hasNext()) {
            setOperation.add(it.next());
        }
        return setOperation;
    }

    /**
     * 获得缓存的set
     *
     * @param key
     * @return
     */
    public <T> Set<T> getCacheSet(final String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 缓存Map
     *
     * @param key
     * @param dataMap
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getCacheMap(final String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     *
     * @param key   Redis键
     * @param hKey  Hash键
     * @param value 值
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return Hash中的对象
     */
    public <T> T getCacheMapValue(final String key, final String hKey) {
        HashOperations<String, String, T> opsForHash = redisTemplate.opsForHash();
        return opsForHash.get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     *
     * @param key   Redis键
     * @param hKeys Hash键集合
     * @return Hash对象集合
     */
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     *
     * @param key  Redis键
     * @param hKey Hash键
     * @return 是否成功
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    public boolean opsForHashHasKey(String key, String field) {
        return Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(key, field));
    }

    /**
     * 获得缓存的基本对象列表
     *
     * @param pattern 字符串前缀
     * @return 对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 使用 SCAN 命令获取匹配的键
     *
     * @param pattern 匹配的模式，例如 "user:queue:*"
     * @return 返回匹配的键集合
     */
    public Set<String> scanKeys(String pattern, long count) {
        return (Set<String>) redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = new HashSet<>();
            ScanOptions options = ScanOptions.scanOptions().match(pattern).count(count).build();
            Cursor<byte[]> cursor = connection.scan(options);

            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }

            return keys;
        });
    }

    public Boolean setIfAbsentExpire(String key, String value, Long exTime, TimeUnit timeUnit) {
        return redisTemplate.opsForValue().setIfAbsent(key,value,exTime,timeUnit);
    }

    public Integer increment(String key) {
        try {
            return redisTemplate.opsForValue().increment(key).intValue();
        } catch (Throwable e) {
            log.error("redis自增异常，请检查redis指标", e);
            throw e;
        }
    }

    public Integer decrement(String key) {
        try {
            return redisTemplate.opsForValue().decrement(key).intValue();
        } catch (Throwable e) {
            log.error("redis自增异常，请检查redis指标", e);
            throw e;
        }
    }

    /**
     * 从 List 左侧弹出一个元素
     *
     * @param key Redis 键
     * @return 弹出的值，或 null
     */
    public <T> T leftPop(String key) {
        return (T) redisTemplate.opsForList().leftPop(key);
    }

    public Long  leftPopAsLong(String key) {
        Object value = redisTemplate.opsForList().leftPop(key);
        return value != null ? Long.valueOf(value.toString()) : null;
    }

    /**
     * 将元素推入 List 右侧
     *
     * @param key   Redis 键
     * @param value 值
     * @return 操作后 list 的长度
     */
    public <T> Long rightPush(String key, T value) {
        return redisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 将多个元素推入 List 右侧
     *
     * @param key    Redis 键
     * @param values 值列表
     * @return 操作后 list 的长度
     */
    public <T> Long rightPushAll(String key, List<T> values, long timeout, TimeUnit timeUnit) {
        Long result = redisTemplate.opsForList().rightPushAll(key, values);
        redisTemplate.expire(key, timeout, timeUnit);
        return result;
    }

    /**
     * 向Set中添加元素
     *
     * @param key   Redis键
     * @param value 值
     * @return 添加成功的数量
     */
    public <T> Long setCacheSetValue(final String key, final T value) {
        return redisTemplate.opsForSet().add(key, value);
    }

    /**
     * 向Set中添加元素
     *
     * @param key   Redis键
     * @param value 值
     * @return 添加成功的数量
     */
    public <T> Long setCacheSetValue(final String key, final T value,long timeout, TimeUnit timeUnit ) {
        Long result = redisTemplate.opsForSet().add(key, value);
        redisTemplate.expire(key, timeout, timeUnit);
        return result;
    }

    /**
     * 从Set中删除元素
     *
     * @param key   Redis键
     * @param value 值
     * @return 删除成功的数量
     */
    public <T> Long deleteCacheSetValue(final String key, final T value) {
        return redisTemplate.opsForSet().remove(key, value);
    }

    /**
     * 向Set中批量添加元素
     *
     * @param key   Redis键
     */
    public <T> void setCacheSetBatch(final String key, final Set<T> values, long timeout, TimeUnit unit) {
        if (values != null && !values.isEmpty()) {
            redisTemplate.opsForSet().add(key, values.toArray());
            redisTemplate.expire(key, timeout, unit);
        }
    }

    /**
     * 判断元素是否在Set中
     *
     * @param key   Redis键
     * @param value 值
     * @return 是否存在
     */
    public <T> Boolean isMemberOfSet(final String key, final T value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }


    /**
     * 原子重命名 Redis Key，如果 newKey 已存在将被覆盖
     *
     * @param oldKey 原始 key
     * @param newKey 新的 key
     */
    public void renameKey(final String oldKey, final String newKey) {
        redisTemplate.rename(oldKey, newKey);
    }

    // ========== ZSet 相关操作 ==========

    /**
     * 向ZSet中添加元素
     *
     * @param key   Redis键
     * @param value 值
     * @param score 分数
     * @return 是否添加成功
     */
    public Boolean addToSortedSet(final String key, final String value, final double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 从ZSet中移除元素
     *
     * @param key   Redis键
     * @param value 值
     * @return 移除的数量
     */
    public Long removeFromSortedSet(final String key, final String value) {
        return redisTemplate.opsForZSet().remove(key, value);
    }

    /**
     * 根据分数范围获取ZSet中的元素
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 元素集合
     */
    public Set<String> rangeByScore(final String key, final double min, final double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    /**
     * 根据分数范围移除ZSet中的元素
     *
     * @param key Redis键
     * @param min 最小分数
     * @param max 最大分数
     * @return 移除的数量
     */
    public Long removeRangeByScore(final String key, final double min, final double max) {
        return redisTemplate.opsForZSet().removeRangeByScore(key, min, max);
    }

    /**
     * 获取ZSet中元素的分数
     *
     * @param key   Redis键
     * @param value 值
     * @return 分数，如果元素不存在返回null
     */
    public Double getScoreFromSortedSet(final String key, final String value) {
        return redisTemplate.opsForZSet().score(key, value);
    }

    /**
     * 获取ZSet的大小
     *
     * @param key Redis键
     * @return ZSet大小
     */
    public Long getSortedSetSize(final String key) {
        return redisTemplate.opsForZSet().size(key);
    }
}
