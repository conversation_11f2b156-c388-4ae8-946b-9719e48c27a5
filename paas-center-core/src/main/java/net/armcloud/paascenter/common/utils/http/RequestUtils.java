package net.armcloud.paascenter.common.utils.http;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public class RequestUtils {

    /**
     * 获取当前请求的HttpServletRequest对象。
     * 如果当前不在请求上下文中，则返回null。
     *
     * @return 当前请求的HttpServletRequest对象，或者null（如果不在请求上下文中）
     */
    public static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attr == null) {
            throw new NullPointerException();
        }
        return attr.getRequest();
    }

}
