package net.armcloud.paascenter.common.core.aop;

import net.armcloud.paascenter.common.core.annotation.TableNameConcat;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Aspect
@Component
public class MonthlyTableQueryAspect {
    @Around("@annotation(tableNameConcat)")
    public Object executeQueryWithMonthlyTable(ProceedingJoinPoint joinPoint, TableNameConcat tableNameConcat) throws Throwable {
        String tableNamePrefix = tableNameConcat.tableNamePrefix();

        // 获取当前月份
        String currentMonth = LocalDate.now().format(DateTimeFormatter.ofPattern(tableNameConcat.concatType().getFormat()));
        String tableName = tableNamePrefix + currentMonth;
        Object[] args = joinPoint.getArgs();
        args[0] = tableName;
        return joinPoint.proceed(args);
    }
}
