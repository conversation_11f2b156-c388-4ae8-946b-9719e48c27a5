package net.armcloud.paascenter.common.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/19 21:13
 * @Description:
 */
@Data
public class StorageCapacityDetailVO {
    @ApiModelProperty(value = "可使用资源(单位:GB)")
    private BigDecimal storageCapacityAvailable  =  BigDecimal.ZERO;
    @ApiModelProperty(value = "总资源容量(单位:GB)")
    private long storageCapacityTotal  = 0L;

    @ApiModelProperty(value = "已使用资源容量(单位:GB)")
    private BigDecimal storageCapacityUsedTotal  = BigDecimal.ZERO;

    /**
     * 集群编号
     */
    private String clusterCode;

    private List<NetStorageRes> netStorageResList;


    public StorageCapacityDetailVO(){

    };
}
