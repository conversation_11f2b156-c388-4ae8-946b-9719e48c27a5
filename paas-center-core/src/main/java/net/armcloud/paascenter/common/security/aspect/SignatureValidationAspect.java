package net.armcloud.paascenter.common.security.aspect;/*
package net.armcloud.paascenter.common.security.aspect;

import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.core.utils.http.PaaSHttpUtils;
import net.armcloud.paascenter.common.core.utils.sign.PaasSignUtils;
import net.armcloud.paascenter.common.security.annotation.SignatureValidation;
import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;

*/
/**
 * paas 签名验证 aop
 *//*

@Aspect
@Component
public class SignatureValidationAspect {
    */
/*@Resource
    private ICustomerAccessService*//*


    @Before("@annotation(signatureValidation)")
    public void validateSignature(SignatureValidation signatureValidation) {
        // 获取请求的 HttpServletRequest 对象
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (attributes == null) {
            throw new BasicException(BasicExceptionCode.UNABLE_OBTAIN_HTTP_SERVLET_REQUEST_OBJECT);
        }

        request = attributes.getRequest();
        Map<String, String> headerMap = PaaSHttpUtils.getAllHeaders(request);
        if (headerMap != null && headerMap.containsKey(Constants.AUTHORIZATION)) {

            String xDate = headerMap.get(Constants.X_DATE);
            String host = headerMap.get(Constants.HOST);

            Map<String, String> authorizationMap = PaasSignUtils.parseHeader(headerMap.get(Constants.AUTHORIZATION));
            String credential = authorizationMap.get(Constants.HMAC_SHA256_CREDENTIAL);
            String accessKeyId = PaasSignUtils.parseAccessKeyId(credential);

            String signedHeaders = authorizationMap.get(Constants.SIGNED_HEADERS);
            String signature = authorizationMap.get(Constants.SIGNATURE);

            //计算签名
            String sign = PaasSignUtils.calculateSignature();

            if (!signature.equals(sign)) {
                throw new BasicException(BasicExceptionCode.VERIFICATION_SIGNATURE_FAILED);
            }
        } else {
            throw new BasicException(BasicExceptionCode.REQUEST_HEADER_MISSING_NECESSARY_PARAMETER_AUTHORIZATION);
        }
    }
}
*/
