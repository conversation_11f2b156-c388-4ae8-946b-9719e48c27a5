package net.armcloud.paascenter.common.client.internal.feign;

import net.armcloud.paascenter.cms.model.request.ProxyDetectionRequest;
import net.armcloud.paascenter.cms.model.request.SystemCmdRequest;
import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;

@FeignClient(name = "armcloud-container-system", url = "placeholder")
public interface ContainerSystemFeignClient {

    /**
     * 执行系统命令
     */
    @PostMapping(value = "/armcloud-container/open/system/cmd/sync")
    Result<String> execSyncCmd(URI host, @RequestBody SystemCmdRequest req);

    @PostMapping(value = "/armcloud-container/open/system/proxy_detection")
    Result<ProxyDetectionResponse> proxyDetection(URI host, @RequestBody ProxyDetectionRequest req);

}
