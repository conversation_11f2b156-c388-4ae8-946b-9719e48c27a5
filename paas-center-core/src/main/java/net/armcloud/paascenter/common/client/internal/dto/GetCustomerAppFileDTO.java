package net.armcloud.paascenter.common.client.internal.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class GetCustomerAppFileDTO {

    /**
     * 客户id
     */
    @NotNull(message = "customerId cannot null")
    private Long customerId;

    /**
     * 应用id
     */
    @NotNull(message = "appId cannot null")
    private String appId;

    /**
     * 应用名称
     */
    @NotNull(message = "appName cannot null")
    private String appName;

    /**
     * 包名
     */
    @NotNull(message = "pkgName cannot null")
    private String pkgName;
}
