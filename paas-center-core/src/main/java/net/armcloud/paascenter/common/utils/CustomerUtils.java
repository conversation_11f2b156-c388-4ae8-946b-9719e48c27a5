package net.armcloud.paascenter.common.utils;

import net.armcloud.paascenter.common.core.exception.BasicException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

import static net.armcloud.paascenter.common.core.constant.Constants.CUSTOMER_ID;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.MISSING_NECESSARY_CUSTOMER_INFORMATION;

@Slf4j
public class CustomerUtils {

    public static long getAndVerifyUserId(HttpServletRequest httpServletRequest) {
        String customerId = httpServletRequest.getHeader(CUSTOMER_ID);
        if (StringUtils.isBlank(customerId)) {
            throw new BasicException(MISSING_NECESSARY_CUSTOMER_INFORMATION);
        }

        return Long.parseLong(customerId);
    }

    public static long getAndVerifyUserIdByAttribute(HttpServletRequest httpServletRequest) {
        String customerId = httpServletRequest.getAttribute("customerId").toString();
        if (StringUtils.isBlank(customerId)) {
            throw new BasicException(MISSING_NECESSARY_CUSTOMER_INFORMATION);
        }

        return Long.parseLong(customerId);
    }

}
