package net.armcloud.paascenter.common.rocketmq.support;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DefaultRocketMqProducerWrapper implements IRocketMqProducerWrapper {

    private final RocketMQTemplate rocketMQTemplate;

    @Resource
    private Producer alimqProducer;

    @Resource
    private ClientServiceProvider clientServiceProvider;

    public DefaultRocketMqProducerWrapper(RocketMQTemplate rocketMQTemplate) {
        this.rocketMQTemplate = rocketMQTemplate;
    }

    public String producerNormalMessage(String topic, String msg) {
        return producerNormalMessage(topic, null, msg);
    }

    public String producerOrderlyNormalMessage(String topic, String msg,String hashKey) {
        return producerOrderlyNormalMessage(topic, null, msg,hashKey);
    }

    /**
     * 发送普通消息
     *
     * @param topic 消息主题
     * @param tag   消息标签
     * @param msg   消息主体
     * @return messageId 消息Id
     */
    @Override
    public String producerNormalMessage(String topic, String tag, String msg) {
        if (StringUtils.isAnyBlank(topic, msg)) {
            if (StringUtils.isBlank(topic)) {
                throw new RuntimeException("topic is blank");
            }
            return "";
        }
        if (StringUtils.isNotBlank(tag)) {
            topic = topic + ":" + tag;
        }
        rocketMQTemplate.sendOneWay(topic, msg);
//        log.info("send rocket mq normal message >>>> topic:{}", topic);
        return StringUtils.EMPTY;
    }

    /**
     * 发送普通消息
     *
     * @param topic 消息主题
     * @param tag   消息标签
     * @param msg   消息主体
     * @return messageId 消息Id
     */
    @Override
    public String producerOrderlyNormalMessage(String topic, String tag, String msg,String hashKey) {
        if (StringUtils.isAnyBlank(topic, msg)) {
            if (StringUtils.isBlank(topic)) {
                throw new RuntimeException("topic is blank");
            }
            return "";
        }
        if (StringUtils.isNotBlank(tag)) {
            topic = topic + ":" + tag;
        }
        rocketMQTemplate.sendOneWayOrderly(topic, msg,hashKey);
//        log.info("send rocket mq normal orderly message >>>> topic:{}", topic);
        return StringUtils.EMPTY;
    }

    @Override
    public String producerOrderlyMessage(String topic, String hashKey, String msg) {
        final SendResult result = rocketMQTemplate.syncSendOrderly(topic, msg, hashKey);
        String msgId = result.getMsgId();
        /*log.info("send rocket mq order message >>>> topic:{},hashKey:{},messageId:{}", topic, hashKey, msgId);*/
        return msgId;
    }

    /**
     * 发送延时消息（无标签）
     *
     * @param topic      消息主题
     * @param msg        消息内容
     * @param delayTime  延时时间（毫秒）
     * @return 消息id
     */
    @Override
    public String producerDelayMessage(String topic, String msg, long delayTime) {
        return producerDelayMessage(topic, null, msg, delayTime);
    }

    /**
     * 发送延时消息
     *
     * @param topic      消息主题
     * @param tag        消息标签
     * @param msg        消息内容
     * @param delayTime  延时时间（毫秒）
     * @return 消息id
     */
    @Override
    public String producerDelayMessage(String topic, String tag, String msg, long delayTime) {
        try {
            if (StringUtils.isAnyBlank(topic, msg)) {
                if (StringUtils.isBlank(topic)) {
                    throw new RuntimeException("topic is blank");
                }
                return "";
            }
            if (StringUtils.isNotBlank(tag)) {
                topic = topic + ":" + tag;
            }
            SendResult sendResult = rocketMQTemplate.syncSendDelayTimeMills(topic, msg, delayTime);
            log.info("send delay message >>>> topic:{}, msgId:{}", topic, sendResult.getMsgId());
            return sendResult.getMsgId();
        } catch (Exception e) {
            log.error("send delay message failed, topic:{}, tag:{}, delayTime:{}ms, error:{}",
                    topic, tag, delayTime, e.getMessage(), e);
            throw new RuntimeException("send delay message failed: " + e.getMessage(), e);
        } catch (Throwable t) {
            log.error("Failed to send message", t);
        }
        return null;
    }
}
