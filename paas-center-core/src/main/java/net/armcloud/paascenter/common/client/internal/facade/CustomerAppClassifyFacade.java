package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.client.internal.dto.QueryAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.vo.AppClassifyNameVO;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CustomerAppClassifyFacade {

    /**
     * 根据appIds 查询类别名称
     * @param dto
     * @return
     */
    @PostMapping(value = "/openapi/internal/customer/app/classify")
    Result<List<AppClassifyNameVO>> queryAppClassifyName(@RequestBody QueryAppClassifyNameDTO dto);
}
