package net.armcloud.paascenter.common.utils;


import lombok.extern.slf4j.Slf4j;


/**
 * 板卡实例异常告警群客户端
 */
@Slf4j
public class DingTalkPadClient {

    private static final String DEFAULT_DINGTALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=86a71fb3d2feaf491c2749a058a977322e7eaccf6eaedaa5f5774d9e415b00a0";


    public static void sendMessage( String springProfilesActive, String message) {
        DingTalkRobotClient.sendMessage(DEFAULT_DINGTALK_URL, message , springProfilesActive);
    }
}

