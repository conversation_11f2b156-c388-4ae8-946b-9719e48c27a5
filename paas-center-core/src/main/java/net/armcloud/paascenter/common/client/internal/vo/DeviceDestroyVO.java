package net.armcloud.paascenter.common.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeviceDestroyVO {

    @ApiModelProperty(value = "物理机ID")
    private Long deviceId;

    @ApiModelProperty(value = "物理机IP")
    private String deviceIp;

    @ApiModelProperty(value = "物理机编码")
    private String deviceCode;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "机房编码")
    private String dcCode;

    @ApiModelProperty(value = "集群公网ip")
    private String clusterPublicIp;

    @ApiModelProperty(value = "服务器 mac vlan")
    private String macVlan;
}
