package net.armcloud.paascenter.common.client.internal.vo;

import net.armcloud.paascenter.common.core.constant.paas.PadConstant;
import lombok.Data;

@Data
public class PadInfoVO {
    private Long customerId;
    private String padCode;
    private Integer padStatus;
    private Integer online;
    private Integer streamType;
    private String padIp;
    private Integer padSn;
    private Long padId;
    private Integer status;
    private String deviceLevel;

    public boolean isOnline() {
        return PadConstant.OnlineValue.ONLINE == this.getOnline();
    }
}
