package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.ADD_PHONE_RECORD;

@Getter
@Setter
@Accessors(chain = true)
public class PadCallRecordsCMDDTO extends BasePadCMDDTO {

    private List<PhoneRecord> phoneRecordList;

    @Getter
    @Setter
    public static class PhoneRecord {
        /**电话号码*/
        private String number;

        /**
         * 通话类型
         * 1: 拨出
         * 2: 接听
         * 3: 未接
         */
        private Integer inputType;

        /**通话时长(秒)*/
        private Integer duration;

        /**通话时间字符串 格式：yyyy-MM-dd HH:mm:ss*/
        private String timeString;
    }

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(ADD_PHONE_RECORD);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}