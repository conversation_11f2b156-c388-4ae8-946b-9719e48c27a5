package net.armcloud.paascenter.common.utils;

import java.util.Random;

public class MACUtils {
    public static String generateMacAddress() {
        Random random = new Random();
        byte[] mac = new byte[6];

        // 设置第一个字节：清除多播和本地位，确保合法的MAC地址
        mac[0] = (byte) (random.nextInt(256) & 0xFC & 0x7F); // 清除低2位以符合规范

        for (int i = 1; i < 6; i++) {
            mac[i] = (byte) random.nextInt(256);
        }

        // 将字节数组转换为标准MAC地址格式 (XX:XX:XX:XX:XX:XX)
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < mac.length; i++) {
            sb.append(String.format("%02X", mac[i]));
            if (i < mac.length - 1) {
                sb.append(":");
            }
        }

        return sb.toString().toLowerCase();
    }
}
