package net.armcloud.paascenter.common.lingdian.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ArmCardNetworkVO {
    /**
     * 每个节点对应的唯一标识uuid，用于后续节点相关操作
     */
    @JSONField(name = "id")
    private String id;
    /**
     * ip 地址
     */
    @JSONField(name = "ip")
    private String ip;
    /**
     * 子网掩码
     */
    @JSONField(name = "netmask")
    private String netmask;
    /**
     * 网关
     */
    @JSONField(name = "gateway")
    private String gateway;
    /**
     * vlan id
     */
//    @JSONField(name = "vlan")
//    private String vlan;


}
