package net.armcloud.paascenter.common.bmccloud.configure;

public class BmcCloudPath {
    public static final String BMC_LOGIN_URL = "/armcloud-bmc-manage/user/login";

    public static final String BMC_CARD_NODEINFO = "/armcloud-bmc-manage/card/nodeInfo";
    public static final String BMC_POWER_RESTART = "/armcloud-bmc-manage/card/powerRestart";
    public static final String BMC_SERVER_INIT = "/armcloud-bmc-manage/server/init";
    public static final String BMC_HEARTBEAT_URL = "/armcloud-bmc-manage/heartbeat/status";
    public static final String BMC_SERVER_DELETE = "/armcloud-bmc-manage/server/delete";
    public static final String BMC_DEVICE_KERNEL_INFO = "/armcloud-container/open/device/info";
    public static final String BMC_SET_DEVICE_NETWORK = "/armcloud-bmc-manage/card/setNetwork";

    public static final String BMC_SERVER_PULL_INCREMENT = "/armcloud-bmc-manage/server/pull/increment";

    public static final String BMC_SERVER_UPLOADIMAGES = "/armcloud-bmc-manage/server/uploadImages";

    public static final String BMC_CARD_TASKINFO = "/armcloud-bmc-manage/card/taskInfo";

    public static final String BMC_CARD_REINSTALL = "/armcloud-bmc-manage/card/reinstall";
}
