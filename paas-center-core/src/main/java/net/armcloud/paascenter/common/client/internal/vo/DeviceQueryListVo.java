package net.armcloud.paascenter.common.client.internal.vo;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 设备查询Vo
 */
@Data
public class DeviceQueryListVo extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  实例分配状态：-2删除失败 -1分配失败 0-未分配；1-分配中 2-已分配 3-删除中
     */
    private Integer padAllocationStatus;

    /**
     * 物理机状态 0-离线；1-在线
     */
    private Integer deviceStatus;

    /**
     * 服务器状态 0-离线；1-在线
     */
    private Integer armServerStatus;

    private Long customerId;

    /**
     *服务器code
     */
    private String armServerCode;

    /**
     * 集群code
     */
    private String clusterCode;

    /**
     * 板卡code
     */
    private String deviceCode;

    /**
     * 板卡ip
     */
    private String deviceIp;

    /**
     * 外部机房编码
     */
    private String idc;

    /**
     * 板卡Ip List
     */
    private List<String> deviceIpList;

}
