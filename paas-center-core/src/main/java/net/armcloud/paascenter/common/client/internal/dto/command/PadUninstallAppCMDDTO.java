package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UNINSTALL_APP_CMD;

@Getter
@Setter
@Accessors(chain = true)
public class PadUninstallAppCMDDTO extends BasePadCMDDTO {
    private Long fileId;

    private String packageName;

    private String appName;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode, Long customerFileId, String oprBy) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UNINSTALL_APP_CMD);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        padCMDForwardDTO.setCustomerFileId(customerFileId);
        padCMDForwardDTO.setOprBy(oprBy);

        return padCMDForwardDTO;
    }
}
