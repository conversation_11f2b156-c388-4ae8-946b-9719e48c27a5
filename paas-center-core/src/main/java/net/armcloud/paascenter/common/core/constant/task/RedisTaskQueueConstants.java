package net.armcloud.paascenter.common.core.constant.task;

/**
 * <AUTHOR>
 */
public class RedisTaskQueueConstants {
    public static final String TASK_QUEUE_PREFIX = "task_queue:";
    public static final String PAD_TASK_QUEUE_PREFIX = TASK_QUEUE_PREFIX + "pad:";
    public static final String PAD_TASK_QUEUE_DETAIL_PREFIX = PAD_TASK_QUEUE_PREFIX + "detail:";
    public static final String PAD_TASK_CUSTOMER_ID = PAD_TASK_QUEUE_PREFIX + "task:";

    public static final String PAD_TASK_CUSTOMER_TASK_ID = PAD_TASK_QUEUE_PREFIX + "customer_task_id:";

    /**
     * redis hash field
     */
    public static final String TASK_QUEUE_DETAIL_HASH_FIELD = "detail";
    public static final String TASK_QUEUE_CREATE_TIME_HASH_FIELD = "createTime";

    public static final String PAD_TASK_PULL_MODE_QUEUE_PREFIX = "task_queue_pull_mode:pad:";
    public static final String PAD_TASK_DETAIL_PULL_MODE_QUEUE_PREFIX = "task_queue_detail_pull_mode:task:";

    public static final String NET_SYNC_FIFO_TASKID_QUEUE  = "task_queue_net_sync:taskId";
    public static final String NET_SYNC_FIFO_TASK_HASH  = "task_queue_net_sync:task";
}
