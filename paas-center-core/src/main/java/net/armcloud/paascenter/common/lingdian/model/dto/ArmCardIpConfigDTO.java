package net.armcloud.paascenter.common.lingdian.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ArmCardIpConfigDTO {
    /**
     * ip 地址
     */
    @JSONField(name = "ip")
    private String ip;

    @JSONField(name = "dns")
    private String dns;

    /**
     * 子网掩码
     */
    @JSONField(name = "netmask")
    private String netmask;
    /**
     * 网关
     */
    @JSONField(name = "gateway")
    private String gateway;


}
