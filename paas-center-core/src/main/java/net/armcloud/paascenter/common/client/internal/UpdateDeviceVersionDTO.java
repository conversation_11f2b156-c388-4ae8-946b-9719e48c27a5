package net.armcloud.paascenter.common.client.internal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/2/17 17:43
 * @Version 1.0
 */
@Data
public class UpdateDeviceVersionDTO {
    @NotNull(message = "板卡debian系统不能为空")
    @ApiModelProperty("板卡debian系统打包信息")
    private String debianSysInfo;

    @NotNull(message = "板卡debian内核不能为空")
    @ApiModelProperty("板卡debian系统内核信息")
    private String debianBootInfo;

    @NotNull(message = "板卡CBS容器管理引擎不能为空")
    @ApiModelProperty("板卡CBS容器管理引擎信息")
    private String cbsInfo;

    @NotNull(message = "板卡存储寿命信息不能为空")
    @ApiModelProperty("板卡存储寿命信息")
    private String extLifeTimeInfo;

    @NotNull(message = "板卡ip不能为空")
    @ApiModelProperty("板卡ip信息")
    private String deviceIp;

}
