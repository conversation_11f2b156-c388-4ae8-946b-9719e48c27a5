package net.armcloud.paascenter.common.client.internal.vo;

import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.OnlineValue.OFFLINE;
import static net.armcloud.paascenter.common.core.constant.paas.PadConstant.OnlineValue.ONLINE;

@Data
public class GeneratePadTaskVO {
    @ApiModelProperty(value = "任务id")
    private Integer taskId;

    @ApiModelProperty(value = "实例编号")
    private String padCode;

    @ApiModelProperty(value = "实例在线状态")
    private Integer vmStatus;

    @ApiModelProperty(value = "任务状态：-1(任务已存在,请问重复提交),1(任务已添加)")
    private Integer taskStatus;

    public static List<GeneratePadTaskVO> builder(PadTaskBO padTaskBO) {
        if (Objects.isNull(padTaskBO) || CollectionUtils.isEmpty(padTaskBO.getSubTasks())) {
            return Collections.emptyList();
        }

        List<GeneratePadTaskVO> subTasks = new ArrayList<>(padTaskBO.getSubTasks().size());
        padTaskBO.getSubTasks().forEach(subTaskBO -> {
            GeneratePadTaskVO generatePadTaskVO = new GeneratePadTaskVO();
            generatePadTaskVO.setTaskId(subTaskBO.getCustomerTaskId());
            generatePadTaskVO.setPadCode(subTaskBO.getPadCode());
            // todo 更新此状态值
            generatePadTaskVO.setVmStatus(Boolean.TRUE.equals(subTaskBO.getSendCmdSuccess()) ? ONLINE : OFFLINE);
            if(subTaskBO.getOnline() != null){
                generatePadTaskVO.setVmStatus(subTaskBO.getOnline() ? 1 : 0);
            }
            // 默认设置任务状态为1（任务已添加）
            generatePadTaskVO.setTaskStatus(1);
            subTasks.add(generatePadTaskVO);
        });
        return subTasks;
    }
}
