package net.armcloud.paascenter.common.core.domain;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OptimizedPage<T> {
    /**
     * 每页的数量
     */
    private int rows;

    /**
     * 当前页的数量
     */
    private int size;

    /**
     * 当前页数据
     */
    private List<T> pageData;

    /**
     * 最后一条记录的ID，用于下次分页查询
     * 如果为null表示已经是最后一页
     */
    private Long lastId;

    /**
     * 是否还有下一页
     */
    private boolean hasNext;

    public OptimizedPage() {
    }

    public OptimizedPage(List<T> pageData, int rows) {
        this.pageData = pageData;
        this.rows = rows;
        this.size = pageData != null ? pageData.size() : 0;
        this.hasNext = this.size >= rows; // 如果返回的数据量等于请求的rows，说明可能还有下一页
        
        // 设置lastId
        if (pageData != null && !pageData.isEmpty() && hasNext) {
            // 这里需要根据实际的实体类来获取ID，暂时设为null
            // 在具体使用时需要手动设置lastId
            this.lastId = null;
        }
    }

    /**
     * 手动设置lastId
     * @param lastId 最后一条记录的ID
     */
    public void setLastId(Long lastId) {
        this.lastId = lastId;
    }
}
