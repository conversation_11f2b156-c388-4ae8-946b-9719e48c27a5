package net.armcloud.paascenter.common.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Data
public class GenerateDeviceTaskVO {

    @ApiModelProperty(value = "任务id")
    private Integer taskId;

    @ApiModelProperty(value = "云机ip")
    private String deviceIp;

    @ApiModelProperty(value = "错误原因")
    private String errorMsg;

    @ApiModelProperty(value = "云机编号")
    private String deviceOutCode;

    public static List<GenerateDeviceTaskVO> builder(List<AddDeviceTaskVO> deviceTasks) {
        if (Objects.isNull(deviceTasks)) {
            return null;
        }
        List<GenerateDeviceTaskVO> subTasks = new ArrayList<>(deviceTasks.size());
        deviceTasks.forEach(subTaskBO -> {
            GenerateDeviceTaskVO generateDeviceTaskVO = new GenerateDeviceTaskVO();
            if (isNotEmpty(subTaskBO.getErrorMsg())) {
                generateDeviceTaskVO.setErrorMsg(subTaskBO.getErrorMsg());
            } else {
                generateDeviceTaskVO.setTaskId(subTaskBO.getCustomerTaskId());
            }
            generateDeviceTaskVO.setDeviceIp(subTaskBO.getDeviceIp());
            generateDeviceTaskVO.setDeviceOutCode(subTaskBO.getDeviceOutCode());
            subTasks.add(generateDeviceTaskVO);
        });
        return subTasks;
    }
}
