package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.INJECTION_AUDIO;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class InjectAudioToMicCMDDTO extends BasePadCMDDTO{


    private String injectUrl;

    private Boolean enable;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));

        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(INJECTION_AUDIO);
        padCMDForwardDTO.setSourceCode(sourceCode);
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}
