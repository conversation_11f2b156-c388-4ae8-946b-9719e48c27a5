package net.armcloud.paascenter.common.utils;

public class NumberUtils {
    /**
     * 计算总页数
     *
     * @param total 总记录数
     * @param size  大小
     * @return int
     */
    public static int roundUp(int total, int size) {
        if (size <= 0) {
            throw new IllegalArgumentException("size must be greater than zero.");
        }

        // 总记录数除以每页显示的记录数，向上取整
        return (int) Math.ceil((double) total / size);
    }


    /**
     * 使用String.format()方法将Long类型的ID转换为固定长度的字符串形式，前面补零
     *
     * @param id     id
     * @param length 长度
     * @return String
     */
    public static String formatIdWithLeadingZeros(Long id, int length) {
        // 使用String.format()方法将Long类型的ID转换为固定长度的字符串形式，前面补零
        return String.format("%0" + length + "d", id);
    }
}
