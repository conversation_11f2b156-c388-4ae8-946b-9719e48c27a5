package net.armcloud.paascenter.common.utils;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.service.SlsLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志工具类
 * 提供便捷的日志记录方法，支持结构化日志和SLS日志
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Component
public class LogUtils {

    private static SlsLogService slsLogService;

    @Autowired(required = false)
    private SlsLogService autowiredSlsLogService;

    @PostConstruct
    public void init() {
        LogUtils.slsLogService = this.autowiredSlsLogService;
    }

    /**
     * 记录业务操作日志
     */
    public static void logBusiness(String businessType, String operation, String result, Object data) {
        Logger logger = LoggerFactory.getLogger("BUSINESS");
        String message = String.format("业务操作: %s - %s, 结果: %s", businessType, operation, result);
        
        if (data != null) {
            logger.info("{}, 数据: {}", message, data);
        } else {
            logger.info(message);
        }

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> businessData = new HashMap<>();
            if (data != null) {
                businessData.put("data", data.toString());
            }
            slsLogService.sendBusinessLog(businessType, operation, result, 
                MDC.get("traceId"), businessData);
        }
    }

    /**
     * 记录API调用日志
     */
    public static void logApiCall(String apiName, String method, String url, 
                                 long duration, int statusCode, Object request, Object response) {
        Logger logger = LoggerFactory.getLogger("API");
        String message = String.format("API调用: %s %s %s, 耗时: %dms, 状态码: %d", 
            method, url, apiName, duration, statusCode);
        
        logger.info(message);

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> apiData = new HashMap<>();
            apiData.put("apiName", apiName);
            apiData.put("method", method);
            apiData.put("url", url);
            apiData.put("duration", String.valueOf(duration));
            apiData.put("statusCode", String.valueOf(statusCode));
            if (request != null) {
                apiData.put("request", request.toString());
            }
            if (response != null) {
                apiData.put("response", response.toString());
            }
            
            slsLogService.sendLogAsync("INFO", "API", message, MDC.get("traceId"), apiData);
        }
    }

    /**
     * 记录数据库操作日志
     */
    public static void logDatabase(String operation, String table, String condition, 
                                  long duration, int affectedRows) {
        Logger logger = LoggerFactory.getLogger("DATABASE");
        String message = String.format("数据库操作: %s %s, 条件: %s, 耗时: %dms, 影响行数: %d", 
            operation, table, condition, duration, affectedRows);
        
        logger.info(message);

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> dbData = new HashMap<>();
            dbData.put("operation", operation);
            dbData.put("table", table);
            dbData.put("condition", condition);
            dbData.put("duration", String.valueOf(duration));
            dbData.put("affectedRows", String.valueOf(affectedRows));
            
            slsLogService.sendLogAsync("INFO", "DATABASE", message, MDC.get("traceId"), dbData);
        }
    }

    /**
     * 记录性能监控日志
     */
    public static void logPerformance(String component, String operation, long duration, 
                                     Map<String, Object> metrics) {
        Logger logger = LoggerFactory.getLogger("PERFORMANCE");
        String message = String.format("性能监控: %s - %s, 耗时: %dms", component, operation, duration);
        
        if (metrics != null && !metrics.isEmpty()) {
            logger.info("{}, 指标: {}", message, metrics);
        } else {
            logger.info(message);
        }

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> perfData = new HashMap<>();
            perfData.put("component", component);
            perfData.put("operation", operation);
            perfData.put("duration", String.valueOf(duration));
            if (metrics != null) {
                metrics.forEach((key, value) -> perfData.put("metric_" + key, String.valueOf(value)));
            }
            
            slsLogService.sendLogAsync("INFO", "PERFORMANCE", message, MDC.get("traceId"), perfData);
        }
    }

    /**
     * 记录错误日志
     */
    public static void logError(String component, String operation, String errorMessage, 
                               Throwable throwable, Object context) {
        Logger logger = LoggerFactory.getLogger("ERROR");
        String message = String.format("错误: %s - %s, 错误信息: %s", component, operation, errorMessage);
        
        if (context != null) {
            logger.error("{}, 上下文: {}", message, context, throwable);
        } else {
            logger.error(message, throwable);
        }

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> errorData = new HashMap<>();
            errorData.put("component", component);
            errorData.put("operation", operation);
            errorData.put("errorMessage", errorMessage);
            if (context != null) {
                errorData.put("context", context.toString());
            }
            if (throwable != null) {
                errorData.put("exceptionClass", throwable.getClass().getName());
                errorData.put("exceptionMessage", throwable.getMessage());
            }
            
            slsLogService.sendLogAsync("ERROR", "ERROR", message, MDC.get("traceId"), errorData);
        }
    }

    /**
     * 记录安全相关日志
     */
    public static void logSecurity(String event, String user, String ip, String resource, 
                                  String result, String details) {
        Logger logger = LoggerFactory.getLogger("SECURITY");
        String message = String.format("安全事件: %s, 用户: %s, IP: %s, 资源: %s, 结果: %s", 
            event, user, ip, resource, result);
        
        if (details != null && !details.isEmpty()) {
            logger.warn("{}, 详情: {}", message, details);
        } else {
            logger.warn(message);
        }

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> securityData = new HashMap<>();
            securityData.put("event", event);
            securityData.put("user", user);
            securityData.put("ip", ip);
            securityData.put("resource", resource);
            securityData.put("result", result);
            if (details != null) {
                securityData.put("details", details);
            }
            
            slsLogService.sendLogAsync("WARN", "SECURITY", message, MDC.get("traceId"), securityData);
        }
    }

    /**
     * 记录结构化日志
     */
    public static void logStructured(String level, String logger, String message, 
                                   Map<String, Object> fields) {
        Logger log = LoggerFactory.getLogger(logger);
        
        // 构建结构化消息
        StringBuilder sb = new StringBuilder(message);
        if (fields != null && !fields.isEmpty()) {
            sb.append(", 字段: ").append(fields);
        }
        
        String finalMessage = sb.toString();
        
        // 根据级别记录日志
        switch (level.toUpperCase()) {
            case "DEBUG":
                log.debug(finalMessage);
                break;
            case "INFO":
                log.info(finalMessage);
                break;
            case "WARN":
                log.warn(finalMessage);
                break;
            case "ERROR":
                log.error(finalMessage);
                break;
            default:
                log.info(finalMessage);
        }

        // 发送到SLS
        if (slsLogService != null) {
            Map<String, String> extraFields = new HashMap<>();
            if (fields != null) {
                fields.forEach((key, value) -> extraFields.put(key, String.valueOf(value)));
            }
            slsLogService.sendLogAsync(level.toUpperCase(), logger, message, 
                MDC.get("traceId"), extraFields);
        }
    }
}
