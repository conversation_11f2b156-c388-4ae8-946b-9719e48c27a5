package net.armcloud.paascenter.common.rocketmq.configure;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import net.armcloud.paascenter.common.rocketmq.concurrent.CommonThreadPoolExecutors;
import net.armcloud.paascenter.common.rocketmq.properties.AliRocketmqConfig;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.TraceIdHelper;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.PushConsumer;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import static org.springframework.aop.support.AopUtils.getTargetClass;

@Slf4j
@Service
public class InitialLoadingConsumer implements CommandLineRunner {
    private final AliRocketmqConfig rocketmqConfig;
    int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
    final ExecutorService executor = CommonThreadPoolExecutors.newThreadPoolExecutor(corePoolSize + 1, corePoolSize * 2, "rocketMqConsumer");
    private List<PushConsumer> pushConsumers = new ArrayList<>();

    @Override
    public void run(String... args) {
        log.info("start initializing rocketmq consumer...");
        if (Boolean.FALSE.equals(rocketmqConfig.getEnableConsumer())) {
            log.info("disable rocketmq consumer...");
            return;
        }

        SpringUtil.getBeansOfType(AliRocketMQListener.class).values().forEach(listener -> {
            AliRocketMQMsgListener annotation = getTargetClass(listener).getAnnotation(AliRocketMQMsgListener.class);
            if (annotation == null) {
                log.error("The listener {} is not annotated with AliRocketMQMsgListener", listener.getClass().getName());
                return;
            }

            executor.submit(() -> {
                // 解析占位符
                Environment environment = SpringUtil.getBean(Environment.class);
                String consumerGroup = environment.resolvePlaceholders(annotation.consumerGroup());
                String topic = environment.resolvePlaceholders(annotation.topic());
                log.info("start initialize RocketMQ listener for topic {} ", topic);
                ClientServiceProvider clientServiceProvider = SpringUtil.getBean(ClientServiceProvider.class);
                ClientConfiguration clientConfiguration = SpringUtil.getBean(ClientConfiguration.class);
                FilterExpression filterExpression = new FilterExpression(annotation.tag(), FilterExpressionType.TAG);
                log.info("RocketMQ NameServer Address: {}", clientConfiguration.getEndpoints());
                try {
                    PushConsumer pushConsumer = clientServiceProvider.newPushConsumerBuilder().setClientConfiguration(clientConfiguration)
                            .setClientConfiguration(clientConfiguration)
                            .setConsumerGroup(consumerGroup)
                            .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
                            .setMessageListener(messageView -> {
                                try {
                                    TraceIdHelper.setTraceId(null);
//                                    log.info("topic:{} consumerGroup:{} start consumer message:{}", topic, consumerGroup, messageView.getMessageId().toString());
                                    listener.onMessage(messageView);
                                    return ConsumeResult.SUCCESS;
                                } catch (Exception e) {
                                    log.error("onMessage error>>>>messageView:{} topic:{} consumerGroup:{} start consumer message:{} ", JSON.toJSONString(messageView), topic, consumerGroup, messageView.getMessageId().toString(), e);
                                    return ConsumeResult.SUCCESS;
                                }
                            })
                            .build();
                    pushConsumers.add(pushConsumer);
                } catch (Exception e) {
                    log.error("ClientException error>>>>", e);
                    throw new RuntimeException(e);
                }
            });
        });
    }

    public InitialLoadingConsumer(AliRocketmqConfig rocketmqConfig) {
        this.rocketmqConfig = rocketmqConfig;
    }

    public void shutdown(){
        int consumerCount = pushConsumers.size();
        CountDownLatch latch = new CountDownLatch(consumerCount);
        log.info("InitialLoadingConsumer shutdown start, pushConsumers size:{}", consumerCount);

        if (consumerCount == 0) {
            log.info("InitialLoadingConsumer shutdown end - no consumers to shutdown");
            return;
        }

        pushConsumers.forEach(pushConsumer -> {
            try {
                executor.submit(() -> {
                    String consumerGroup = "unknown";
                    try {
                        consumerGroup = pushConsumer.getConsumerGroup();
                        log.info("InitialLoadingConsumer shutdown start consumerGroup:{}", consumerGroup);
                        pushConsumer.close();
                        log.info("InitialLoadingConsumer shutdown end consumerGroup:{}", consumerGroup);
                    } catch (Exception e) {
                        log.error("InitialLoadingConsumer shutdown close error for consumerGroup:{}", consumerGroup, e);
                    } finally {
                        // 确保无论是否出现异常都要countDown
                        latch.countDown();
                    }
                });
            } catch (Exception e) {
                // 如果submit失败，也要countDown避免死锁
                log.error("InitialLoadingConsumer shutdown submit task failed", e);
                latch.countDown();
            }
        });

        // 全部执行完成后再继续执行，设置超时时间避免无限等待
        try {
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            if (!completed) {
                log.warn("InitialLoadingConsumer shutdown timeout after 30 seconds, remaining count: {}", latch.getCount());
            }
        } catch (InterruptedException e) {
            log.error("InitialLoadingConsumer shutdown await error", e);
            Thread.currentThread().interrupt();
        }

        log.info("InitialLoadingConsumer shutdown end");
    }
}
