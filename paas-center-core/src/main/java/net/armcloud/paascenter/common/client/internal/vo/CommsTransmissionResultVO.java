package net.armcloud.paascenter.common.client.internal.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class CommsTransmissionResultVO {
    /**
     * pad code
     */
    private String padCode;

    /**
     * requestId
     */
    private String requestId;

    /**
     * 是否发送成功
     */
    private Boolean sendSuccess;

    /**
     * 是否找到padCode
     */
    private Boolean foundPadCode;

    public static CommsTransmissionResultVO builderWithNotFoundServer(String padCode) {
        return new CommsTransmissionResultVO()
                .setPadCode(padCode)
                .setFoundPadCode(false)
                .setSendSuccess(false);
    }

    public static CommsTransmissionResultVO builderWithSendFail(String padCode) {
        return new CommsTransmissionResultVO()
                .setPadCode(padCode)
                .setFoundPadCode(true)
                .setSendSuccess(false);
    }

    public static CommsTransmissionResultVO builderWithSendSuccess(String padCode) {
        return new CommsTransmissionResultVO()
                .setPadCode(padCode)
                .setFoundPadCode(true)
                .setSendSuccess(true);
    }
}
