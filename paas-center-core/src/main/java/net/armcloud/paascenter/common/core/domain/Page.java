package net.armcloud.paascenter.common.core.domain;


import lombok.Getter;
import lombok.Setter;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageResUnitVO;

import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@Setter
public class Page<T> {
    /**
     * 当前页
     */
    private int page;

    /**
     * 每页的数量
     */
    private int rows;

    /**
     * 当前页的数量
     */
    private int size;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 总页数
     */
    private int totalPage;

    private List<T> pageData;

    public Page() {
    }

    public Page(List<T> pageData) {
        if (pageData instanceof com.github.pagehelper.Page) {
            com.github.pagehelper.Page<T> page = (com.github.pagehelper.Page<T>) pageData;
            this.page = page.getPageNum();
            this.rows = page.getPageSize();
            this.totalPage = page.getPages();
            this.pageData = page;
            this.size = page.size();
            this.total = page.getTotal();
        } else if (pageData instanceof Collection) {
            this.page = 1;
            this.rows = pageData.size();
            this.totalPage = 1;
            this.pageData = pageData;
            this.size = pageData.size();
            this.total = pageData.size();
        }
    }

    /**
     * 分页之后转换成具体的封装对象,减少字段返回
     * @param pageData 源数据
     * @param converter 转换函数
     */
    public Page(List<?> pageData, Function<Object, T> converter) {
        // 如果是 Page 类型，获取分页信息
        if (pageData instanceof com.github.pagehelper.Page) {
            com.github.pagehelper.Page<?> page = (com.github.pagehelper.Page<?>) pageData;
            this.page = page.getPageNum();
            this.rows = page.getPageSize();
            this.totalPage = page.getPages();
            this.size = page.size();
            this.total = page.getTotal();
        } else if (pageData instanceof Collection) {
            this.page = 1;
            this.rows = pageData.size();
            this.totalPage = 1;
            this.size = pageData.size();
            this.total = pageData.size();
        }

        // 使用传入的 Function 转换数据
        this.pageData = pageData.stream()
                .map(converter)
                .collect(Collectors.toList());
    }


    public void updatePageInfo(List pageData) {
        if (pageData instanceof com.github.pagehelper.Page) {
            com.github.pagehelper.Page<T> page = (com.github.pagehelper.Page<T>) pageData;
            this.page = page.getPageNum();
            this.rows = page.getPageSize();
            this.totalPage = page.getPages();
            this.total = page.getTotal();
        }
    }
}
