package net.armcloud.paascenter.common.utils.sign;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class PaasSignUtils {

    public static final String service = "armcloud-paas";
    public static String signature(String contentType, String signedHeaders, String host, String xDate, String sk, byte[] body) throws Exception {

        if (body == null) {
            body = new byte[0];
        }
//        log.info("body[]={}", Arrays.toString(body));
        String text = new String(body, StandardCharsets.UTF_8);
//        log.info("text:{}", text);
        String xContentSha256 = hashSHA256(body);
//        log.info("xContentSha256:{}", xContentSha256);
        String shortXDate = xDate.substring(0, 8);
//        log.info("shortXDate:{}", shortXDate);

        String canonicalStringBuilder = "host:" + host + "\n" + "x-date:" + xDate + "\n" + "content-type:" + contentType + "\n" + "signedHeaders:" + signedHeaders + "\n" + "x-content-sha256:" + xContentSha256;
//        log.info("canonicalStringBuilder:{}", canonicalStringBuilder);

        String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
//        System.out.println("hashcanonicalString:{}" + hashcanonicalString);

        String credentialScope = shortXDate + "/" + service + "/request";
        String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;
//        log.info("signString:{}", signString);

        byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, service);
//        log.info("signKey:{}", signKey);
        return bytesToHex(hmacSHA256(signKey, signString));
    }

    public static String signature2(String contentType, String signedHeaders, String host, String xDate, String sk, String bodyString) throws Exception {
        byte[] body = bodyString.getBytes();
        if (body == null) {
            body = new byte[0];
        }
        log.info("body[]={}", Arrays.toString(body));
        String text = new String(body, StandardCharsets.UTF_8);
        log.info("text:{}", text);
        String xContentSha256 = hashSHA256(body);
        log.info("xContentSha256:{}", xContentSha256);
        String shortXDate = xDate.substring(0, 8);
        log.info("shortXDate:{}", shortXDate);

        String canonicalStringBuilder = "host:" + host + "\n" + "x-date:" + xDate + "\n" + "content-type:" + contentType + "\n" + "signedHeaders:" + signedHeaders + "\n" + "x-content-sha256:" + xContentSha256;
        log.info("canonicalStringBuilder:{}", canonicalStringBuilder);

        String hashcanonicalString = hashSHA256(canonicalStringBuilder.getBytes());
        System.out.println("hashcanonicalString:{}" + hashcanonicalString);

        String credentialScope = shortXDate + "/" + service + "/request";
        String signString = "HMAC-SHA256" + "\n" + xDate + "\n" + credentialScope + "\n" + hashcanonicalString;
        log.info("signString:{}", signString);

        byte[] signKey = genSigningSecretKeyV4(sk, shortXDate, service);
        log.info("signKey:{}", signKey);
        byte[] singByte =  hmacSHA256(signKey, signString);
        log.info("singByte:{}", singByte);
        return bytesToHex(hmacSHA256(signKey, signString));
    }

    public static String hashSHA256(byte[] content) throws Exception {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            return bytesToHex(md.digest(content));
        } catch (Exception e) {
            throw new Exception("Unable to compute hash while signing request: " + e.getMessage(), e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String content) throws Exception {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(key, "HmacSHA256"));
            return mac.doFinal(content.getBytes());
        } catch (Exception e) {
            throw new Exception("Unable to calculate a request signature: " + e.getMessage(), e);
        }
    }

    private static byte[] genSigningSecretKeyV4(String secretKey, String date, String service) throws Exception {
        byte[] kDate = hmacSHA256((secretKey).getBytes(), date);
//        log.info("kDate:{}", kDate);
        byte[] kService = hmacSHA256(kDate, service);
//        log.info("kService:{}", kService);
        return hmacSHA256(kService, "request");
    }

    public static String bytesToHex(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }
        final StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    public static Map<String, String> parseHeader(String header) {
        Map<String, String> parts = new HashMap<>();
        String[] tokens = header.split(", ");
        for (String token : tokens) {
            String[] keyValue = token.split("=");
            parts.put(keyValue[0], keyValue[1]);
        }
        return parts;
    }

    public static String parseAccessKeyId(String str) {
        String[] parts = str.split("/");
        if (parts.length > 0) {
            return parts[0];
        } else {
            return null;
        }
    }

    public static byte[] getParamBytes(Map<String, String> paramMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        String paramString = sb.toString();
        return paramString.getBytes(StandardCharsets.UTF_8);
    }


}