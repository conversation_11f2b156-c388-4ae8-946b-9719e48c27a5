package net.armcloud.paascenter.common.filter;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.annotation.TraceId;
import net.armcloud.paascenter.common.utils.TraceIdHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Component
public class AddRequestIdFilter extends OncePerRequestFilter {


  @Override
  protected void doFilterInternal(
      HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
      throws ServletException, IOException {
    String traceId = request.getHeader(TraceIdHelper.TRACE_ID());
    if (Strings.isNullOrEmpty(traceId)) {
      traceId = TraceIdHelper.buildTraceId();
    }

    MDC.put(TraceIdHelper.TRACE_ID(), traceId);
    response.setHeader(TraceIdHelper.TRACE_ID(), traceId);

    try {
      filterChain.doFilter(request, response);
    } finally {
      MDC.remove(TraceIdHelper.TRACE_ID());
    }
  }
}
