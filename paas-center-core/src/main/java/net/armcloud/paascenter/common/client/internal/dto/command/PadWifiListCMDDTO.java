package net.armcloud.paascenter.common.client.internal.dto.command;

import lombok.Data;
import lombok.experimental.Accessors;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.SET_WIFI_LIST;

/**
 * <AUTHOR>
 * @Date 2025/3/6 15:52
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class PadWifiListCMDDTO extends BasePadCMDDTO {

    /**
     * 修改的wifi列表
     */
    private String  wifiJsonList;


    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        /**
         * 设置wifi属性列表指令
         */
        padCMDForwardDTO.setCommand(SET_WIFI_LIST);
        padCMDForwardDTO.setSourceCode(sourceCode);
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }

}
