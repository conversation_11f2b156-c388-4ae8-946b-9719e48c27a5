package net.armcloud.paascenter.common.core.exception.code;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum BasicExceptionCode implements ExceptionCode {
    SUCCESS(200, "成功"),
    SYSTEM_EXCEPTION(500, "系统异常"),
    PARAMETER_EXCEPTION(100000, "请求参数不正确"),
    PERMISSION_DENIED_EXCEPTION(100001, "没有访问权限"),
    UNABLE_OBTAIN_HTTP_SERVLET_REQUEST_OBJECT(100002, "无法获取HttpServlet请求对象"),
    REQUEST_HEADER_MISSING_NECESSARY_PARAMETER_AUTHORIZATION(100003, "请求头信息缺失必要参数：Authorization"),
    INVALID_KEY(100004, "无效的密钥"),
    VERIFICATION_SIGNATURE_FAILED(100005, "验证签名失败"),
    REQUEST_HEADER_MISSING_NECESSARY_PARAMETER_TOKEN(100006, "请求头信息缺失必要参数：token"),
    INVALID_TOKEN(100007, "无效的token"),
    VERIFICATION_TOKEN_FAILED(100008, "token 验证失败"),
    MISSING_NECESSARY_CUSTOMER_INFORMATION(100009, "非法请求,缺失必要客户信息"),
    PROCESSING_FAILED(100010, "处理失败"),
    LOCK_ACQUISITION_FAILED(100011, "当前有正在进行的相同请求,请稍后重试"),
    INTERFACE_NOT_SUPPORT_HTTP_METHOD(100012, "此接口不支持此HTTP请求方式"),
    PARAMETER_TYPE_ERROR(100013, "参数类型或格式错误"),
    OPERATION_TOO_FREQUENT(110014, "操作频繁请稍后再试"),
    UUID_BOUND_TO_THE_TOKEN_INCONSISTENT_REQUESTED_UUID(120006, "token绑定的uuid与请求uuid不一致"),
    INTERFACE_NOT_SUPPORT(120007, "此接口暂不支持此功能"),
    TOKEN_NOT_BELONGING_CUSTOM(120008,"token不属于当前用户"),

    /**
     * paas api实例相关异常
     */
    PAD_CODE_NOT_EXIST(110028, "实例不存在"),
    PAD_CODE_NOT_EMPTY(110029, "实例不能为空"),
    PAD_CODE_ONLINE_NOT_SUCCESS(110031, "当前实例状态未就绪,请检查实例状态后执行"),
    THE_PATH_IS_INCORRECT(120008, "文件存储路径不正确"),

    INVALID_FILE(120009,"文件地址无效"),

    UNSUPPORTED_COUNTRY_CODE(120010, "不支持的国家编码");
    ;

    private final int status;
    private final String msg;
}
