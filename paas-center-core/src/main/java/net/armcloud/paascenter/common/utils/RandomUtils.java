package net.armcloud.paascenter.common.utils;

import java.util.Random;

public class RandomUtils {
    private static final Random RANDOM = new Random();

    public static long generateRandomNumbersBetween3And5() {
        return generateRandomNumber(3, 5);
    }

    /**
     * 生成指定范围内的随机整数
     */
    public static long generateRandomNumber(int min, int max) {
        if (min >= max) {
            throw new IllegalArgumentException("最小值必须小于最大值");
        }
        return RANDOM.nextInt((max - min) + 1) + min;
    }

    private RandomUtils() {
    }
}
