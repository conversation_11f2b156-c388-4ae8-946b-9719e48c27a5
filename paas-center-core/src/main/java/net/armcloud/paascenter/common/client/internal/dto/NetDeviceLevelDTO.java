package net.armcloud.paascenter.common.client.internal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.util.List;

@Data
public class NetDeviceLevelDTO extends PageDTO {

    private List<String> deviceCodes;

    @ApiModelProperty(value = "板卡规格")
    private String deviceLevel;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;

    @ApiModelProperty(value = "服务器编号")
    private String armServerCode;
}
