package net.armcloud.paascenter.common.redis.delay;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.netpadv2.utils.SnowflakeIdGeneratorV3;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 基于Redis的延时任务管理器
 * 使用Redis的ZSet实现延时任务调度
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Slf4j
@Component
public class RedisDelayTaskManager implements ApplicationContextAware {
    
    /**
     * Redis延时任务队列Key前缀
     */
    private static final String DELAY_TASK_QUEUE_KEY = "delay:task:queue";
    
    /**
     * Redis延时任务数据Key前缀
     */
    private static final String DELAY_TASK_DATA_KEY = "delay:task:data:";
    
    /**
     * 最大延时时间（毫秒）
     */
    private static final long MAX_DELAY_TIME_MS = 10 * 1000L;
    
    /**
     * 扫描间隔（毫秒）
     */
    private static final long SCAN_INTERVAL_MS = 10L;
    
    @Resource
    private RedisService redisService;
    
    private ApplicationContext applicationContext;
    
    /**
     * 延时任务扫描线程池
     */
    private ScheduledExecutorService scanExecutor;
    
    /**
     * 延时任务执行线程池
     */
    private ThreadPoolExecutor taskExecutor;
    
    /**
     * 是否正在运行
     */
    private final AtomicBoolean running = new AtomicBoolean(false);
    
    /**
     * 延时任务执行器缓存
     */
    private final ConcurrentHashMap<String, RedisDelayTaskExecutor> executorCache = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 创建扫描线程池
        scanExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "redis-delay-task-scanner");
            t.setDaemon(true);
            return t;
        });
        
        // 创建任务执行线程池
        taskExecutor = new ThreadPoolExecutor(
            5, 20, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            r -> {
                Thread t = new Thread(r, "redis-delay-task-executor");
                t.setDaemon(true);
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        // 启动扫描任务
        startScanning();
        
        log.info("RedisDelayTaskManager initialized, max delay time: {}ms, scan interval: {}ms", 
                MAX_DELAY_TIME_MS, SCAN_INTERVAL_MS);
    }
    
    @PreDestroy
    public void destroy() {
        log.info("RedisDelayTaskManager shutting down...");
        running.set(false);
        
        if (scanExecutor != null) {
            scanExecutor.shutdown();
            try {
                if (!scanExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    scanExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                scanExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (taskExecutor != null) {
            taskExecutor.shutdown();
            try {
                if (!taskExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    taskExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                taskExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("RedisDelayTaskManager shutdown completed");
    }
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    
    /**
     * 提交延时任务
     * 
     * @param executorName 执行器名称
     * @param taskData 任务数据
     * @param delayTimeMs 延时时间（毫秒）
     * @return 任务ID
     */
    public String submitDelayTask(String executorName, String taskData, long delayTimeMs) {
        if (!running.get()) {
            log.warn("RedisDelayTaskManager is not running, reject task: {}", executorName);
            return null;
        }
        
        // 检查延时时间限制
        if (delayTimeMs <= 0) {
            log.warn("Invalid delay time: {}ms, must be positive", delayTimeMs);
            return null;
        }
        
        if (delayTimeMs > MAX_DELAY_TIME_MS) {
            log.warn("Delay time {}ms exceeds maximum {}ms, using maximum", delayTimeMs, MAX_DELAY_TIME_MS);
            delayTimeMs = MAX_DELAY_TIME_MS;
        }
        
        try {
            // 生成任务ID
            String taskId = generateTaskId();
            
            // 计算执行时间
            long executeTime = System.currentTimeMillis() + delayTimeMs;
            
            // 创建任务对象
            RedisDelayTask task = new RedisDelayTask();
            task.setTaskId(taskId);
            task.setExecutorName(executorName);
            task.setTaskData(taskData);
            task.setCreateTime(System.currentTimeMillis());
            task.setExecuteTime(executeTime);
            
            // 存储任务数据
            String taskDataKey = DELAY_TASK_DATA_KEY + taskId;
            redisService.setCacheObject(taskDataKey, JSON.toJSONString(task), delayTimeMs + 60000, TimeUnit.MILLISECONDS);
            
            // 添加到延时队列
            redisService.addToSortedSet(DELAY_TASK_QUEUE_KEY, taskId, executeTime);
            
            log.debug("Delay task submitted: taskId={}, executor={}, delay={}ms", taskId, executorName, delayTimeMs);
            return taskId;
            
        } catch (Exception e) {
            log.error("Failed to submit delay task: executor={}, delay={}ms", executorName, delayTimeMs, e);
            return null;
        }
    }
    
    /**
     * 取消延时任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelDelayTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            // 从延时队列中移除
            redisService.removeFromSortedSet(DELAY_TASK_QUEUE_KEY, taskId);
            
            // 删除任务数据
            String taskDataKey = DELAY_TASK_DATA_KEY + taskId;
            redisService.deleteObject(taskDataKey);
            
            log.debug("Delay task cancelled: taskId={}", taskId);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to cancel delay task: taskId={}", taskId, e);
            return false;
        }
    }
    
    /**
     * 启动扫描任务
     */
    private void startScanning() {
        running.set(true);
        
        scanExecutor.scheduleWithFixedDelay(() -> {
            if (!running.get()) {
                return;
            }
            
            try {
                scanAndExecuteTasks();
            } catch (Exception e) {
                log.error("Error scanning delay tasks", e);
            }
        }, SCAN_INTERVAL_MS, SCAN_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        log.info("Redis delay task scanning started");
    }
    
    /**
     * 扫描并执行到期任务
     */
    private void scanAndExecuteTasks() {
        long currentTime = System.currentTimeMillis();
        
        // 获取到期的任务
        Set<String> expiredTaskIds = redisService.rangeByScore(DELAY_TASK_QUEUE_KEY, 0, currentTime);
        
        if (expiredTaskIds == null || expiredTaskIds.isEmpty()) {
            return;
        }
        
        log.debug("Found {} expired delay tasks", expiredTaskIds.size());
        
        for (String taskId : expiredTaskIds) {
            try {
                if (!running.get()) {
                    return;
                }
                // 从队列中移除任务（原子操作）
                Long removed = redisService.removeFromSortedSet(DELAY_TASK_QUEUE_KEY, taskId);
                if (removed == null || removed == 0) {
                    // 任务已被其他实例处理
                    continue;
                }
                
                // 获取任务数据
                String taskDataKey = DELAY_TASK_DATA_KEY + taskId;
                String taskDataJson = redisService.getCacheObject(taskDataKey);
                if (taskDataJson == null) {
                    log.warn("Task data not found for taskId: {}", taskId);
                    continue;
                }
                
                // 解析任务数据
                RedisDelayTask task = JSON.parseObject(taskDataJson, RedisDelayTask.class);
                if (task == null) {
                    log.error("Failed to parse task data for taskId: {}", taskId);
                    continue;
                }
                
                // 异步执行任务
                taskExecutor.submit(() -> executeTask(task));
                
                // 删除任务数据
                redisService.deleteObject(taskDataKey);
                
            } catch (Exception e) {
                log.error("Error processing expired task: taskId={}", taskId, e);
            }
        }
    }
    
    /**
     * 执行延时任务
     */
    private void executeTask(RedisDelayTask task) {
        try {
            log.debug("Executing delay task: taskId={}, executor={}", task.getTaskId(), task.getExecutorName());
            
            // 获取执行器
            RedisDelayTaskExecutor executor = getExecutor(task.getExecutorName());
            if (executor == null) {
                log.error("DelayTaskExecutor not found: {}", task.getExecutorName());
                return;
            }
            
            // 执行任务
            executor.execute(task.getTaskData());
            
            log.debug("Delay task completed: taskId={}, executor={}", task.getTaskId(), task.getExecutorName());
            
        } catch (Exception e) {
            log.error("Delay task execution failed: taskId={}, executor={}", 
                    task.getTaskId(), task.getExecutorName(), e);
        }
    }
    
    /**
     * 获取延时任务执行器
     */
    private RedisDelayTaskExecutor getExecutor(String executorName) {
        return executorCache.computeIfAbsent(executorName, name -> {
            try {
                return applicationContext.getBean(name, RedisDelayTaskExecutor.class);
            } catch (Exception e) {
                log.error("Failed to get RedisDelayTaskExecutor bean: {}", name, e);
                return null;
            }
        });
    }
    
    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return Thread.currentThread().getId() + "-" +
               SnowflakeIdGeneratorV3.nextIdLong();
    }
}
