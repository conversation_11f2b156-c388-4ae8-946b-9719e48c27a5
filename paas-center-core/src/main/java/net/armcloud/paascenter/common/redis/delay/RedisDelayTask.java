package net.armcloud.paascenter.common.redis.delay;

import lombok.Data;

/**
 * Redis延时任务数据模型
 * 
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
public class RedisDelayTask {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 执行器名称
     */
    private String executorName;
    
    /**
     * 任务数据
     */
    private String taskData;
    
    /**
     * 创建时间
     */
    private Long createTime;
    
    /**
     * 执行时间
     */
    private Long executeTime;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 0;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;
}
