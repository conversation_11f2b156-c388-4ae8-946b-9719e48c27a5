package net.armcloud.paascenter.common.client.internal.dto.command;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.comms.CommsCommandEnum.UPDATE_SIM;

@Getter
@Setter
@Accessors(chain = true)
public class UpdatePadSIMCMDDTO extends BasePadCMDDTO{
    private String imei;
    private String imeisv;
    private String meid;
    private String operatorLongname;
    private String operatorShortnam;
    private String operatorNumeric;
    private String spn;
    private String iccid;
    private String imsi;
    private String phonenum;
    private String netCountry;
    private String simCountry;
    private String type;
    private String mcc;
    private String mnc;
    private String tac;
    private String cellid;
    private String narfcn;
    private String physicalcellid;

    public PadCMDForwardDTO builderForwardDTO(List<String> padCodes, SourceTargetEnum sourceCode) {
        List<PadCMDForwardDTO.PadInfoDTO> padInfos = new ArrayList<>();
        padCodes.forEach(padCode -> padInfos.add(new PadCMDForwardDTO.PadInfoDTO().setData(this).setPadCode(padCode)));

        PadCMDForwardDTO padCMDForwardDTO = new PadCMDForwardDTO();
        padCMDForwardDTO.setCommand(UPDATE_SIM);
        padCMDForwardDTO.setSourceCode(sourceCode);
        padCMDForwardDTO.setPadInfos(padInfos);
        return padCMDForwardDTO;
    }
}
