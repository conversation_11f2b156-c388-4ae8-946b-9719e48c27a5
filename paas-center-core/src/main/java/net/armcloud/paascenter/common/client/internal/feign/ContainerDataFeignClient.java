package net.armcloud.paascenter.common.client.internal.feign;

import net.armcloud.paascenter.cms.model.request.InstanceBackupDataRequest;
import net.armcloud.paascenter.cms.model.request.InstanceRestoreBackupDataRequest;
import net.armcloud.paascenter.cms.model.response.InstanceBackupDataResponse;
import net.armcloud.paascenter.cms.model.response.InstanceRestoreBackupDataResponse;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;
import java.util.List;


@FeignClient(name = "armcloud-container-data", url = "placeholder")
public interface ContainerDataFeignClient {
    /**
     * 数据备份
     */
    @PostMapping(value = "/armcloud-container/open/instance/backupData")
    Result<List<InstanceBackupDataResponse>> backupData(URI host, @RequestBody InstanceBackupDataRequest req);

    /**
     * 数据备份恢复
     */
    @PostMapping(value = "/armcloud-container/open/instance/restoreBackupData")
    Result<List<InstanceRestoreBackupDataResponse>> restoreBackupData(URI host, @RequestBody InstanceRestoreBackupDataRequest req);
}
