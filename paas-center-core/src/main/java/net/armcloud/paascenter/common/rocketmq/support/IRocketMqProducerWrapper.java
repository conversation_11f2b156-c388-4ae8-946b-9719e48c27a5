package net.armcloud.paascenter.common.rocketmq.support;

public interface IRocketMqProducerWrapper {
    /**
     * 发送普通消息
     */
    String producerNormalMessage(String topic, String tag, String msg);

    /**
     * 发送顺序普通消息
     */
    String producerOrderlyNormalMessage(String topic, String tag, String msg,String hashKey);

    /**
     * 发送顺序消息
     *
     * @param topic   topic
     * @param hashKey 用于实现顺序存入队列的key,使用业务唯一id作为key,例如订单id/实例id等
     * @param msg     发送消息内容
     * @return 消息id
     */
    String producerOrderlyMessage(String topic, String hashKey, String msg);

    /**
     * 发送延时消息（无标签）
     *
     * @param topic      消息主题
     * @param msg        消息内容
     * @param delayTime  延时时间（毫秒）
     * @return 消息id
     */
    String producerDelayMessage(String topic, String msg, long delayTime);

    String producerDelayMessage(String topic, String tag, String msg, long delayTime);
}
