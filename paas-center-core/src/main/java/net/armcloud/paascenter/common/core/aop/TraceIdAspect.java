// package net.armcloud.paascenter.common.core.aop;
//
// import org.aspectj.lang.annotation.After;
// import org.aspectj.lang.annotation.Aspect;
// import org.aspectj.lang.annotation.Before;
// import org.aspectj.lang.annotation.Pointcut;
// import org.slf4j.MDC;
// import org.springframework.stereotype.Component;
//
// import java.util.UUID;
//
// @Aspect
// @Component
// public class TraceIdAspect {
//     @Pointcut("@annotation(net.armcloud.paascenter.common.core.annotation.TraceId)")  // 需要明确指定完整的包路径
//     public void traceIdPointcut() {}
//
//     @Before("traceIdPointcut()")
//     public void beforeMethod() {
//         String traceId = MDC.get("traceId");
//         if (traceId == null) {
//             traceId = UUID.randomUUID().toString();
//         }
//         MDC.put("traceId", traceId);
//     }
//
//     @After("traceIdPointcut()")
//     public void afterMethod() {
//         MDC.clear();
//     }
// }
