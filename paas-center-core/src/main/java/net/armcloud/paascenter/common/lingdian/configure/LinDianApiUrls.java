package net.armcloud.paascenter.common.lingdian.configure;

public interface LinDianApiUrls {
    /**
     * 登录
     */
    String LOGIN = "/login";
    /**
     * 服务器信息
     */
    String ARM_SERVER_INFO = "/arm_server/info";
    /**
     * 查询全部节点信息
     */
    String ARM_CARDS_INFO = "/arm_cards/info";
    /**
     * 查询全部节点状态
     */
    String ARM_CARDS_STATUS = "/arm_cards/status";
    /**
     * 查询单个节点状态
     */
    String ARM_CARD_SINGLE_STATUS = "/arm_cards/{{arm_card_id}}/status";
    /**
     * 节点上电
     */
    String ARM_CARDS_POWER_ON = "/arm_cards/{{arm_card_id}}/power_on";
    /**
     * 节点下电
     */
    String ARM_CARDS_POWER_OFF = "/arm_cards/{{arm_card_id}}/power_off";
    /**
     * 配置节点IP
     */
    String ARM_CARD_IP_CONFIG = "/arm_cards/{{arm_card_id}}/ip_config";
    /**
     * 配置节点DNS
     */
    String ARM_CARD_DNS_CONFIG = "/arm_cards/{{arm_card_id}}/dns_config";
    /**
     * 配置节点VLAN
     */
    String ARM_CARD_VLAN_CONFIG = "/arm_cards/{{arm_card_id}}/vlan_config";
    /**
     * 查询全部节点网络配置
     */
    String ARM_CARDS_NETWORK_INFO = "/arm_cards/network_info";
    /**
     * 查询单个节点网络配置
     */
    String ARM_CARD_NETWORK_INFO = "/arm_cards/{{arm_card_id}}/network_info";
    /**
     * 节点恢复出厂设置
     */
    String ARM_CARDS_RE_INIT = "/arm_cards/{{arm_card_id}}/reinit";
    /**
     * 查询任务信息
     */
    String JOB_INFO = "/job/{{job_id}}";
    /**
     * 上传镜像
     */
    String UPLOAD_IMAGES = "/images";
    /**
     * 获取镜像信息
     */
    String IMAGES_INFO = "/images/{{rom_name}}";
    /**
     * 删除镜像
     */
    String DELETE_IMAGES = "/images/{{rom_name}}";
    /**
     * 节点刷机
     */
    String REINSTALL = "/arm_cards/{{arm_card_id}}/reinstall";
    /**
     * node 信息
     */
    String NODE_INFO = "/mars_node/{{node}}/summary";

}
