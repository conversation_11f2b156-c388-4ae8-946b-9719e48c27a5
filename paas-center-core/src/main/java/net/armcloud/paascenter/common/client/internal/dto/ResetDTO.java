package net.armcloud.paascenter.common.client.internal.dto;

import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
@Data
public class ResetDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @ApiModelProperty(value = "实例组ID")
    private List<Integer> groupIds;

    /**
     * 客户ID
     */
    @ApiModelProperty(hidden = true)
    private Long customerId;

    /**
     * userId
     */
    @ApiModelProperty(hidden = true)
    private Long userId;

    /**
     * 任务来源
     */
    @ApiModelProperty(hidden = true)
    private SourceTargetEnum taskSource;

    /**
     * 操作人
     */
    @ApiModelProperty(hidden = true)
    private String oprBy;
}
