package net.armcloud.paascenter.common.lingdian.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ArmCardVO {
    /**
     * 每个节点对应的唯一标识uuid，用于后续节点相关操作
     */
    @JSONField(name = "id")
    private String id;
    /**
     * 节点的 mac 地址
     */
    @JSONField(name = "mac")
    private String mac;
    /**
     * 节点所以在的物理位置
     */
    @JSONField(name = "position")
    private String position;


}
