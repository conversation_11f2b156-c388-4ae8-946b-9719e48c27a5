package net.armcloud.paascenter.common.core.test;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RocketMQMessageListener(topic = "testMq", consumerGroup = "testMq-group")
public class TestConsumer implements RocketMQListener<String> {
    public void onMessage(String message) {
        log.info("TestConsumer s:{}", message);
    }
}
