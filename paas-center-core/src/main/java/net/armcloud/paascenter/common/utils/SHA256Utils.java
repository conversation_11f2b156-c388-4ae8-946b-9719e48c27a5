package net.armcloud.paascenter.common.utils;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.security.MessageDigest;

public class SHA256Utils {

    public static String generateSHA256(File file) throws Exception {
        try(InputStream inputStream = new FileInputStream(file)) {
            return generateSHA256(inputStream);
        }
    }

    public static String generateSHA256(MultipartFile file) throws Exception {
        try(InputStream inputStream = file.getInputStream()) {
            return generateSHA256(inputStream);
        }
    }

    public static String generateSHA256(InputStream inputStream) throws Exception {
        MessageDigest md5;
        byte[] buffer = new byte[1024];
        md5 = MessageDigest.getInstance("SHA-256");
        for (int numRead; (numRead = inputStream.read(buffer)) > 0; ) {
            md5.update(buffer, 0, numRead);
        }

        return toHexString(md5.digest());
    }

    private static String toHexString(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder();
        for (byte b : bytes) {
            stringBuilder.append(Integer.toHexString(b & 0xFF));
        }

        return stringBuilder.toString();
    }
}
