package net.armcloud.paascenter.common.client.internal.facade;

import net.armcloud.paascenter.common.client.internal.dto.CleanServerDTO;
import net.armcloud.paascenter.common.core.domain.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ServerInternalFacade {
    @PostMapping(value = "/comms-center/internal/server/cleanServer")
    Result cleanServer(@RequestBody CleanServerDTO request);

}
