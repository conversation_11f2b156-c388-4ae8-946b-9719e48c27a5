package net.armcloud.paascenter.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtils {
    public static final String FORMART1 ="yyyy-MM-dd HH:mm:ss";


    /**
     * 使用UTC时间，精确到秒
     *
     * @param dateTime LocalDateTime
     * @return String
     */
    public static String DateToUTC(LocalDateTime dateTime) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("uuuuMMdd'T'HHmmss'Z'");
        return dateTime.format(formatter);
    }

    public static String DateToYMDHMS(LocalDateTime dateTime) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }


    /**
     * 比较一个给定的时间是否 小于（当前时间 - minutes）
     *
     * @param dateTime 给定的时间
     * @param minutes  分钟数
     * @return Boolean
     */
    public static Boolean isBeforeMinutesAgo(LocalDateTime dateTime, Integer minutes) {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime fiveMinutesAgo = currentTime.minusMinutes(minutes);

        return dateTime.isBefore(fiveMinutesAgo);
    }

    /**
     * 获取当前时间加几分钟的时间
     * @param minutes 分钟
     * @return LocalDateTime
     */
    public static LocalDateTime plusMinutesDateTime(Integer minutes) {
        // 获取当前时间
        LocalDateTime currentTime = LocalDateTime.now();
        return currentTime.plusMinutes(minutes);
    }


}
