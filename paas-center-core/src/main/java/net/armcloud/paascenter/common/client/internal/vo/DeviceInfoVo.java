package net.armcloud.paascenter.common.client.internal.vo;

import lombok.Data;

@Data
public class DeviceInfoVo {

    /**
     * 物理机ID
     */
    private Long deviceId;

    /**
     * 物理机IP
     */
    private String deviceIp;

    /**
     * 外部物理机编码
     */
    private String deviceOutCode;

    /**
     * 物理机编码
     */
    private String deviceCode;

    /**
     * 板卡规格
     */
    private String deviceLevel;

    /**
     * 集群编号
     */
    private String clusterCode;

    /**
     * 机房ID
     */
    private String dcId;

    /**
     * 型号
     */
    private String socModel;

    /**
     * 集群公网IP
     */
    private String clusterPublicIp;

    private String id;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 实例信息
     */
    private String padCode;

    private Integer padAllocationStatus;

    /**
     * 服务器id
     */
    private Long ArmServerId;
}
