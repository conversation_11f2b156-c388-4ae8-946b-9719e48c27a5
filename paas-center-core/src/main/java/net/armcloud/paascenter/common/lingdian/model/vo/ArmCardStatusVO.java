package net.armcloud.paascenter.common.lingdian.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ArmCardStatusVO {
    /**
     * 每个节点对应的唯一标识uuid，用于后续节点相关操作
     */
    @JSONField(name = "id")
    private String id;
    /**
     * 节点状态包括：
     *  on 节点上电
     * running 节点运行
     *  off 节点下电
     * not in pos 节点不在pos中
     */
    @JSONField(name = "status")
    private String status;


}
