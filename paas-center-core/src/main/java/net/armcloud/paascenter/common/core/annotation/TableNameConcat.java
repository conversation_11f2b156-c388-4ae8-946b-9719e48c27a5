package net.armcloud.paascenter.common.core.annotation;

import lombok.Getter;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TableNameConcat {


    /**
     * 表名前缀，例如：data_table_
     */
    String tableNamePrefix();

    ConcatType concatType() default ConcatType.YEAR_MONTH;

    @Getter
    enum ConcatType {
        YEAR_MONTH {
            @Override
            public String getFormat() {
                return "yyyyMM";
            }
        },

        YEAR_MONTH_DAY {
            @Override
            public String getFormat() {
                return "yyyyMMdd";
            }
        };


        public abstract String getFormat();
    }
}
