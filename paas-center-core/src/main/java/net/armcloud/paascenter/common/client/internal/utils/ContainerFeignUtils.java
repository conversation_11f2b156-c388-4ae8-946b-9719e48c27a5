package net.armcloud.paascenter.common.client.internal.utils;

import net.armcloud.paascenter.common.core.exception.BasicException;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URISyntaxException;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;

@Slf4j
public class ContainerFeignUtils {
    private ContainerFeignUtils() {
    }


    //TODO 2025-01月之前的私有化部署调用cms没有走gateway,后续更新私有化这块要走这个代码
//    public static URI builderHost(String deviceIp) {
//        int port = 19001;
//        String host = "http://" + deviceIp + ":" + port;
//        try {
//            return new URI(host);
//        } catch (URISyntaxException e) {
//            log.error("builderHost error>>>deviceIp:{}", deviceIp, e);
//            throw new BasicException(PROCESSING_FAILED);
//        }
//    }

    public static URI builderHost(String deviceIp) {
        String host = "http://" + deviceIp;
        try {
            return new URI(host);
        } catch (URISyntaxException e) {
            log.error("builderHost error>>>deviceIp:{}", deviceIp, e);
            throw new BasicException(PROCESSING_FAILED);
        }
    }
}
