package net.armcloud.paascenter.common.client.internal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeviceVO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 物理机编号
     */
    @ApiModelProperty(value = "物理机编号")
    private String deviceCode;

    /**
     * 实例信息
     */
    @ApiModelProperty(value = "实例信息")
    private String instanceType;

    /**
     * 外部信息-供应商
     */
    @ApiModelProperty(value = "外部信息-供应商")
    private String cloudVendorType;

    /**
     * 外部信息-供应商名称
     */
    @ApiModelProperty(value = "外部信息-供应商名称")
    private String cloudVendorTypeName;

    /**
     * 外部信息-所在机房
     */
    @ApiModelProperty(value = "外部信息-所在机房")
    private String dcName;

    @ApiModelProperty(value = "外部信息-所在机房")
    private String clusterName;

    /**
     * 外部信息-云机id
     */
    @ApiModelProperty(value = "外部信息-云机id")
    private String deviceOutCode;

    /**
     * 外部信息-云机ip
     */
    @ApiModelProperty(value = "外部信息-云机ip")
    private String deviceIp;

    /**
     * 外部信息-mac
     */
    @ApiModelProperty(value = "外部信息-mac")
    private String macAddress;

    /**
     * 外部信息-SOC型号
     */
    @ApiModelProperty(value = "外部信息-SOC型号")
    private String socModel;

    /**
     * 外部信息-所属服务器
     */
    @ApiModelProperty(value = "外部信息-所属服务器")
    private String armServerCode;

    /**
     * 归属客户-客户账户
     */
    @ApiModelProperty(value = "归属客户-客户账户")
    private String customerAccount;

    /**
     * 归属客户-客户id
     */
    @ApiModelProperty(value = "归属客户-客户id")
    private Long customerId;

    @ApiModelProperty(value = "归属客户-客户编号")
    private String customerCode;

    /**
     * 归属客户-分配时间
     */
    @ApiModelProperty(value = "归属客户-分配时间")
    private String assignTime;

    /**
     * 归属客户-到期时间
     */
    @ApiModelProperty(value = "归属客户-到期时间")
    private String expireTime;

    /**
     * 归属客户-回收时间
     */
    @ApiModelProperty(value = "归属客户-回收时间")
    private String recoveryTime;

    /**
     * 状态-业务状态
     */
    @ApiModelProperty(value = "状态-业务状态")
    private Integer status;

    /**
     * 状态-业务状态名称
     */
    @ApiModelProperty(value = "状态-业务状态名称")
    private String statusName;

    /**
     * 状态-云机状态
     */
    @ApiModelProperty(value = "状态-物理机状态")
    private Integer cloudStatus;

    /**
     * 状态-云机状态名称
     */
    @ApiModelProperty(value = "状态-物理机状态名称")
    private String cloudStatusName;

    /**
     * 状态-实例创建
     */
    @ApiModelProperty(value = "状态-实例创建")
    private String padAllocationStatusName;

    /**
     * 状态-实例创建
     */
    @ApiModelProperty(value = "状态-实例创建")
    private Integer padAllocationStatus;

    /**
     * 状态-实例规格数
     */
    private int padTotal;

    /**
     * 状态-实例创建数
     */
    private int createPadNumber = 0;

    /**
     * 状态-初始化状态
     */
    @ApiModelProperty(value = "状态-初始化状态")
    private Integer initStatus;

    @ApiModelProperty(value = "回收信息-创建时间")
    private String createTime;

    @ApiModelProperty(value = "服务器状态")
    private String armServerOnline;

    @ApiModelProperty(value = "服务器状态名称")
    private String armServerOnlineName;

    @ApiModelProperty(value = "在线设备数")
    private Integer onlineSum;

    @ApiModelProperty(value = "离线设备数")
    private Integer offlineSum;

    @ApiModelProperty(value = "设备总数")
    private Integer count;

    @ApiModelProperty(value = "板卡debian系统打包信息")
    private String debianSysInfo;

    @ApiModelProperty(value = "板卡debian系统内核信息")
    private String debianBootInfo;

    @ApiModelProperty(value = "板卡存储寿命信息")
    private String extLifeTimeInfo;

    @ApiModelProperty(value = "主存储信息")
    private String extLifeTimeInfo_A;

    @ApiModelProperty(value = "元数据信息")
    private String extLifeTimeInfo_B;

    @ApiModelProperty(value = "板卡debian系统内核信息")
    private String cbsInfo;

    @ApiModelProperty(value = "板卡网关")
    private String gateway;

    @ApiModelProperty(value = "服务器ip")
    private String armServerIp;

    @ApiModelProperty(value = "用户名称")
    private String customerName;
}
