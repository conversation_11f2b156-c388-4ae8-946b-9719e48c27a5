package net.armcloud.paascenter.openapi.service.impl;

import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.mapper.DcInfoMapper;
import net.armcloud.paascenter.openapi.model.dto.DeleteFileVO;
import net.armcloud.paascenter.openapi.service.FileService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/24 11:23
 * @Version 1.0
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {


    @Value("${strategy.user:3}")
    private Integer minioDeleteUser;

    private final DcInfoMapper dcInfoMapper;
    private final RestTemplate restTemplate;

    public FileServiceImpl(DcInfoMapper dcInfoMapper, RestTemplate restTemplate) {
        this.dcInfoMapper = dcInfoMapper;
        this.restTemplate = restTemplate;
    }

    /**
     * 后续可能存在多个边缘机房维护dcInfo表，因此需要遍历所有边缘机房获取地址进行删除操作
     *
     * @param deleteFileVO
     */
    @Override
    public void deleteFile(DeleteFileVO deleteFileVO) {
//        long andVerifyUserId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
//        if (andVerifyUserId != minioDeleteUser.longValue()) {
//            throw new BasicException("只能罗韬用户删除文件");
//        }
//        log.info("deleteFile start >>>>>>>>>>>>>>>>>>>>>>> andVerifyUserId:{}", andVerifyUserId);
//        // TODO: 2025/1/22 只允许罗韬删除文件
//        log.info("deleteFile request param: {}", deleteFileVO.toString());
//        List<DcInfo> list = dcInfoMapper.list();
//        log.info("deleteFile list : {}", list);
//        list.forEach(info -> {
//            if(info.getOssScreenshotEndpoint() != null){
//                String url = info.getOssScreenshotEndpoint() + "/oss/open/object/file/deleteFile";
//                log.info("request url:{}", url);
//                // 设置请求头
//                HttpHeaders headers = new HttpHeaders();
//                headers.setContentType(MediaType.APPLICATION_JSON);
//                // 创建请求实体
//                HttpEntity<DeleteFileVO> requestEntity = new HttpEntity<>(deleteFileVO, headers);
//                try {
//                    String result = restTemplate.postForObject(url, requestEntity, String.class);
//                    log.info("deleteFile result: {}", result);
//                } catch (RestClientException e) {
//                    // 处理超时异常
//                    if (e.getCause() instanceof java.net.SocketTimeoutException) {
//                        throw new RuntimeException("请求超时，请稍后重试", e);
//                    }
//                    throw new RuntimeException("请求失败: " + e.getMessage(), e);
//                }
//            }
//            log.error("deleteFile failed, ossScreenshotEndpoint is null");
//
//        });
    }
}
