package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePadAndroidPropDTO extends BaseDTO{
    @NotBlank(message = "padCode cannot null")
    private String padCode;

    @NotNull(message = "props cannot null")
    private Map<String, String> props;

    private Boolean restart;
}
