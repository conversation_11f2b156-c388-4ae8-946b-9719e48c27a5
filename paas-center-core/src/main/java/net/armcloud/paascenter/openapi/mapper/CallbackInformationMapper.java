package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.CallbackInformation;
import net.armcloud.paascenter.common.model.vo.api.CallbackUrlVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CallbackInformationMapper extends BaseMapper<CallbackInformation> {

    /**
     * 查询回调地址
     *
     * @param customerId Long
     * @param type       Integer
     * @return CallbackUrlVO
     */
    CallbackUrlVO selectCallbackUrl(@Param("customerId") Long customerId, @Param("type") Integer type);

}
