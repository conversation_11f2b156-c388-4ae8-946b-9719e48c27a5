package net.armcloud.paascenter.openapi.rocketmq;

import com.alibaba.fastjson.JSON;
import net.armcloud.paascenter.common.model.mq.container.ContainerDeviceTaskResultMQ;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@Service
@AliRocketMQMsgListener(topic = "${mq.container-device-task-message.topic}", consumerGroup = "${mq.container-device-task-message.group}")
public class ContainerDeviceTaskResultConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private IDeviceService deviceService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("ContainerDeviceTaskResultConsumer onMessage:{},MessageId:{}", str, messageView.getMessageId());

        ContainerDeviceTaskResultMQ dto = JSON.parseObject(str, ContainerDeviceTaskResultMQ.class);
        //消息幂等
        String key = RedisKeyPrefix.VIRTUALIZE_DEVICE_TASK_RESULT_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.tryLock(key, 0, 5);
        if (Objects.isNull(lock)) {
            log.info("ContainerDeviceTaskResultConsumer not get lock onMessage:{},MessageId:{}", str, messageView.getMessageId());
            return;
        }
        try {
            ContainerTaskResultVO containerTaskResult = new ContainerTaskResultVO();
            BeanUtils.copyProperties(dto, containerTaskResult);
            deviceService.ContainerDeviceTaskResult(containerTaskResult);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

}
