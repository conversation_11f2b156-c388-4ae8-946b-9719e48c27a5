package net.armcloud.paascenter.openapi.controller;

import static net.armcloud.paascenter.cms.constants.LockKeyConstants.IpClashLock.*;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.CARD_DOES_NOT_EXIST;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.openapi.mapper.ArmServerMapper;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2IpManager;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.vo.DeviceQueryListVo;
import net.armcloud.paascenter.common.client.internal.vo.DeviceVO;
import net.armcloud.paascenter.common.client.internal.vo.GenerateDeviceTaskVO;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.service.ICbsFileVersionService;
import net.armcloud.paascenter.openapi.service.IDeviceService;

import net.armcloud.paascenter.common.client.internal.dto.DeviceCbsUpdateDTO;
import net.armcloud.paascenter.common.client.internal.dto.DeviceDestroyDTO;
import net.armcloud.paascenter.common.client.internal.dto.PowerResetDTO;
import net.armcloud.paascenter.common.client.internal.dto.VirtualizeDeviceDTO;

import net.armcloud.paascenter.openapi.utils.IpUtil;

@RestController
@RequestMapping("/openapi/open/device")
@Api(tags = "物理机")
@Slf4j
public class DeviceController {

    @Resource
    private IDeviceService deviceService;
    @Resource
    private ICbsFileVersionService cbsFileVersionService;

    @Resource
    private RedisService redisService;

    @Resource
    private ArmServerMapper armServerMapper;
    @Resource
    private NetPadV2IpManager netPadV2IpManager;

    // @RequestMapping("/deviceRestart")
    // public Result<?> deviceRestart(@Valid @RequestBody DeviceRestartDTO param) {
    // param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
    // deviceService.deviceRestart(param);
    // return Result.ok();
    // }

    /**
     * 物理机重启
     *
     * @param param
     * @return
     */
    @RequestMapping("/powerReset")
    public Result<?> powerReset(@Valid @RequestBody PowerResetDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return deviceService.powerReset(param);
    }

    /**
     * 物理机创建云机任务
     *
     * @param param
     * @return
     */
    @RequestMapping("/virtualize")
    public Result<?> virtualize(@Valid @RequestBody VirtualizeDeviceDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.virtualize(param));
    }

    /**
     * 物理机删除云机任务
     *
     * @param param
     * @return
     */
    @RequestMapping("/deviceDestroy")
    public Result<?> deviceDestroy(@Valid @RequestBody DeviceDestroyDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.deviceDestroy(param));
    }

    /**
     * 板卡列表list
     *
     * @param param
     * @return
     */
    @PostMapping("/list")
    public Result<?> list(@Valid @RequestBody DeviceQueryListVo param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.listByDeviceQueryDTO(param));
    }

    /**
     * cbs更新
     *
     * @param param
     * @return
     */
    @RequestMapping("/cbsUpdate")
    public Result<?> cbsUpdate(@Valid @RequestBody DeviceCbsUpdateDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.cbsUpdate(param));
    }


    /**
     * cbs预热镜像
     *
     * @param param
     * @return
     */
    @RequestMapping("/boardImageWarmup")
    public Result<?> boardImageWarmup(@Valid @RequestBody DeviceBoardImageWarmupDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.boardImageWarmup(param,SourceTargetEnum.PAAS));
    }

    /**
     * 获取最新的cbs
     *
     * @return
     */
    @RequestMapping("/getLastCbs")
    public Result<?> getLastCbs() {
        return Result.ok(cbsFileVersionService.getLastCbs());
    }

    @RequestMapping(value = "/resetDevice", method = RequestMethod.POST)
    @ApiOperation(value = "重置板卡", httpMethod = "POST", notes = "重置板卡")
    public Result<?> resetDevice(@RequestBody @Valid DeviceDestroyDTO req) {
        req.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        List<DeviceVO> deviceList = null;
        if (CollUtil.isNotEmpty(req.getDeviceCodes())) {
            deviceList = deviceService.getDeviceInfoByCode(req.getDeviceCodes());
        } else {
            deviceList = deviceService.getDeviceInfo(req.getDeviceIps());
        }
        if (CollUtil.isEmpty(deviceList)) {
            throw new BasicException(PadExceptionCode.DEVICE_IP_NOT_ONLINE);
        }
        if (!redisService.isAdmin(req.getCustomerId())) {
            deviceList.forEach(s -> {
                if (!s.getCustomerId().equals(req.getCustomerId())) {
                    throw new BasicException(CARD_DOES_NOT_EXIST);
                }
            });
        }

        List<String> offlineDeviceIps = new ArrayList<>();
        StringBuilder failureMsg = new StringBuilder();
        List<String> deviceIps = new ArrayList<>();
        List<String> deviceCodes = new ArrayList<>();
        for (DeviceVO device : deviceList) {
            if (!device.getStatus().equals(Constants.DEVICE_STATUS_INIT_SUCCESS)) {
                offlineDeviceIps.add(device.getDeviceIp());
            } else {
                deviceIps.add(device.getDeviceIp());
                deviceCodes.add(device.getDeviceCode());
            }
        }
        // if (isNotEmpty(failureMsg)) {
        // failureMsg.append("板卡状态离线！");
        // }
        if (deviceIps.isEmpty()) {
            // 打印离线的板卡信息
            if (!offlineDeviceIps.isEmpty()) {
                for (String deviecIp : offlineDeviceIps) {
                    failureMsg.append(deviecIp).append(",");
                }
                // 移除最后一个逗号
                if (failureMsg.length() > 0) {
                    failureMsg.deleteCharAt(failureMsg.length() - 1);
                }
                failureMsg.append(" 板卡状态离线!");
            }
            // 没有需要重置的板卡
            return Result.fail(failureMsg.toString() + ", 没有其他需要重置的板卡!");
        }
        deviceIps = deviceIps.stream().distinct().collect(Collectors.toList());
        req.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        req.setDeviceIps(deviceIps);
        req.setSourceCode(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        req.setOprBy(SourceTargetEnum.ADMIN_SYSTEM.getCode());
        Result<?> destroy = deviceDestroy(req);

        if (destroy.getCode() == Result.SUCCESS) {
            List<GenerateDeviceTaskVO> data = (List<GenerateDeviceTaskVO>) destroy.getData();
            long count = data.stream().filter(dto -> isNotEmpty(dto.getTaskId())).count();
            return Result.ok(failureMsg.toString() + count + "个板卡删除实例！");
        } else {
            return Result.fail(failureMsg.toString() + destroy.getMsg());
        }
    }


    @RequestMapping(value = "/net/deviceLevel/list", method = RequestMethod.POST)
    @ApiOperation(value = "网存板卡列表", httpMethod = "POST", notes = "网存板卡列表")
    public Result<Page<Device>> netDeviceLevelList(@RequestBody @Valid NetDeviceLevelDTO req) {
        req.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.netDeviceLevelList(req));
    };


    @PostMapping(value = "net/setDeviceLevel")
    @ApiOperation(value = "网存集群设置板卡规格", httpMethod = "POST", notes = "网存集群设置板卡规格")
    public Result<String> setDeviceLevel(@RequestBody @Valid DeviceLevelDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(deviceService.setDeviceLevel(param));
    }


    /**
     * CBS上报实例编号和IP
     * @return 处理结果
     */
    @PostMapping("/reportInstanceIp")
    @ApiOperation(value = "上报实例IP", httpMethod = "POST", notes = "CBS每5分钟上报实例编号和IP")
    public Result<?> reportInstanceIp(@Valid @RequestBody DeviceInstanceIpReportDTO param) {
//        log.info("reportInstanceIp param:{}", param);
//        String deviceIp = param.getDeviceIp();
//        // 按板卡维度缓存
//        cacheIp(param.getInstanceList(), deviceIp);
//        // 按服务器维度记录已使用的IP
//        List<String> ipList = CollectionUtil.isEmpty(param.getInstanceList()) ? Collections.emptyList() : param.getInstanceList().stream().map(DeviceInstanceIpReportDTO.InstanceIpReportDTO::getPadIp).collect(Collectors.toList());
//        netPadV2IpManager.recordUsedIp(deviceIp, ipList);
        return Result.ok();
    }

    private void cacheIp(List<DeviceInstanceIpReportDTO.InstanceIpReportDTO> instanceList, String key) {
        if(CollUtil.isEmpty(instanceList)){
            log.info("reportInstanceIp 值为空,清掉对应IP");
            String redisKey = CBS_DEVICE_IP_TO_USE + "{" + key + "}";
            redisService.deleteObject(redisKey);
            return;
        }

        String redisKey = CBS_DEVICE_IP_TO_USE + "{" + key + "}";
        String tempRedisKey = CBS_DEVICE_IP_TO_USE_TEMP + "{" + key + "}";

        // 1. 构建全量 IP 集合
        Set<String> ipSet = instanceList.stream()
                .map(DeviceInstanceIpReportDTO.InstanceIpReportDTO::getPadIp)
                .collect(Collectors.toSet());

        // 2. 删除临时 key（防止残留）
        redisService.deleteObject(tempRedisKey);

        // 3. 写入临时 key
        redisService.setCacheSetBatch(tempRedisKey, ipSet, 1, TimeUnit.HOURS);

        // 4. 原子重命名临时 key 覆盖原 key
        redisService.renameKey(tempRedisKey, redisKey);
    }


}
