package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class PadModelInfoDTO {

    @ApiModelProperty(value = "实例id", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, max = 200, message = "实例数量不多余200个")
    private List<String> padCodes;
}
