package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 黑白名单查询响应对象
 */
@Data
public class AppClassifyDetailVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;
    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    private Integer classifyType;
    @ApiModelProperty(value = "应用数量")
    private Integer appNum;
    @ApiModelProperty(value = "描述")
    private String remark;
    @ApiModelProperty(value = "是否应用所有实例 0否 1是 默认1")
    private Boolean applyAllInstances;
    @ApiModelProperty(value = "关联应用集合")
    private List<AppInfo> appInfos;

    @Data
    public static class AppInfo{
        @ApiModelProperty(value = "文件id")
        private Long fileId;
        @ApiModelProperty(value = "应用id")
        private Long appId;
        @ApiModelProperty(value = "应用名称")
        private String appName;
        @ApiModelProperty(value = "包名")
        private String packageName;
        @ApiModelProperty(value = "版本号")
        private Long appVersionNo;
        @ApiModelProperty(value = "版本名称")
        private String appVersionName;
    }

}
