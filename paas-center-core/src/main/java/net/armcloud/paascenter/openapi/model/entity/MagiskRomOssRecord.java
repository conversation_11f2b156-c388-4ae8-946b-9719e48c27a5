package net.armcloud.paascenter.openapi.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Magisk ROM OSS记录实体类
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@TableName("magisk_rom_oss_record")
@ApiModel(value = "MagiskRomOssRecord", description = "Magisk ROM OSS记录")
public class MagiskRomOssRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * oss地址
     */
    @ApiModelProperty(value = "oss地址")
    @TableField("oss_url")
    private String ossUrl;

    /**
     * 包版本
     */
    @ApiModelProperty(value = "包版本")
    @TableField("version")
    private String version;

    /**
     * 替换时间
     */
    @ApiModelProperty(value = "替换时间")
    @TableField("replace_time")
    private Date replaceTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField("update_by")
    private String updateBy;
}
