package net.armcloud.paascenter.openapi.service;

import net.armcloud.paascenter.common.client.internal.vo.GeneratePadBackupTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.DataDelDTO;
import net.armcloud.paascenter.common.model.dto.api.DataDelMasterDTO;
import net.armcloud.paascenter.common.model.dto.api.PadBackupDTO;
import net.armcloud.paascenter.common.model.dto.api.PadRestoreDTO;

import java.util.List;

public interface IPadBackupService {
    /**
     * 实例备份
     *
     * @return 任务信息
     */
    List<GeneratePadBackupTaskVO> backup(PadBackupDTO param);

    /**
     * 实例数据备份恢复
     *
     * @return 任务信息
     */
    List<GeneratePadTaskVO> restore(PadRestoreDTO param);


    /**
     * 批量实例数据删除（支持批量，逻辑删除）
     * @return 任务信息
     */
    List<DataDelDTO> dataDel(DataDelMasterDTO param);
}
