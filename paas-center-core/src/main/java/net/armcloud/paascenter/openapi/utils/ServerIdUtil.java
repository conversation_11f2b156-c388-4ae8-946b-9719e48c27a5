package net.armcloud.paascenter.openapi.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

/**
 * 服务器唯一标识 服务启动时生成
 */
public class ServerIdUtil {

    /**服务当前的唯一标识 雪花算法id + 6位随机数*/
    public static String SERVER_UNIQUE_NO;

    public static void initServerUniqueNo(){
        SERVER_UNIQUE_NO = IdWorker.getIdStr() + "_" +  PadCodeRamdomUtil.generateRandomString(6);
    }

    public static String getServerUniqueNo(){
        if(SERVER_UNIQUE_NO != null){
            return SERVER_UNIQUE_NO;
        }else{
            synchronized (ServerIdUtil.class){
                if(SERVER_UNIQUE_NO == null){
                    initServerUniqueNo();
                }
                return SERVER_UNIQUE_NO;
            }
        }
    }
}
