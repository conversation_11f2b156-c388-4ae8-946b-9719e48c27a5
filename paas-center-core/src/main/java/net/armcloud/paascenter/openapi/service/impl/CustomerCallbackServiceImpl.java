package net.armcloud.paascenter.openapi.service.impl;

import net.armcloud.paascenter.common.client.internal.dto.CustomerCallbackDTO;
import net.armcloud.paascenter.common.client.internal.vo.CustomerCallbackVO;
import net.armcloud.paascenter.openapi.mapper.CustomerCallbackMapper;
import net.armcloud.paascenter.openapi.service.ICustomerCallbackService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.redis.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CustomerCallbackServiceImpl implements ICustomerCallbackService {

    @Resource
    private   RedisService redisService;

    @Resource
    private CustomerCallbackMapper customerCallbackMapper;


    @Override
    public Integer DeleteCallback(List<Long> ids) {
        return customerCallbackMapper.DeleteCallback(ids);
    }

    @Override
    public Result<?> insertCallback(List<CustomerCallbackDTO> list, Long customerId) {
        for (CustomerCallbackDTO item : list) {
            customerCallbackMapper.deleteByCustomerIdAndCallBackId(customerId,item.getCallbackId());
            item.setCustomerId(customerId);
            item.setEnable(1);
            item.setCreateBy(String.valueOf(customerId));
            item.setCreateTime(new Date());
        }
        return Result.ok(customerCallbackMapper.batchInsert(list));
    }
    @Override
    public Result<?> updateCallback(List<CustomerCallbackDTO> list,Long customerId) {
        customerCallbackMapper.deleteByCustomerId(customerId);
        for (CustomerCallbackDTO item : list) {
            item.setCustomerId(customerId);
            item.setEnable(1);
            item.setCreateBy(String.valueOf(customerId));
            item.setCreateTime(new Date());
        }
        return Result.ok(customerCallbackMapper.batchInsert(list));
    }

    @Override
    public List<CustomerCallbackVO> selectList() {
        return customerCallbackMapper.selectList();
    }


    @Override
    public Result<?> selectCallback(long userId) {
        return Result.ok(customerCallbackMapper.selectCallback(userId));
    }
}
