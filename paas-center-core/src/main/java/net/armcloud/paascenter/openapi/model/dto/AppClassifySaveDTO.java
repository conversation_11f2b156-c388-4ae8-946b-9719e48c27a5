package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 黑白名单保存对象
 */
@Data
public class AppClassifySaveDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long customerId;
    @ApiModelProperty(value = "分类名称")
    @NotEmpty(message = "classifyName cannot null")
    private String classifyName;
    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    @NotNull(message = "classifyType cannot null")
    @Min(value = 1,message = "无效的分类")
    @Max(value = 2,message = "无效的分类")
    private Integer classifyType;
    @ApiModelProperty(value = "应用数量")
    private Integer appNum;
    @ApiModelProperty(value = "描述")
    private String remark;
    @ApiModelProperty(value = "关联应用集合")
    @Size(max = 500, message = "应用数量不能超过500个")
    @Valid
    private List<AppInfo> appInfos;

    @ApiModelProperty(value = "是否应用所有实例")
    private Boolean applyAllInstances = false;

    @Data
    public static class AppInfo{
        @ApiModelProperty(value = "文件id")
        private Long fileId;
        @ApiModelProperty(value = "应用id")
        private Long appId;
        @ApiModelProperty(value = "应用名称")
        private String appName;
        @ApiModelProperty(value = "包名")
        @NotEmpty(message = "packageName cannot null")
        private String packageName;
        @ApiModelProperty(value = "版本号")
        private Long appVersionNo;
        @ApiModelProperty(value = "版本名称")
        private String appVersionName;
    }
}
