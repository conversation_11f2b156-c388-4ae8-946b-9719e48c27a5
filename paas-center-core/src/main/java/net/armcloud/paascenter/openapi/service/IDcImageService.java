package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.dto.api.UpdateDcImageStatusDTO;
import net.armcloud.paascenter.common.model.dto.message.DcImagePushMessageDTO;
import net.armcloud.paascenter.common.model.entity.paas.DcImage;

import java.util.List;

public interface IDcImageService extends IService<DcImage> {
    /**
     * 添加dcImage,并发送镜像文件推送到容器消息
     * @param addDcImage
     */
    Boolean AddDcImageAndSendContainerMessage(DcImagePushMessageDTO addDcImage);

    /**
     * 更新镜像推送到机房结果
     * @param updateDcImageStatusDTO
     */
    void updateDcImageStatus(UpdateDcImageStatusDTO updateDcImageStatusDTO);

    /**
     * 镜像预装中或预热成功的机房列表
     * @param imageId
     * @return
     */
    List<Long> imageSyncDcs(String imageId);

    /**
     * 退订机房
     * @param imageId
     * @param dcIds
     * @return
     */
    int unsubscribeDcImages(String imageId, List<Long> dcIds);
}
