package net.armcloud.paascenter.openapi.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class Ipv4Util {
    /**
     *  检查IP是否在CIDR范围内
     */
    public static boolean isIpInRange(String ip, String cidr) {
        String[] parts = cidr.split("/");
        String baseIp = parts[0];
        int prefixLength = Integer.parseInt(parts[1]);

        // 计算子网掩码
        int subnetMask = 0xFFFFFFFF << (32 - prefixLength);

        // 计算CIDR范围
        long startIp = ipv4ToLong(baseIp) & subnetMask;
        long endIp = startIp | (~subnetMask & 0xFFFFFFFF);

        // 检查IP是否在范围内
        long targetIp = ipv4ToLong(ip);
        return targetIp >= startIp && targetIp <= endIp;
    }

    /**
     * 将IPv4地址转换为长整型
     */
    public static long ipv4ToLong(String ip) {
        String[] parts = ip.split("\\.");
        return (Long.parseLong(parts[0]) << 24)
                + (Long.parseLong(parts[1]) << 16)
                + (Long.parseLong(parts[2]) << 8)
                + Long.parseLong(parts[3]);
    }

    /**
     * 输入的子网CIDR归属于服务器网络
     * @param subnet
     * @param network
     * @return
     */
    public static boolean isSubnetOf(String subnet, String network) {
        try {
            String[] subnetParts = subnet.split("/");
            String[] networkParts = network.split("/");

            InetAddress subnetAddress = InetAddress.getByName(subnetParts[0]);
            InetAddress networkAddress = InetAddress.getByName(networkParts[0]);

            int subnetPrefixLength = Integer.parseInt(subnetParts[1]);
            int networkPrefixLength = Integer.parseInt(networkParts[1]);

            // Convert IP addresses to binary
            byte[] subnetBytes = subnetAddress.getAddress();
            byte[] networkBytes = networkAddress.getAddress();

            // Check prefix length
            if (subnetPrefixLength < networkPrefixLength) {
                return false;
            }

            // Compare prefix bits
            int fullBytes = networkPrefixLength / 8;
            int remainingBits = networkPrefixLength % 8;

            // Compare full bytes
            for (int i = 0; i < fullBytes; i++) {
                if (subnetBytes[i] != networkBytes[i]) {
                    return false;
                }
            }

            // Compare remaining bits
            if (remainingBits > 0) {
                int mask = 0xFF00 >> remainingBits & 0xFF;
                if ((subnetBytes[fullBytes] & mask) != (networkBytes[fullBytes] & mask)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断板卡网络知否在指定范围内
     * @param cidr
     * @param rangeStart
     * @param rangeEnd
     * @return
     * @throws UnknownHostException
     */
    public static boolean isCIDRInRange(String cidr, String rangeStart, String rangeEnd) {
        try {
            String[] parts = cidr.split("/");
            String cidrAddress = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);

            InetAddress cidrInetAddress = InetAddress.getByName(cidrAddress);
            long cidrStart = ipToLong(cidrInetAddress);
            long cidrEnd = cidrStart + (1L << (32 - prefixLength)) - 1;

            long rangeStartAddr = ipToLong(InetAddress.getByName(rangeStart));
            long rangeEndAddr = ipToLong(InetAddress.getByName(rangeEnd));

            return cidrStart >= rangeStartAddr && cidrEnd <= rangeEndAddr;
        } catch (Exception e) {
            return false;
        }
    }

    private static long ipToLong(InetAddress ip) {
        byte[] octets = ip.getAddress();
        long result = 0;
        for (byte octet : octets) {
            result = (result << 8) | (octet & 0xFF);
        }
        return result;
    }
}
