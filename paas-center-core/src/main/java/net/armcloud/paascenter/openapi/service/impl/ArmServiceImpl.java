package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import net.armcloud.paascenter.common.bmccloud.model.dto.ArmServerInitDTO;
import net.armcloud.paascenter.common.bmccloud.model.vo.ArmServerInitVO;
import net.armcloud.paascenter.common.bmccloud.model.vo.BmcVO;
import net.armcloud.paascenter.common.bmccloud.service.IBmcService;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.dto.ArmServerDTO;
import net.armcloud.paascenter.openapi.model.dto.SaveArmServerDTO;
import net.armcloud.paascenter.openapi.model.vo.*;
import net.armcloud.paascenter.openapi.service.*;
import net.armcloud.paascenter.openapi.utils.CodeUtil;
import net.armcloud.paascenter.openapi.utils.Ipv4Util;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.bmc.*;
import net.armcloud.paascenter.common.model.dto.console.dto.ConsoleUploadFileDTO;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.NumberConst.ONE;
import static net.armcloud.paascenter.common.core.constant.manage.ManageConstants.ONLINE_STATUS_ADDING;
import static net.armcloud.paascenter.common.core.constant.manage.ManageConstants.ONLINE_STATUS_ONLINE;
import static net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant.*;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;

@Service
@Slf4j
public class ArmServiceImpl extends ServiceImpl<ArmServerMapper, ArmServer> implements IArmService {
    @Resource
    private ArmServerMapper armServerMapper;

    @Resource
    private NetDeviceMapper netDeviceMapper;

    @Resource
    private NetPadMapper netPadMapper;

    @Resource
    private GatewayDeviceMapper gatewayDeviceMapper;

    @Resource
    private GatewayPadMapper gatewayPadMapper;

    @Resource
    private EdgeClusterMapper edgeClusterMapper;

    @Resource
    private NetServerMapper netServerMapper;

    @Resource
    private CustomerMapper customerMapper;

    @Resource
    private CustomerArmServerMapper customerArmServerMapper;

    @Resource
    private ArmPadIpMapper armPadIpMapper;

    @Resource
    private DcInfoMapper dcInfoMapper;

    @Resource
    private IBmcService bmcService;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private IBmcTasksService bmcTasksService;
    @Resource
    private ImgFlashingMachineService ImgFlashingMachineService;

    @Resource
    private IDcInfoService dcInfoService;

    @Resource
    private IBmcTasksOtherService bmcTasksOtherService;



    private final ExecutorService executorService = Executors.newFixedThreadPool(20); // 创建一个线程池
    @Override
    public Result<?> armServerStatusCallback(String armSn, Byte status) {
        log.info("armServerStatusCallback armSn:{}, status:{}", armSn, status);
        ArmServer armServer = armServerMapper.selectOne(new QueryWrapper<ArmServer>().eq("arm_sn", armSn).eq("delete_flag", NumberConsts.ZERO));
        if (ObjectUtil.isNotNull(armServer)&& !(status.equals(ONLINE_STATUS_ONLINE) &&armServer.getOnline().equals(ONLINE_STATUS_ADDING))) {
            if (!status.equals(armServer.getOnline())) {
                ArmServer updateArm = new ArmServer();
                updateArm.setId(armServer.getId());
                updateArm.setOnline(status);
                updateArm.setUpdateTime(new Date());
                armServerMapper.updateById(updateArm);
            }
        }
        return Result.ok();
    }


    @Override
    public ArmServer getArmServerDetail(String serverCode) {
        LambdaQueryWrapper<ArmServer> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ArmServer::getArmServerCode, serverCode);
        return this.getOne(wrapper);
    }

    @Override
    public void armServerUpdateStatus(String clusterCode, String ip, Integer status,String version) {
        log.info("armServerUpdateStatus clusterCode:{}, ip:{}, status:{},version:{}", clusterCode, ip, status,version);
        Byte bStatus = status.byteValue();
        ArmServer armServer = armServerMapper.selectOne(new QueryWrapper<ArmServer>().eq("cluster_code", clusterCode).eq("arm_ip", ip).eq("delete_flag", NumberConsts.ZERO));
        if (ObjectUtil.isNotNull(armServer)&& !(bStatus.equals(ONLINE_STATUS_ONLINE) && armServer.getOnline().equals(ONLINE_STATUS_ADDING))) {
            if (!bStatus.equals(armServer.getOnline())) {
                ArmServer updateArm = new ArmServer();
                updateArm.setId(armServer.getId());
                updateArm.setOnline(bStatus);
                updateArm.setUpdateTime(new Date());
                armServerMapper.updateById(updateArm);
            }
        }
        if (StrUtil.isNotBlank(version) && !Objects.equals(version, armServer.getBmcInfo())) {
            log.info("ArmServer updateVerison id:{},verison:{}", armServer.getId(), version);
            ArmServer updateArm = new ArmServer();
            updateArm.setId(armServer.getId());
            updateArm.setBmcInfo(version);
            updateArm.setUpdateTime(new Date());
            armServerMapper.updateById(updateArm);
        }
    }

}
