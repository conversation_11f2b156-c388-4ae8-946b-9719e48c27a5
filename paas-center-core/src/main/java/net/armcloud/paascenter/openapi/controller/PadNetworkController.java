package net.armcloud.paascenter.openapi.controller;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.util.concurrent.RateLimiter;
import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.annotation.CustomerPadAuthValid;
import net.armcloud.paascenter.openapi.model.dto.padnetowrk.*;
import net.armcloud.paascenter.openapi.service.IPadNetworkService;
import net.armcloud.paascenter.openapi.utils.IpUtil;
import net.armcloud.paascenter.common.core.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static net.armcloud.paascenter.common.core.constant.Constants.CUSTOMER_ID_HUMP;

@RestController
@RequestMapping("/openapi/open/network")
@Slf4j
@RefreshScope
public class PadNetworkController {
    private final IPadNetworkService padNetworkService;

    @Value("#{'${interruptLimitWhiteCustomerIds:}'.split(',')}")
    private List<String> interruptLimitWhiteCustomerIds;

    static RateLimiter proxyInterruptRateLimiter = null;

    @PostMapping("/proxy/set")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> setProxy(@Valid @RequestBody JSONObject param) {
        param.put(CUSTOMER_ID_HUMP, CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padNetworkService.setProxyV2(param));
    }

    @PostMapping("/proxy/info")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> proxyInfo(@Valid @RequestBody PadCodesDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padNetworkService.proxyInfo(param));
    }

    @PostMapping("/proxy/interrupt")
    @CustomerPadAuthValid
    public Result<List<GeneratePadTaskVO>> interrupt(@Valid @RequestBody PadNetworkInterruptEnableDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        //todo 限流 暂时使用guava本地限流 后期可能使用sentinel方案或者redis方案

        return Result.ok(padNetworkService.networkInterruptEnable(param));
    }

    @PostMapping("/proxy/detection")
    public Result<ProxyDetectionResponse> proxyDetection(@Valid @RequestBody ProxyDetectionDTO param){
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padNetworkService.proxyDetection(param));
    }

    @GetMapping("/proxy/getMyIp")
    public String getMyIp(HttpServletRequest request){
        String ip = IpUtil.getPublicIp(request);
        Map<String,String> respMap = new HashMap<String,String>();
        respMap.put("ip",ip);
        return JSONObject.toJSONString(respMap);
    }

    public PadNetworkController(IPadNetworkService padNetworkService, @Value("${proxyInterruptPermitsPerSecond:10.0}")Double proxyInterruptPermitsPerSecond) {
        this.padNetworkService = padNetworkService;
        proxyInterruptRateLimiter = RateLimiter.create(proxyInterruptPermitsPerSecond);
    }

}
