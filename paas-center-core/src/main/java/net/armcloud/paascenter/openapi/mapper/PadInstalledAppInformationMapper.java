package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.common.model.entity.paas.PadInstalledAppInformation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface PadInstalledAppInformationMapper {
    PadInstalledAppInformation getByPodCode(@Param("padCode") String padCode);

    /**
     * 批量查询已安装应用信息
     * @param padCodes 实例编码列表
     * @return 已安装应用信息列表
     */
    List<PadInstalledAppInformation> batchGetByPadCodes(@Param("padCodes") List<String> padCodes);
}