package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.callback.manager.CustomerCallbackManager;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.constant.paas.ImageUploadStatus;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.UploadImageDTO;
import net.armcloud.paascenter.common.model.dto.message.DcImagePushMessageDTO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.common.model.vo.api.UploadImageErrorVO;
import net.armcloud.paascenter.common.model.vo.api.UploadImageVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.feign.DockerPushClient;
import net.armcloud.paascenter.feign.dto.DownloadAndPushDTO;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.CustomerUploadImageMapper;
import net.armcloud.paascenter.openapi.model.dto.DownloadAndPushResultDTO;
import net.armcloud.paascenter.openapi.model.dto.QueryImageDTO;
import net.armcloud.paascenter.openapi.model.dto.SelectImageInfoDTO;
import net.armcloud.paascenter.openapi.model.dto.UploadImageFromUrlDTO;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.model.vo.QueryImageVO;
import net.armcloud.paascenter.openapi.model.vo.SelectImageInfoVO;
import net.armcloud.paascenter.openapi.model.vo.UploadImageFromUrlVO;
import net.armcloud.paascenter.openapi.service.ICustomerUploadImageService;
import net.armcloud.paascenter.openapi.service.IDCService;
import net.armcloud.paascenter.openapi.service.IDcImageService;
import net.armcloud.paascenter.openapi.utils.IdGenerateUtils;
import net.armcloud.paascenter.task.service.impl.ImageTaskServerImpl;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ONE;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants.IMAGE_DOWNLOAD_AND_PUSH;
import static net.armcloud.paascenter.common.core.constant.paas.ImageType.CONTAINER;
import static net.armcloud.paascenter.common.core.constant.paas.ImageUploadStatus.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.DOWNLOAD_PUSH_IMAGE;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.OPERATION_TOO_FREQUENT;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PERMISSION_DENIED_EXCEPTION;
import static net.armcloud.paascenter.openapi.constants.LockConstants.UPLOAD_IMAGE_TASK;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;
import static net.armcloud.paascenter.openapi.utils.IdGenerateUtils.generateImageUploadUniqueId;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.springframework.util.ObjectUtils.isEmpty;

@Slf4j
@Service
public class CustomerUploadImageServiceImpl extends ServiceImpl<CustomerUploadImageMapper, CustomerUploadImage> implements ICustomerUploadImageService {

    private final String ARM_CLOUD = "armcloud";

    @Resource
    private CustomerMapper customerMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private IDCService dcService;
    @Resource
    private IDcImageService dcImageService;
    @Resource
    private ImageTaskServerImpl imageTaskServer;
    @Resource
    private DockerPushClient dockerPushClient;
    @Resource
    private CustomerCallbackManager customerCallbackManager;
    @Value("${docker.callbackUrl}")
    private String callbackUrl;


    public Integer splitAndroidImageVersion(String romVersion) {
        if (StrUtil.isNotBlank(romVersion) && romVersion.startsWith("android")) {
            String number = romVersion.replaceAll("[^0-9]", "");
            if (StrUtil.isNotBlank(number)) {
                return Integer.parseInt(number);
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UploadImageVO uploadImageContainer(UploadImageDTO param) {
        log.info("镜像上传入参 {}", JSONUtil.toJsonStr(param));
        List<Long> customerIds = param.getCustomerIds();
        Long customerIdFront = param.getCustomerId();
        String md5 = param.getMd5();
        String originalUrl = param.getOriginalUrl();
        String customerKey = isEmpty(param.getCustomerId()) ? "admin" : String.valueOf(param.getCustomerId());
        RLock rLock = redissonDistributedLock.tryLock(UPLOAD_IMAGE_TASK + customerKey, 1, 60);
        if (Objects.isNull(rLock)) {
            throw new BasicException(PLEASE_NOT_SUBMIT_FREQUENTLY);
        }
        try {
            Long customerId = SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource()) ? null : param.getCustomerId();
            String customerAccount = initializeCustomerAccount(customerId);
            // 默认推送到所有机房
            List<DcInfo> dcInfos = dcService.list();
            if (CollUtil.isEmpty(dcInfos)) {
                throw new BasicException(PERMISSION_DENIED_EXCEPTION);
            }
            List<Long> dcIds = dcInfos.stream().map(DcInfo::getId).distinct().collect(Collectors.toList());
            String createBy = isEmpty(param.getCreateBy()) ? String.valueOf(customerId) : param.getCreateBy();

            List<UploadImageErrorVO> errorList = new ArrayList<>();
            for (UploadImageDTO.ImageInfo imageInfo : param.getImageFiles()) {
                if(customerIdFront!=null){
                    String imageId = customerAccount + ":" + imageInfo.getImageTag();
                    String imageUploadUniqueId = generateImageUploadUniqueId();
                    CustomerUploadImage customerUploadImage = buildCustomerUploadImage(param, customerIdFront, imageInfo, imageId,
                            createBy, imageUploadUniqueId, md5, originalUrl);
                    try {
                        if (SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource())) {
                            long count = this.count(new QueryWrapper<CustomerUploadImage>().lambda().isNull(CustomerUploadImage::getCustomerId).eq(CustomerUploadImage::getImageUrl, imageInfo.getImageFileUrl()));
                            if (count > ZERO) {
                                throw new BasicException(IMAGE_EXIST);
                            }
                        }
                        this.save(customerUploadImage);
                    } catch (Exception e) {
                        log.error("镜像上传失败：", e);
                        UploadImageErrorVO uploadImageErrorVO = handleUploadImageException(param, customerId, dcIds, createBy, imageInfo, imageId);
                        if (uploadImageErrorVO != null) {
                            errorList.add(uploadImageErrorVO);
                        }
                        continue;
                    }
                    // 新版本是直接传aliyun的，所以不需要另外去预热处理
                    if (customerUploadImage.getStatus().equals(ImageUploadStatus.SUCCESS.getStatus())) {
                        continue;
                    }
                    UploadImageErrorVO uploadImageErrorVO = handleSuccessfulUpload(param, dcIds, customerId, createBy, imageInfo, imageUploadUniqueId, customerUploadImage);
                    if (Objects.nonNull(uploadImageErrorVO)) {
                        errorList.add(uploadImageErrorVO);
                    }
                }else{
                    String imageId = customerAccount + ":" + imageInfo.getImageTag();
                    String imageUploadUniqueId = generateImageUploadUniqueId();
                    CustomerUploadImage customerUploadImage = buildCustomerUploadImage(param, null, imageInfo, imageId, createBy,
                            imageUploadUniqueId, md5 , originalUrl);
                    try {
                        if (SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource())) {
                            long count = this.count(new QueryWrapper<CustomerUploadImage>().lambda().isNull(CustomerUploadImage::getCustomerId).eq(CustomerUploadImage::getImageUrl, imageInfo.getImageFileUrl()));
                            if (count > ZERO) {
                                throw new BasicException(IMAGE_EXIST);
                            }
                        }
                        this.save(customerUploadImage);
                    } catch (Exception e) {
                        log.error("镜像上传失败：", e);
                        UploadImageErrorVO uploadImageErrorVO = handleUploadImageException(param, customerId, dcIds, createBy, imageInfo, imageId);
                        if (uploadImageErrorVO != null) {
                            errorList.add(uploadImageErrorVO);
                        }
                        continue;
                    }
                    // 新版本是直接传aliyun的，所以不需要另外去预热处理
                    if (customerUploadImage.getStatus().equals(ImageUploadStatus.SUCCESS.getStatus())) {
                        continue;
                    }
                    UploadImageErrorVO uploadImageErrorVO = handleSuccessfulUpload(param, dcIds, customerId, createBy, imageInfo, imageUploadUniqueId, customerUploadImage);
                    if (Objects.nonNull(uploadImageErrorVO)) {
                        errorList.add(uploadImageErrorVO);
                    }
                }
            }
            UploadImageVO data = new UploadImageVO();
            data.setErrorList(errorList);

            return data;

        } finally {
            redissonDistributedLock.unlock(rLock);
        }
    }

    private UploadImageErrorVO handleSuccessfulUpload(UploadImageDTO param, List<Long> dcIds, Long customerId, String createBy, UploadImageDTO.ImageInfo imageInfo, String imageUploadUniqueId, CustomerUploadImage customerUploadImage) {
        LambdaQueryWrapper<CustomerUploadImage> tagWrapper = new QueryWrapper<CustomerUploadImage>().lambda();
        if (SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource())) {
            tagWrapper.isNull(CustomerUploadImage::getCustomerId);
        } else {
            tagWrapper.eq(CustomerUploadImage::getCustomerId, customerId);
        }
        tagWrapper.eq(CustomerUploadImage::getImageTag, imageInfo.getImageTag());
        long count = this.count(tagWrapper);
        if (count > ONE) {
            this.removeById(customerUploadImage.getId());
            return createErrorVO(imageInfo.getImageTag(), "该镜像版本已存在");
        }
        //发送镜像文件推送到容器消息，并记录机房和镜像关联记录
        DcImagePushMessageDTO addDcImage = getDcImagePushMessageDTO(dcIds, createBy, imageInfo.getImageTag(), imageInfo.getImageFileUrl(), imageUploadUniqueId);
        Boolean pushResult;
        try {
            pushResult = dcImageService.AddDcImageAndSendContainerMessage(addDcImage);
        } catch (Exception e) {
            pushResult = false;
            log.error("AddDcImageAndSendContainerMessage imageId:{},error:{}", addDcImage.getImageId(), e.getMessage());
        }
        if (!pushResult) {
            this.removeById(customerUploadImage.getId());
            return createErrorVO(imageInfo.getImageTag(), "创建镜像推送任务失败");
        }
        return null;
    }

    private UploadImageErrorVO handleUploadImageException(UploadImageDTO param, Long customerId, List<Long> dcIds, String createBy, UploadImageDTO.ImageInfo imageInfo, String imageId) {
        LambdaQueryWrapper<CustomerUploadImage> queryWrapper = new QueryWrapper<CustomerUploadImage>().lambda();
        if (SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource())) {
            queryWrapper.isNull(CustomerUploadImage::getCustomerId);
        } else {
            queryWrapper.eq(CustomerUploadImage::getCustomerId, customerId);
        }
        queryWrapper.eq(CustomerUploadImage::getImageUrl, imageInfo.getImageFileUrl()).last("LIMIT 1");
        CustomerUploadImage uploadImage = this.getOne(queryWrapper);
        if (isNotEmpty(uploadImage) && !uploadImage.getStatus().equals(UPLOAD_EXECUTE.getStatus())) {
            dcImageService.unsubscribeDcImages(uploadImage.getUniqueId(), dcIds);

            //镜像同步其他机房
            List<Long> syncDcImages = dcImageService.imageSyncDcs(uploadImage.getUniqueId());
            List<Long> filterDcList = dcIds.stream()
                    .filter(item -> !syncDcImages.contains(item))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(filterDcList)) {
                return createErrorVO(imageInfo.getImageTag(), "镜像文件上传记录已存在");
            }
            //发送镜像文件推送到容器消息，并记录机房和镜像关联记录
            DcImagePushMessageDTO addDcImage = getDcImagePushMessageDTO(filterDcList, createBy, imageInfo.getImageTag(), uploadImage.getImageUrl(), uploadImage.getUniqueId());
            Boolean pushResult;
            try {
                pushResult = dcImageService.AddDcImageAndSendContainerMessage(addDcImage);
            } catch (Exception e2) {
                pushResult = false;
                log.error("AddDcImageAndSendContainerMessage imageId:{},error:{}", addDcImage.getImageId(), e2.getMessage());
            }
            if (pushResult) {
                uploadImage.setStatus(UPLOAD_EXECUTE.getStatus());
                uploadImage.setUpdateBy("同步：" + createBy);
                uploadImage.setUpdateTime(new Date());
                uploadImage.setImageTag(imageInfo.getImageTag());
                uploadImage.setImageName(imageId);
                uploadImage.setReleaseType(param.getReleaseType());
                uploadImage.setTestCaseFilePath(param.getTestCaseFilePath());
                uploadImage.setRomVersion(param.getRomVersion());
                uploadImage.setImageDesc(imageInfo.getImageDesc());
                String[] imageTags = imageInfo.getImageTag().split("_");
                String serialNo = imageTags[0] + StrUtil.padPre(imageTags[1], 3, '0');
                uploadImage.setSerialNo(Long.parseLong(serialNo));
                this.updateById(uploadImage);
            } else {
                return createErrorVO(imageInfo.getImageTag(), "创建镜像推送任务失败");
            }
            return null;
        } else {
            return createErrorVO(imageInfo.getImageTag(), "镜像文件已存在，请勿重复上传");
        }
    }

    private UploadImageErrorVO createErrorVO(String imageTag, String errorMessage) {
        UploadImageErrorVO errorDTO = new UploadImageErrorVO();
        errorDTO.setImageTag(imageTag);
        errorDTO.setErrorMessage(errorMessage);
        return errorDTO;
    }

    private CustomerUploadImage buildCustomerUploadImage(UploadImageDTO param, Long customerId, UploadImageDTO.ImageInfo imageInfo,
                                                         String imageId, String createBy, String imageUploadUniqueId,String md5,String originalUrl) {
        CustomerUploadImage customerUploadImage = new CustomerUploadImage();

        customerUploadImage.setCustomerId(customerId);
        customerUploadImage.setImageTag(imageInfo.getImageTag());
        customerUploadImage.setImageName(imageId);
        customerUploadImage.setImageUrl(imageInfo.getImageFileUrl());
        customerUploadImage.setServerType(param.getServerType());
        customerUploadImage.setRomVersion(param.getRomVersion());
        if (imageInfo.getImageFileUrl().startsWith("armcloud/")) {
            customerUploadImage.setStatus(ImageUploadStatus.SUCCESS.getStatus());
            customerUploadImage.setUniqueId(imageInfo.getImageFileUrl().substring(9));
        } else {
            customerUploadImage.setStatus(UPLOAD_EXECUTE.getStatus());
            customerUploadImage.setUniqueId(imageUploadUniqueId);
        }
        customerUploadImage.setCreateBy(createBy);
        customerUploadImage.setImageDesc(imageInfo.getImageDesc());
        customerUploadImage.setImageSize(imageInfo.getImageSize());
        customerUploadImage.setReleaseType(param.getReleaseType());
        customerUploadImage.setType(CONTAINER.getType());
        customerUploadImage.setTestCaseFilePath(param.getTestCaseFilePath());
        String[] imageTags = imageInfo.getImageTag().split("_");
        String serialNo = imageTags[0] + StrUtil.padPre(imageTags[1], 3, '0');
        customerUploadImage.setSerialNo(Long.parseLong(serialNo));
        customerUploadImage.setAndroidImageVersion(this.splitAndroidImageVersion(customerUploadImage.getRomVersion()));
        customerUploadImage.setMd5(md5);
        customerUploadImage.setOriginalUrl(originalUrl);
        return customerUploadImage;
    }

    private String initializeCustomerAccount(Long customerId) {
        if (Objects.isNull(customerId)) return ARM_CLOUD;

        CustomerInfoVo customer = customerMapper.getCustomerInfoById(customerId);
        if (Objects.isNull(customer)) {
            throw new BasicException(USER_NOT_EXIST);
        }
        return customer.getCustomerAccount();
    }

    private DcImagePushMessageDTO getDcImagePushMessageDTO(List<Long> dcIds, String createBy, String imageTag, String imageFileUrl, String imageUploadUniqueId) {
        DcImagePushMessageDTO addDcImage = new DcImagePushMessageDTO();
        addDcImage.setDcIds(dcIds);
        addDcImage.setImageId(imageUploadUniqueId);
        addDcImage.setImageTag(imageTag);
        addDcImage.setImageFileUrl(imageFileUrl);
        addDcImage.setOprBy(createBy);
        return addDcImage;
    }

    @Override
    public IPage<QueryImageVO> queryImageList(QueryImageDTO param) {
        IPage<QueryImageVO> page = new Page<>(param.getPage(), param.getRows());
        return customerMapper.queryImageList(page,param);
    }

    @Override
    public UploadImageFromUrlVO uploadImageFromUrl(UploadImageFromUrlDTO param) {
        if (StrUtil.isBlank(param.getImageUrl())) {
            throw new BasicException(NETWORK_INSTANCE_UPDATE_PARAM_NOT_EMPTY);
        }
        String customerKey = isEmpty(param.getCustomerId()) ? "admin" : String.valueOf(param.getCustomerId());
        RLock rLock = redissonDistributedLock.tryLock(UPLOAD_IMAGE_TASK + customerKey, 1, 60);
        if (Objects.isNull(rLock)) {
            throw new BasicException(PLEASE_NOT_SUBMIT_FREQUENTLY);
        }
        try {
            CustomerUploadImage customerUploadImage = buildCustomerUploadImage(param);
            this.save(customerUploadImage);
            return new UploadImageFromUrlVO(customerUploadImage.getUniqueId());
        } catch (Exception e) {
            log.error("uploadImageFromUrl", e);
        } finally {
            redissonDistributedLock.unlock(rLock);
        }
        return null;
    }

    @Override
    public void sendDataToDockerDownloadAndPush(){
        log.info("sendDataToDockerDownloadAndPush");
        LambdaQueryWrapper<CustomerUploadImage> lam = new LambdaQueryWrapper<>();
        lam.eq(CustomerUploadImage::getDeleteFlag,ZERO);
        lam.eq(CustomerUploadImage::getStatus,INITIALIZE.getStatus());
        lam.orderByAsc(CustomerUploadImage::getCreateTime);
        lam.last("limit 1");

        CustomerUploadImage customerUploadImage = this.getOne(lam);

        if(customerUploadImage != null){
            DownloadAndPushDTO dto = new DownloadAndPushDTO();
            dto.setImageDownloadUrl(customerUploadImage.getDownloadUrl());
            dto.setImageDockerAddress(customerUploadImage.getImageUrl());
            dto.setCallBackUrl(callbackUrl);
            dto.setExpand(customerUploadImage.getId() + "");
            log.info("downloadAndPush start");
            try {
                Result<?> result = dockerPushClient.downloadAndPush(dto);
                log.info("downloadAndPush end result:{}", JSONObject.toJSONString(result));
                LambdaUpdateWrapper<CustomerUploadImage> updateWrapper = new LambdaUpdateWrapper<>();

                if (result.getCode() == Constants.SUCCESS) {
                    updateWrapper.set(CustomerUploadImage::getStatus, DOWNLOAD_AND_PUSH.getStatus());
                    updateWrapper.eq(CustomerUploadImage::getStatus, INITIALIZE.getStatus());
                }else if(result.getCode() == OPERATION_TOO_FREQUENT.getStatus()){
                    return;
                }else{
                    updateWrapper.set(CustomerUploadImage::getStatus, FAIL.getStatus());
                    updateWrapper.set(CustomerUploadImage::getFailMsg, result.getMsg());
                }
                updateWrapper.eq(CustomerUploadImage::getId,customerUploadImage.getId());
                this.update(updateWrapper);
            }catch (Exception e){
                log.error("sendDataToDockerDownloadAndPush", e);
            }
        }
    }

    @Override
    public SelectImageInfoVO selectImageInfo(SelectImageInfoDTO param) {
        if (redisService.isAdmin(param.getCustomerId())) {
            param.setCustomerId(null);
        }
        return this.baseMapper.getCustomerUploadImageInfoByUniqueId(param.getImageId(),param.getCustomerId());
    }

    @Override
    public void callBackResult(DownloadAndPushResultDTO dto) {
        log.info("callBackResult dto:{}", dto);
        String expand = dto.getExpand();
        CustomerUploadImage customerUploadImage = this.getById(Long.parseLong(expand));
        if (customerUploadImage != null) {
            LambdaUpdateWrapper<CustomerUploadImage> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CustomerUploadImage::getId, customerUploadImage.getId());
            if (dto.getSuccess()) {
                updateWrapper.set(CustomerUploadImage::getStatus, ImageUploadStatus.SUCCESS.getStatus());
                updateWrapper.set(CustomerUploadImage::getImageSize, dto.getSize());
            } else {
                updateWrapper.set(CustomerUploadImage::getStatus, ImageUploadStatus.FAIL.getStatus());
                updateWrapper.set(CustomerUploadImage::getFailMsg, dto.getFailMsg());
            }
            this.update(updateWrapper);

            JSONObject json = new JSONObject();
            json.put("taskBusinessType", DOWNLOAD_PUSH_IMAGE.getType());
            json.put("taskStatus", dto.getSuccess() ? 3 : -1);
            json.put("imageId", customerUploadImage.getUniqueId());
            if (!dto.getSuccess()) {
                json.put("failMsg", dto.getFailMsg());
            }
            customerCallbackManager.callback(customerUploadImage.getCustomerId(), IMAGE_DOWNLOAD_AND_PUSH, () -> json);
        }


    }

    private CustomerUploadImage buildCustomerUploadImage(UploadImageFromUrlDTO param) {
        String imageFileUrl =  "armcloud/" + IdGenerateUtils.generateImageUploadUniqueIdPro();
        String imageTag = generateImageTag(param.getCustomerId());
        String customerAccount = initializeCustomerAccount(param.getCustomerId());
        String imageId = customerAccount + ":" + imageTag;

        CustomerUploadImage customerUploadImage = new CustomerUploadImage();
        customerUploadImage.setCustomerId(param.getCustomerId());
        customerUploadImage.setImageTag(imageTag);
        customerUploadImage.setImageName(imageId);
        customerUploadImage.setImageUrl(imageFileUrl);
        customerUploadImage.setServerType(param.getServerType());
        customerUploadImage.setRomVersion(param.getRomVersion());
        customerUploadImage.setStatus(INITIALIZE.getStatus());
        customerUploadImage.setUniqueId(customerUploadImage.getImageUrl().substring(9));
        customerUploadImage.setCreateBy(SourceTargetEnum.PAAS.getCode());
        customerUploadImage.setImageDesc(param.getImageDesc());
        customerUploadImage.setReleaseType(param.getReleaseType());
        customerUploadImage.setType(CONTAINER.getType());
        customerUploadImage.setTestCaseFilePath(param.getTestCaseFilePath());
        String[] imageTags = customerUploadImage.getImageTag().split("_");
        String serialNo = imageTags[0] + StrUtil.padPre(imageTags[1], 3, '0');
        customerUploadImage.setSerialNo(Long.parseLong(serialNo));
        customerUploadImage.setAndroidImageVersion(this.splitAndroidImageVersion(customerUploadImage.getRomVersion()));
        customerUploadImage.setDownloadUrl(param.getImageUrl());

        return customerUploadImage;
    }


    /**
     * 生成镜像版本号
     * 格式 yyyyMMdd_SerialNo ; SerialNo为序号 由redis保证当天唯一
     * 每个客户维护一个 公共镜像的customerId定为0
     * @return
     */
    private String generateImageTag(Long customerId){
        customerId = customerId == null ? 0L : customerId;
        String imageTag = null;
        String nowDate = DateUtil.format(new Date(),"yyyyMMdd");
        String cacheKey = RedisKeyPrefix.CUSTOMER_IMAGE_TAG_SERIAL_NO + nowDate + ":" + customerId;
        Integer no = redisService.increment(cacheKey);
        if(no <= 1){
            redisService.expire(cacheKey,1, TimeUnit.DAYS);
        }
        imageTag = nowDate + "_" + no;
        return imageTag;
    }
}
