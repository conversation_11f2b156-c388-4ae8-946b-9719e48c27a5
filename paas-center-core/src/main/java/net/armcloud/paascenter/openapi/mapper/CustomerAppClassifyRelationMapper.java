package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyDetailCheckVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户黑白名单关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Mapper
public interface CustomerAppClassifyRelationMapper extends BaseMapper<CustomerAppClassifyRelation> {

    int batchInsert(List<CustomerAppClassifyRelation> list);

    List<AppClassifyDetailCheckVO> selectAppList(@Param("customerId") Long customerId, @Param("appClassifyIds")List<Long> appClassifyIds);
}
