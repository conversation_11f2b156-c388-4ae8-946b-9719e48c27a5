package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.openapi.model.vo.DcInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DcInfoMapper extends BaseMapper<DcInfo> {
    /**
     * 机房信息
     *
     * @param dcId dcId
     * @return DcInfoVO
     */
    DcInfoVO selectDcInfoByCode(@Param("dcId") String dcId);

    DcInfo getByPadCode(String padCode);

//    List<DcInfo> listByCustomerId(@Param("customerId") long customerId);

    DcInfoVO getVoByPadCode(@Param("padCode") String padCode);

    DcInfo getById(@Param("dcId") long dcId);

    Long getIdByDcCode(@Param("dcCode") String dcCode);

    List<DcInfo> list();

}
