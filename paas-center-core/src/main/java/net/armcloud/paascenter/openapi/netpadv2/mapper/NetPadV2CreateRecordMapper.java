package net.armcloud.paascenter.openapi.netpadv2.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadV2CreateRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网存实例V2创建记录Mapper
 *
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Mapper
public interface NetPadV2CreateRecordMapper extends BaseMapper<NetPadV2CreateRecord> {

    /**
     * 批量插入创建记录
     */
    int batchInsert(@Param("records") List<NetPadV2CreateRecord> records);

    int countByPadCodes(@Param("padCodes") List<String> padCodes);

    List<String> getByPadCodeList(@Param("padCodes") List<String> padCodes);
}
