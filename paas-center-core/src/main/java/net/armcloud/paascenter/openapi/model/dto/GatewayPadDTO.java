package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * getaway_pad
 */
@Data
public class GatewayPadDTO extends PageDTO {
    @ApiModelProperty(value = "主键")
    private Long id;
    @NotNull(message = "netmask不能为空")
    @ApiModelProperty(value = "子网掩码")
    @Pattern(
            regexp = "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12]?[0-9])$",
            message = "IP地址格式不正确"
    )
    private String netmask;
    @NotNull(message = "网关不能为空")
    @ApiModelProperty(value = "网关")
    @Pattern(
            regexp = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$",
            message = "IP地址格式不正确"
    )
    private String gateway;
    @NotNull(message = "ip范围不能为空")
    @ApiModelProperty(value = "ip范围")
    @Pattern(
            regexp = "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12]?[0-9])$",
            message = "IP地址格式不正确"
    )
    private String ipRange;

    @ApiModelProperty(value = "是否启用（1-启用 0--禁用")
    private Byte status = 1;
}