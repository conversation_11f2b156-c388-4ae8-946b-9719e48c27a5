package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import net.armcloud.paascenter.common.model.entity.paas.PadPropertiesSub;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class UpdatePadPropertiesDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于1个")
    private List<String> padCodes;

    /**
     * Modem-持久化-属性列表
     */
    // todo 可优化为转为mao传输，减少内容
    private List<PadPropertiesSub> modemPersistPropertiesList;

    /**
     * Modem-非持久化-属性列表
     */
    private List<PadPropertiesSub> modemPropertiesList;


    /**
     * 系统-持久化-属性列表
     */
    private List<PadPropertiesSub> systemPersistPropertiesList;

    /**
     * 系统-非持久化-属性列表
     */
    private List<PadPropertiesSub> systemPropertiesList;


    /**
     * setting-属性列表
     */
    private List<PadPropertiesSub> settingPropertiesList;

    /**
     * oaid-属性列表
     */
    private List<PadPropertiesSub> oaidPropertiesList;

}
