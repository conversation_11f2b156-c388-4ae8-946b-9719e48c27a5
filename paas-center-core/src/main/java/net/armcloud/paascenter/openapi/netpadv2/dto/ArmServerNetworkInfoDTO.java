package net.armcloud.paascenter.openapi.netpadv2.dto;

import lombok.Data;
import lombok.Setter;

import java.io.Serializable;

/**
 * 服务器网络信息DTO
 */
@Data
public class ArmServerNetworkInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    // 服务器编码
    private String armServerCode;
    // 服务器ID
    private Long armServerId;
    // macVlan
    private String macVlan;
    // 子网掩码
    private String subnet;
    // IP范围
    private String ipRange;
    // 网关
    private String gateway;

}
