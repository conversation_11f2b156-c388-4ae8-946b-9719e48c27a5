package net.armcloud.paascenter.openapi.constants;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 读取配置文件中的机型配置相关信息
 */
@Component
@Slf4j
public class DeviceConfigConstants implements CommandLineRunner {


    /**
     * key
     */
    public static final HashMap<String, Map<String,Object>> LANGUAGE_MAP = Maps.newHashMap();
    public static final HashMap<String, Map<String,Object>> TIME_ZONE_MAP = Maps.newHashMap();
    @javax.annotation.Resource
    private ResourceLoader resourceLoader;



    public void loadClassConfig() throws IOException {
        // 使用 ResourceLoader 加载 classpath 下的文件
        Resource languageResource = resourceLoader.getResource("classpath:config/language.json");
        Resource timeResource = resourceLoader.getResource("classpath:config/timezones.json");
        // 读取文件内容
        loadDeviceConfig(languageResource,LANGUAGE_MAP,"country");
        loadDeviceConfig(timeResource,TIME_ZONE_MAP,"City");

    }

    private void loadDeviceConfig(Resource resource,Map<String, Map<String,Object>> resultMap,String key) throws IOException {
        try (InputStream inputStream = resource.getInputStream(); BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            String jsonStr = content.toString();
            if (StringUtils.isNotEmpty(jsonStr)) {
                List<Map<String, Object>> list = JSON.parseObject(jsonStr, new TypeReference<List<Map<String, Object>>>() {
                });
                list.forEach(map -> resultMap.put(map.get(key).toString(), map));
            }
        }catch (Exception e) {
            log.error("loadDeviceConfig", e);
        }
    };

    @Override
    public void run(String... args) throws Exception {
        loadClassConfig();
    }
}
