package net.armcloud.paascenter.openapi.init;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.utils.ServerIdUtil;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Set;

/**
 * 清除为每个服务生成的padcode第一位随机数
 */
@Component
@Slf4j
public class GeneratePadCodeInit implements CommandLineRunner {

    @Resource
    private RedisService redisService;

    @Override
    public void run(String... args) throws Exception {
        //初始化服务的唯一标识
        ServerIdUtil.initServerUniqueNo();
        //清除GeneratePadCode相关的缓存
        String nowDate = DateUtil.format(new Date(),"yyyyMMdd");
        String redisCacheKey = RedisKeyPrefix.NEW_PAD_CODE_RAMDOM_FIRST + nowDate + ":";
        //这里数据量很少 数量为最多为 api服务器数量*2 目前最多6个
        Set<String> padTaskQueueKeys = redisService.scanKeys(redisCacheKey + "*", 100);
        if(CollUtil.isNotEmpty(padTaskQueueKeys)){
            redisService.deleteObject(padTaskQueueKeys);
        }
    }
}
