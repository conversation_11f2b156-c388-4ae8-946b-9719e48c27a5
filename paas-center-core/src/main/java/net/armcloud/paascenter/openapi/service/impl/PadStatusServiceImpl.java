package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.client.internal.vo.PadInfoVO;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.model.dto.api.UpdatePadOnlineDTO;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.vo.api.CallbackUrlVO;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadStatusMapper;
import net.armcloud.paascenter.openapi.rocketmq.MqTopicConfig;
import net.armcloud.paascenter.openapi.service.ICallbackInformationService;
import net.armcloud.paascenter.openapi.service.ICustomerAccessService;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.openapi.service.IPadStatusService;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import net.armcloud.paascenter.common.model.mq.callback.PadStatusTaskMessageMQ;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PadStatusServiceImpl extends ServiceImpl<PadStatusMapper, PadStatus> implements IPadStatusService {
    @Autowired
    private MqTopicConfig mqTopicConfig;
    @Autowired
    private DefaultRocketMqProducerWrapper mqProducerService;
    @Resource
    private ICustomerAccessService customerAccessService;
    @Resource
    private ICallbackInformationService callbackInformationService;
    @Resource
    private PadStatusMapper padStatusMapper;
    @Resource
    private PadMapper padMapper;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    @Lazy
    private IDeviceService deviceService;

    @Override
    public Boolean updatePadStatusService(List<String> padCodes, Integer padStatus) {
        PadStatus updateStatus = new PadStatus();
        updateStatus.setPadStatus(padStatus);
        updateStatus.setUpdateTime(new Date());
        return padStatusMapper.update(updateStatus, new QueryWrapper<PadStatus>().in("Pad_code", padCodes)) > NumberConsts.ZERO;
    }

    /**
     * 发送云机实例状态变更回调消息(状态回调）
     *
     * @param customerId
     * @param padStatus
     * @param padCodes
     */
    public void sendPadStatusService(Long customerId, Integer padStatus, List<String> padCodes) {
        if (padCodes.isEmpty() || customerId == null || padStatus == null) {
            return;
        }
        //用户是否订阅了实例状态回调
        CallbackUrlVO callbackUrlVO = callbackInformationService.getCallbackUrlService(customerId, CallbackTypeConstants.PAD_STATUS_CALLBACK_TYPE);
        if (Objects.isNull(callbackUrlVO)) {
            return;
        }

        //发送实例状态变更回调通知消息
        CustomerAccess customerAccess = customerAccessService.getAccessByCustomerId(customerId);
        if (Objects.isNull(customerAccess)) {
            return;
        }

        for (String padCode : padCodes) {
            PadStatusTaskMessageMQ padStatusChangeMessageMQ = new PadStatusTaskMessageMQ();
            padStatusChangeMessageMQ.setPadStatus(padStatus);
            padStatusChangeMessageMQ.setAk(customerAccess.getAccessKeyId());
            padStatusChangeMessageMQ.setSk(customerAccess.getSecretAccessKey());
            padStatusChangeMessageMQ.setHost(callbackUrlVO.getHost());
            padStatusChangeMessageMQ.setUrl(callbackUrlVO.getCallbackUrl());
            padStatusChangeMessageMQ.setIsConnectStatusCall(0);

            LambdaQueryWrapper<Pad> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.select(Pad::getOnline);
            queryWrapper.eq(Pad::getPadCode, padCode);
            List<Pad> pads = padMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(pads)) {
                Pad pad = pads.get(0);
                if(padStatus.equals(PadStatusConstant.RESTARTING)){
                    padStatusChangeMessageMQ.setPadConnectStatus(0);
                }else{
                    padStatusChangeMessageMQ.setPadConnectStatus(pad.getOnline());
                }
            }
            padStatusChangeMessageMQ.setPadCode(padCode);
            String jsonString = JSON.toJSONString(padStatusChangeMessageMQ);
//            log.info("sendPadStatusService------------> jsonString:{}", jsonString);
            log.info("实例连接状态回调消息 {}", JSONUtil.toJsonStr(padStatusChangeMessageMQ));
            String msgId = mqProducerService.producerNormalMessage(mqTopicConfig.getVcpPodStatus(), null, jsonString);
//            log.info("sendPadStatusService-------------> msgId:{}", msgId);
        }
    }


    /**
     * 发送云机实例状态变更回调消息(状态回调及实例连接状态）
     *
     * 仅在实例的状态发生变化时调用,如:由在线改为离线,由离线改为在线时调用
     *
     * @param customerId
     * @param padStatus
     * @param padConnectStatus 1:在线,0:离线
     * @param padCodes
     */
    public void sendPadStatusService(Long customerId, Integer padStatus, Integer padConnectStatus, List<String> padCodes) {
        if (padCodes.isEmpty() || customerId == null || padStatus == null) {
            return;
        }
        //用户是否订阅了实例状态回调
        CallbackUrlVO callbackUrlVO = callbackInformationService.getCallbackUrlService(customerId, CallbackTypeConstants.PAD_STATUS_CALLBACK_TYPE);
        if (Objects.isNull(callbackUrlVO)) {
            return;
        }

        //发送实例状态变更回调通知消息
        CustomerAccess customerAccess = customerAccessService.getAccessByCustomerId(customerId);
        if (Objects.isNull(customerAccess)) {
            return;
        }
        PadStatusTaskMessageMQ padStatusChangeMessageMQ = new PadStatusTaskMessageMQ();
        padStatusChangeMessageMQ.setPadStatus(padStatus);
        padStatusChangeMessageMQ.setPadConnectStatus(padConnectStatus);
        padStatusChangeMessageMQ.setIsConnectStatusCall(1);

        padStatusChangeMessageMQ.setAk(customerAccess.getAccessKeyId());
        padStatusChangeMessageMQ.setSk(customerAccess.getSecretAccessKey());
        padStatusChangeMessageMQ.setHost(callbackUrlVO.getHost());
        padStatusChangeMessageMQ.setUrl(callbackUrlVO.getCallbackUrl());

        for (String padCode : padCodes) {
            padStatusChangeMessageMQ.setPadCode(padCode);
            String jsonString = JSON.toJSONString(padStatusChangeMessageMQ);
//            log.info("sendPadStatusService------------> jsonString:{}", jsonString);
            log.info("实例连接状态回调消息变更 {}", JSONUtil.toJsonStr(padStatusChangeMessageMQ));
            String msgId = mqProducerService.producerNormalMessage(mqTopicConfig.getVcpPodStatus(), null, jsonString);
//            log.info("sendPadStatusService-------------> msgId:{}", msgId);
        }
    }

    @Override
    public void updatePadOnline(UpdatePadOnlineDTO param) {
        padMapper.updateOnline(param);
    }

    @Override
    public Boolean updatePadStatusAndSendPadStatusCallback(List<String> padCodes, Integer status, Long customerId, String oprBusiness) {
        log.info("updatePadStatusAndSendPadStatusCallback------------> padCodes:{},status:{},oprBusiness:{}", JSON.toJSONString(padCodes), status, oprBusiness);
        Boolean flag = applicationContext.getBean(IPadStatusService.class).updatePadStatusService(padCodes, status);
        if (flag) {
            if (CollUtil.isNotEmpty(padCodes)) {
                List<PadInfoVO> padInfoList = padMapper.queryPadCustomerId(padCodes);
                if (CollUtil.isNotEmpty(padInfoList)) {
                    padInfoList.stream().collect(Collectors.groupingBy(PadInfoVO::getCustomerId)).forEach((customer, padInfos) -> {
                        List<String> padCodeList = padInfos.stream().map(PadInfoVO::getPadCode).collect(Collectors.toList());
                        this.sendPadStatusService(customer, status, padCodeList);
                    });
                }
            }
        }
        return flag;
    }

    @Override
    public void updatePadStatus(String padCode, Integer status) {
        baseMapper.updatePadStatus(padCode, status);
    }
}
