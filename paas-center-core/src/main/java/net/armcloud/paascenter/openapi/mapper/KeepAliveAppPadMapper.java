package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.KeepAliveAppPad;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 保活应用实例 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Mapper
public interface KeepAliveAppPadMapper extends BaseMapper<KeepAliveAppPad> {

    int batchInsert(List<KeepAliveAppPad> keepAliveAppPad);

}
