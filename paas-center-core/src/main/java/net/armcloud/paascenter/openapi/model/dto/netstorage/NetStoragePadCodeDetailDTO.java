package net.armcloud.paascenter.openapi.model.dto.netstorage;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.DevicePad;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageComputeUnit;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageComputeUnitPad;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;

/**
 * <AUTHOR>
 * @Date 2025/3/25 17:13
 * @Description:实例跟算力网存映射关系类
 */
@Data
public class NetStoragePadCodeDetailDTO {
    private String padCode;

    /**
     * 网存算力信息
     */
    private NetStorageComputeUnit netStorageComputeUnit;

    /**
     * 算力跟实例关系映射
     */
    private NetStorageComputeUnitPad netStorageComputeUnitPad;

    /**
     * 网络存储详情
     */
    private NetStorageResUnit netStorageResUnit;

    /**
     * 实例详情信息
     */
    private PadDetailsVO padDetailsVO;

    /**
     * 实例板卡映射
     */
    private DevicePad devicePad;
    /**
     * 板卡规格(算力规格-实例规格)
     */
    private String deviceLevel;

    /**
     * 集群编号
     */
    private String clusterCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 安卓属性
     */
    private JSONObject androidProp ;

}
