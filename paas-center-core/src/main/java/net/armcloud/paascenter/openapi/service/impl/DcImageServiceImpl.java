package net.armcloud.paascenter.openapi.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.constant.paas.ImageUploadStatus;
import net.armcloud.paascenter.common.model.dto.api.UpdateDcImageStatusDTO;
import net.armcloud.paascenter.common.model.dto.message.DcImagePushMessageDTO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.common.model.entity.paas.DcImage;
import net.armcloud.paascenter.common.model.mq.api.UploadImageTaskMessageMQ;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.openapi.mapper.CustomerUploadImageMapper;
import net.armcloud.paascenter.openapi.mapper.DcImageMapper;
import net.armcloud.paascenter.openapi.rocketmq.MqTopicConfig;
import net.armcloud.paascenter.openapi.rocketmq.config.ContainerPushImageMessageConfig;
import net.armcloud.paascenter.openapi.service.IDcImageService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.paas.DcImageStatus.*;
import static org.springframework.util.ObjectUtils.isEmpty;

@RefreshScope
@Slf4j
@Service
public class DcImageServiceImpl extends ServiceImpl<DcImageMapper, DcImage> implements IDcImageService {

    @Autowired
    private DefaultRocketMqProducerWrapper mqProducerService;
    @Autowired
    private MqTopicConfig mqTopicConfig;
    @Autowired
    private ContainerPushImageMessageConfig containerPushImageMessageConfig;
    @Autowired
    private CustomerUploadImageMapper customerUploadImageMapper;
    @Value("${upload-image.timeout}")
    private Long uploadImageTimeout;

    /**
     * 添加dcImage,并发送镜像文件推送到容器消息
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean AddDcImageAndSendContainerMessage(DcImagePushMessageDTO dto) {
        List<DcImage> dcImageList = new ArrayList<>();
        for (Long dcId : dto.getDcIds()) {
            DcImage dcImage = new DcImage();
            dcImage.setDcId(dcId);
            dcImage.setImageId(dto.getImageId());
            dcImage.setStatus(SYNCHRONIZE_SUCCESS.getStatus());
            dcImage.setCreateBy(dto.getOprBy());
            dcImage.setTimeoutTime(new Date(System.currentTimeMillis() + uploadImageTimeout));
            dcImageList.add(dcImage);
        }
        for (DcImage dcImage : dcImageList) {
            DcImage data = this.getOne(new QueryWrapper<DcImage>().lambda()
                    .eq(DcImage::getDcId, dcImage.getDcId())
                    .eq(DcImage::getImageId, dto.getImageId())
                    .eq(DcImage::getDeleteFlag, false));
            if (isEmpty(data)) {
                this.save(dcImage);
            } else {
                data.setStatus(SYNCHRONIZE_SUCCESS.getStatus());
                data.setUpdateTime(new Date());
                data.setUpdateBy("同步" + dto.getOprBy());
                data.setTimeoutTime(new Date(System.currentTimeMillis() + uploadImageTimeout));
                this.updateById(data);
            }
        }
        UploadImageTaskMessageMQ messageMQ = new UploadImageTaskMessageMQ();
        BeanUtils.copyProperties(dto, messageMQ);
        // 目前未有需求放开此参数设置权限,暂为固定值
        messageMQ.setImageTag("latest");
        String jsonString = JSON.toJSONString(messageMQ);
        log.info("sendPushImageTask------------> jsonString:{}", jsonString);
        //cms消费 主题：vcp_upload_image
        String msgId = mqProducerService.producerNormalMessage(containerPushImageMessageConfig.getTopic(), jsonString);
        log.info("sendPushImageTask-------------> imageId:{},msgId:{}", dto.getImageId(), msgId);
        return true;
    }

    /**
     * 更新镜像推送到机房结果
     *
     * @param updateDcImageStatusDTO
     * @return
     */
    @Override
    public void updateDcImageStatus(UpdateDcImageStatusDTO updateDcImageStatusDTO) {
        DcImage dcImage = this.getOne(new QueryWrapper<DcImage>()
                .lambda()
                .eq(DcImage::getDcId, updateDcImageStatusDTO.getDcId())
                .eq(DcImage::getImageId, updateDcImageStatusDTO.getImageId())
                .eq(DcImage::getDeleteFlag, false));
        if (isEmpty(dcImage)) {
            dcImage = new DcImage();
            dcImage.setDcId(updateDcImageStatusDTO.getDcId());
            dcImage.setImageId(updateDcImageStatusDTO.getImageId());
            dcImage.setStatus(updateDcImageStatusDTO.getResult() ? SYNCHRONIZE_SUCCESS.getStatus() : SYNCHRONIZE_FAIL.getStatus());
            dcImage.setCreateBy("PushImageTaskResult");
            dcImage.setErrorMsg(updateDcImageStatusDTO.getErrorMsg());
            this.save(dcImage);
        } else if (!SYNCHRONIZE_SUCCESS.getStatus().equals(dcImage.getStatus())) {
            DcImage updateDcImage = new DcImage();
            updateDcImage.setId(dcImage.getId());
            updateDcImage.setStatus(updateDcImageStatusDTO.getResult() ? SYNCHRONIZE_SUCCESS.getStatus() : SYNCHRONIZE_FAIL.getStatus());
            updateDcImage.setUpdateBy("PushImageTaskResult");
            updateDcImage.setUpdateTime(new Date());
            updateDcImage.setErrorMsg(updateDcImageStatusDTO.getErrorMsg());
            this.updateById(updateDcImage);
        }
        refreshCustomerUploadImageStatus(updateDcImageStatusDTO.getImageId());
    }

    /**
     * 获取镜像同步机房列表
     *
     * @param imageId
     * @return
     */
    @Override
    public List<Long> imageSyncDcs(String imageId) {
        return this.baseMapper.imageSyncDcs(imageId);
    }

    /**
     * 退订机房
     *
     * @param imageId
     * @param dcIds
     * @return
     */
    @Override
    public int unsubscribeDcImages(String imageId, List<Long> dcIds) {
        return this.baseMapper.unsubscribeDcImages(imageId, dcIds);
    }

    /**
     * 刷新镜像上传总状态
     *
     * @param imageId
     * @return
     */
    private int refreshCustomerUploadImageStatus(String imageId) {
        CustomerUploadImage uploadImage = new CustomerUploadImage();
        long total = 0;
        long successCount = 0;
        long failCount = 0;
        List<DcImage> dcImageList = this.list(new QueryWrapper<DcImage>().lambda()
                .eq(DcImage::getImageId, imageId)
                .eq(DcImage::getDeleteFlag, false));
        total = dcImageList.size();
        failCount = dcImageList.stream().filter(dto -> Objects.equals(dto.getStatus(), SYNCHRONIZE_FAIL.getStatus())).count();
        successCount = dcImageList.stream().filter(dto -> Objects.equals(dto.getStatus(), SYNCHRONIZE_SUCCESS.getStatus())).count();

        if (successCount == total) {
            uploadImage.setStatus(ImageUploadStatus.SUCCESS.getStatus());
        }

        if (failCount > 0) {
            uploadImage.setStatus(ImageUploadStatus.FAIL.getStatus());
        }

        if (Objects.isNull(uploadImage.getStatus())) {
            return 0;
        }
        uploadImage.setUpdateTime(new Date());
        uploadImage.setUpdateBy("PushImageTaskResult");
        return customerUploadImageMapper.update(uploadImage, new QueryWrapper<CustomerUploadImage>().lambda().eq(CustomerUploadImage::getUniqueId, imageId));
    }
}
