package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.openapi.service.IPadPropertiesService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.PadProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/openapi/open/padProperties")
@Api(tags = "实例属性")
public class PadPropertiesController {

    @Resource
    private IPadPropertiesService padPropertiesService;

    @PostMapping("submitPadProperties")
    @ApiOperation(value = "gameService上报实例属性")
    public Result<?> submitPadProperties(@Valid @RequestBody PadProperties param) {
        return Result.ok(padPropertiesService.saveOrUpdateService(param));
    }
}
