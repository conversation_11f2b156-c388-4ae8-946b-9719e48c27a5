package net.armcloud.paascenter.openapi.model.vo;

import net.armcloud.paascenter.common.core.constant.pad.PadConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PadListVO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 云机编号
     */
    private String padCode;

    /**
     * 实例规格
     */
    private String padGrade;

    /**
     * 实例运行状态
     */
    private Integer padStatus;

    /**
     * 分组ID
     */
    private Integer groupId;

    /**
     * 机房id
     */
    private String idc;

    /**
     * 云机ip
     */
    private String deviceIp;

    /**
     * padIp
     */
    private String padIp;

    /**
     * 实例类型
     *
     * {@link PadConstants.Type}
     */
    private String padType;

    /**
     *adb开关状态(1开启 为空为0都是关闭状态)
     */
    private String adbOpenStatus;

    /**
     * 服务器编号
     */
    private String armServerCode;

    /**
     * 板卡编号
     */
    private String deviceCode;

    /**
     * 集群编号
     */
    private String clusterCode;

    /**
     * 是否网存集群 0 本地集群 1网存集群
     */
    private Integer netStorageResFlag;

    /**
     * 已安装应用的ID
     */
    private List<String> apps;

    /**
     * 镜像ID
     */
    private String imageId;

    /**
     * armIp
     */
    private String armIp;
}
