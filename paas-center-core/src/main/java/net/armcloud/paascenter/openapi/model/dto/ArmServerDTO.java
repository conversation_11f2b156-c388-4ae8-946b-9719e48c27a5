package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class ArmServerDTO extends PageDTO  implements Serializable {

    /**
     * 机房ID
     */
    private String idc;

    /**
     * 集群编号
     */
    private String clusterCode;

    /**
     * 服务器编号
     */
    private String armServerCode;

    /**
     * 服务器名称
     */
    private String armServerName;

    /**
     * 服务器SN
     */
    private String armSn;
    /**
     * 服务器IP
     */
    private String armIp;
    /**
     * 在线状态
     */
    private String online;

    /**
     * 客户ID
     */
    private Long customerId;



}
