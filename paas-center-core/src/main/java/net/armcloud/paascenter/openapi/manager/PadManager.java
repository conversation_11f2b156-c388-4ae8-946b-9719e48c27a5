package net.armcloud.paascenter.openapi.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.openapi.service.IPadStatusService;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.mapper.TaskMapper;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.core.constant.paas.DictConstants.Type.GENERATE_PAD_PREVIEW_LIMIT;
import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.PAD_GENERATE_PREVIEW_MARK_PREFIX;

@Component
public class PadManager {
    private final RedisService redisService;
    private final DictManager dictManager;
    private final PadMapper padMapper;
    private final IPadStatusService padStatusService;
    private final PadTaskMapper padTaskMapper;
    private final TaskMapper taskMapper;

    public boolean getGeneratePreviewMark(String padCode) {
        String key = PAD_GENERATE_PREVIEW_MARK_PREFIX + padCode;
        return Boolean.TRUE.equals(redisService.hasKey(key));
    }

    public void generatePreviewMark(List<String> padCodes) {
        Map<String, String> param = new HashMap<>(padCodes.size());
        padCodes.forEach(padCode -> {
            String key = PAD_GENERATE_PREVIEW_MARK_PREFIX + padCode;
            param.put(key, "1");
        });

        long limitSeconds = Long.parseLong(dictManager.getSingleValueByType(GENERATE_PAD_PREVIEW_LIMIT));
        redisService.setBatchCacheObject(param, limitSeconds, TimeUnit.SECONDS);
    }

    public void updatePadStatus(Long padTaskId) {
        PadTask padTask = padTaskMapper.getById(padTaskId);
        if(padTask != null && (TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(padTask.getStatus()) || TaskStatusConstants.EXECUTING.getStatus().equals(padTask.getStatus()))){
            Task task = taskMapper.selectById(padTask.getTaskId());
            TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(task.getType());
            if(taskTypeAndChannelEnum.getPadStatusConstant() != null){
                padStatusService.updatePadStatusAndSendPadStatusCallback(Collections.singletonList(padTask.getPadCode()), taskTypeAndChannelEnum.getPadStatusConstant(), padTask.getCustomerId(), null);
            }
        }
    }

    public PadManager(RedisService redisService, DictManager dictManager,PadMapper padMapper,IPadStatusService padStatusService,
                      PadTaskMapper padTaskMapper,TaskMapper taskMapper) {
        this.redisService = redisService;
        this.dictManager = dictManager;
        this.padMapper = padMapper;
        this.padStatusService = padStatusService;
        this.padTaskMapper = padTaskMapper;
        this.taskMapper = taskMapper;
    }
}
