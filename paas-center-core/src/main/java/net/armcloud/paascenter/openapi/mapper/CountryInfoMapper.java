package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import net.armcloud.paascenter.common.model.entity.paas.CountryInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 国家信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Mapper
public interface CountryInfoMapper extends BaseMapper<CountryInfo> {

   // 根据countryCode获得国家信息
   CountryInfo selectCountryInfoByCountryCode(@Param("countryCode") String countryCode);

}
