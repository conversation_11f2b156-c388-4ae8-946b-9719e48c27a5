package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 保活应用保存对象
 */
@Data
public class SetKeepAliveAppSaveDTO implements Serializable {
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long customerId;
    @ApiModelProperty(value = "实例集合")
    @Size(max = 500, message = "padCodes数量不能超过500个")
    @Valid
    private List<String> padCodes;
    @ApiModelProperty(value = "应用集合")
    @Size(max = 500, message = "应用数量不能超过500个")
    @Valid
    private List<AppInfo> appInfos;
    @ApiModelProperty(value = "是否应用所有实例")
    private Boolean applyAllInstances;

    @Data
    public static class AppInfo{
        @ApiModelProperty(value = "应用需要拉起的服务")
        @NotEmpty(message = "serverName cannot null")
        private String serverName;
    }
}
