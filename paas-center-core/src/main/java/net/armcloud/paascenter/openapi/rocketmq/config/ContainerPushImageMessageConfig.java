package net.armcloud.paascenter.openapi.rocketmq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "mq.container-push-image-message")
public class ContainerPushImageMessageConfig {
    /**
     * 云手机PAAS平台-推送镜像主题
     */
    private String topic;
}
