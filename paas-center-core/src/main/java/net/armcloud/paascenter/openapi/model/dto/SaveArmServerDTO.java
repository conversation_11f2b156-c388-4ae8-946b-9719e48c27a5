package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/11 14:24
 * @Version 1.0
 */
@Data
public class SaveArmServerDTO  extends PageDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "服务器查询")
    private String queryServer;

    @ApiModelProperty(value = "集群名称")
    private String clusterName;

    @ApiModelProperty(value = "SN")
    private String snNumber;

    @ApiModelProperty(value = "ip")
    @NotBlank(message = "IP不能为空")
    @Pattern(
            regexp = "^(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])){3}$",
            message = "IP格式错误"
    )
    private String serverIp;

    @ApiModelProperty(value = "服务器ID")
    private String armServerCode;

    @ApiModelProperty(value = "SOC型号")
    @NotBlank(message = "SOC型号不能为空")
    private String socModelCode;

    @ApiModelProperty(value = "启用状态 0-停用 1-启用")
    private Byte status = 1;

    @ApiModelProperty(value = "在线状态 0-离线 1-在线")
    private Byte onlineStatus = 1;

    @ApiModelProperty(value = "集群code")
    private String clusterCode;

    @ApiModelProperty(value = "板卡子网")
    @NotBlank(message = "板卡子网不能为空")
    private String deviceSubNet;

    @ApiModelProperty(value = "实例子网")
    @NotNull(message = "实例子网不能为空")
    private List<Long> netPadIds;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @NotNull(message = "板卡网关id不能为空")
    @ApiModelProperty(value = "板卡网关id")
    private Long gatewayDeviceId;

    @NotNull(message = "实例网关id不能为空")
    @ApiModelProperty(value = "实例网关id")
    private Long gatewayPadId;

    @ApiModelProperty(value = "客户id")
    private Long customerId;

    @ApiModelProperty(value = "板卡mac vlan")
    private String macVlan;

    @ApiModelProperty(value = "品牌id")
    private Integer brandId;

    @ApiModelProperty(value = "deviceStatus")
    private Integer deviceStatus = 1;

    /**机箱标签*/
    @ApiModelProperty(value = "chassisLabel")
    @NotBlank(message = "机箱标签不能为空")
    private String chassisLabel;

    /**机柜/U位*/
    @ApiModelProperty(value = "chassisCabinetU")
    @NotBlank(message = "机柜/U位不能为空")
    private String chassisCabinetU;
}
