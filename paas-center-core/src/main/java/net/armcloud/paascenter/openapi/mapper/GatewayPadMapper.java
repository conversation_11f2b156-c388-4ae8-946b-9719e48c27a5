package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.openapi.model.dto.GatewayPadDTO;
import net.armcloud.paascenter.openapi.model.vo.GatewayPadVO;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GatewayPadMapper {
    int insert(GatewayPad record);

    GatewayPadVO selectById(Long id);

    int update(GatewayPad record);

    int delete(@Param("status")Byte status,@Param("id") Long id);

    List<GatewayPadVO> selectList(GatewayPadDTO dto);

    int countByNameAndNotDeleted(@Param("gateway") String name);

    int updateGatewayPadStatus(GatewayPadDTO gatewayPadDTO);
}
