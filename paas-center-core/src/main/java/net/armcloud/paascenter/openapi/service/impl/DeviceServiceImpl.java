package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.cms.model.request.*;
import net.armcloud.paascenter.cms.model.response.DeviceDestroyResponse;
import net.armcloud.paascenter.cms.model.response.DeviceRestartResponse;
import net.armcloud.paascenter.cms.model.response.DeviceVirtualizeResponse;
import net.armcloud.paascenter.cms.model.response.SelfUpdateResponse;
import net.armcloud.paascenter.common.bmccloud.service.IBmcService;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.feign.ContainerDeviceFeignClient;
import net.armcloud.paascenter.common.client.internal.vo.*;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.constant.SystemConfigurationConstants;
import net.armcloud.paascenter.common.core.constant.callback.CallbackTypeConstants;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.core.constant.task.ImageUploadStatus;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.AddDeviceTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.AddPadTaskDTO;
import net.armcloud.paascenter.common.model.dto.api.DeviceNetworkDTO;
import net.armcloud.paascenter.common.model.dto.bmc.BmcDeviceNetworkDTO;
import net.armcloud.paascenter.common.model.dto.bmc.BmcDeviceRestartDTO;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.mq.callback.DeviceStatusMessageMQ;
import net.armcloud.paascenter.common.model.vo.api.BmcTaskInfoVO;
import net.armcloud.paascenter.common.model.vo.api.CallbackUrlVO;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import net.armcloud.paascenter.common.model.vo.api.DeviceCustomerVo;
import net.armcloud.paascenter.common.model.vo.job.EdgeClusterVO;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import net.armcloud.paascenter.common.utils.FeignUtils;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.manager.AdiCertificateManager;
import net.armcloud.paascenter.openapi.manager.devicenettraversal.DeviceNetTraversalManage;
import net.armcloud.paascenter.openapi.manager.devicenettraversal.feign.response.DeviceNetTraversalResponse;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.dto.ApplyDeviceConnectDTO;
import net.armcloud.paascenter.openapi.model.dto.BoardImageWarmupDTO;
import net.armcloud.paascenter.openapi.model.vo.ApplyDeviceConnectVO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2ComputeMatchService;
import net.armcloud.paascenter.openapi.rocketmq.MqTopicConfig;
import net.armcloud.paascenter.openapi.service.*;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitService;
import net.armcloud.paascenter.openapi.utils.AndroidDeviceInfoUtils;
import net.armcloud.paascenter.openapi.utils.CIDRUtils;
import net.armcloud.paascenter.openapi.utils.PadCodeRamdomUtil;
import net.armcloud.paascenter.openapi.utils.ServerIdUtil;
import net.armcloud.paascenter.task.config.PullModeConfigHolder;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.mapper.DeviceTaskMapper;
import net.armcloud.paascenter.task.service.ITaskService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URI;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.bean.BeanUtil.isEmpty;
import static cn.hutool.core.bean.BeanUtil.isNotEmpty;
import static net.armcloud.paascenter.common.client.internal.utils.ContainerFeignUtils.builderHost;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.*;
import static net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants.DEVICE_INIT;
import static net.armcloud.paascenter.common.core.constant.device.DeviceStatusConstants.DEVICE_SUCCESS;
import static net.armcloud.paascenter.common.core.constant.device.PadAllocationStatusConstants.*;
import static net.armcloud.paascenter.common.core.constant.paas.PadOperBusinessType.DELETE;
import static net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants.*;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.*;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;
import static net.armcloud.paascenter.openapi.exception.code.DeviceExceptionCode.DEVICE_NOT_FOUND_EXCEPTION;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;

@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {

    @Value("#{'${ip.sub.num.whitelist:}'.split(',')}")
    private List<String> ipSubNumStrings;

    @Value("${android-prop.armcloud-server-addr}")
    public String armcloudServerAddr;

    public static final List<Integer> ipSubNumWhiteList = new ArrayList<>();
    @Autowired
    private NetPadV2ComputeMatchService netPadV2ComputeMatchService;

    @PostConstruct
    private void postConstruct() {
        for (String ipSubNumString : ipSubNumStrings) {
            try {
                Integer parsedNum = Integer.parseInt(ipSubNumString);
                if (parsedNum >= 0) {
                    ipSubNumWhiteList.add(parsedNum);
                }
            } catch (Exception exception) {
                // nothing
            }
        }
    }

    /**
     * Mapper
     */
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private DevicePadMapper devicePadMapper;
    @Resource
    private PadMapper padMapper;
    @Resource
    private DcInfoMapper dcInfoMapper;
    @Resource
    private NetPadMapper netPadMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private IPadStatusService padStatusService;
    @Resource
    private ICallbackInformationService callbackInformationService;
    @Resource
    private ICustomerAccessService customerAccessService;
    @Resource
    private IBmcService bmcService;
    @Resource
    private ArmServerMapper armServerMapper;
    @Autowired
    private MqTopicConfig mqTopicConfig;
    @Autowired
    private DefaultRocketMqProducerWrapper mqProducerService;
    @Autowired
    private ResourceSpecificationMapper resourceSpecificationMapper;
    @Autowired
    private CustomerUploadImageMapper customerUploadImageMapper;
    @Autowired
    private ScreenLayoutMapper screenLayoutMapper;
    @Autowired
    private ContainerDeviceFeignClient containerDeviceFeignClient;
    @Autowired
    private IPadService padService;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private PadOperLogMapper padOperLogMapper;
    @Resource
    private EdgeClusterMapper edgeClusterMapper;
    @Autowired
    private CustomerDeviceMapper customerDeviceMapper;
    @Autowired
    private DeviceNetTraversalManage deviceNetTraversalManage;
    @Autowired
    private CustomerConnectDeviceRecordMapper customerConnectDeviceRecordMapper;
    @Autowired
    private CustomerConfigMapper customerConfigMapper;
    @Autowired
    private AdiCertificateRepositoryMapper adiCertificateRepositoryMapper;
    @Autowired
    private AdiCertificateManager adiCertificateManager;

    private final ExecutorService executor = Executors.newFixedThreadPool(3);

    /**是否走创建实例新规则*/
    @Value("${createPadCodeNew:false}")
    private Boolean createPadCodeNew;
    @Autowired
    private RedissonDistributedLock redissonDistributedLock;

    @Autowired
    private ITaskService taskService;
    @Autowired
    private TaskQueueManager taskQueueManager;
    @Autowired
    private DeviceTaskMapper deviceTaskMapper;
    @Autowired
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;
    @Autowired
    private IDeviceHistoryService deviceHistoryService;
    @Resource
    private EdgeClusterConfigurationMapper edgeClusterConfigurationMapper;
    @Resource
     BoardImageWarmupMapper boardImageWarmupMapper;

    @Override
    public Result<?> powerReset(PowerResetDTO param) {
        log.info(">>>>>>>>>>>>>>>>云机断电重启 param={}", JSON.toJSONString(param));
        long customerId = param.getCustomerId();
        Integer type = param.getType();
        if (CollUtil.isEmpty(param.getDeviceIps())) {
            throw new BasicException(EXECUTE_POWER_RESTART_FAIL_EXCEPTION);
        }
        List<DeviceInfoVo> deviceInfos = new ArrayList<>();
        if (SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource())) {
            deviceInfos = deviceMapper.selectDeviceInfoByDeviceIpDc(param.getDeviceIps(), null, null);
        } else {
            deviceInfos = deviceMapper.selectDeviceInfoByDeviceIpDc(param.getDeviceIps(), null, customerId);
        }
        if (isEmpty(deviceInfos) || deviceInfos.size() != param.getDeviceIps().size()) {
            throw new BasicException(DEVICE_IP_NOT_EXIST);
        }
        List<Integer> padAllocationStatus = Arrays.asList(ALLOCATING.getStatus(), DELETING.getStatus());
        List<String> errorDeviceIps = deviceInfos.stream().filter(a -> padAllocationStatus.contains(a.getPadAllocationStatus())).map(DeviceInfoVo::getDeviceIp).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errorDeviceIps)) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), JSON.toJSONString(errorDeviceIps) + "板卡存在进行中任务");
        }

        //检测是否有已存在的任务
        List<Integer> status = Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus());
        List<Pad> pads = padMapper.selectPadByDeviceCode(deviceInfos, null);
        if (CollUtil.isNotEmpty(pads)) {
            PadTaskAndStatusDTO padTaskAndStatusDTO = new PadTaskAndStatusDTO();
            padTaskAndStatusDTO.setPads(pads);
            padTaskAndStatusDTO.setStatus(status);
            List<PadTask> tasks = taskService.selectTaskByTaskTypeAndTaskStatus(padTaskAndStatusDTO.getPads(), padTaskAndStatusDTO.getStatus());
            if (CollUtil.isNotEmpty(tasks)) {
                throw new BasicException(PROCESSING_FAILED.getStatus(), "板卡已存在任务，请稍后再试");
            }
        }

        String socModel = deviceInfos.get(ZERO).getSocModel();
        if (socModel.startsWith("QAC")) {
            return powerResetCa(param, customerId, type, deviceInfos);
        } else {
            Boolean isPullMode = PullModeConfigHolder.isPullMode(String.valueOf(customerId),null,deviceInfos.get(0).getDeviceCode());
            if (type.equals(TWO)) {
                if(isPullMode){
                    return pullModeBmcPowerReset(param, deviceInfos);
                }else{
                    //断电重启
                    return bmcPowerReset(param, deviceInfos);
                }
            } else {
                if(isPullMode){
                    //物理机重启
                    return pullModePowerResetCMS(param, customerId, deviceInfos);
                }else{
                    //物理机重启
                    return powerResetCMS(param, customerId, deviceInfos);
                }
            }
        }
    }

    /**
     * 启朔重启
     *
     * @param param
     * @param customerId
     * @param type
     * @param deviceInfos
     * @return
     */
    private Result<List<GenerateDeviceTaskVO>> powerResetCa(PowerResetDTO param, long customerId, Integer type, List<DeviceInfoVo> deviceInfos) {
        List<String> deviceOutCodes = new ArrayList<>(deviceInfos.size());
        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());
        deviceInfos.stream().forEach(deviceInfoVo -> {
            deviceOutCodes.add(deviceInfoVo.getDeviceOutCode());
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });
        return null;
    }

    /**
     * 物理机重启（CMS）
     *
     * @param param
     * @param customerId
     * @param deviceInfos
     * @return
     */
    private Result<List<GenerateDeviceTaskVO>> powerResetCMS(PowerResetDTO param, long customerId, List<DeviceInfoVo> deviceInfos) {
        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());
        deviceInfos.stream().forEach(deviceInfoVo -> {
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });

        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(DEVICE_RESTART.getType());
        addDeviceTaskDTO.setStatus(EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        addDeviceTaskDTO.setTaskSource(param.getTaskSource());
        if (isNotEmpty(param.getUserId())) {
            addDeviceTaskDTO.setCreateBy(customerId + "-" + param.getUserId());
        } else {
            String oprBy = Optional.of(param).map(PowerResetDTO::getOprBy).orElse(String.valueOf(customerId));
            addDeviceTaskDTO.setCreateBy(oprBy);
        }
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));

        //调用CMS创建任务
        HashMap<String, DeviceRestartResponse> deviceRestartResponseMap = new HashMap<>();
        deviceInfos.stream().collect(Collectors.groupingBy(DeviceInfoVo::getClusterPublicIp)).forEach((clusterPublicIp, deviceList) -> {
            List<String> deviceIps = deviceList.stream().map(o -> o.getDeviceIp()).collect(Collectors.toList());
            DeviceIpsRequest req = new DeviceIpsRequest();
            req.setDeviceIps(deviceIps);
            URI deviceHost = builderHost(clusterPublicIp);
            try {
                log.info(">>>>>>>>>>>>>>>>调用CMS云机重启,req={}", JSON.toJSONString(req));
                List<DeviceRestartResponse> virtualizeResponseList = FeignUtils.getContent(containerDeviceFeignClient.restart(deviceHost, req), CONTAINER_RESTART_FAILED, req);
                for (DeviceRestartResponse deviceRestartResponse : virtualizeResponseList) {
                    deviceRestartResponseMap.put(deviceRestartResponse.getDeviceIp(), deviceRestartResponse);
                }
            } catch (Exception e) {
                log.error(">>>>>>>>>>>>>>>>调用CMS云机重启失败,req={},e={}", JSON.toJSONString(req), e);
            }
        });

        for (AddDeviceTaskVO deviceTask : deviceTasks) {
            if (deviceRestartResponseMap.isEmpty() || isEmpty(deviceRestartResponseMap.get(deviceTask.getDeviceIp()))) {
                deviceTask.setSubTaskStatus(FAIL_ALL.getStatus());
                deviceTask.setErrorMsg("container restart failed");
                deviceCodes.remove(deviceTask.getDeviceCode());
            } else {
                DeviceRestartResponse deviceRestartResponse = deviceRestartResponseMap.get(deviceTask.getDeviceIp());
                deviceTask.setContainerTaskId(deviceRestartResponse.getMasterTaskId());
                deviceTask.setSubTaskStatus(deviceRestartResponse.getMasterTaskStatus());
            }
        }
        //回写CMS云机重启任务结果
        taskService.updateDeviceTaskResult(deviceTasks);

        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);
        if (CollUtil.isEmpty(deviceCodes)) {
            return Result.ok(builder);
        }

        //修改物理机状态，发送物理机状态变更回调通知消息
        applicationContext.getBean(DeviceServiceImpl.class).updateDeviceStatusAndSendDeviceStatusCallback(deviceCodes, DEVICE_INIT.getStatus(), customerId);

        //根据devicecode查询出pad，创建padtask任务和devicetask任务。修改pad状态和device状态
        for (String deviceCode : deviceCodes) {
            Device device = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", deviceCode));
            List<DevicePad> devicePads = devicePadMapper.selectList(device.getId());
            if (CollUtil.isEmpty(devicePads)) {
                break;
            }
            List<Long> devicePadIds = devicePads.stream().map(DevicePad::getPadId).collect(Collectors.toList());
            List<Pad> padList = padMapper.selectList(new QueryWrapper<Pad>().in("id", devicePadIds).eq("status", ONE));
            if (CollUtil.isEmpty(padList)) {
                break;
            }
            List<String> padCodes = padList.stream().map(Pad::getPadCode).collect(Collectors.toList());
            //添加pad任务
            AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
            addTaskDTO.setPadCodes(padCodes);
            addTaskDTO.setType(RESTART.getType());
            addTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
            addTaskDTO.setCustomerId(customerId);
            addTaskDTO.setSourceCode(param.getTaskSource());
            addDeviceTaskDTO.setType(DEVICE_RESTART.getType());
            List<AddPadTaskVO> padTasks = taskService.addPadTaskService(addTaskDTO);
            //修改实例状态
            //修改实例状态，发送实例状态变更回调通知消息
            padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.RESTARTING, customerId, "powerResetCMS");
        }
        return Result.ok(builder);
    }


    @Override
    public Device selectByDeviceCode(String deviceCode) {
        return deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", deviceCode).eq("delete_flag", ZERO).last("LIMIT 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callbackUpdateDevice(DeviceInfoDTO param) {
        log.info(">>>>>>>>>>>>>>>>云机信息更新回调 param={}", param.toString());
        String deviceIp = param.getDeviceIp();
        String deviceOutCode = param.getDeviceOutCode();
        Device device = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_out_code", deviceOutCode).eq("delete_flag", ZERO));
        if (ObjectUtil.isNotNull(device)) {
            LambdaUpdateWrapper<Device> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(Device::getId, device.getId());
            lambdaUpdateWrapper.set(Device::getDeviceStatus, param.getInitStatus());
            lambdaUpdateWrapper.set(Device::getInitStatus, param.getInitStatus());
            lambdaUpdateWrapper.set(Device::getDeviceIp, deviceIp);

            deviceMapper.update(lambdaUpdateWrapper);
        }
    }

    @Override
    public Result<?> bmcPowerReset(PowerResetDTO param, List<DeviceInfoVo> deviceInfos) {
        log.info(">>>>>>>>>>>>>>>>BMC云机断电重启 param={}", JSON.toJSONString(param));
        long customerId = param.getCustomerId();
        List<String> deviceOutCodes = new ArrayList<>(deviceInfos.size());
        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());
        deviceInfos.stream().forEach(deviceInfoVo -> {
            deviceOutCodes.add(deviceInfoVo.getDeviceOutCode());
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });
        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(POWER_RESET.getType());
        addDeviceTaskDTO.setStatus(EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        addDeviceTaskDTO.setTaskSource(param.getTaskSource());
        if (isNotEmpty(param.getUserId())) {
            addDeviceTaskDTO.setCreateBy(customerId + "-" + param.getUserId());
        } else {
            String oprBy = Optional.of(param).map(PowerResetDTO::getOprBy).orElse(String.valueOf(customerId));
            addDeviceTaskDTO.setCreateBy(oprBy);
        }
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));
        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);
        //获取bmcToken，调用bmc断电重启接口接口
        try {
            //根据云机armServerCode分组，组装数据。有几组就循环几次调用重启接口
            Map<String, List<AddDeviceTaskVO>> groupedByArmServerCode = deviceTasks.stream()
                    .collect(Collectors.groupingBy(AddDeviceTaskVO::getArmServerCode));
            groupedByArmServerCode.forEach((armServerCode, tasks) -> {
                List<BmcDeviceRestartDTO.deviceInfo> bmcList = new ArrayList<>();
                tasks.forEach(task -> {
                    BmcDeviceRestartDTO.deviceInfo deviceInfo = new BmcDeviceRestartDTO.deviceInfo();
                    deviceInfo.setTaskId(task.getCustomerTaskId().toString());
                    deviceInfo.setCardId(task.getDeviceOutCode());
                    bmcList.add(deviceInfo);
                });
                BmcDeviceRestartDTO bmcDeviceRestartDTO = new BmcDeviceRestartDTO();
                //根据armServerCode查询对应armServer的ipv4地址
                ArmServer armServer = armServerMapper.selectOne(new QueryWrapper<ArmServer>().eq("arm_server_code", armServerCode));
                String socApiUrl = armServer.getArmBMCApiUri();
                bmcDeviceRestartDTO.setSocApiUrl(socApiUrl);
                bmcDeviceRestartDTO.setTasks(bmcList);
                bmcDeviceRestartDTO.setTimeOut(TEN);
                EdgeClusterVO edgeClusterVO = edgeClusterMapper.selectClusterByArmServerCodeAndStatusAndOnline(armServer.getClusterCode(), ONE, ONE);
                if (ObjectUtil.isNull(edgeClusterVO)) {
                    log.error(">>>>>>>>>>>>>>>>没有可用的边缘集群 armServerCode={},clusterCode={}", armServer.getCode(), armServer.getClusterCode());
                    throw new BasicException(EDGE_CLUSTER_NOT_EXIST);
                }
                String bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeClusterVO.getClusterPublicIp());
                List<BmcTaskInfoVO> bmcTaskInfoVOs = bmcService.powerReset(bmcToken, bmcDeviceRestartDTO, edgeClusterVO.getClusterPublicIp());
                bmcTaskInfoVOs.forEach(bmcTaskInfoVO -> bmcTaskInfoVO.setTaskType(POWER_RESET.getType()));
                //保存bmc返回的任务id到deviceTask表中
                log.info("bmcTaskInfoVOs={}", bmcTaskInfoVOs);
                taskService.addDeviceTaskBmcTaskId(bmcTaskInfoVOs);
            });
        } catch (Exception e) {
            log.error(">>>>>>>>>>>>>>>>云机断电重启调用BMC接口error builder={},e={}", JSON.toJSONString(builder), e);
        }
        //修改物理机状态，发送物理机状态变更回调通知消息
        applicationContext.getBean(DeviceServiceImpl.class).updateDeviceStatusAndSendDeviceStatusCallback(deviceCodes, DEVICE_INIT.getStatus(), customerId);
        //根据devicecode查询出pad，创建padtask任务和devicetask任务。修改pad状态和device状态
        for (String deviceCode : deviceCodes) {
            Device device = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", deviceCode));
            List<DevicePad> devicePads = devicePadMapper.selectList(device.getId());
            if (CollUtil.isEmpty(devicePads)) {
                break;
            }
            List<Long> devicePadIds = devicePads.stream().map(DevicePad::getPadId).collect(Collectors.toList());
            List<Pad> padList = padMapper.selectList(new QueryWrapper<Pad>().in("id", devicePadIds).eq("status", ONE));
            if (CollUtil.isEmpty(padList)) {
                break;
            }
            List<String> padCodes = padList.stream().map(Pad::getPadCode).collect(Collectors.toList());
            //添加pad任务
            AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
            addTaskDTO.setPadCodes(padCodes);
            addTaskDTO.setType(RESTART.getType());
            addTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
            addTaskDTO.setCustomerId(customerId);
            addTaskDTO.setSourceCode(param.getTaskSource());
            addDeviceTaskDTO.setType(POWER_RESET.getType());
            List<AddPadTaskVO> padTasks = taskService.addPadTaskService(addTaskDTO);
            //修改实例状态
            //修改实例状态，发送实例状态变更回调通知消息
            padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.RESTARTING, customerId, "bmcPowerReset");
        }
        return Result.ok(builder);
    }

    @Override
    public Result<?> pullModeBmcPowerReset(PowerResetDTO param, List<DeviceInfoVo> deviceInfos) {
        log.info(">>>>>>>>>>>>>>>>BMC云机断电重启 param={}", JSON.toJSONString(param));
        long customerId = param.getCustomerId();
        List<String> deviceOutCodes = new ArrayList<>(deviceInfos.size());
        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());
        deviceInfos.stream().forEach(deviceInfoVo -> {
            deviceOutCodes.add(deviceInfoVo.getDeviceOutCode());
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });
        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(POWER_RESET.getType());
        addDeviceTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        addDeviceTaskDTO.setTaskSource(param.getTaskSource());
        if (isNotEmpty(param.getUserId())) {
            addDeviceTaskDTO.setCreateBy(customerId + "-" + param.getUserId());
        } else {
            String oprBy = Optional.of(param).map(PowerResetDTO::getOprBy).orElse(String.valueOf(customerId));
            addDeviceTaskDTO.setCreateBy(oprBy);
        }
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));
        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);

        //按照板卡ip维度 生成子任务
        for(AddDeviceTaskVO addDeviceTaskVO : deviceTasks){
            Device device = deviceMapper.selectOne(new QueryWrapper<>(Device.class).eq("device_code",addDeviceTaskVO.getDeviceCode()).eq("delete_flag",0));
            if(device != null && StrUtil.isNotEmpty(device.getDeviceOutCode())){
                taskQueueManager.addDeviceTaskPullMode(TaskTypeAndChannelEnum.POWER_RESET,addDeviceTaskVO,device.getDeviceOutCode(), new Date());
            }
        }
        return Result.ok(builder);
    }


    /**
     * 调用CMS删除云机任务
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<GenerateDeviceTaskVO> deviceDestroy(DeviceDestroyDTO param) {
        log.info(">>>>>>>>>>>>>>>>物理机删除云机CMS param={}", JSON.toJSONString(param));
        if (CollUtil.isEmpty(param.getDeviceIps())) {
            throw new BasicException(DEVICE_IP_NOT_EXIST);
        }
        List<DeviceDestroyVO> devices = deviceMapper.selectCanDeviceByDeviceIp(param.getDeviceIps());
        if (CollUtil.isEmpty(devices)) {
            throw new BasicException(DEVICE_IP_NOT_EXIST);
        }

        Map<String,String> macVlanMap = devices.stream().collect(Collectors.toMap(DeviceDestroyVO::getDeviceCode, DeviceDestroyVO::getMacVlan,(key1 , key2) -> key1));

        //是否拉模式
        Boolean isPullMode = PullModeConfigHolder.isPullMode(String.valueOf(param.getCustomerId()),null,devices.get(0).getDeviceCode());

        List<String> deviceCodes = new ArrayList<>(devices.size());
        devices.stream().forEach(deviceInfoVo -> {
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });

        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(CONTAINER_DEVICE_DESTROY.getType());
        addDeviceTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addDeviceTaskDTO.setCustomerId(param.getCustomerId());
        addDeviceTaskDTO.setTaskSource(param.getSourceCode());
        addDeviceTaskDTO.setCreateBy(param.getOprBy());
        addDeviceTaskDTO.setRemark(param.getRemark());
        addDeviceTaskDTO.setPullMode(isPullMode);
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));

        if(isPullMode){
            for(AddDeviceTaskVO addDeviceTaskVO : deviceTasks){
                DeviceIpsRequest req = new DeviceIpsRequest();
                req.setDeviceIps(Collections.singletonList(addDeviceTaskVO.getDeviceIp()));
                req.setNetworkDeviceName(macVlanMap.get(addDeviceTaskVO.getDeviceCode()));
                taskQueueManager.addDeviceTaskPullMode(TaskTypeAndChannelEnum.CONTAINER_DEVICE_DESTROY,addDeviceTaskVO,req, new Date());
            }
            return GenerateDeviceTaskVO.builder(deviceTasks);
        }else{
            //调用CMS创建任务
            HashMap<String, DeviceDestroyResponse> deviceDestroyResponseMap = new HashMap<>();
            devices.stream().collect(Collectors.groupingBy(DeviceDestroyVO::getClusterPublicIp)).forEach((clusterPublicIp, deviceList) -> {
                List<String> deviceIps = deviceList.stream().map(o -> o.getDeviceIp()).collect(Collectors.toList());
                DeviceIpsRequest req = new DeviceIpsRequest();
                req.setDeviceIps(deviceIps);
                URI deviceHost = builderHost(clusterPublicIp);
                try {
                    log.info(">>>>>>>>>>>>>>>>调用CMS云机刪除,req={}", JSON.toJSONString(req));
                    List<DeviceDestroyResponse> virtualizeResponseList = FeignUtils.getContent(containerDeviceFeignClient.destroy(deviceHost, req), CONTAINER_DEVICE_DESTROY_FAILED, req);
                    for (DeviceDestroyResponse deviceDestroyResponse : virtualizeResponseList) {
                        deviceDestroyResponseMap.put(deviceDestroyResponse.getDeviceIp(), deviceDestroyResponse);
                    }
                } catch (Exception e) {
                    log.error(">>>>>>>>>>>>>>>>调用CMS云机刪除失败,req={},e={}", JSON.toJSONString(req), e);
                }
            });

            for (AddDeviceTaskVO deviceTask : deviceTasks) {
                if (deviceDestroyResponseMap.isEmpty() || isEmpty(deviceDestroyResponseMap.get(deviceTask.getDeviceIp()))) {
                    deviceTask.setSubTaskStatus(FAIL_ALL.getStatus());
                    deviceTask.setErrorMsg("container destroy failed");
                    deviceCodes.remove(deviceTask.getDeviceCode());
                } else {
                    DeviceDestroyResponse deviceDestroyResponse = deviceDestroyResponseMap.get(deviceTask.getDeviceIp());
                    deviceTask.setContainerTaskId(deviceDestroyResponse.getMasterTaskId());
                    deviceTask.setSubTaskStatus(deviceDestroyResponse.getMasterTaskStatus());
                }
            }
            //回写CMS云机删除任务结果
            taskService.updateDeviceTaskResult(deviceTasks);
            List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);
            if (CollUtil.isEmpty(deviceCodes)) {
                return builder;
            }

            //云机实例分配状态变更
            Device device = new Device();
            device.setPadAllocationStatus(DELETING.getStatus());
            device.setUpdateBy("deviceDestroy:" + param.getOprBy());
            device.setUpdateTime(new Date());
            this.update(device, new QueryWrapper<Device>().lambda().in(Device::getDeviceCode, deviceCodes));
            return builder;
        }

    }



    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<GenerateDeviceTaskVO> virtualize(VirtualizeDeviceDTO param) {
        log.info(">>>>>>>>>>>>>>>>物理机创建云机 param={}", JSON.toJSONString(param));
        if (CollUtil.isEmpty(param.getDeviceIps())) {
            throw new BasicException(DEVICE_IP_NOT_EXIST);
        }
        List<VirtualizeDeviceInfoVO> devices = deviceMapper.selectListVirtualizeInfo(param.getDeviceIps());
        if (devices.isEmpty()) {
            throw new BasicException(DEVICE_IP_NOT_ONLINE);
        }

        //是否拉模式
        Boolean isPullMode = PullModeConfigHolder.isPullMode(String.valueOf(param.getCustomerId()),null,devices.get(0).getDeviceCode());

        if (!redisService.isAdmin(param.getCustomerId())
                && devices.stream().anyMatch(s -> !s.getCustomerId().equals(param.getCustomerId()))) {
            throw new BasicException(CARD_DOES_NOT_EXIST);
        }

        //判断是否存在待执行或者执行中的创建实例任务 有则直接拒掉当前的请求
        List<String> deviceCodes = devices.stream().map(VirtualizeDeviceInfoVO::getDeviceCode).collect(Collectors.toList());
        List<String> runTaskDeviceCodes = deviceTaskMapper.countRunDeviceTaskByType(deviceCodes,CONTAINER_VIRTUALIZE.getType());
        if(CollUtil.isNotEmpty(runTaskDeviceCodes)){
            throw new BasicException(CollUtil.join(new HashSet<>(runTaskDeviceCodes),",") + "板卡存在进行中的创建实例任务，请检查");
        }

        //初始化当前服务的第一位随机数
        if(createPadCodeNew){
            generatePadCodeFirst();
        }

        //实例规格
        ResourceSpecification resourceSpecification = resourceSpecificationMapper.selectOne(new QueryWrapper<ResourceSpecification>().lambda()
                .eq(ResourceSpecification::getSpecificationCode, param.getSpecificationCode())
                .eq(ResourceSpecification::getDeleteFlag, ZERO)
                .eq(ResourceSpecification::getStatus, ONE)
                .last("LIMIT 1"));
        if (isEmpty(resourceSpecification)) {
            throw new BasicException(SPECIFICATION_CODE_NOT_EXIST);
        }
        //镜像
        LambdaQueryWrapper<CustomerUploadImage> imageQueryWrapper = new QueryWrapper<CustomerUploadImage>().lambda();
        if (!SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getSourceCode())) {
            imageQueryWrapper.and(wrapper -> wrapper.eq(CustomerUploadImage::getCustomerId, param.getCustomerId()).or().isNull(CustomerUploadImage::getCustomerId));
        }
        imageQueryWrapper.eq(CustomerUploadImage::getUniqueId, param.getImageId())
                .eq(CustomerUploadImage::getDeleteFlag, ZERO)
                .eq(CustomerUploadImage::getStatus, ImageUploadStatus.SUCCESS.getStatus())
                .last("LIMIT 1");
        CustomerUploadImage customerUploadImage = customerUploadImageMapper.selectOne(imageQueryWrapper);
        if (isEmpty(customerUploadImage)) {
            throw new BasicException(IMAGE_NOT_EXIST);
        }
        //屏幕布局
        LambdaQueryWrapper<ScreenLayout> screenLayoutQueryWrapper = new QueryWrapper<ScreenLayout>().lambda();
        if (!SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getSourceCode())) {
            screenLayoutQueryWrapper.and(wrapper -> wrapper.eq(ScreenLayout::getCustomerId, param.getCustomerId()).or().isNull(ScreenLayout::getCustomerId));
        }
        screenLayoutQueryWrapper.eq(ScreenLayout::getCode, param.getScreenLayoutCode())
                .eq(ScreenLayout::getDeleteFlag, ZERO)
                .eq(ScreenLayout::getStatus, ONE)
                .last("LIMIT 1");
        ScreenLayout screenLayout = screenLayoutMapper.selectOne(screenLayoutQueryWrapper);
        if (isEmpty(screenLayout)) {
            throw new BasicException(SCREEN_LAYOUT_NOT_EXIST);
        }

        //获取网络段
        HashMap<Long, List<String>> armIpMap = new HashMap<>();
        //List<String> deviceCodes = new ArrayList<>();
        for (VirtualizeDeviceInfoVO device : devices) {
            if (isNotEmpty(armIpMap.get(device.getDeviceId()))) {
                break;
            }
            if (Objects.isNull(armIpMap.get(device.getArmServerId()))) {
                List<String> ipv4Cidrs = netPadMapper.selectIpv4CidrsByArmServer(device.getArmServerId());
                if (ipv4Cidrs.isEmpty()) {
                    throw new BasicException(NETWORK_SEGMENT_NOT_EXIST);
                }
                List<String> ipCidrs = new ArrayList<>();
                for (String ipv4Cidr : ipv4Cidrs) {
                    Set<String> ipAddressesFromCIDR;
                    if (ipSubNumWhiteList != null && ipSubNumWhiteList.size() > 0) {
                        log.info(">>>>>> ipSubNumWhiteList enabled value is {} ", ipSubNumWhiteList);
                        ipAddressesFromCIDR = CIDRUtils.getIPAddressesFromCIDR(ipv4Cidr, ipSubNumWhiteList);
                    } else {
                        ipAddressesFromCIDR = CIDRUtils.getIPAddressesFromCIDR(ipv4Cidr);
                    }
                    if (ipAddressesFromCIDR != null && !ipAddressesFromCIDR.isEmpty()) {
                        ipCidrs.addAll(ipAddressesFromCIDR);
                    }
                }
                List<String> ips = CIDRUtils.sortIPs(ipCidrs);
                armIpMap.put(device.getArmServerId(), ips);
            }
            //deviceCodes.add(device.getDeviceCode());
        }

        //获取客户推流类型
        Long customerId = devices.get(ZERO).getCustomerId();
        if (isNotEmpty(customerId)) {
            Integer streamType = customerConfigMapper.getStreamTypeByCustomerId(customerId);
            param.setStreamType(streamType);
        }

        //云机实例分配状态变更
        Device deviceInfo = new Device();
        deviceInfo.setDeviceLevel(resourceSpecification.getSpecificationCode());
        deviceInfo.setPadAllocationStatus(isPullMode ? UNALLOCATED.getStatus() :ALLOCATING.getStatus());
        deviceInfo.setUpdateBy("virtualizeDevice:" + param.getOprBy());
        deviceInfo.setUpdateTime(new Date());
        int update = this.deviceMapper.update(deviceInfo, new QueryWrapper<Device>().lambda().in(Device::getDeviceCode, deviceCodes).in(Device::getPadAllocationStatus, UNALLOCATED.getStatus(), ALLOCATION_FAIL.getStatus()));
        if (update == ZERO) {
            throw new BasicException(PLEASE_NOT_SUBMIT_FREQUENTLY);
        }

        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(CONTAINER_VIRTUALIZE.getType());
        addDeviceTaskDTO.setStatus(isPullMode ? WAIT_EXECUTE.getStatus() : EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(param.getCustomerId());
        addDeviceTaskDTO.setTaskSource(param.getSourceCode());
        JSONObject taskContent = new JSONObject();
        taskContent.put("padType", param.getPadType());
        addDeviceTaskDTO.setTaskContent(JSON.toJSONString(taskContent));
        addDeviceTaskDTO.setCreateBy(param.getOprBy());
        addDeviceTaskDTO.setPullMode(isPullMode);
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));

        // 确保事务提交后执行异步任务
        CompletableFuture.runAsync(() -> {
            if (CollUtil.isEmpty(deviceTasks)) {
                return;
            }
            HashMap<String, AddDeviceTaskVO> deviceTaskMap = new HashMap<>();
            for (AddDeviceTaskVO deviceTask : deviceTasks) {
                deviceTaskMap.put(deviceTask.getDeviceIp(), deviceTask);
            }
            for (VirtualizeDeviceInfoVO device : devices) {
                VirtualizeDeviceDTO virtualizeDeviceDTONew = BeanUtil.copyProperties(param,VirtualizeDeviceDTO.class);
                //添加默认dns
                if(StrUtil.isEmpty(virtualizeDeviceDTONew.getDns())){
                    String dns = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(device.getClusterCode(), EdgeClusterConfigurationEnum.CLUSTER_DNS_DEFAULT_SERVERS);
                    if(StrUtil.isNotBlank(dns)){
                        virtualizeDeviceDTONew.setDns(dns);
                    }
                }
                try {
                    virtualizeDeviceNew(virtualizeDeviceDTONew, resourceSpecification, customerUploadImage, screenLayout, device, armIpMap, deviceTaskMap,isPullMode);
                } catch (Exception e) {
                    //回写CMS云机任务结果
                    AddDeviceTaskVO deviceTask = deviceTaskMap.get(device.getDeviceIp());
                    deviceTask.setSubTaskStatus(FAIL_ALL.getStatus());
                    deviceTask.setErrorMsg("创建实例失败");
                    taskService.updateDeviceTaskResult(Collections.singletonList(deviceTask));
                    ContainerTaskResultVO dto = new ContainerTaskResultVO();
                    dto.setDeviceIp(device.getDeviceIp());
                    dto.setMasterTaskStatus(TaskStatusConstants.FAIL_ALL.getStatus());
                    dto.setMsg(deviceTask.getErrorMsg());
                    updateVirtualizeDeviceTaskResult(dto, "virtualize-rollback");
                    log.error(">>>>>>>>>>>>>>>>创建云机任务失败,device={},e={}", JSON.toJSONString(device), e);
                } finally {
                    List<String> padIps = padMapper.getPadIpsByDeviceId(device.getDeviceId());
                    for (String padIp : padIps) {
                        String key = RedisKeyPrefix.PAD_IP_LOCK + padIp;
                        redisService.deleteObject(key);
                    }
                }
            }
        }, executor);
        return GenerateDeviceTaskVO.builder(deviceTasks);
    }

    @Override
    public void virtualizeDeviceNew(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification, CustomerUploadImage customerUploadImage, ScreenLayout screenLayout, VirtualizeDeviceInfoVO device, HashMap<Long, List<String>> armIpMap, HashMap<String, AddDeviceTaskVO> deviceTaskMap,Boolean isPullMode) {
        String deviceIp = device.getDeviceIp();
        List<String> ips = armIpMap.get(device.getArmServerId());
        VirtualizeDeviceRequest req = new VirtualizeDeviceRequest();
        VirtualizeDeviceRequest.Device data = new VirtualizeDeviceRequest.Device();
        data.setDeviceIp(deviceIp);
        //容器初始化参数
        VirtualizeDeviceRequest.InitInformation initInformation = new VirtualizeDeviceRequest.InitInformation();
        initInformation.setHostStorageSize(device.getHostStorageSize());
        initInformation.setContainerConcurrentSize(resourceSpecification.getPadNumber());
        data.setInitInformation(initInformation);

        List<VirtualizeDeviceRequest.Device.Pad> pads = new ArrayList<>();
        data.setPads(pads);
        List<Pad> extracted = padService.extractedPad(param, resourceSpecification, screenLayout, device, ips);
        List<String> padCodes = new ArrayList<>();
        extracted.forEach(padInfo -> {
            VirtualizeDeviceRequest.Device.Pad padVirtualize = new VirtualizeDeviceRequest.Device.Pad();
            padVirtualize.setPadCode(padInfo.getPadCode());
            ImageRequest image = new ImageRequest();
            image.setId(param.getImageId());
            // 暂不需要放开此功能，暂为固定值
            image.setTag("latest");
//            image.setTag(customerUploadImage.getImageTag());
            padVirtualize.setImage(image);

            DisplayRequest display = new DisplayRequest();
            display.setWidth(screenLayout.getScreenWidth().intValue());
            display.setHeight(screenLayout.getScreenHigh().intValue());
            display.setDpi(screenLayout.getPixelDensity().intValue());
            display.setFps(screenLayout.getScreenRefreshRate().intValue());
            padVirtualize.setDisplay(display);

            SpecRequest spec = new SpecRequest();
            spec.setCpu(param.getIsolateCpu() ? resourceSpecification.getCpu() : device.getCpu());
            spec.setMemory(param.getIsolateMemory() ? resourceSpecification.getMemory() : device.getMemory());
            spec.setDisk(param.getIsolateStorage() ? resourceSpecification.getStorage() : device.getStorage());
            spec.setIsolateDisk(param.getIsolateStorage());
            padVirtualize.setSpec(spec);

            NetworkRequest network = new NetworkRequest();
            network.setIp(padInfo.getPadIp());
            network.setSubnet(device.getSubnet());
            network.setIpRange(device.getIpRange());
            network.setGateway(device.getGateway());
            network.setNetworkDeviceName(device.getMacVlan());
            network.setMac(padInfo.getMac());
            if (!ObjectUtil.isEmpty(param.getDns())) {
                network.setDns(param.getDns());
            }
            padVirtualize.setNetwork(network);

            if (!ObjectUtil.isEmpty(param.getAdiUrl())) {
                VirtualizeDeviceRequest.ADI adi = new VirtualizeDeviceRequest.ADI();
                adi.setTemplateUrl(param.getAdiUrl());
                adi.setTemplatePassword(param.getAdiPassword());
                adi.setRealPhoneTemplateId(param.getRealPhoneTemplateId());

                // 获取image_parameter字段的值（新加字段），需要在customerUploadImageMapper中实现对应方法
                String imageParameter = customerUploadImageMapper.getImageParameterByImageUniqueId(param.getImageId());

                AdiCertificateRepository adiCertificateRepository = adiCertificateManager.useAdiCertificate(padInfo.getPadCode(), imageParameter);
                if (adiCertificateRepository != null) {
                    adi.setAndroidCertData(adiCertificateRepository.getCertificate());
                }

                padVirtualize.setAdi(adi);
            }

            Map<String, String> deviceAndroidProps = new HashMap<>();
            //创建实例默认关闭adb
            deviceAndroidProps.put("persist.sys.cloud.madb_enable","0");
            if (!ObjectUtil.isEmpty(param.getDeviceAndroidProps())) {
                Map map = JSON.parseObject(param.getDeviceAndroidProps(), Map.class);
                map.forEach((key, value) -> deviceAndroidProps.put(String.valueOf(key), String.valueOf(value)));
            }

            String randomPatchDate = AndroidDeviceInfoUtils.getRandomPatchDate();
            deviceAndroidProps.put("ro.build.version.security_patch",randomPatchDate);
            deviceAndroidProps.put("ro.vendor.build.security_patch", randomPatchDate);

            padVirtualize.setDeviceAndroidProps(deviceAndroidProps);

            padVirtualize.setAndroidProp("ro.boot.armcloud_server_addr=" + armcloudServerAddr);

            pads.add(padVirtualize);
            padCodes.add(padInfo.getPadCode());
        });
        req.setDevices(Collections.singletonList(data));
        if(isPullMode){
            AddDeviceTaskVO addDeviceTaskVO = deviceTaskMap.get(device.getDeviceIp());
            //记录task_rel_instance_detail表
            taskService.saveDeviceInstance(addDeviceTaskVO.getMasterTaskId(),addDeviceTaskVO.getSubTaskId(),TaskTypeAndChannelEnum.CONTAINER_VIRTUALIZE.getCbsTaskTypeEnum(),req.getDevices().get(0),device,true);
            taskQueueManager.addDeviceTaskPullMode(TaskTypeAndChannelEnum.CONTAINER_VIRTUALIZE,addDeviceTaskVO,req, new Date());
        }else{
            //调用CMS创建任务
            URI deviceHost = builderHost(device.getClusterPublicIp());
            log.info(">>>>>>>>>>>>>>>>调用CMS云机创建,req={}", JSON.toJSONString(req));
            List<DeviceVirtualizeResponse> virtualizeResponseList = FeignUtils.getContent(containerDeviceFeignClient.virtualize(deviceHost, req), CONTAINER_ADDITION_FAILED, req);

            if (CollUtil.isNotEmpty(virtualizeResponseList) && deviceIp.equals(virtualizeResponseList.get(ZERO).getDeviceIp())) {
                DeviceVirtualizeResponse deviceVirtualizeResponse = virtualizeResponseList.get(ZERO);
                AddDeviceTaskVO deviceTask = deviceTaskMap.get(device.getDeviceIp());
                deviceTask.setContainerTaskId(deviceVirtualizeResponse.getMasterTaskId());
                deviceTask.setSubTaskStatus(deviceVirtualizeResponse.getMasterTaskStatus());
                //回写CMS云机任务结果
                taskService.updateDeviceTaskResult(Collections.singletonList(deviceTask));
            } else {
                log.error(">>>>>>>>>>>>>>>>调用CMS云机创建失败,req={},virtualizeResponseList={}", JSON.toJSONString(req), JSON.toJSONString(virtualizeResponseList));
                throw new BasicException(CONTAINER_ADDITION_FAILED);
            }
        }
    }



    /**
     * 记录实例操作日志
     *
     * @param title        操作模块
     * @param businessType 业务类型
     * @param method       请求方法
     * @param sourceType   来源
     * @param operName     操作人员
     * @param param        参数
     */
    private void recordPadOperLog(String title, Integer businessType, String method, String sourceType, String operName, String param) {
        PadOperLog padOperLog = new PadOperLog();
        padOperLog.setTitle(title);
        padOperLog.setBusinessType(businessType);
        padOperLog.setMethod(method);
        padOperLog.setSourceType(sourceType);
        padOperLog.setOperName(operName);
        padOperLog.setParam(param);
        padOperLog.setOperTime(new Date());
        padOperLogMapper.insert(padOperLog);
    }

    /**
     * 更新物理机CMS任务结果
     *
     * @param dto
     */
    @Override
    public void ContainerDeviceTaskResult(ContainerTaskResultVO dto) {
        //通过返回的msg是否包含关键字判断是开机任务
        if(StringUtils.isNotEmpty(dto.getMsg()) &&dto.getMsg().contains("OFF_PAD_TASK")){
            log.info("{} ContainerDeviceTaskResult_run param:{} ",this.getClass().getName(),JSON.toJSONString(dto));

            //开机任务的结果处理由长连接获取
            if(Objects.equals(dto.getMasterTaskStatus(),TaskStatusConstants.SUCCESS.getStatus())){
                return ;
            }
            //处理开机任务 已废弃任务,先兼容
            taskService.updatePadTaskCallback(dto);
            return ;
        }
        log.info(">>>>>>>>>>>>>>>>updateVirtualizeDeviceTaskResult param={}", JSON.toJSONString(dto));
        if (isEmpty(dto.getDeviceIp())) {
            return;
        }
        //修改任务状态
        Task taskInfo = taskService.updateContainerDeviceTaskResult(dto);
        if (TaskStatusConstants.EXECUTING.getStatus().equals(dto.getMasterTaskStatus()) || TaskStatusConstants.WAIT_EXECUTE.getStatus().equals(dto.getMasterTaskStatus())) {
            return;
        }
        if (isNotEmpty(taskInfo)) {
            if (taskInfo.getType().equals(CONTAINER_VIRTUALIZE.getType())) {
                updateVirtualizeDeviceTaskResult(dto, "virtualize-Consumer");
            } else if (taskInfo.getType().equals(CONTAINER_DEVICE_DESTROY.getType())) {
                updateDestroyDeviceTaskResult(dto);
            } else if (taskInfo.getType().equals(DEVICE_RESTART.getType())) {
                updateRestartDeviceTaskResult(dto);
            } else if (taskInfo.getType().equals(CBS_SELF_UPDATE.getType())) {
                //更新cbs版本号
                cbsSelfUpdateDeviceTaskResult(dto);
            }  else if (taskInfo.getType().equals(CONTAINER_NET_STORAGE_ON.getType())) {
                //更新板卡实例关联关系  由实例连接上来处理任务结果
//                NetStorageOnDeviceTaskResult(dto);
            }
        }

    }

    /**
     * CMS重启云机任务回调业务处理
     *
     * @param dto
     */
    private void updateRestartDeviceTaskResult(ContainerTaskResultVO dto) {
        Integer deviceStatus;
        if (TaskStatusConstants.SUCCESS.getStatus().equals(dto.getMasterTaskStatus())) {
            deviceStatus = DEVICE_SUCCESS.getStatus();
        } else {
            deviceStatus = DEVICE_INIT.getStatus();
        }
        List<DeviceInfoVo> deviceInfoVos = deviceMapper.selectDeviceInfoByDeviceIpDc(Collections.singletonList(dto.getDeviceIp()), null, null);
        if (CollUtil.isEmpty(deviceInfoVos)) {
            log.error(">>>>>>>>>>>>>>>>更新重启云机任务结果失败,deviceIp={},dto={}", dto.getDeviceIp(), JSON.toJSONString(dto));
        }
        DeviceInfoVo deviceInfo = deviceInfoVos.get(ZERO);
        //修改物理机状态，发送物理机状态变更回调通知消息
        applicationContext.getBean(DeviceServiceImpl.class).updateDeviceStatusAndSendDeviceStatusCallback(Collections.singletonList(deviceInfo.getDeviceCode()), deviceStatus, deviceInfo.getCustomerId());
    }

    /**
     * CMS删除云机任务回调业务处理
     *
     * @param dto
     */
    private void updateDestroyDeviceTaskResult(ContainerTaskResultVO dto) {
        Integer masterTaskStatus;
        if (TaskStatusConstants.SUCCESS.getStatus().equals(dto.getMasterTaskStatus())) {
            masterTaskStatus = UNALLOCATED.getStatus();
        } else {
            masterTaskStatus = DELETE_FAIL.getStatus();
        }
        String deviceIp = dto.getDeviceIp();
        int update = deviceMapper.removePadByDeviceIp(masterTaskStatus, deviceIp);
        if (update > ZERO && masterTaskStatus.equals(UNALLOCATED.getStatus())) {
            List<String> padCodes = padMapper.queryRemovePadsByDeviceIp(dto.getDeviceIp());
            if (CollUtil.isNotEmpty(padCodes)) {
                adiCertificateManager.cancelUseByPadCode(padCodes);
                int removeCount = padMapper.removePadByDeviceIp(padCodes);
                if (removeCount > ZERO) {
                    recordPadOperLog("Destroy-Consumer", DELETE.getCode(), "Destroy", SourceTargetEnum.PAAS.getCode(), "admin", JSON.toJSONString(padCodes));
                }
            }
        }
    }

    /**
     * CMS创建云机任务回调业务处理
     *
     * @param dto
     */
    private void updateVirtualizeDeviceTaskResult(ContainerTaskResultVO dto, String title) {
        Integer masterTaskStatus;
        if (TaskStatusConstants.SUCCESS.getStatus().equals(dto.getMasterTaskStatus())) {
            masterTaskStatus = ALLOCATED.getStatus();
        } else {
            masterTaskStatus = ALLOCATION_FAIL.getStatus();
        }
        String deviceIp = dto.getDeviceIp();
        Device device = new Device();
        device.setPadAllocationStatus(masterTaskStatus);
        device.setUpdateTime(new Date());
        int update = deviceMapper.update(device, new QueryWrapper<Device>().lambda()
                .eq(Device::getDeviceIp, deviceIp)
                .eq(Device::getDeleteFlag, ZERO)
                .in(Device::getPadAllocationStatus, Arrays.asList(ALLOCATING.getStatus(),UNALLOCATED.getStatus(),ALLOCATION_FAIL.getStatus())));
        if (update > ZERO) {
            if (masterTaskStatus.equals(ALLOCATION_FAIL.getStatus())) {
                List<String> padCodes = padMapper.queryRemovePadsByDeviceIp(dto.getDeviceIp());
                if (CollUtil.isNotEmpty(padCodes)) {
                    adiCertificateManager.cancelUseByPadCode(padCodes);
                    int removeCount = padMapper.removePadByDeviceIp(padCodes);
                    if (removeCount > ZERO) {
                        recordPadOperLog(title, DELETE.getCode(), "virtualize", SourceTargetEnum.PAAS.getCode(), "admin", JSON.toJSONString(padCodes));
                    }
                }
            } else if (masterTaskStatus.equals(ALLOCATED.getStatus())) {
                Device deviceInfo = deviceMapper.selectOne(new QueryWrapper<Device>().lambda().eq(Device::getDeviceIp, deviceIp).eq(Device::getDeleteFlag, ZERO).last("LIMIT 1"));
                Long customerId = customerDeviceMapper.getCustomerIdByDeviceId(deviceInfo.getId());
                padMapper.enablePadByDeviceIp(dto.getDeviceIp(), customerId);
            }
        }
    }

    @Override
    public Result<?> deviceRestart(DeviceRestartDTO param) {
        log.info(">>>>>>>>>>>>>>>>物理机重启 param={}", param.toString());
        long customerId = param.getCustomerId();
        List<String> deviceCodes = param.getDeviceIps();

        if (deviceCodes.isEmpty()) {
            throw new BasicException(EXECUTE_RESTART_FAIL_EXCEPTION);
        }
        //根据device_code获得device_out_code
        List<String> deviceOutCodes = deviceMapper.selectDeviceOutCodeByDeviceCode(deviceCodes, customerId);
        if (CollUtil.isEmpty(deviceOutCodes)) {
            throw new BasicException(EXECUTE_RESTART_FAIL_EXCEPTION);
        }
        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(DEVICE_RESTART.getType());
        addDeviceTaskDTO.setStatus(EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));
        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);
        //修改物理机状态
        this.updateDeviceStatus(deviceCodes, DEVICE_INIT.getStatus());
        //根据devicecode查询出pad，创建padtask任务和devicetask任务。修改pad状态和device状态
        for (String deviceCode : deviceCodes) {
            Device device = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", deviceCode));
            List<DevicePad> devicePads = devicePadMapper.selectList(device.getId());
            List<Long> devicePadIds = devicePads.stream().map(DevicePad::getPadId).collect(Collectors.toList());
            List<Pad> padList = padMapper.selectList(new QueryWrapper<Pad>().in("id", devicePadIds));
            List<String> padCodes = padList.stream().map(Pad::getPadCode).collect(Collectors.toList());
            //添加pad任务
            AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
            addTaskDTO.setPadCodes(padCodes);
            addTaskDTO.setType(RESTART.getType());
            addTaskDTO.setStatus(EXECUTING.getStatus());
            addTaskDTO.setCustomerId(customerId);
            List<AddPadTaskVO> padTasks = taskService.addPadTaskService(addTaskDTO);
            //修改实例状态，发送实例状态变更回调通知消息
            padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.RESTARTING, customerId, "deviceRestart");
        }
        return Result.ok(builder);
    }

    @Override
    public Device selectByPadCode(String padCode) {
        Pad pad = padMapper.selectOne(new QueryWrapper<Pad>().eq("pad_code", padCode));
        if (ObjectUtil.isNotNull(pad)) {
            DevicePad devicePad = devicePadMapper.selectByPadId(pad.getId());
            if (ObjectUtil.isNotNull(devicePad)) {
                return deviceMapper.selectById(devicePad.getDeviceId());
            }
        }
        return null;
    }

    public Boolean updateDeviceStatus(List<String> deviceCodes, Integer deviceStatus) {
        log.info("updateDeviceStatus deviceCodes:{},deviceStatus:{}", deviceCodes, deviceStatus);
        Device device = new Device();
        device.setDeviceStatus(deviceStatus);
        device.setUpdateTime(new Date());
        return deviceMapper.update(device, new QueryWrapper<Device>().in("device_code", deviceCodes)) > NumberConsts.ZERO;
    }

    /**
     * 发送云机实例状态变更任务回调消息
     *
     * @param customerId
     * @param deviceStatus
     * @param deviceCodes
     */
    public void sendChangeDeviceStatusService(Long customerId, Integer deviceStatus, List<String> deviceCodes) {
        if (deviceCodes.isEmpty() || customerId == null || deviceCodes == null) {
            return;
        }
        //用户是否订阅了实例状态回调
        CallbackUrlVO callbackUrlVO = callbackInformationService.getCallbackUrlService(customerId, CallbackTypeConstants.DEVICE_RESTART_CALLBACK_TYPE);
        if (Objects.isNull(callbackUrlVO)) {
            return;
        }

        //发送实例状态变更回调通知消息
        CustomerAccess customerAccess = customerAccessService.getAccessByCustomerId(customerId);
        if (Objects.isNull(customerAccess)) {
            return;
        }
        DeviceStatusMessageMQ deviceStatusMessageMQ = new DeviceStatusMessageMQ();
        deviceStatusMessageMQ.setDeviceStatus(deviceStatus);

        deviceStatusMessageMQ.setAk(customerAccess.getAccessKeyId());
        deviceStatusMessageMQ.setSk(customerAccess.getSecretAccessKey());
        deviceStatusMessageMQ.setHost(callbackUrlVO.getHost());
        deviceStatusMessageMQ.setUrl(callbackUrlVO.getCallbackUrl());

        for (String deviceCode : deviceCodes) {
            deviceStatusMessageMQ.setDeviceCode(deviceCode);
            String jsonString = JSON.toJSONString(deviceStatusMessageMQ);
            log.info("sendChangeDeviceStatusService------------> jsonString:{}", jsonString);
            String msgId = mqProducerService.producerNormalMessage(mqTopicConfig.getVcpDeviceStatus(), null, jsonString);
            log.info("sendChangeDeviceStatusService-------------> msgId:{}", msgId);
        }
    }

    @Override
    public Boolean updateDeviceStatusAndSendDeviceStatusCallback(List<String> deviceCodes, Integer deviceStatus, Long customerId) {
        log.info("updateDeviceStatusAndSendDeviceStatusCallback------------> deviceCodes:{}", JSON.toJSONString(deviceCodes));
        Boolean flag = this.updateDeviceStatus(deviceCodes, deviceStatus);
        if (flag) {
            if (CollUtil.isNotEmpty(deviceCodes)) {
                // 板卡流水历史表
                try{
                    deviceHistoryService.insertDeviceHistoryRecords(deviceCodes,deviceStatus);
                } catch (Exception e) {
                    log.error("insertDeviceHistoryRecords error deviceCodes:{}", deviceCodes, e);
                }

                List<DeviceCustomerVo> deviceCustomerVos = deviceMapper.queryDeviceCustomer(deviceCodes);
                if (CollUtil.isNotEmpty(deviceCustomerVos)) {
                    deviceCustomerVos.stream().collect(Collectors.groupingBy(DeviceCustomerVo::getCustomerId)).forEach((customer, deviceCodeList) -> {
                        List<String> codes = deviceCodeList.stream().map(DeviceCustomerVo::getDeviceCode).collect(Collectors.toList());
                        this.sendChangeDeviceStatusService(customer, deviceStatus, codes);
                    });
                }
            }
        }
        return flag;
    }


    @Override
    public DeviceVO selectCustomerByDeviceIp(String deviceIp) {
        return deviceMapper.getCustomer(deviceIp);
    }

    @Override
    public DeviceCustomerVo getDeviceCustomerByDeviceIp(String deviceIp) {
        return this.baseMapper.getDeviceCustomerByDeviceIp(deviceIp,null);
    }

    @Override
    public List<Device> selectByDeviceCodes(SelectByDeviceCodesDTO param) {
        //return deviceMapper.selectList(new QueryWrapper<Device>().in("device_code", param.getDeviceCodes()).eq("delete_flag", ZERO));
        return deviceMapper.listByDeviceCodes(param.getDeviceCodes());
    }

    @Override
    public Device selectByDeviceIp(String deviceIp) {
        return deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_ip", deviceIp).eq("delete_flag", ZERO).last("LIMIT 1"));
    }

    @Override
    public List<ApplyDeviceConnectVO> applyDeviceConnect(ApplyDeviceConnectDTO param) {
        List<String> deviceIps = param.getDeviceIps();
        List<Device> devices = deviceMapper.listByIps(deviceIps);
        if (CollectionUtils.isEmpty(devices) || deviceIps.size() != devices.size()) {
            throw new BasicException(DEVICE_NOT_FOUND_EXCEPTION);
        }

        long customerId = param.getCustomerId();
        List<Long> deviceIds = devices.stream().map(Device::getId).collect(Collectors.toList());
        int findSize = customerDeviceMapper.countNotExpiredByDeviceIdAndCustomerId(customerId, deviceIds);
        if (findSize < deviceIds.size()) {
            throw new BasicException(DEVICE_NOT_FOUND_EXCEPTION);
        }

        List<ApplyDeviceConnectVO> applyDeviceConnectVOS = new ArrayList<>(deviceIps.size());
        List<CustomerConnectDeviceRecord> records = new ArrayList<>(deviceIps.size());
        LocalDateTime expirationDateTime = LocalDateTime.now().plusMinutes(param.getActiveDuration());
        Date expirationTime = Date.from(expirationDateTime.atZone(ZoneId.systemDefault()).toInstant());

        devices.forEach(device -> {
            String deviceIp = device.getDeviceIp();
            DeviceNetTraversalResponse response = deviceNetTraversalManage.traversal(customerId, deviceIp);
            ApplyDeviceConnectVO applyDeviceConnectVO = new ApplyDeviceConnectVO();
            applyDeviceConnectVO.setDeviceIp(deviceIp);
            applyDeviceConnectVO.setCommand(response.getCommand());
            applyDeviceConnectVO.setKey(response.getKey());
            applyDeviceConnectVOS.add(applyDeviceConnectVO);

            CustomerConnectDeviceRecord connectDeviceRecord = new CustomerConnectDeviceRecord();
            connectDeviceRecord.setCustomerId(customerId);
            connectDeviceRecord.setDeviceId(device.getId());
            connectDeviceRecord.setDeviceIp(deviceIp);
            connectDeviceRecord.setConnectInfo(JSON.toJSONString(response));
            connectDeviceRecord.setExpirationTime(expirationTime);
            records.add(connectDeviceRecord);
        });

        applicationContext.getBean(DeviceServiceImpl.class).asyncSaveRecord(records);
        return applyDeviceConnectVOS;
    }

    @Async
    public void asyncSaveRecord(List<CustomerConnectDeviceRecord> records) {
        customerConnectDeviceRecordMapper.batchInsert(records);
    }

    @Override
    public List<GenerateDeviceTaskVO> setDeviceGateway(SetDeviceGatewayDTO param) {
        log.info(">>>>>>>>>>>>>>>>设置板卡网关 param={}", JSON.toJSONString(param));
        long customerId = param.getCustomerId();

        if (CollUtil.isEmpty(param.getDeviceCodes())) {
            throw new BasicException(FAILED_EXECUTE_COMMAND_SET_GATEWAY);
        }
        List<DeviceInfoVo> deviceInfos = new ArrayList<>();
        if (SourceTargetEnum.ADMIN_SYSTEM.getCode().equals(param.getTaskSource())) {
            deviceInfos = deviceMapper.selectDeviceInfoByDeviceCodeDc(param.getDeviceCodes(), null, null);
        }
        if (isEmpty(deviceInfos) || deviceInfos.size() != param.getDeviceCodes().size()) {
            throw new BasicException(CARD_DOES_NOT_EXIST);
        }
        List<Integer> padAllocationStatus = Arrays.asList(ALLOCATING.getStatus(), DELETING.getStatus());
        List<String> errorDeviceIps = deviceInfos.stream().filter(a -> padAllocationStatus.contains(a.getPadAllocationStatus())).map(DeviceInfoVo::getDeviceIp).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errorDeviceIps)) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), JSON.toJSONString(errorDeviceIps) + "板卡存在进行中任务");
        }

        //检测是否有已存在的任务
        List<Integer> status = Arrays.asList(TaskStatusConstants.WAIT_EXECUTE.getStatus(), TaskStatusConstants.EXECUTING.getStatus());
        List<Pad> pads = padMapper.selectPadByDeviceCode(deviceInfos, null);
        if (CollUtil.isNotEmpty(pads)) {
            PadTaskAndStatusDTO padTaskAndStatusDTO = new PadTaskAndStatusDTO();
            padTaskAndStatusDTO.setPads(pads);
            padTaskAndStatusDTO.setStatus(status);
            List<PadTask> tasks = taskService.selectTaskByTaskTypeAndTaskStatus(padTaskAndStatusDTO.getPads(), padTaskAndStatusDTO.getStatus());
            if (CollUtil.isNotEmpty(tasks)) {
                throw new BasicException(PROCESSING_FAILED.getStatus(), "板卡已存在任务，请稍后再试");
            }
        }
        //调用bmc设置板卡网关
        Boolean isPullMode = PullModeConfigHolder.isPullMode(String.valueOf(customerId),null,param.getDeviceCodes().get(0));
        if(isPullMode){
            return pullModeSetDeviceGateway(param, deviceInfos);
        }
        return setDeviceGateway(param, deviceInfos);
    }

    public List<GenerateDeviceTaskVO> setDeviceGateway(SetDeviceGatewayDTO param, List<DeviceInfoVo> deviceInfos) {
        log.info(">>>>>>>>>>>>>>>>BMC设置网关 param={}", JSON.toJSONString(param));
        long customerId = param.getCustomerId();

        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());

        deviceInfos.forEach(deviceInfoVo -> {
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });
        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(SET_GATEWAY.getType());
        addDeviceTaskDTO.setStatus(EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        addDeviceTaskDTO.setTaskSource(param.getTaskSource());
        if (isNotEmpty(param.getUserId())) {
            addDeviceTaskDTO.setCreateBy(customerId + "-" + param.getUserId());
        } else {
            String oprBy = Optional.of(param).map(SetDeviceGatewayDTO::getOprBy).orElse(String.valueOf(customerId));
            addDeviceTaskDTO.setCreateBy(oprBy);
        }
        DeviceNetworkDTO network = new DeviceNetworkDTO();
        network.setGateway(param.getGateway());
        addDeviceTaskDTO.setRemark(JSON.toJSONString(network));

        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));
        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);
        try {
            //根据云机armServerCode分组，组装数据。有几组就循环几次调用重启接口
            Map<String, List<AddDeviceTaskVO>> groupedByArmServerCode = deviceTasks.stream()
                    .collect(Collectors.groupingBy(AddDeviceTaskVO::getArmServerCode));

            groupedByArmServerCode.forEach((armServerCode, tasks) -> {
                List<BmcDeviceNetworkDTO.CardNetworkInfos> bmcList = new ArrayList<>();

                tasks.forEach(task -> {
                    BmcDeviceNetworkDTO.CardNetworkInfos networkInfos = new BmcDeviceNetworkDTO.CardNetworkInfos();
                    networkInfos.setOutTaskId(task.getCustomerTaskId().toString());
                    networkInfos.setTimeOut(TEN);
                    networkInfos.setCardId(task.getDeviceOutCode());
                    networkInfos.setIp(task.getDeviceIp());
                    networkInfos.setDns(task.getDeviceDns());
                    networkInfos.setNetmask(task.getDeviceNetmask());
                    networkInfos.setGateway(param.getGateway());
                    bmcList.add(networkInfos);
                });

                BmcDeviceNetworkDTO bmcDeviceRestartDTO = new BmcDeviceNetworkDTO();
                //根据armServerCode查询对应armServer的ipv4地址
                ArmServer armServer = armServerMapper.selectOne(new QueryWrapper<ArmServer>().eq("arm_server_code", armServerCode));
                String socApiUrl = armServer.getArmBMCApiUri();
                bmcDeviceRestartDTO.setSocApiUrl(socApiUrl);
                bmcDeviceRestartDTO.setCardNetworkInfos(bmcList);


                EdgeClusterVO edgeClusterVO = edgeClusterMapper.selectClusterByArmServerCodeAndStatusAndOnline(armServer.getClusterCode(), ONE, ONE);
                if (ObjectUtil.isNull(edgeClusterVO)) {
                    log.error(">>>>>>>>>>>>>>>>没有可用的边缘集群 armServerCode={},clusterCode={}", armServer.getCode(), armServer.getClusterCode());
                    throw new BasicException(EDGE_CLUSTER_NOT_EXIST);
                }

                String bmcToken = bmcService.getBmcTokenAndSave(null, null, edgeClusterVO.getClusterPublicIp());
                List<BmcTaskInfoVO> bmcTaskInfoVOs = bmcService.setCardNetwork(bmcToken, bmcDeviceRestartDTO, edgeClusterVO.getClusterPublicIp());
                bmcTaskInfoVOs.forEach(bmcTaskInfoVO -> bmcTaskInfoVO.setTaskType(SET_GATEWAY.getType()));

                //保存bmc返回的任务id到deviceTask表中
                log.info("bmcTaskInfoVOs={}", bmcTaskInfoVOs);
                taskService.addDeviceTaskBmcTaskId(bmcTaskInfoVOs);
            });
        } catch (Exception e) {
            log.info(">>>>>>>>>>>>>>>>设置板卡网关调用BMC接口error builder={},e={}", JSON.toJSONString(builder), e);
        }
        //修改物理机状态，发送物理机状态变更回调通知消息
        applicationContext.getBean(DeviceServiceImpl.class).updateDeviceStatusAndSendDeviceStatusCallback(deviceCodes, DEVICE_INIT.getStatus(), customerId);
        //根据devicecode查询出pad，创建padtask任务和devicetask任务。修改pad状态和device状态
        for (String deviceCode : deviceCodes) {
            Device device = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", deviceCode));
            List<DevicePad> devicePads = devicePadMapper.selectList(device.getId());
            if (CollUtil.isEmpty(devicePads)) {
                break;
            }
            List<Long> devicePadIds = devicePads.stream().map(DevicePad::getPadId).collect(Collectors.toList());
            List<Pad> padList = padMapper.selectList(new QueryWrapper<Pad>().in("id", devicePadIds).eq("status", ONE));
            if (CollUtil.isEmpty(padList)) {
                break;
            }
            List<String> padCodes = padList.stream().map(Pad::getPadCode).collect(Collectors.toList());
            //添加pad任务
            AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
            addTaskDTO.setPadCodes(padCodes);

            addTaskDTO.setType(RESTART.getType());
            addTaskDTO.setStatus(EXECUTING.getStatus());
            addTaskDTO.setCustomerId(customerId);
            addTaskDTO.setSourceCode(param.getTaskSource());
            addDeviceTaskDTO.setType(SET_GATEWAY.getType());
            List<AddPadTaskVO> padTasks = taskService.addPadTaskService(addTaskDTO);
            //修改实例状态，发送实例状态变更回调通知消息
            padStatusService.updatePadStatusAndSendPadStatusCallback(padCodes, PadStatusConstant.RESTARTING, customerId, "bmcSetNetwork");
        }
        return builder;
    }

    public List<GenerateDeviceTaskVO> pullModeSetDeviceGateway(SetDeviceGatewayDTO param, List<DeviceInfoVo> deviceInfos) {
        log.info(">>>>>>>>>>>>>>>>BMC设置网关 param={}", JSON.toJSONString(param));
        long customerId = param.getCustomerId();

        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());

        deviceInfos.forEach(deviceInfoVo -> {
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });
        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(SET_GATEWAY.getType());
        addDeviceTaskDTO.setStatus(EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        addDeviceTaskDTO.setTaskSource(param.getTaskSource());
        if (isNotEmpty(param.getUserId())) {
            addDeviceTaskDTO.setCreateBy(customerId + "-" + param.getUserId());
        } else {
            String oprBy = Optional.of(param).map(SetDeviceGatewayDTO::getOprBy).orElse(String.valueOf(customerId));
            addDeviceTaskDTO.setCreateBy(oprBy);
        }
        DeviceNetworkDTO network = new DeviceNetworkDTO();
        network.setGateway(param.getGateway());
        addDeviceTaskDTO.setRemark(JSON.toJSONString(network));

        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));
        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);

        for(AddDeviceTaskVO addDeviceTaskVO : deviceTasks){
            BmcDeviceNetworkDTO.CardNetworkInfos networkInfos = new BmcDeviceNetworkDTO.CardNetworkInfos();
            networkInfos.setOutTaskId(addDeviceTaskVO.getCustomerTaskId().toString());
            networkInfos.setTimeOut(TEN);
            networkInfos.setCardId(addDeviceTaskVO.getDeviceOutCode());
            networkInfos.setIp(addDeviceTaskVO.getDeviceIp());
            networkInfos.setDns(addDeviceTaskVO.getDeviceDns());
            networkInfos.setNetmask(addDeviceTaskVO.getDeviceNetmask());
            networkInfos.setGateway(param.getGateway());

            taskQueueManager.addDeviceTaskPullMode(TaskTypeAndChannelEnum.SET_GATEWAY,addDeviceTaskVO,networkInfos, new Date());
        }
        return builder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Device updateDeviceByCode(Device device) {
        log.info("updateDeviceByCode device:{}", device);
        Device oldDevice = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", device.getDeviceCode()).eq("delete_flag", ZERO));
        if (ObjectUtil.isNull(oldDevice)) {
            return null;
        }
        device.setId(oldDevice.getId());
        deviceMapper.updateById(device);
        return device;
    }

    @Override
    public List<DeviceInfoVo> getDeviceMountVersionV1() {
        return  deviceMapper.getDeviceMountVersionV1();
    }

    @Override
    public Page<DeviceQueryDTO> listByDeviceQueryDTO(DeviceQueryListVo param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<DeviceQueryDTO> list = deviceMapper.listByDeviceQueryDTO(param);
        return new Page<>(list);
    }

    /**
     * cbs更新
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GenerateDeviceTaskVO> cbsUpdate(DeviceCbsUpdateDTO param) {
        //业务校验
        log.info(">>>>>>>>>>>>>>>>cbs更新 param={}", JSON.toJSONString(param));
        if (CollUtil.isEmpty(param.getDeviceIps())) {
            throw new BasicException(DEVICE_IP_NOT_EXIST);
        }
        List<DeviceDestroyVO> devices = deviceMapper.selectCanDeviceByDeviceIpAndCbsUpdate(param.getDeviceIps());
        if (CollUtil.isEmpty(devices)) {
            throw new BasicException(DEVICE_IP_NOT_EXIST);
        }

        List<String> deviceCodes = new ArrayList<>(devices.size());
        devices.stream().forEach(deviceInfoVo -> {
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });

        //添加device task 但不会由paas-job执行  仅是记录
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(CBS_SELF_UPDATE.getType());
        addDeviceTaskDTO.setStatus(EXECUTING.getStatus());
        addDeviceTaskDTO.setCustomerId(param.getCustomerId());
        addDeviceTaskDTO.setTaskSource(param.getSourceCode());
        addDeviceTaskDTO.setCreateBy(param.getOprBy());
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>添加cbs更新 device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));

        //发送cms任务
        HashMap<String, SelfUpdateResponse> deviceDestroyResponseMap = new HashMap<>();
        devices.stream().collect(Collectors.groupingBy(DeviceDestroyVO::getClusterPublicIp)).forEach((clusterPublicIp, deviceList) -> {
            List<String> deviceIps = deviceList.stream().map(o -> o.getDeviceIp()).collect(Collectors.toList());
            SelfUpdateRequest req = new SelfUpdateRequest();
            req.setDeviceIps(deviceIps);
            req.setCbsFileUrl(param.getCbsFileUrl());
            req.setCbsVersion(param.getCbsVersion());
            URI deviceHost = builderHost(clusterPublicIp);
            try {
                log.info(">>>>>>>>>>>>>>>>调用CMS cbs更新,req={}", JSON.toJSONString(req));
                List<SelfUpdateResponse> virtualizeResponseList = FeignUtils.getContent(containerDeviceFeignClient.selfUpdate(deviceHost, req), CONTAINER_DEVICE_CBS_UPDATE_FAILED, req);
                for (SelfUpdateResponse selfUpdateResponse : virtualizeResponseList) {
                    deviceDestroyResponseMap.put(selfUpdateResponse.getDeviceIp(), selfUpdateResponse);
                }
            } catch (Exception e) {
                log.error(">>>>>>>>>>>>>>>>调用CMS cbs更新失败,req={},e={}", JSON.toJSONString(req), e);
            }
        });

        for (AddDeviceTaskVO deviceTask : deviceTasks) {
            if (deviceDestroyResponseMap.isEmpty() || isEmpty(deviceDestroyResponseMap.get(deviceTask.getDeviceIp()))) {
                deviceTask.setSubTaskStatus(FAIL_ALL.getStatus());
                deviceTask.setErrorMsg("cbs update failed");
                deviceCodes.remove(deviceTask.getDeviceCode());
            } else {
                SelfUpdateResponse selfUpdateResponse = deviceDestroyResponseMap.get(deviceTask.getDeviceIp());
                deviceTask.setContainerTaskId(selfUpdateResponse.getMasterTaskId());
                deviceTask.setSubTaskStatus(selfUpdateResponse.getMasterTaskStatus());
            }
        }
        //回写任务结果
        taskService.updateDeviceTaskResult(deviceTasks);
        return GenerateDeviceTaskVO.builder(deviceTasks);
    }

    @Override
    public List<DeviceVO> getDeviceInfo(List<String> deviceIps) {
        return deviceMapper.getDeviceInfo(deviceIps);
    }

    @Override
    public List<DeviceVO> getDeviceInfoByCode(List<String> deviceCodes) {
        return deviceMapper.getDeviceInfoByCode(deviceCodes);
    }

    @Override
    public List<String> selectPadByDeviceCode(List<String> deviceCodes) {
        return deviceMapper.selectPadByDeviceCode(deviceCodes);
    }

    @Override
    public int selectTaskByTaskTypeAndTaskStatus(List<String> pads, List<Integer> status) {
        return deviceMapper.selectTaskByTaskTypeAndTaskStatus(pads, status);
    }

    @Override
    public DeviceCustomerVo getDeviceCustomerByDeviceIpAndClusterCode(String deviceIp, String clusterCode) {
        return this.baseMapper.getDeviceCustomerByDeviceIp(deviceIp,clusterCode);
    }

    @Override
    public void updateAllocationStatus(String deviceCode,Integer allocationStatus) {
        Device deviceInfo = new Device();
        deviceInfo.setPadAllocationStatus(allocationStatus);
        deviceInfo.setUpdateTime(new Date());
        deviceMapper.update(deviceInfo, new QueryWrapper<Device>().lambda().eq(Device::getDeviceCode, deviceCode));
    }

    @Override
    public Page<Device> netDeviceLevelList(NetDeviceLevelDTO req) {
        PageHelper.startPage(req.getPage(), req.getRows());
        return new Page<>(deviceMapper.netDeviceLevelList(req));
    }

    private void cbsSelfUpdateDeviceTaskResult(ContainerTaskResultVO dto) {
        if (TaskStatusConstants.SUCCESS.getStatus().equals(dto.getMasterTaskStatus())) {
            //更新cbs版本号
            if(StrUtil.isNotEmpty(dto.getData())){
                try{
                    JSONObject jsonObject = JSONObject.parseObject(dto.getData());
                    String cbsVersion = jsonObject.getOrDefault("cbsVersion","") + "";
                    if(StrUtil.isNotEmpty(cbsVersion)){
                        Device device = deviceMapper.selectOne(new QueryWrapper<>(Device.class).select("id").eq("device_ip",dto.getDeviceIp()).last("limit 1"));
                        if(device != null){
                            Device deviceUpdate = new Device();
                            deviceUpdate.setId(device.getId());
                            deviceUpdate.setCbsInfo(cbsVersion);
                            deviceUpdate.setUpdateTime(new Date());
                            deviceMapper.updateById(deviceUpdate);
                        }
                    }
                }catch (Exception e){
                    log.error("cbsSelfUpdateDeviceTaskResult error:",e);
                }
            }
        }
    }

    /**
     * 验证实例规格是否存在
     * @param deviceLevel
     * @return
     */
    private Boolean checkDeviceLevel(String deviceLevel) {
        ResourceSpecification resourceSpecification = resourceSpecificationMapper.selectOne(new QueryWrapper<ResourceSpecification>().lambda()
                .eq(ResourceSpecification::getSpecificationCode, deviceLevel)
                .eq(ResourceSpecification::getDeleteFlag, ZERO)
                .eq(ResourceSpecification::getStatus, ONE)
                .last("LIMIT 1"));
        return Objects.isNull(resourceSpecification);
    }

    @Resource
    private NetStorageComputeUnitService netStorageComputeUnitService;

    /**
     *  设置板卡规格
     *  先验证对应的规格是否存在,再验证当前板卡下的算力单元是否有实例在运行. 验证不通过抛出异常
     *  先删除板卡的算力单元,再创建新的,再修改板卡的规格信息
     *  TODO 添加操作日志记录
     * @param param
     * @return
     */
    @Override
    public String setDeviceLevel(DeviceLevelDTO param) {
        Boolean checkDeviceLevel = this.checkDeviceLevel(param.getDeviceLevel());
        if(checkDeviceLevel){
            throw  new BasicException(PadExceptionCode.NETWORK_INSTANCE_DEVICE_LEVEL_NOT_EXIST_REQUIRED_MESSAGE);
        }
        List<String> deviceCodeList = deviceMapper.selectDeviceOutCodeByDeviceCode(param.getDeviceCodes(), param.getCustomerId());
        if(deviceCodeList.size() != param.getDeviceCodes().size()){
            throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_DEVICE_CODE_NOT_EXIST_REQUIRED_MESSAGE);
        }
        Boolean unitIsBind = netStorageComputeUnitService.NetStorageComputeUnitIsBind(param.getDeviceCodes());
        if(unitIsBind){
            throw new BasicException(NETWORK_INSTANCE_UPDATE_HAS_RUNNING);
        }
        //删除算力
        netStorageComputeUnitService.deleteNetStorageComputeUnitByDeviceCodeList(param.getDeviceCodes());
        // 清除算力相关缓存
        netPadV2ComputeMatchService.removeDeviceCodes(param.getDeviceCodes());
        SelectByDeviceCodesDTO deviceCodesDTO = new SelectByDeviceCodesDTO();
        deviceCodesDTO.setDeviceCodes(param.getDeviceCodes());
        List<Device> deviceList = selectByDeviceCodes(deviceCodesDTO);
        //创建归属于该板卡新的算力
        deviceMapper.updateDeviceLevelByDeviceCode(param.getDeviceCodes(),param.getDeviceLevel());
        netStorageComputeUnitService.createNetStorageComputeUnitByDeviceCodeList(deviceList,param.getDeviceLevel());
        return StringUtils.EMPTY;

    }

    @Override
    public List<GenerateDeviceTaskVO> boardImageWarmup(DeviceBoardImageWarmupDTO param,SourceTargetEnum sourceTargetEnum) {
         //获取所有网存的板卡
        List<Device> devices = deviceMapper.selectListByCustomerId(param);
        LambdaQueryWrapper<BoardImageWarmup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BoardImageWarmup::getCustomerId,param.getCustomerId());
        //获取所有配置(目前只会配网存集群,这里直接取所有的镜像id)
        List<BoardImageWarmup> boardImageWarmups = boardImageWarmupMapper.selectList(wrapper);
        List<String> imageIdList = boardImageWarmups.stream()
                .map(BoardImageWarmup::getImageId)  // 提取 imageId
                .distinct()  // 去重
                .limit(3)  // 只保留前三个
                .collect(Collectors.toList());  // 收集成列表
        if(CollectionUtils.isEmpty(devices) || CollectionUtils.isEmpty(imageIdList)){
            throw new BasicException(NETWORK_DEVICE_BOARD_IMAGE_EMPTY);
        }
        String harborServer = edgeClusterConfigurationMapper.queryEdgeClusterConfigurationByKey( param.getClusterCode(),SystemConfigurationConstants.HARBOR_SERVER);
        String harborProjectName = edgeClusterConfigurationMapper.queryEdgeClusterConfigurationByKey( param.getClusterCode(),SystemConfigurationConstants.HARBOR_PROJECT_NAME);
        String harborProxyName = edgeClusterConfigurationMapper.queryEdgeClusterConfigurationByKey(param.getClusterCode(),SystemConfigurationConstants.HARBOR_PROJECT_PROXY_NAME );
        String harborUserName = edgeClusterConfigurationMapper.queryEdgeClusterConfigurationByKey(param.getClusterCode(),SystemConfigurationConstants.HARBOR_AUTH_USERNAME);
        String harborAuthPassWord = edgeClusterConfigurationMapper.queryEdgeClusterConfigurationByKey(param.getClusterCode(),SystemConfigurationConstants.HARBOR_AUTH_PASSWORD);
        String harborAddr = harborServer + "/" + harborProxyName + "/" + harborProjectName;

        List<BoardImageWarmupDTO> imageDownUrlList = imageIdList.stream().map(imageId ->BoardImageWarmupDTO.builder()
                .url(harborAddr + "/" + imageId).harborUserName(harborUserName).harborAuthPassWord(harborAuthPassWord).sleepTime(0L)
                .build()).collect(Collectors.toList());
        log.info("{} boardImageWarmup harborAddr:{} ,imageDownUrlList:{}",this.getClass().getName(),harborAddr,JSONObject.toJSONString(imageDownUrlList));
        List<String> deviceCodes = devices.stream().map(Device::getDeviceCode).collect(Collectors.toList());
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        //预热镜像
        addDeviceTaskDTO.setType(BOARD_IMAGE_WARMUP.getType());
        addDeviceTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addDeviceTaskDTO.setCustomerId(param.getCustomerId());
        addDeviceTaskDTO.setTaskSource(sourceTargetEnum.getCode());
        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>boardImageWarmup_添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));
        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);

        long delayStep = 150; // 每组递增的延迟时间（单位：秒）
        if(Objects.nonNull(param.getSleepTime())){
            delayStep = param.getSleepTime();
        }
        int counter = 0;     // 计数器，用来追踪当前处理了多少个任务
        long delayTime = 0;   // 初始延迟时间（单位：秒）
        int batchSize = 40;  // 每组的大小
        if(Objects.nonNull(param.getBatchSize())){
            batchSize = param.getBatchSize();
        }

        //按照板卡ip维度 生成子任务
        for(AddDeviceTaskVO addDeviceTaskVO : deviceTasks){
            if (counter % batchSize == 0 && counter > 0) {
                // 每40个任务，延迟时间递增150秒
                delayTime += delayStep;
            }
            counter++;
            //设置延迟执行时间
            for (BoardImageWarmupDTO imageDownUrl : imageDownUrlList) {
                imageDownUrl.setSleepTime(delayTime);
            }
            log.info("boardImageWarmup_add device task deviceTasksId={},imageDownUrlList:{} ,customerId:{}", addDeviceTaskVO.getSubTaskId(), JSONObject.toJSONString(imageDownUrlList),param.getCustomerId());
            taskQueueManager.addDeviceTaskPullMode(TaskTypeAndChannelEnum.BOARD_IMAGE_WARMUP,addDeviceTaskVO,JSON.toJSONString(imageDownUrlList), new Date());
        }
        return builder;
    }

    @Override
    public void updateCbsInfoById(Long id, String version) {
        log.info("Device updateCbsInfoById id:{},version:{}", id, version);
        LambdaUpdateWrapper<Device> lam = new LambdaUpdateWrapper<>();
        lam.eq(Device::getId, id);
        lam.set(Device::getCbsInfo, version);
        lam.set(Device::getUpdateTime, new Date());
        this.update(lam);
    }

    /**
     * 为每个服务生成一位随机数
     */
    private void generatePadCodeFirst(){
        //这里需要加全局锁(这里执行很快 且为低频调用)
        String lockKey = RedisKeyPrefix.NEW_PAD_CODE_RAMDOM_FIRST_LOCK;
        RLock lock = redissonDistributedLock.mustLocked(lockKey);
        try {
            String nowDate = DateUtil.format(new Date(),"yyyyMMdd");
            String redisCacheKey = RedisKeyPrefix.NEW_PAD_CODE_RAMDOM_FIRST + nowDate + ":";
            String serverCacheKey = redisCacheKey + ServerIdUtil.getServerUniqueNo();
            if(redisService.getCacheObject(serverCacheKey) != null){
                return;
            }
            String nowFirstRamDom = null;
            for(int i=0;i<5;i++){
                //先生成一位随机数 去redis中比较是否冲突
                String firstRamDom = PadCodeRamdomUtil.generateRandomString(1);
                Boolean exist = redisService.setIfAbsentExpire(redisCacheKey + firstRamDom,"",1L,TimeUnit.DAYS);
                if(exist){
                    nowFirstRamDom = firstRamDom;
                    break;
                }
            }
            //然后缓存到当前服务器唯一标识key的redis缓存
            if(StrUtil.isNotEmpty(nowFirstRamDom)){
                redisService.setCacheObject(serverCacheKey,nowFirstRamDom,1L,TimeUnit.DAYS);
            }else{
                throw new BasicException(PAD_CODE_GENERATE_FAIL);
            }
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }

    private Result<List<GenerateDeviceTaskVO>> pullModePowerResetCMS(PowerResetDTO param, long customerId, List<DeviceInfoVo> deviceInfos) {
        List<String> deviceCodes = new ArrayList<>(deviceInfos.size());
        deviceInfos.stream().forEach(deviceInfoVo -> {
            deviceCodes.add(deviceInfoVo.getDeviceCode());
        });

        //添加device任务
        AddDeviceTaskDTO addDeviceTaskDTO = new AddDeviceTaskDTO();
        addDeviceTaskDTO.setDeviceCodes(deviceCodes);
        addDeviceTaskDTO.setType(DEVICE_RESTART.getType());
        addDeviceTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
        addDeviceTaskDTO.setCustomerId(customerId);
        addDeviceTaskDTO.setTaskSource(param.getTaskSource());
        addDeviceTaskDTO.setPullMode(true);
        if (isNotEmpty(param.getUserId())) {
            addDeviceTaskDTO.setCreateBy(customerId + "-" + param.getUserId());
        } else {
            String oprBy = Optional.of(param).map(PowerResetDTO::getOprBy).orElse(String.valueOf(customerId));
            addDeviceTaskDTO.setCreateBy(oprBy);
        }

        List<AddDeviceTaskVO> deviceTasks = taskService.addDeviceTaskService(addDeviceTaskDTO);
        log.info(">>>>>>>>>>>>>>>>拉模式添加device任务成功 deviceTasksId={}", deviceTasks.stream().map(AddDeviceTaskVO::getSubTaskId).collect(Collectors.toList()));

        List<GenerateDeviceTaskVO> builder = GenerateDeviceTaskVO.builder(deviceTasks);
        if (CollUtil.isEmpty(deviceCodes)) {
            return Result.ok(builder);
        }

        //按照板卡ip维度 生成子任务
        for(AddDeviceTaskVO addDeviceTaskVO : deviceTasks){
            DeviceIpsRequest req = new DeviceIpsRequest();
            req.setDeviceIps(Collections.singletonList(addDeviceTaskVO.getDeviceIp()));
            taskQueueManager.addDeviceTaskPullMode(TaskTypeAndChannelEnum.DEVICE_RESTART,addDeviceTaskVO,req, new Date());
        }

        //根据devicecode查询出pad，创建padtask任务和devicetask任务。修改pad状态和device状态
        for (String deviceCode : deviceCodes) {
            Device device = deviceMapper.selectOne(new QueryWrapper<Device>().eq("device_code", deviceCode));
            List<DevicePad> devicePads = devicePadMapper.selectList(device.getId());
            if (CollUtil.isEmpty(devicePads)) {
                break;
            }
            List<Long> devicePadIds = devicePads.stream().map(DevicePad::getPadId).collect(Collectors.toList());
            List<Pad> padList = padMapper.selectList(new QueryWrapper<Pad>().in("id", devicePadIds).eq("status", ONE));
            if (CollUtil.isEmpty(padList)) {
                break;
            }
            List<String> padCodes = padList.stream().map(Pad::getPadCode).collect(Collectors.toList());
            //添加pad任务
            AddPadTaskDTO addTaskDTO = new AddPadTaskDTO();
            addTaskDTO.setPadCodes(padCodes);
            addTaskDTO.setType(RESTART.getType());
            addTaskDTO.setStatus(WAIT_EXECUTE.getStatus());
            addTaskDTO.setCustomerId(customerId);
            addTaskDTO.setSourceCode(param.getTaskSource());
            addDeviceTaskDTO.setType(DEVICE_RESTART.getType());
            if(PullModeConfigHolder.isPullMode(String.valueOf(customerId),null,deviceInfos.get(0).getDeviceCode())){
                //这里不再发送实例重启任务
                //taskService.addPadTaskServicePullMode(addTaskDTO);
            }else{
                taskService.addPadTaskService(addTaskDTO);
            }
        }
        return Result.ok(builder);
    }

     @Override
     public List<AddDeviceTaskVO> pullModeAddDeviceTask(AddDeviceTaskDTO addDeviceTaskDTO){
         TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(addDeviceTaskDTO.getType());
         List<AddDeviceTaskVO> addDeviceTaskVOs = null;
         if(taskTypeAndChannelEnum != null){
             addDeviceTaskDTO.setPullMode(true);
             //添加device任务
             addDeviceTaskVOs = taskService.addDeviceTaskService(addDeviceTaskDTO);

             if(CollUtil.isNotEmpty(addDeviceTaskVOs)){
                 Map<String,Object> map = JSON.parseObject(addDeviceTaskDTO.getTaskContent());
                 for(AddDeviceTaskVO addDeviceTaskVO : addDeviceTaskVOs){
                     addDeviceTaskVO.setArmIp(map.get("armIp")+"");
                     addDeviceTaskVO.setClusterCode(map.get("clusterCode")+"");
                     taskQueueManager.addDeviceTaskPullMode(taskTypeAndChannelEnum,addDeviceTaskVO,addDeviceTaskDTO.getRequestParam(), new Date());
                 }
             }
         }
         return addDeviceTaskVOs;
     }


}