package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ScreenLayoutVO implements Serializable {
    /**账号ID*/
    private Long id;
    /**屏幕布局编码*/
    private String code;
    /**屏幕宽度*/
    private String screenWidth;
    /**屏幕高度*/
    private String screenHigh;
    /**像素密度*/
    private String pixelDensity;
    /**屏幕刷新率*/
    private String screenRefreshRate;

    /**
     * 状态 0-停用 1-启用
     */
    private Integer status;

    /**
     * 是否删除（1：已删除；0：未删除）
     */
    private Integer deleteFlag;

}
