package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.PadPropertiesKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PadPropertiesKeyMapper extends BaseMapper<PadPropertiesKey> {

    /**
     * 根据属性类型查询属性key
     *
     * @param propertiesType 属性类型
     * @return 属性key
     */
    List<String> selectPropertiesKeyByType(@Param("propertiesType") Integer propertiesType);
}
