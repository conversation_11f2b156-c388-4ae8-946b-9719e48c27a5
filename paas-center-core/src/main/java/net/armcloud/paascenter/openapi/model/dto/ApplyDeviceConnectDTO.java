package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ApplyDeviceConnectDTO extends BaseDTO {

    @Size(min = 1, message = "deviceIps cannot null")
    @NotNull(message = "deviceIps cannot null")
    @ApiModelProperty(value = "板卡IP列表")
    private List<String> deviceIps;

    @Min(value = 1, message = "activeDuration value illegal")
    @NotNull(message = "activeDuration cannot null")
    @ApiModelProperty(value = "连接凭证有效时长")
    private Integer activeDuration;
}
