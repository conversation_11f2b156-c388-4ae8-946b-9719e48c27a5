package net.armcloud.paascenter.openapi.controller.internal;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.facade.DeviceInternalFacade;
import net.armcloud.paascenter.common.client.internal.vo.DeviceVO;
import net.armcloud.paascenter.common.client.internal.vo.GenerateDeviceTaskVO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.vo.api.ContainerTaskResultVO;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.task.service.IDeviceTaskService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
public class DeviceInternalController implements DeviceInternalFacade {

    @Resource
    private IDeviceService deviceService;
    @Resource
    private IDeviceTaskService deviceTaskService;


    public Result<?> deviceRestart(DeviceRestartDTO param) {
        deviceService.deviceRestart(param);
        return Result.ok();
    }

    public Result<Device> selectByPadCode(String padCode) {
        return Result.ok(deviceService.selectByPadCode(padCode));
    }

    @Override
    public Result<?> powerReset(PowerResetDTO param) {
        deviceService.powerReset(param);
        return Result.ok();
    }

    @Override
    public Result<Boolean> updateDeviceStatusAndSendDeviceStatusCallback(SendDeviceStatusDTO param) {
        return Result.ok(deviceService.updateDeviceStatusAndSendDeviceStatusCallback(param.getDeviceCodes(), param.getDeviceStatus(), param.getCustomerId()));
    }

    @Override
    public Result<Device> selectByDeviceCode(String deviceCode) {
        return Result.ok(deviceService.selectByDeviceCode(deviceCode));
    }

    @Override
    public Result<Device> selectByDeviceIp(String deviceIp) {
        return Result.ok(deviceService.selectByDeviceIp(deviceIp));
    }

    @Override
    public Result<List<Device>> selectByDeviceCodes(@RequestBody @Valid SelectByDeviceCodesDTO param) {
        return Result.ok(deviceService.selectByDeviceCodes(param));
    }

    @Override
    public Result<List<GenerateDeviceTaskVO>> virtualize(@RequestBody @Valid VirtualizeDeviceDTO param) {
        return Result.ok(deviceService.virtualize(param));
    }

    @Override
    public Result<List<GenerateDeviceTaskVO>> deviceDestroy(@RequestBody @Valid DeviceDestroyDTO param) {
        return Result.ok(deviceService.deviceDestroy(param));
    }

    @Override
    public Result<?> callbackUpdateDevice(DeviceInfoDTO param) {
        deviceService.callbackUpdateDevice(param);
        return Result.ok();
    }

    @Override
    public Result<?> ContainerDeviceTaskResult(@RequestBody ContainerTaskResultVO containerTaskResult) {
        deviceService.ContainerDeviceTaskResult(containerTaskResult);
        return Result.ok();
    }

    @Override
    public Result<?> callbackDeviceTakes(CallbackDeviceTakes param) {
        deviceTaskService.callbackDeviceTakes(param);
        return Result.ok();
    }

    @Override
    public Result<?> setGateway(SetDeviceGatewayDTO param) {
        deviceService.setDeviceGateway(param);
        return Result.ok();
    }

    @Override
    public Result<Device> updateDeviceByCode(Device device) {
        return Result.ok(deviceService.updateDeviceByCode(device));
    }

    @Override
    public Result<DeviceVO> selectCustomerByDeviceIp(String deviceIp) {
        return Result.ok(deviceService.selectCustomerByDeviceIp(deviceIp));
    }

    @Override
    public Result<String> setDeviceLevel(DeviceLevelDTO param) {
        return Result.ok(deviceService.setDeviceLevel(param));
    }
    @Override
    public Result<?> boardImageWarmup(DeviceBoardImageWarmupDTO deviceBoardImageWarmupDTO) {
        return Result.ok(deviceService.boardImageWarmup(deviceBoardImageWarmupDTO, SourceTargetEnum.ADMIN_SYSTEM));
    }

}
