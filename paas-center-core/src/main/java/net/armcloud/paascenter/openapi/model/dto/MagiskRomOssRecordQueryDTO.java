package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Magisk ROM OSS记录查询请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@ApiModel(value = "MagiskRomOssRecordQueryDTO", description = "Magisk ROM OSS记录查询请求")
public class MagiskRomOssRecordQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 包版本
     */
    @ApiModelProperty(value = "包版本", example = "v1.0.0")
    private String version;
}
