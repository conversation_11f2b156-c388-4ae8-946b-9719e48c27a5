package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.AdiCustomerRecord;
import net.armcloud.paascenter.openapi.mapper.AdiCustomerRecordMapper;
import net.armcloud.paascenter.openapi.service.IAdiCustomerRecordService;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AdiCustomerRecordServiceImpl extends ServiceImpl<AdiCustomerRecordMapper, AdiCustomerRecord> implements IAdiCustomerRecordService {

   public void add(AdiCustomerRecord adiCustomerRecord) {
       log.info("adiCustomerRecord:{}", adiCustomerRecord);
       this.save(adiCustomerRecord);
   }
}
