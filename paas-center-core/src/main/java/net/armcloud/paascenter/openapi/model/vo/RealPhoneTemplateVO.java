package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import java.util.Objects;

@Data
public class RealPhoneTemplateVO extends RealPhoneTemplate {
    @Data
    public static class TemplateUniqueKey {
        private String brand;
        private String model;
        private String fingerprintMd5;
        private String resourceSpecificationCode;
        private String deviceName;


        public static TemplateUniqueKey build(RealPhoneTemplateVO template) {
            TemplateUniqueKey key = new TemplateUniqueKey();
            key.setBrand(template.getBrand());
            key.setModel(template.getModel());
            key.setResourceSpecificationCode(template.getResourceSpecificationCode());
            key.setFingerprintMd5(template.getFingerprintMd5());
            key.setDeviceName(template.getDeviceName());
            return key;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TemplateUniqueKey that = (TemplateUniqueKey) o;
            return Objects.equals(brand, that.brand) &&
                   Objects.equals(model, that.model) &&
                   Objects.equals(fingerprintMd5, that.fingerprintMd5) &&
                   Objects.equals(deviceName, that.deviceName) &&
                   Objects.equals(resourceSpecificationCode, that.resourceSpecificationCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(brand, model, deviceName, fingerprintMd5, resourceSpecificationCode);
        }
    }
}
