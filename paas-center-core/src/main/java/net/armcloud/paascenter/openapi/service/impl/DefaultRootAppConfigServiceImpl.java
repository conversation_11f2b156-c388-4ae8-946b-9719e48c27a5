package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.mapper.DefaultRootAppConfigMapper;
import net.armcloud.paascenter.openapi.model.vo.DefaultRootAppVO;
import net.armcloud.paascenter.openapi.service.IDefaultRootAppConfigService;
import net.armcloud.paascenter.common.model.entity.paas.DefaultRootAppConfig;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 默认root应用
 */
@Service
public class DefaultRootAppConfigServiceImpl extends ServiceImpl<DefaultRootAppConfigMapper, DefaultRootAppConfig> implements IDefaultRootAppConfigService {

    @Resource
    private RedisService redisService;
    /**
     * 默认root应用
     */
    @Override
    public DefaultRootAppVO getDefaultRootAppList() {
        DefaultRootAppVO defaultRootAppVO = new DefaultRootAppVO();
        String key = RedisKeyPrefix.DEFAULT_ROOT_APP_CONFIG;
        Object cacheObj = redisService.getCacheObject(key);
        if(Objects.nonNull(cacheObj)){
            return JSON.parseObject(cacheObj.toString(),DefaultRootAppVO.class);
        }
        List<DefaultRootAppConfig> defaultRootAppConfigList = baseMapper.selectList(new QueryWrapper<>(DefaultRootAppConfig.class).select("app_pkg").eq("status",1));
        List<String> appPkgList = new ArrayList<>();
        if(CollUtil.isNotEmpty(defaultRootAppConfigList)){
            Set<String> appPkgSet = defaultRootAppConfigList.stream().map(DefaultRootAppConfig::getAppPkg).collect(Collectors.toSet());
            appPkgList = new ArrayList<>(appPkgSet);
        }
        defaultRootAppVO.setRootlists(appPkgList);
        redisService.setCacheObject(key, JSON.toJSONString(defaultRootAppVO),24L, TimeUnit.HOURS);
        return defaultRootAppVO;
    }
}
