package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paascenter.openapi.mapper.ScreenLayoutMapper;
import net.armcloud.paascenter.openapi.model.vo.ScreenLayoutVO;
import net.armcloud.paascenter.openapi.service.IScreenLayoutService;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ScreenLayoutServiceImpl implements IScreenLayoutService {

    private final ScreenLayoutMapper screenLayoutMapper;

    public ScreenLayoutServiceImpl(ScreenLayoutMapper screenLayoutMapper){
        this.screenLayoutMapper = screenLayoutMapper;
    }


    @Override
    public List<ScreenLayoutVO> list() {
        List<ScreenLayout> screenLayoutList = screenLayoutMapper.selectList(new QueryWrapper<>(ScreenLayout.class).eq("status",1).eq("delete_flag",0).isNull("customer_id"));
        List<ScreenLayoutVO> screenLayoutVOList = null;
        if(CollectionUtils.isNotEmpty(screenLayoutList)){
            screenLayoutVOList = BeanUtil.copyToList(screenLayoutList,ScreenLayoutVO.class);
        }
        return screenLayoutVOList;
    }

    @Override
    public ScreenLayoutVO getByCode(String code) {
        ScreenLayout screenLayout = screenLayoutMapper.selectOne(new QueryWrapper<>(ScreenLayout.class).eq("code", code).eq("status",1).eq("delete_flag",0));
        return BeanUtil.copyProperties(screenLayout,ScreenLayoutVO.class);
    }

    @Override
    public void addScreenLayout(ScreenLayoutVO screenLayoutvo,String customer) {
        ScreenLayout screenLayout = BeanUtil.copyProperties(screenLayoutvo,ScreenLayout.class);
        screenLayout.setRemarks("云真机分辨率");
        screenLayout.setCreateTime(new Date());
        screenLayout.setUpdateBy(customer);
        screenLayout.setCreateBy(customer);
        screenLayout.setUpdateTime(new Date());
        screenLayout.setStatus(1);
        screenLayoutMapper.insert(screenLayout);
    }
}
