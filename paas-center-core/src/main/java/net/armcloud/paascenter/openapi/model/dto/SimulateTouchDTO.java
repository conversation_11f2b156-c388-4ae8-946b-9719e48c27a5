package net.armcloud.paascenter.openapi.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 模拟触控cmd请求参数
 */
@Data
public class SimulateTouchDTO {

    private Long customerId;
    /**实例编号 必填*/
    @Size(min = 1,max = 200,message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;
    /**容器宽度 必填*/
    @NotNull(message = "width cannot null")
    private Integer width;
    /**容器高度 必填*/
    @NotNull(message = "height cannot null")
    private Integer height;
    /**点击坐标组*/
    @Size(min = 1,message = "点击坐标组数量不少于1个")
    @NotNull(message = "positions cannot null")
    private List<Position> positions;


    @Data
    @Valid
    public static class Position {
        /**操作类型 0按下 1抬起 2触摸中*/
        @NotNull(message = "actionType cannot null")
        private Integer actionType;
        /**点击的x坐标*/
        @NotNull(message = "x cannot null")
        private Float x;
        /**点击的y坐标*/
        @NotNull(message = "y cannot null")
        private Float y;
        /**多组坐标时，触发下一组点击坐标的等待间隔时间ms毫秒值*/
        private Integer nextPositionWaitTime;

        /**滚动距离  -1 下划  1 上划   */
        private Float swipe;

        /**事件   gestureSwipe 划动事件 gesture  触控事件 keystroke  按键事件 其他或不传默认按下抬起*/
        private String touchType;

        /**按键的code */
        private Integer keyCode;

    }

}
