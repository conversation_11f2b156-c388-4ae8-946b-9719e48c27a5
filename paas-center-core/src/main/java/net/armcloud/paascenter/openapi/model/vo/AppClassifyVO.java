package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 黑白名单列表查询响应对象
 */
@Data
public class AppClassifyVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;
    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    private Integer classifyType;
    @ApiModelProperty(value = "应用数量")
    private Integer appNum;
    @ApiModelProperty(value = "描述")
    private String remark;
    @ApiModelProperty(value = "是否应用所有实例")
    private Boolean applyAllInstances;
}
