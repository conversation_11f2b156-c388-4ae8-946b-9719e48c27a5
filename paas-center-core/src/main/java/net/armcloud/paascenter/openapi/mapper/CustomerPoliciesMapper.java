package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.common.model.entity.paas.CustomerPolicies;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CustomerPoliciesMapper {
    CustomerPolicies selectByPrimaryKey(Long id);

    List<CustomerPolicies> getByCustomerIdAndType(@Param("customerId") long customerId);
}