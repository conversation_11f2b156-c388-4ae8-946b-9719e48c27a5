package net.armcloud.paascenter.openapi.model.dto.netstorage;

import io.swagger.annotations.ApiModelProperty;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/4/25 1:19
 * @Description:
 */
public class NetStorageCreateDTO {
    @ApiModelProperty(value = "集群编码")
    @NotBlank(message = "clusterCode cannot null")
    private String clusterCode;

    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "SOC型号")
    private String socModel;



    @ApiModelProperty(value = "实例规格编码")
    @NotBlank(message = "specificationCode cannot null")
    private String specificationCode;

    @ApiModelProperty(value = "镜像ID")
    @NotBlank(message = "imageId cannot null")
    private String imageId;

    @ApiModelProperty(value = "屏幕布局编码")
    @NotBlank(message = "screenLayoutCode cannot null")
    private String screenLayoutCode;

    @ApiModelProperty(value = "CPU是否隔离")
    private Boolean isolateCpu = false;

    @ApiModelProperty(value = "内存是否隔离")
    private Boolean isolateMemory = false;

    @ApiModelProperty(value = "存储是否限制")
    private Boolean isolateStorage = false;

    @ApiModelProperty(value = "云机数量")
    @NotNull(message = "number cannot null")
    private Integer number;

    @ApiModelProperty(value = "是否网存实例")
    private Integer netStorageResFlag;

    /**
     * adi模板Id
     */
    private Long realPhoneTemplateId;

    /**
     * 实例dns
     */
    private String dns;
    /**
     * 实例 adi
     */
    private String adiUrl;
    /**
     * adi密码
     */
    private String adiPassword;
    /**
     * 实例安卓系统属性
     */
    private String deviceAndroidProps;



    /**
     * 存储大小(单位/GB)
     */
    @ApiModelProperty(value = "存储大小")
    @NotNull(message = "storageSize cannot null")
    private Integer storageSize;

    private String oprBy;

    /**
     * 推拉流方式 默认火山
     */
    private Integer streamType = 1;

    private SourceTargetEnum sourceTarget;
}
