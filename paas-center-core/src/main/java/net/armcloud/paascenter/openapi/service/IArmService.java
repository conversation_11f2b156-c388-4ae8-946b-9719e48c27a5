package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.openapi.model.dto.ArmServerDTO;
import net.armcloud.paascenter.openapi.model.dto.SaveArmServerDTO;
import net.armcloud.paascenter.openapi.model.vo.ArmServerVo;
import net.armcloud.paascenter.openapi.model.vo.UploadImagesVo;

public interface IArmService extends IService<ArmServer> {
    Result<?> armServerStatusCallback(String armSn, Byte status);

    ArmServer getArmServerDetail(String serverCode);

    void armServerUpdateStatus(String clusterCode,String ip, Integer status,String version);
}
