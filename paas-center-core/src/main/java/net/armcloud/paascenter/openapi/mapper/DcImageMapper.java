package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.DcImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DcImageMapper  extends BaseMapper<DcImage> {
    List<Long> imageSyncDcs(@Param("imageId") String imageId);

    int unsubscribeDcImages(@Param("imageId") String imageId, @Param("dcIds") List<Long> dcIds);
}
