package net.armcloud.paascenter.openapi.utils;

import java.util.Random;

public class AnddroidPropUtils {
    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    /**
     * 随机生成蓝牙地址
     * @return
     */
    public static String generateRandomBluetoothName() {
        Random random = new Random();
        int nameLength = random.nextInt(11) + 5; // 随机生成长度，范围为5-15
        StringBuilder bluetoothName = new StringBuilder();

        for (int i = 0; i < nameLength; i++) {
            char randomChar = CHAR_POOL.charAt(random.nextInt(CHAR_POOL.length()));
            bluetoothName.append(randomChar);
        }

        return bluetoothName.toString();
    }

    /**
     * 随机生成蓝牙名称
     * @return
     */
    public static String generateRandomBluetoothMac() {
        Random random = new Random();
        StringBuilder macAddress = new StringBuilder();

        // 蓝牙地址的第一个字节应该是偶数，确保是可用的 Unicast 地址
        int firstByte = random.nextInt(256) & 0xFE; // 清除最低位以确保为偶数
        macAddress.append(String.format("%02X", firstByte));

        // 剩余的5个字节
        for (int i = 0; i < 5; i++) {
            int byteValue = random.nextInt(256); // 生成0-255之间的随机数
            macAddress.append(":").append(String.format("%02X", byteValue));
        }

        return macAddress.toString();
    }

}
