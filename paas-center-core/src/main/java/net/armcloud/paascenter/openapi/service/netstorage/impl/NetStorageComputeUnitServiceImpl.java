package net.armcloud.paascenter.openapi.service.netstorage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.component.CommonPadTaskComponent;
import net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.core.constant.NumberConsts;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.bo.task.PadTaskBO;
import net.armcloud.paascenter.common.model.dto.api.NetWorkOnDTO;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.model.dto.NetStorageComputeDTO;
import net.armcloud.paascenter.openapi.model.dto.PowerOffForceDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStoragePadCodeDetailDTO;
import net.armcloud.paascenter.openapi.model.vo.NetStorageComputeVO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.DeviceCodeWithComputeUnitCntDTO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2ComputeMatchService;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2IpManager;
import net.armcloud.paascenter.openapi.netpadv2.service.impl.NetPadV2ComputeMatchServiceImpl;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ComputeUnitCache;
import net.armcloud.paascenter.openapi.service.impl.DeviceServiceImpl;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitService;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import net.armcloud.paascenter.openapi.utils.AndroidDeviceInfoUtils;
import net.armcloud.paascenter.openapi.utils.CIDRUtils;
import net.armcloud.paascenter.openapi.utils.IdGeneratorUtils;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static net.armcloud.paascenter.cms.constants.LockKeyConstants.IpClashLock.*;
import static net.armcloud.paascenter.cms.constants.LockKeyConstants.NetStorageProcess.*;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ONE;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants.FORCE_POWER_OFF;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;

/**
 * <AUTHOR>
 * @Date 2025/3/24 19:58
 * @Description:
 */
@Slf4j
@Service("netStorageComputeUnitService")
public class NetStorageComputeUnitServiceImpl extends ServiceImpl<NetStorageComputeUnitMapper, NetStorageComputeUnit> implements NetStorageComputeUnitService {


    @Resource
    private CustomerDeviceMapper customerDeviceMapper;
    @Resource
    private ResourceSpecificationMapper resourceSpecificationMapper;
    @Resource
    private NetStorageResUnitService netStorageResUnitService;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private NetPadMapper netPadMapper;

    @Resource
    private  PadMapper padMapper;

    @Resource
    private CommonPadTaskComponent padTaskComponent;

    @Resource
    private  ArmServerMapper armServerMapper;

    @Resource
    private  RedisService redisService;

    @Resource
    NetStorageComputeUnitPadMapper netStorageComputeUnitPadMapper;

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Autowired
    private NetPadV2IpManager netPadV2IpManager;

    /**
     * 根据板卡对应的规格构建网存单元
     * @param device
     * @return
     */
    private List<NetStorageComputeUnit> buildNetStorageComputeUnitList(Device device,String deviceLevel) {
        //规格永远存在，所以不用判断是否存在
        ResourceSpecification resourceSpecification = resourceSpecificationMapper.selectOne(new QueryWrapper<ResourceSpecification>().lambda()
                .eq(ResourceSpecification::getSpecificationCode, deviceLevel)
                .eq(ResourceSpecification::getDeleteFlag, ZERO)
                .eq(ResourceSpecification::getStatus, ONE)
                .last("LIMIT 1"));

        return IntStream.range(0, resourceSpecification.getPadNumber())
                .mapToObj(i -> buildNetStorageComputeUnit(device,deviceLevel))
                .collect(Collectors.toList());
    }

    /**
     * 从一个板卡构建得到一个网存单元对象
     * @param device
     * @return
     */

    private NetStorageComputeUnit buildNetStorageComputeUnit(Device device,String deviceLevel) {
        //获取板卡所属用户Id
        Long customerIdByDeviceId = customerDeviceMapper.getCustomerIdByDeviceId(device.getId());
        NetStorageComputeUnit netStorageComputeUnit = new NetStorageComputeUnit();
        ArmServer armServer = armServerMapper.selectByArmServerCode(device.getArmServerCode());
        if(Objects.nonNull(armServer)){
            netStorageComputeUnit.setClusterCode(armServer.getClusterCode());
        }
        netStorageComputeUnit.setDeviceId(device.getId());
        netStorageComputeUnit.setDeviceCode(device.getDeviceCode());
        netStorageComputeUnit.setBindFlag(0);
        netStorageComputeUnit.setDeviceLevel(deviceLevel);
        netStorageComputeUnit.setCustomerId(customerIdByDeviceId);
        netStorageComputeUnit.setCreateTime(new Date());
        netStorageComputeUnit.setNetStorageComputeUnitCode(IdGeneratorUtils.generateStorageComputeUnitCode());
        try {
            netStorageComputeUnit.setIp(netPadV2IpManager.getAvailableIp(armServer.getId()));
        } catch (Exception e) {
            log.error("获取IP失败");
            throw new BasicException("分配IP失败");
        }
        return netStorageComputeUnit;
    };
    @Override
    public Boolean createNetStorageComputeUnitByDeviceCodeList(List<Device> deviceCodeList,String deviceLevel) {
        List<NetStorageComputeUnit> insertBatchList = Lists.newArrayList();
        deviceCodeList.forEach(device -> insertBatchList.addAll(buildNetStorageComputeUnitList(device,deviceLevel)));
        boolean b = this.saveBatch(insertBatchList);

        //创建成功时,可用算力发生变化,清除可用算力队列
        if (b) {
            Set<String> uniqueKeys = insertBatchList.stream()
                    .map(unit -> COMPUTE_UNIT_QUEUE + ":" + unit.getCustomerId() + ":" + unit.getClusterCode() + ":" + unit.getDeviceLevel())
                    .collect(Collectors.toSet());
            cleanComputeUnitQueue(uniqueKeys);
        }
        return b;
    }

    private void cleanComputeUnitQueue(Set<String> uniqueKeys){
        if(CollUtil.isEmpty(uniqueKeys)){
            return;
        }
        for (String uniqueKey : uniqueKeys) {
            boolean b = redisService.deleteObject(uniqueKey);
            log.info("cleanComputeUnitQueue uniqueKey:{},b:{}", uniqueKey, b);
        }
    }

    @Override
    public Boolean deleteNetStorageComputeUnitByDeviceCodeList(List<String> deviceCodeList) {
        LambdaUpdateWrapper<NetStorageComputeUnit> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(NetStorageComputeUnit::getDeviceCode,deviceCodeList);
        return this.remove(wrapper);

    }

    @Override
    public Boolean NetStorageComputeUnitIsBind(List<String> deviceCodeList) {
        LambdaQueryWrapper<NetStorageComputeUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NetStorageComputeUnit::getBindFlag, NumberConsts.ONE);
        queryWrapper.in(NetStorageComputeUnit::getDeviceCode,deviceCodeList);
        return this.count(queryWrapper) >0 ;
    }

    @Override
    public NetStorageDTO matchUnboundComputeUnitsBySpecAndQuantity(String clusterCode,String deviceLevel, List<PadDetailsVO> padVoList,String countryCode, JSONObject androidProp) {
        NetStorageDTO netStorageDTO = new NetStorageDTO();
        netStorageDTO.setDeviceLevel(deviceLevel);
        netStorageDTO.setClusterCode(clusterCode);
        List<NetStorageComputeUnit> netStorageComputeResultList =Lists.newArrayList();
        //实例跟算力绑定关系
        List<NetStorageComputeUnitPad> padBindings = new ArrayList<>();
        //板卡跟实例绑定关系
        List<DevicePad> devicePadList = new ArrayList<>();
        List<PadDetailsVO> padDetailsResultList = new ArrayList<>();
        List<NetStoragePadCodeDetailDTO> netStoragePadCodeDetailDTOList = new ArrayList<>();
        //根据用户分组
        Map<Long, List<PadDetailsVO>> padDetailMap = padVoList.stream()
                .collect(Collectors.groupingBy(PadDetailsVO::getCustomerId));

        padDetailMap.forEach((customerId,padVoByCustomerIdList) ->{
            List<NetStorageComputeUnit> computeUnitList = new ArrayList<>();
            /*for (int i = 0; i < padVoByCustomerIdList.size(); i++) {
                Long unitId = acquireAvailableNetStorageComputeUnit(clusterCode, deviceLevel, customerId);
                if (unitId != null) {
                    LambdaUpdateWrapper<NetStorageComputeUnit> updateLambda = new LambdaUpdateWrapper<>();
                    updateLambda.eq(NetStorageComputeUnit::getNetStorageComputeUnitId, unitId);
                    updateLambda.eq(NetStorageComputeUnit::getBindFlag, 0);
                    updateLambda.set(NetStorageComputeUnit::getBindFlag, 2);
                    boolean update = this.update(updateLambda);
                    if(update){
                        computeUnitList.add(this.getById(unitId));
                    }
                }
            }*/

            List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> netPadRelationList = SpringUtil.getBean(NetPadV2ComputeMatchService.class).batchMatchComputeAndStorage(clusterCode, padVoByCustomerIdList, customerId);
            if(CollectionUtils.isEmpty(netPadRelationList) ){
                return ;
            }
            netPadRelationList.forEach(netPadRelation -> {
                if(!netPadRelation.isMatchSuccess()){
                    return;
                }
                NetStorageComputeUnit computeUnit = this.getOne(new LambdaQueryWrapper<NetStorageComputeUnit>()
                        .eq(NetStorageComputeUnit::getNetStorageComputeUnitCode, netPadRelation.getComputeUnitCode()));
                if(Objects.nonNull(computeUnit)){
                    computeUnitList.add(computeUnit);
                }
            });

            //挑不到算力
            if(CollectionUtils.isEmpty(computeUnitList) ){
                return ;
            }
            //挑选前N个
            List<PadDetailsVO> matchedPadList = padVoByCustomerIdList.subList(0, Math.min(computeUnitList.size(), padVoByCustomerIdList.size()));
            padDetailsResultList.addAll(matchedPadList);
            netStorageComputeResultList.addAll(computeUnitList);
            //添加实例跟算力绑定关系
            for (int i = 0; i < computeUnitList.size(); i++) {
                NetStorageComputeUnit computeUnit = computeUnitList.get(i);
                PadDetailsVO padDetails = matchedPadList.get(i);
                if(!netStorageResUnitService.checkBackup(padDetails)){
                    throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_RES_UNIT_INSUFFICIENT_CAPACITY);
                }
                //IP不足时会有问题,这里暂时随机分配一个ip,后续需要改成分配逻辑
//                List<String> ipList = getIpList(computeUnit.getDeviceId(), 1);
//                String ip = ipList.get(ThreadLocalRandom.current().nextInt(ipList.size()));

                DeviceInfoVo deviceInfoByDeviceCode = deviceMapper.getDeviceInfoByDeviceCode(computeUnit.getDeviceCode());
                if (StrUtil.isBlank(computeUnit.getIp())) {
                    String ip = netPadV2IpManager.getAvailableIp(deviceInfoByDeviceCode.getArmServerId());
                    computeUnit.setIp(ip);
                    this.updateById(computeUnit);
                    padDetails.setPadIp(ip);
                } else {
                    padDetails.setPadIp(computeUnit.getIp());
                }
                //写入ip信息
                padDetails.setDeviceIp(deviceInfoByDeviceCode.getDeviceIp());
                //实例跟算力网存绑定
                NetStorageComputeUnitPad padBinding = new NetStorageComputeUnitPad();
                padBinding.setPadCode(padDetails.getPadCode());  // 绑定 padCode
                padBinding.setNetStorageComputeUnitCode(computeUnit.getNetStorageComputeUnitCode()); // 绑定算力单元 Code
                //网存id
                padBinding.setNetStorageResCode(padDetails.getNetStorageResId());
                padBindings.add(padBinding);
                //向前兼容,写入板卡跟实例的关系映射表(这里网存可以通过算力去找板卡,但是部分查询使用了实例跟板卡映射表去查找,方便兼容,这里同样写入)
                DevicePad devicePad = new DevicePad();
                devicePad.setDeviceId(computeUnit.getDeviceId());
                devicePad.setPadId(padDetails.getPadId());
                devicePadList.add(devicePad);
                NetStoragePadCodeDetailDTO detailDTO = new NetStoragePadCodeDetailDTO();
                detailDTO.setPadCode(padDetails.getPadCode());
                detailDTO.setNetStorageComputeUnit(computeUnit);
                detailDTO.setDevicePad(devicePad);
                detailDTO.setPadDetailsVO(padDetails);
                detailDTO.setNetStorageComputeUnitPad(padBinding);
                JSONObject props = new JSONObject();
                if(StringUtils.isNotEmpty(countryCode)){
                    AndroidDeviceInfoUtils.ramdomSimAndGPSInfo(countryCode, props);
                    AndroidDeviceInfoUtils.ramdomBatteryInfo(countryCode, props);
                    AndroidDeviceInfoUtils.ramdomWifiInfo(countryCode, props);
                    AndroidDeviceInfoUtils.ramdomBluetoothInfo(countryCode, props);
                    props.putAll(Objects.isNull(androidProp)?new JSONObject():androidProp);
                }
                detailDTO.setAndroidProp(props);
                detailDTO.setCountryCode(countryCode);
                netStoragePadCodeDetailDTOList.add(detailDTO);

            }
        });
        //写入返回对象
        netStorageDTO.setNetStorageComputeUnitList(netStorageComputeResultList);
        netStorageDTO.setNetStorageComputeUnitPadList(padBindings);
        netStorageDTO.setDevicePadList(devicePadList);
        netStorageDTO.setPadDetailsVOList(padDetailsResultList);
        netStorageDTO.setNetStoragePadCodeDetailDTOList(netStoragePadCodeDetailDTOList);
        return netStorageDTO;
    }

    public String getUsableIp(Long deviceId){
        // 先获取对应的设备信息
        VirtualizeDeviceInfoVO infoVO = deviceMapper.selectListVirtualizeInfoByNetStorageRes(deviceId);
        if (Objects.isNull(infoVO)) {
            log.error("getDeviceInfo error, info is null, deviceId: {}", deviceId);
            throw new BasicException(PAD_IP_NOT_ENOUGH);
        }
        String redisKey = COMPUTE_IP_QUEUE + infoVO.getArmServerId();
        String lockKey = redisKey + ":lock";

        // 先尝试从 Redis 队列中弹出一个
        String ip = redisService.leftPop(redisKey);
        if (StrUtil.isNotBlank(ip)) {
            return ip;
        }

        // 队列没数据，加分布式锁补数据（防止多个线程同时查库）
        RLock lock = redissonDistributedLock.tryLock(lockKey, 0, 5);
        if (lock != null) {
            try {
                // 再确认一次 Redis 中是否已有数据（双检）
                ip = redisService.leftPop(redisKey);
                if (StrUtil.isNotBlank(ip)) {
                    return ip;
                }


                // 从数据库或其他渠道取出 IP 列表
                long startTime = System.currentTimeMillis();
                List<String> ipList = getIpList(infoVO.getArmServerId(),infoVO.getDeviceIp(), 500);
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;

                if (duration > 5000) {
                    log.warn("getIpList执行时间过长: {}ms, armServerId: {}, deviceIp: {}, padNumber: 1000",
                            duration, infoVO.getArmServerId(), infoVO.getDeviceIp());
                }
                if (CollectionUtils.isEmpty(ipList)) {
                    throw new BasicException(PAD_IP_NOT_ENOUGH);
                }

                // 批量推入 Redis 队列
                redisService.rightPushAll(redisKey, ipList, 5L, TimeUnit.MINUTES);

                // 弹出一个返回
                return redisService.leftPop(redisKey);
            } finally {
                lock.unlock();
            }
        } else {
            // 如果没抢到锁，说明别人正在补数据，等待一会重试最多10次
            int retry = 10;
            while (retry-- > 0) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }

                ip = redisService.leftPop(redisKey);
                if (StrUtil.isNotBlank(ip)) {
                    return ip;
                }
            }
            log.info("getUsableIp 重试10次后还是没有获取到ip,deviceId: {}", deviceId);
            throw new BasicException(PAD_IP_NOT_ENOUGH); // 重试也没数据，抛异常
        }
    }

    public Long acquireAvailableNetStorageComputeUnit(String clusterCode, String deviceLevel, Long customerId) {
        log.info("acquireAvailableNetStorageComputeUnit cluster code {} deviceLevel {} customerId {}", clusterCode, deviceLevel, customerId );
        Long unitId = matchComputeUnits(clusterCode, deviceLevel, customerId);
        log.info("acquireAvailableNetStorageComputeUnit unitId: {}", unitId );
        if (unitId == null) {
            return null;
        }

        String redisKey = USE_COMPUTE_UNIT_ID + unitId;
        int maxAttempts = 5;

        for (int i = 0; i < maxAttempts; i++) {
            boolean locked = redisService.setIfAbsentExpire(redisKey, String.valueOf(unitId), 5L, TimeUnit.SECONDS);
            if (locked) {
                log.info("acquireAvailableNetStorageComputeUnit lock success,unitId: {}", unitId );
                return unitId;
            }

            // 可以适当加一点延迟，避免过于频繁
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        log.warn("获取 computeUnit 失败: customerId={}, clusterCode={}, deviceLevel={}", customerId, clusterCode, deviceLevel);
        return null; // 最多尝试5次加锁都失败，返回 null
    }


    public Long matchComputeUnits(String clusterCode, String deviceLevel, Long customerId) {
        String redisKey = COMPUTE_UNIT_QUEUE + ":" + customerId + ":" + clusterCode + ":" + deviceLevel;
        String emptyFlagKey = redisKey + ":empty";
        String lockKey = redisKey + ":lock";

        // 先尝试从 Redis 拿一个
        Long netStorageComputeUnitId = redisService.leftPopAsLong(redisKey);
        if (netStorageComputeUnitId != null) {
            log.info("matchComputeUnits 从redis获取可用算力,redisKey:{},netStorageComputeUnitId:{}", redisKey, netStorageComputeUnitId);
            return netStorageComputeUnitId;
        }

        // 如果有空值标记，也直接返回 null
        if (Boolean.TRUE.equals(redisService.hasKey(emptyFlagKey))) {
            return null;
        }

        // 尝试获取锁
        RLock rLock = redissonDistributedLock.tryLock(lockKey, 0, 60);
        if (rLock != null) {
            try {
                // 再次确认 Redis 中是否已有数据，避免重复查库
                netStorageComputeUnitId = redisService.leftPopAsLong(redisKey);
                if (netStorageComputeUnitId != null) {
                    return netStorageComputeUnitId;
                }

                // 查库
                LambdaQueryWrapper<NetStorageComputeUnit> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(NetStorageComputeUnit::getBindFlag, 0)
                        .eq(NetStorageComputeUnit::getDeviceLevel, deviceLevel)
                        .eq(NetStorageComputeUnit::getClusterCode, clusterCode)
                        .eq(NetStorageComputeUnit::getCustomerId, customerId)
                        .exists("SELECT 1 FROM device d WHERE d.id = net_storage_compute_unit.device_id AND d.device_status = 1");

                List<NetStorageComputeUnit> dbList = this.list(queryWrapper);

                if (CollectionUtils.isEmpty(dbList)) {
                    // 空值标记防止缓存穿透
                    redisService.setCacheObject(emptyFlagKey, "1", 1L, TimeUnit.SECONDS);
                    return null;
                }

                List<Long> ids = dbList.stream().map(NetStorageComputeUnit::getNetStorageComputeUnitId).collect(Collectors.toList());


                List<String> idsStringValues = ids.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());

                // 打乱顺序，确保分散性
                Collections.shuffle(idsStringValues);
                redisService.rightPushAll(redisKey, idsStringValues, 5L, TimeUnit.MINUTES);
                log.info("matchComputeUnits 加载可用算力到redis,redisKey:{},ids:{}", redisKey, idsStringValues);
                return redisService.leftPopAsLong(redisKey);
            } finally {
                rLock.unlock(); // 释放锁
            }
        } else {
            // 没拿到锁：说明其他线程在查库中，等待 Redis 填充
            int retry = 5;
            while (retry-- > 0) {
                try {
                    Thread.sleep(50);
                } catch (Exception e) {
                    log.error("matchComputeUnits", e);
                }
                netStorageComputeUnitId = redisService.leftPopAsLong(redisKey);
                if (netStorageComputeUnitId != null) {
                    return netStorageComputeUnitId;
                }
            }
            // 最终还是没有数据，返回 null
            return null;
        }
    }



    @Override
    public  Map<String, NetStorageComputeVO> retrieveNetStorageComputeUsage(NetStorageComputeDTO param) {
        LambdaQueryWrapper<NetStorageComputeUnit> queryWrapper = new LambdaQueryWrapper<>();
        if(Objects.nonNull(param.getCustomerId())){
            queryWrapper.eq(NetStorageComputeUnit::getCustomerId,param.getCustomerId());
        }
        if(StringUtils.isNotEmpty(param.getClusterCode())){
            queryWrapper.eq(NetStorageComputeUnit::getClusterCode,param.getClusterCode());
        }
        queryWrapper.select(NetStorageComputeUnit::getDeviceLevel, NetStorageComputeUnit::getBindFlag);
        List<NetStorageComputeUnit> netStorageComputeUnits = this.list(queryWrapper);
        //查找mysql数据到内存计算.
        Map<String, NetStorageComputeVO> computeVOMap = netStorageComputeUnits.stream()
                .collect(Collectors.groupingBy(
                        NetStorageComputeUnit::getDeviceLevel,
                        Collectors.reducing(
                                null,
                                //先转化成当前对象为目标对象
                                unit -> {
                                    NetStorageComputeVO vo = new NetStorageComputeVO();
                                    vo.setDeviceLevel(unit.getDeviceLevel());
                                    vo.setOnNumber(unit.getBindFlag() == 1 ? 1L : 0L);
                                    vo.setTotalNumber(1L);
                                    return vo;
                                },
                                //处理累加结果跟当前结果的运算
                                (vo1, vo2) -> {
                                    if (vo1 == null) return vo2;
                                    if (vo2 == null) return vo1;
                                    vo1.setOnNumber(vo1.getOnNumber() + vo2.getOnNumber());
                                    vo1.setTotalNumber(vo1.getTotalNumber() + vo2.getTotalNumber());
                                    return vo1;
                                }
                        )
                ));
        return computeVOMap;
//        List<NetStorageComputeVO> computeVOList = computeVOMap.entrySet().stream()
//                .map(entry -> {
//                    // 从 entry 获取 key 和 value
//                    String deviceLevelKey = entry.getKey();
//                    NetStorageComputeVO netStorageComputeVO = entry.getValue();
//
//                    // 将 deviceLevel 设置为当前的 key
//                    netStorageComputeVO.setDeviceLevel(deviceLevelKey);
//
//                    return netStorageComputeVO; // 返回修改后的对象
//                })
//                .collect(Collectors.toList()); // 将结果收集到一个列表中
//        return computeVOList;
    }

    @Override
    public DeviceInfoVo obtainAComputingIPAddressInTheCluster(String clusterCode) {
        LambdaQueryWrapper<NetStorageComputeUnit> wrapper = new LambdaQueryWrapper<>();
       //这里只挑选集群,不挑选用户.防止用户只有存储,没有算力,然后去回收实例
        wrapper.eq(NetStorageComputeUnit::getClusterCode,clusterCode);
//                        .eq(NetStorageComputeUnit::getCustomerId,padDetailsVO.getCustomerId());
        wrapper.last(" ORDER BY RAND() LIMIT 1");  // 追加自定义排序和限制条数
        //随机挑选一个算力
        NetStorageComputeUnit computeUnit = this.getOne(wrapper);
        //集群下没有任何算力
        if(Objects.isNull(computeUnit)){
            throw new BasicException(PadExceptionCode.NETWORK_INSTANCE_COMPUTE_UNIT_NOT);
        }
        List<DeviceInfoVo> deviceInfoVos = deviceMapper.selectBatchById(Lists.newArrayList(String.valueOf(computeUnit.getDeviceId())));
        return deviceInfoVos.get(0);
    }


    /**
     * 获取未使用的IP列表
     *
     * @param armServerId 服务器id
     * @param padNumber 需要的IP数量
     * @return
     */
    private List<String> getIpList(Long armServerId,String deviceIp, Integer padNumber) {
        List<String> ipv4Cidrs = netPadMapper.selectIpv4CidrsByArmServer(armServerId);
        if (ipv4Cidrs.isEmpty()) {
            throw new BasicException(NETWORK_SEGMENT_NOT_EXIST);
        }

        List<String> ipCidrs = new ArrayList<>();
        for (String ipv4Cidr : ipv4Cidrs) {
            Set<String> ipAddressesFromCIDR;
            if (DeviceServiceImpl.ipSubNumWhiteList != null && !DeviceServiceImpl.ipSubNumWhiteList.isEmpty()) {
                log.info(">>>>>> ipSubNumWhiteList enabled value is {}", DeviceServiceImpl.ipSubNumWhiteList);
                ipAddressesFromCIDR = CIDRUtils.getIPAddressesFromCIDR(ipv4Cidr, DeviceServiceImpl.ipSubNumWhiteList);
            } else {
                ipAddressesFromCIDR = CIDRUtils.getIPAddressesFromCIDR(ipv4Cidr);
            }
            if (CollUtil.isNotEmpty(ipAddressesFromCIDR)) {
                ipCidrs.addAll(ipAddressesFromCIDR);
            }
        }

        List<String> ips = CIDRUtils.sortIPs(ipCidrs);
        if (CollUtil.isEmpty(ips)) {
            throw new BasicException(PAD_IP_NOT_ENOUGH);
        }

        // 查询已入库的 IP
        List<String> usedPadIps = padMapper.selectUsePadIps(ips);

        List<String> lockedIps = new ArrayList<>();
        for (String ip : ips) {
            String lockKey = RedisKeyPrefix.PAD_IP_LOCK + ip;
            if (Boolean.TRUE.equals(redisService.hasKey(lockKey))) {
                lockedIps.add(ip); // 已被其他线程锁定
            }
        }

        Collections.shuffle(ips);
        List<String> unusedIps = new ArrayList<>();
        for (String ip : ips) {
            if (usedPadIps.contains(ip) || lockedIps.contains(ip)) {
                continue; // 跳过已入库或已被锁定的 IP
            }
            String redisKey = CBS_DEVICE_IP_TO_USE + "{" + deviceIp + "}";
            Boolean memberOfSet = redisService.isMemberOfSet(redisKey, ip);
            if (memberOfSet) {
                log.info("getIpList ip:{} is in use,key:{}", ip, redisKey);
                continue;
            }

            String lockKey = RedisKeyPrefix.PAD_IP_LOCK + ip;
            redisService.setCacheObject(lockKey, ip, RedisKeyTime.minute_10, TimeUnit.MINUTES);

            unusedIps.add(ip);
            if (unusedIps.size() == padNumber) {
                break;
            }
        }


        return unusedIps;
    }

    @Override
    public NetStorageComputeUnit getBoundComputeUnitByPadCode(String padCode) {
        if (StringUtils.isBlank(padCode)) {
            return null;
        }
        try {
            // 使用关联查询直接获取已绑定（bind_flag=1）的算力单元
            NetStorageComputeUnit boundUnit = baseMapper.selectBoundComputeUnitByPadCode(padCode);
            if (boundUnit != null) {
                log.info("Found bound compute unit for pad: {}, compute unit: {}",
                        padCode, boundUnit.getNetStorageComputeUnitCode());
                NetStorageComputeUnitPad netStorageComputeUnitPad = netStorageComputeUnitPadMapper.selectOne(new LambdaQueryWrapper<NetStorageComputeUnitPad>()
                        .eq(NetStorageComputeUnitPad::getNetStorageComputeUnitCode,boundUnit.getNetStorageComputeUnitCode())
                        .orderByDesc(NetStorageComputeUnitPad::getCreateTime)
                        .last("limit 1"));
                log.info("Finally Found bound compute unit: {}",netStorageComputeUnitPad);
                if(netStorageComputeUnitPad.getPadCode().equalsIgnoreCase(padCode)){
                    return boundUnit;
                }
            } else {
                log.info("No bound compute unit found for pad code: {}", padCode);
            }
            return null;
        } catch (Exception e) {
            log.error("Error getting bound compute unit for pad code: {}, error: {}", padCode, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<NetStorageDTO> getPadComputeUnitExists(Map<String, List<PadDetailsVO>> deviceLevelMap, String clusterCode, Map<String, NetStorageComputeUnit> failedShutdownResourceMap, NetWorkOnDTO param){
        List<NetStorageDTO> netStorageDTOS = new ArrayList<>();
        for (Map.Entry<String, List<PadDetailsVO>> deviceLevelEntry : deviceLevelMap.entrySet()) {
            String deviceLevel = deviceLevelEntry.getKey();
            List<PadDetailsVO> padDetailsList = deviceLevelEntry.getValue();

            NetStorageDTO netStorageDTO = new NetStorageDTO();
            netStorageDTO.setDeviceLevel(deviceLevel);
            netStorageDTO.setClusterCode(clusterCode);

            // 准备需要的集合
            List<NetStorageComputeUnit> computeUnits = new ArrayList<>();
            List<NetStorageComputeUnitPad> computeUnitPads = new ArrayList<>();
            List<DevicePad> devicePads = new ArrayList<>();
            List<NetStoragePadCodeDetailDTO> padCodeDetailDTOs = new ArrayList<>();

            for (PadDetailsVO padDetailsVO : padDetailsList) {
                try {
                    String padCode = padDetailsVO.getPadCode();
                    NetStorageComputeUnit computeUnit = failedShutdownResourceMap.get(padCode);

                    if (computeUnit != null) {
                        // 获取设备信息
                        DeviceInfoVo deviceInfo = deviceMapper.getDeviceInfoByDeviceCode(computeUnit.getDeviceCode());

                        // 确保IP信息正确
                        Pad pad = padMapper.getByPadCode(padCode);
                        padDetailsVO.setPadIp(pad.getPadIp());
                        // 设置设备IP
                        if (deviceInfo != null) {
                            padDetailsVO.setDeviceIp(deviceInfo.getDeviceIp());
                        }

                        // 创建算力与实例绑定关系
                        NetStorageComputeUnitPad computeUnitPad = new NetStorageComputeUnitPad();
                        computeUnitPad.setPadCode(padCode);
                        computeUnitPad.setNetStorageComputeUnitCode(computeUnit.getNetStorageComputeUnitCode());
                        computeUnitPad.setNetStorageResCode(padDetailsVO.getNetStorageResId());

                        // 创建设备与实例绑定关系
                        DevicePad devicePad = new DevicePad();
                        devicePad.setDeviceId(computeUnit.getDeviceId());
                        devicePad.setPadId(padDetailsVO.getPadId());

                        // 创建详细信息对象
                        NetStoragePadCodeDetailDTO detailDTO = new NetStoragePadCodeDetailDTO();
                        detailDTO.setPadCode(padCode);
                        detailDTO.setNetStorageComputeUnit(computeUnit);
                        detailDTO.setDevicePad(devicePad);
                        detailDTO.setPadDetailsVO(padDetailsVO);
                        detailDTO.setNetStorageComputeUnitPad(computeUnitPad);
                        detailDTO.setClusterCode(clusterCode);
                        detailDTO.setDeviceLevel(deviceLevel);

                        // 生成AndroidProp
                        JSONObject props = new JSONObject();

                        if (StringUtils.isNotEmpty(param.getCountryCode())) {
                            detailDTO.setCountryCode(param.getCountryCode());
                            // 随机生成各种设备信息
                            AndroidDeviceInfoUtils.ramdomSimAndGPSInfo(param.getCountryCode(), props);
                            AndroidDeviceInfoUtils.ramdomBatteryInfo(param.getCountryCode(), props);
                            AndroidDeviceInfoUtils.ramdomWifiInfo(param.getCountryCode(), props);
                            AndroidDeviceInfoUtils.ramdomBluetoothInfo(param.getCountryCode(), props);
                            props.putAll(Objects.isNull(param.getAndroidProp())?new JSONObject():param.getAndroidProp());
                        }

                        detailDTO.setAndroidProp(props);
                        computeUnits.add(computeUnit);
                        computeUnitPads.add(computeUnitPad);
                        devicePads.add(devicePad);
                        padCodeDetailDTOs.add(detailDTO);
                    }
                } catch (Exception e) {
                    log.error("Error processing failed shutdown instance {}: {}", padDetailsVO.getPadCode(), e.getMessage(), e);
                }
            }

            // 设置到NetStorageDTO
            netStorageDTO.setNetStorageComputeUnitList(computeUnits);
            netStorageDTO.setNetStorageComputeUnitPadList(computeUnitPads);
            netStorageDTO.setDevicePadList(devicePads);
            netStorageDTO.setPadDetailsVOList(padDetailsList);
            netStorageDTO.setNetStoragePadCodeDetailDTOList(padCodeDetailDTOs);
            netStorageDTOS.add(netStorageDTO);
        }
        return netStorageDTOS;
    }

    @Override
    public List<GeneratePadTaskVO> powerOffForce(PowerOffForceDTO param) {
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        // 校验当前传入的实例是否是当前customer
        if (CollUtil.isNotEmpty(param.getPadCodes())) {
            List<Pad> padList = padMapper.getPadByGroupIds(param.getPadCodes(), null, customerId);
            if (CollUtil.isEmpty(padList)) {
                throw new BasicException(PAD_CODE_NOT_EXIST);
            }
            if (padList.size() != param.getPadCodes().size()) {
                throw new BasicException(PAD_CODE_NOT_BELONG_TO_CUSTOMER);
            }
            if (padList.stream().anyMatch(pad -> pad.getStatus() == -1)) {
                throw new BasicException(PAD_IS_DELETED);
            }
            // 校验是否是网存实例
            if (padList.stream().anyMatch(pad -> pad.getNetStorageResFlag() == 0)) {
                throw new BasicException(PAD_IS_NOT_NET_STORAGE);
            }
        }
        // 添加关机任务
        PadTaskBO padTaskBO = padTaskComponent.addPadTask(customerId, param.getPadCodes(), FORCE_POWER_OFF,
                null, JSON.toJSONString(param), SourceTargetEnum.PAAS);
        return GeneratePadTaskVO.builder(padTaskBO);
    }

    @Override
    public List<String> batchQueryAvailableUnits(String clusterCode, String deviceLevel, int requiredCount) {
        return baseMapper.batchQueryAvailableUnits(clusterCode, deviceLevel, requiredCount);
    }

    @Override
    public List<DeviceCodeWithComputeUnitCntDTO> getAllDeviceWithComputeUnitCnt(String clusterCode, String deviceLevel, Long customerId) {
        return baseMapper.getAllDeviceWithComputeUnitCnt(clusterCode, deviceLevel, customerId);
    }

    private final static String computeCodeListKey = "net_storage_compute_unit:device_code";

    @Override
    public List<NetPadV2ComputeUnitCache> getAvailableComputeUnitByDeviceCode(String deviceCode, int size) {
        List<NetPadV2ComputeUnitCache> result = new ArrayList<>();
        // 校验空值标记
        boolean empty = computeUnitIsEmpty(deviceCode);
        if (empty) {
            return result;
        }
        // 从缓存中获取
        result.addAll(new ArrayList<>(getComputeCodeList(size, deviceCode)));
        if (result.size() == size) {
            return result;
        }
        // 数量不足，锁板卡
        String lockKey = RedisKeyUtils.lockKey(computeCodeListKey, deviceCode);
        RLock rLock = redissonDistributedLock.tryLock(lockKey, 10, 30);
        if (rLock == null) {
            log.info("getAvailableComputeUnitByDeviceCode 获取锁失败,deviceCode:{}", deviceCode);
            return result;
        }
        try {
            // 获取到锁后再查空值标记与缓存
            empty = computeUnitIsEmpty(deviceCode);
            if (empty) {
                return result;
            }
            // 从缓存中获取
            result.addAll(new ArrayList<>(getComputeCodeList(size - result.size(), deviceCode)));
            if (result.size() == size) {
                return result;
            }
            result.addAll(getAndCacheComputeUnit(deviceCode, size - result.size()));
            return result;
        } finally {
            redissonDistributedLock.unlock(rLock);
        }
    }

    @Override
    public int updateBatchBindFlagByCodeList(List<String> computeUnitCodeList, int bindFlag) {
        return baseMapper.updateBatchBindFlagByCodeList(computeUnitCodeList, bindFlag);
    }

    @Override
    public List<String> getUsedIpListByArmServerId(Long armServerId) {
        return baseMapper.getUsedIpListByArmServerId(armServerId);
    }

    @Override
    public int updateIpByCode(String computeUnitCode, String ip) {
        return baseMapper.updateIpByCode(computeUnitCode, ip);
    }

    private int updateBindingByCodeList(List<String> computeUnitCodeList) {
        return baseMapper.updateBindingByCodeList(computeUnitCodeList);
    }

    private List<NetPadV2ComputeUnitCache> getAndCacheComputeUnit(String deviceCode, int size) {
        String computeUnitListCacheKey = getComputeUnitListCacheKey(deviceCode);
        List<NetPadV2ComputeUnitCache> computeUnits = baseMapper.queryAvailableComputeUnit(deviceCode);
        if (CollUtil.isEmpty(computeUnits)) {
            setDeviceComputeUnitIsEmpty(deviceCode);
            return new ArrayList<>();
        }
        List<NetPadV2ComputeUnitCache> resList = new ArrayList<>(size);
        if (size >= computeUnits.size()) {
            // 数量不满足本次的数量，返回全部，写入空值标记。
            setDeviceComputeUnitIsEmpty(deviceCode);
            resList.addAll(computeUnits);
        } else {
            for (int i = 0; i < size; i++) {
                resList.add(computeUnits.get(i));
            }
            computeUnits.removeAll(resList);
            // 缓存该板卡剩余的全部算力单元2小时
            log.info("getAndCacheComputeUnit 缓存算力单元,deviceCode:{},computeUnits:{}", deviceCode, computeUnits);
            List<String> computeUnitJsonList = computeUnits.stream().map(JSONObject::toJSONString).collect(Collectors.toList());
            redisService.rightPushAll(computeUnitListCacheKey, computeUnitJsonList, 1L, TimeUnit.HOURS);
        }
        // 更新算力bind_flag为2
        List<String> computeUnitCodeList = resList.stream().map(NetPadV2ComputeUnitCache::getComputeUnitCode).collect(Collectors.toList());
        int updateCnt = this.updateBindingByCodeList(computeUnitCodeList);
        if (updateCnt != resList.size()) {
            log.error("更新算力单元绑定状态失败,板卡编号：{}, 更新数量：{}", deviceCode, updateCnt);
            throw new BasicException(PadExceptionCode.COMPUTE_UNIT_GET_EXCEPTION);
        }
        return resList;
    }

    private void setDeviceComputeUnitIsEmpty(String deviceCode) {
        // 写入空值防止缓存穿透
        String emptyFlagKey = getEmptyFlagKey(deviceCode);
        redisService.setCacheObject(emptyFlagKey, "1", 5L, TimeUnit.SECONDS);
    }

    @NotNull
    private static String getEmptyFlagKey(String deviceCode) {
        return RedisKeyUtils.cacheKey(computeCodeListKey, "empty", deviceCode);
    }

    @NotNull
    private static String getComputeUnitListCacheKey(String deviceCode) {
        return RedisKeyUtils.cacheKey(computeCodeListKey, deviceCode);
    }

    private List<NetPadV2ComputeUnitCache> getComputeCodeList(int size, String deviceCode) {
        List<NetPadV2ComputeUnitCache> result = new ArrayList<>();
        String computeUnitListCacheKey = getComputeUnitListCacheKey(deviceCode);
        // 先从缓存获取
        if (redisService.hasKey(computeUnitListCacheKey)) {
            for (int i = 0; i < size; i++) {
                Object v = redisService.leftPop(computeUnitListCacheKey);
                if (v == null) {
                    break;
                }
                NetPadV2ComputeUnitCache computeUnitCode = JSONObject.parseObject(v.toString(), NetPadV2ComputeUnitCache.class);
                int updateCnt = this.updateBindingByCodeList(Collections.singletonList(computeUnitCode.getComputeUnitCode()));
                if (updateCnt != 1) {
                    log.error("更新算力单元绑定状态失败,板卡编号：{}, 更新数量：{}", deviceCode, updateCnt);
                    throw new BasicException(PadExceptionCode.COMPUTE_UNIT_GET_EXCEPTION);
                }
                result.add(computeUnitCode);
            }
        }
        return result;
    }

    private boolean computeUnitIsEmpty(String deviceCode) {
        String emptyFlagKey = getEmptyFlagKey(deviceCode);
        return redisService.hasKey(emptyFlagKey);
    }

}
