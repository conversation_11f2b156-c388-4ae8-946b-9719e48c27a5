package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * ADI模板正式版标记DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板正式版标记对象", description = "用于将ADI模板标记为测试版或正式版")
public class AdiTemplateOfficialDTO {

    @ApiModelProperty(value = "模板ID", required = true, example = "123")
    @NotNull(message = "模板ID不能为空")
    private Long id;

    @ApiModelProperty(value = "是否正式版(0:测试版 1:正式版)", required = true, example = "1")
    @NotNull(message = "是否正式版不能为空")
    private Integer isOfficial;
} 