package net.armcloud.paascenter.openapi.utils;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.DefaultProxyRoutePlanner;
import org.apache.http.util.EntityUtils;

import java.util.concurrent.TimeUnit;

@Slf4j
public class ProxyDetectionUtil {
    /**
     * 通过http代理获取出口ip
     * @param ip
     * @param port
     * @param username
     * @param password
     * @return
     */
    public static String getHttpExportIp(String domain,String ip,Integer port,String username,String password) {
        // 目标URL
        String url = domain + "/openapi/open/network/proxy/getMyIp";

        // 创建HTTP客户端
        try {
            CloseableHttpClient httpClient = null;
            if(StrUtil.isEmpty(username) || StrUtil.isEmpty(password)){
                httpClient = getHttpClient(ip,port);
            }else{
                httpClient = getHttpClient(ip,port,username,password);
            }
            // 创建POST请求
            HttpPost postRequest = new HttpPost(url);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(2000) // 连接超时时间2秒
                    .setSocketTimeout(5000)  // 数据读取超时时间5秒
                    .build();
            postRequest.setConfig(requestConfig);
            // 执行请求并处理响应
            try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
                log.debug("ProxyDetectionUtil resp:{}", JSONObject.toJSONString(response));
                // 读取响应内容
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                return JSONObject.parseObject(responseBody).getString("ip");
            }
        } catch (Exception e) {
            log.error("ProxyDetectionUtil error", e);
        }
        return null;
    }

    /**
     * 通过http代理获取出口ip
     * @param ip
     * @param port
     * @param username
     * @param password
     * @return
     */
    public static String getS5ExportIp(String domain,String ip,Integer port,String username,String password) {
        String url = domain + "/openapi/open/network/proxy/getMyIp";
        try{
            // 创建HttpHost对象，表示Socks代理服务器
            HttpHost socksProxy = new HttpHost(ip, port, "socks");
            // 创建CredentialsProvider对象，表示账号密码
            CredentialsProvider credsProvider = new BasicCredentialsProvider();
            credsProvider.setCredentials(
                    new AuthScope(ip, port),
                    new UsernamePasswordCredentials(username, password));
            // 创建HttpClientBuilder对象，用于构建HttpClient对象
            HttpClientBuilder builder = HttpClientBuilder.create();
            builder.setDefaultCredentialsProvider(credsProvider);
            // 设置代理服务器和认证信息
            builder.setRoutePlanner(new DefaultProxyRoutePlanner(socksProxy));
            // 创建HttpClient对象
            CloseableHttpClient httpClient = builder.build();
            // 创建HttpPost对象，表示POST请求
            HttpPost httpPost = new HttpPost(url);
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            // 设置请求配置，包括连接超时时间和请求超时时间
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(5000)
                    .setSocketTimeout(5000)
                    .build();
            httpPost.setConfig(requestConfig);
            // 执行请求并处理响应
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                log.debug("ProxyDetectionUtil resp:{}", JSONObject.toJSONString(response));
                // 读取响应内容
                String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
                return JSONObject.parseObject(responseBody).getString("ip");
            }
        }catch (Exception e) {
            log.error("ProxyDetectionUtil error", e);
        }
        return null;
    }

    /**
     * 代理需要账号密码认证的httpClient
     */
    private static CloseableHttpClient getHttpClient(String proxyHost,int proxyPort, String acc, String pwd) {
        HttpHost proxy = new HttpHost(proxyHost, proxyPort, "HTTP");
        CredentialsProvider provider = new BasicCredentialsProvider();
        provider.setCredentials(new AuthScope(proxy),new UsernamePasswordCredentials(acc, pwd));
        return HttpClients.custom()
                .setProxy(proxy)
                .setDefaultCredentialsProvider(provider)
                .build();
    }

    /**
     * 代理不需要账号密码认证的httpClient
     */
    private static CloseableHttpClient getHttpClient(String proxyHost, int proxyPort) {
        HttpHost proxy = new HttpHost(proxyHost, proxyPort, "HTTP");
        return HttpClients.custom()
                .setProxy(proxy).setConnectionTimeToLive(2, TimeUnit.SECONDS)
                .build();
    }
}
