package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 黑白名单列表查询响应对象
 */
@Data
public class TriggerAppClassifyPadVO implements Serializable {
    @ApiModelProperty(value = "实例编号")
    private String padCode;
    @ApiModelProperty(value = "包名")
    private String packageName;
    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    private Integer classifyType;


}
