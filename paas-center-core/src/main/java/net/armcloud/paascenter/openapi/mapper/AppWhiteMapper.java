package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.AppWhite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 应用白名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Mapper
public interface AppWhiteMapper extends BaseMapper<AppWhite> {

    List<String> selectWhiteAppPkgList(@Param("customerId") Long customerId, @Param("specificationCode") String specificationCode);
}
