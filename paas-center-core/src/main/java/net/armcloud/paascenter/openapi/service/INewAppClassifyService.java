package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerNewAppClassify;
import net.armcloud.paascenter.openapi.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.NewAppClassifyVO;

import java.util.List;

/**
 * 应用分类业务层 - 接口
 */
public interface INewAppClassifyService extends IService<CustomerNewAppClassify> {

    /**
     * 获取应用分类
     * @param param
     * @return
     */
    List<NewAppClassifyVO> list(NewAppClassifyQueryDTO param);

    /**
     * 通过appid查询应用分类
     * @return
     */
    List<NewAppClassifyNameVO> appListByAppIds(QueryNewAppClassifyNameDTO param);
}
