package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class ExecuteADBDTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, message = "实例数量不少于于1个")
    private List<String> padCodes;

    @ApiModelProperty(value = "ADB命令", required = true)
    @NotBlank(message = "scriptContent cannot null")
    private String scriptContent;

    @ApiModelProperty(hidden = true)
    private Long customerId;
}
