package net.armcloud.paascenter.openapi.manager;

import cn.hutool.core.collection.CollUtil;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.mapper.DictMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.utils.RandomUtils.generateRandomNumbersBetween3And5;
import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.DICT_LIST_VALUE_PREFIX;
import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.DICT_SINGLE_VALUE_PREFIX;


@Component
public class DictManager {
    private final RedisService redisService;
    private final DictMapper dictMapper;

    public String getSingleValueByType(String type) {
        String key = DICT_SINGLE_VALUE_PREFIX + type;
        String obj = redisService.getCacheObject(key);
        if (StringUtils.isNotEmpty(obj)) {
            return obj;
        }

        String value = dictMapper.getSingleValueByType(type);
        redisService.setCacheObject(key, value, generateRandomNumbersBetween3And5(), TimeUnit.DAYS);
        return value;
    }

    public List<String> selectValueByTypeList(String dictType) {
        String key = DICT_LIST_VALUE_PREFIX + dictType;
        List<String> cacheList = redisService.getCacheList(key);
        if (CollUtil.isNotEmpty(cacheList)) {
            return cacheList;
        }

        List<String> list = dictMapper.selectValueByTypeList(dictType);
        if (CollUtil.isNotEmpty(list)) {
            redisService.setCacheList(key, list);
        }
        return list;
    }

    public DictManager(RedisService redisService, DictMapper dictMapper) {
        this.redisService = redisService;
        this.dictMapper = dictMapper;
    }


}
