package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;

@Data
public class PadModelInfoVO {

    /**
     * 实例编码
     */
    private String padCode;

    /**
     * IMEI
     */
    private String imei;

    /**
     * 序列号
     */
    private String serialno;


    /**
     * Wi-Fi的mac地址
     */
    private String wifimac;

    /**
     * Android实例唯一标识
     */
    private String androidid;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 厂商
     */
    private String manufacturer;

    /**
     * 是否是ROOT权限
     */
    private String isRoot;

    /**
     * 云手机的宽 最大不超过1080
     */
    private Integer width;

    /**
     * 云手机的高 最大不超过1920
     */
    private Integer height;

    /**
     * 内存限额
     */
    private Integer memoryLimit;

    /**
     * 蓝牙地址
     */
    private String bluetoothaddr;

    /**
     * 手机号码
     */
    private String phonenum;

    /**
     * 内存大小
     */
    private String dataSize;


    /**
     * 可用内存大小
     */
    private String dataSizeAvailable;
    /**
     *已使用内存
     */
    private String dataSizeUsed;

    private String romVersion;


}
