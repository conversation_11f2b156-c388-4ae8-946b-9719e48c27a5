package net.armcloud.paascenter.openapi.controller;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.AdiCustomerRecord;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateLogVO;
import net.armcloud.paascenter.openapi.service.IAdiCustomerRecordService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/openapi/adiCustomerRecord")
public class AdiCustomerRecordController {

    @Resource
    private IAdiCustomerRecordService adiCustomerRecordService;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Result<Page<AdiTemplateLogVO>> list(@RequestBody @Valid AdiCustomerRecord record) {
        record.setCreateBy(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()) + "");
        adiCustomerRecordService.save(record);
        return Result.ok();
    }

}
