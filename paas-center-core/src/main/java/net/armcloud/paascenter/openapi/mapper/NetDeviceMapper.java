package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.openapi.model.dto.NetDeviceDTO;
import net.armcloud.paascenter.openapi.model.vo.NetDeviceVO;
import net.armcloud.paascenter.common.model.entity.paas.NetDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NetDeviceMapper {
    List<NetDeviceVO> listNetDevice(NetDeviceDTO param);

    void saveNetDevice(NetDevice param);

    void updateNetDevice(NetDevice param);

    void deleteNetDevice(Long id);

    NetDevice selectNetDeviceByIpv4(String ipv4Cidr);

    List<NetDevice> selectNetDeviceByIpv4OrNameExcludingId(@Param("ipv4Cidr")String ipv4Cidr, @Param("name")String name, @Param("id")Long id);

    NetDevice selectById(Long id);

    NetDeviceVO selectVoById(Long id);

    List<NetDeviceVO> selectListNetDevice(Integer bindFlag);

    void updateNetDeviceBindFlag(@Param("deviceSubnet")String deviceSubnet,@Param("bindFlag") byte bindFlag);
}
