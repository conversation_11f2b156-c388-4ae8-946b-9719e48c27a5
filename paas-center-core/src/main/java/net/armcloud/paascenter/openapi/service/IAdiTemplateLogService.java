package net.armcloud.paascenter.openapi.service;


import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.AdiTemplateLog;
import net.armcloud.paascenter.openapi.model.dto.AdiTemplateLogQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateLogVO;

/**
 * <p>
 * ADI模板操作日志服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface IAdiTemplateLogService {

    /**
     * 查询ADI模板操作日志列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<AdiTemplateLogVO> queryAdiTemplateLogs(AdiTemplateLogQueryDTO queryDTO);

    /**
     * 获取ADI模板操作日志详情
     *
     * @param id 日志ID
     * @return 日志详情
     */
    AdiTemplateLogVO getAdiTemplateLogDetail(Long id);

    /**
     * 记录ADI模板操作日志
     *
     * @param templateId     ADI模板ID
     * @param operationType  操作类型
     * @param operator    操作者
     * @param operationDetail 操作详情
     * @return 是否成功
     */
    boolean recordLog(Long templateId, Integer operationType, String operator, String operationDetail);

    /**
     * 记录ADI模板操作日志
     *
     * @param log 日志实体
     * @return 是否成功
     */
    boolean recordLog(AdiTemplateLog log);
}
