package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.PadGroupDTO;
import net.armcloud.paascenter.openapi.model.vo.PadGroupVO;
import net.armcloud.paascenter.openapi.service.IPadGroupService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/openapi/open/group")
@Api(tags = "实例")
public class PadGroupController {

    @Resource
    private IPadGroupService padGroupService;

    @PostMapping("infos")
    @ApiOperation(value = "查询实例分组列表")
    public Result<List<PadGroupVO>> infos(@Valid @RequestBody PadGroupDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(padGroupService.padGroupListService(param));
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建分组")
    public Result<?> add(@Validated(PadGroupDTO.create.class) @RequestBody PadGroupDTO param) {
        padGroupService.addPadGroup(param);
        return Result.ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除分组")
    public Result<?> delete(@Validated(PadGroupDTO.delete.class) @RequestBody PadGroupDTO param) {
        padGroupService.deletePadGroup(param.getIds());
        return Result.ok();
    }

    @PostMapping("/move")
    @ApiOperation(value = "移动分组")
    public Result<?> move(@Validated(PadGroupDTO.move.class) @RequestBody PadGroupDTO param) {
        padGroupService.movePadGroup(param);
        return Result.ok();
    }
}
