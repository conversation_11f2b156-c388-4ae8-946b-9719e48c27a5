package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.openapi.model.dto.DeleteFileVO;
import net.armcloud.paascenter.openapi.service.FileService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/1/24 10:44
 * @Version 1.0
 */
@RestController
@RequestMapping("/openapi/open/file")
@Api(tags = "文件")
@Slf4j
public class FileController {

    private final FileService fileService;

    public FileController(FileService fileService) {
        this.fileService = fileService;
    }


    @PostMapping("/deleteFile")
    @ApiOperation(value = "删除文件")
    public Result<?> screenshotLocal(@Valid @RequestBody DeleteFileVO deleteFileVO) {
//        fileService.deleteFile(deleteFileVO);
        log.error("FileController screenshotLocal ??????????");
        return Result.ok();
    }
}
