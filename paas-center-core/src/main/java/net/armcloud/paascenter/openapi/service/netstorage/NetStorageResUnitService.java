package net.armcloud.paascenter.openapi.service.netstorage;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.EdgeCluster;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResUnitDTO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageResUnitVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/25 18:35
 * @Description:
 */
public interface NetStorageResUnitService extends IService<NetStorageResUnit> {

    /**
     * 根据网存code批量更新网存绑定状态
     * @param netStorageResCodeList
     */
    void updateBatchBindByCodeList(List<String> netStorageResCodeList,Integer bindStatus);

    /**
     * 获取存储详情
     * @param param
     * @return
     */
    Page<NetStorageResUnitVO> netStoragePadResUnit(NetStorageResUnitDTO param);

    /**
     * 根据网存获取集群详细信息
     * @param netStorageResUnitCode
     */
    EdgeCluster getEdgeClusterByCode(String netStorageResUnitCode);

    /**
     * 验证当前是否可以用目标存储id开机
     * @param padDetailsVO
     * @return
     */
    Boolean checkBackup(PadDetailsVO padDetailsVO);

    /**
     * 根据存储code更新当前存储的值
     * @param netStorageResUnitList
     */
    void updateNetStorageResUnitByCode(List<NetStorageResUnit> netStorageResUnitList);

    /**
     * 根据存储code获取存储详情
     * @param netStorageResUnitCode 网存code
     * @return 网存详情
     */
    NetStorageResUnitVO getByCode(String netStorageResUnitCode);
}
