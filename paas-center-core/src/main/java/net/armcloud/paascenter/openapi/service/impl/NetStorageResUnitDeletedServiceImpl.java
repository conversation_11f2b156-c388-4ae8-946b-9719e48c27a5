package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.mapper.NetStorageResUnitDeletedMapper;
import net.armcloud.paascenter.openapi.model.entity.NetStorageResUnitDeleted;
import net.armcloud.paascenter.openapi.service.NetStorageResUnitDeletedService;
import org.springframework.stereotype.Service;

/**
 * 网络存储单元删除记录Service实现类
 * <AUTHOR>
 * @Date 2025/6/25
 * @Description: 网络存储单元删除记录服务实现
 */
@Service
@Slf4j
public class NetStorageResUnitDeletedServiceImpl extends ServiceImpl<NetStorageResUnitDeletedMapper, NetStorageResUnitDeleted> implements NetStorageResUnitDeletedService {

}
