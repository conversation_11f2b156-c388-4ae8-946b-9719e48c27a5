package net.armcloud.paascenter.openapi.service.impl;


import net.armcloud.paascenter.common.client.internal.vo.GeneratePadBackupTaskVO;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.DataDelDTO;
import net.armcloud.paascenter.common.model.dto.api.DataDelMasterDTO;
import net.armcloud.paascenter.common.model.dto.api.PadBackupDTO;
import net.armcloud.paascenter.common.model.dto.api.PadRestoreDTO;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.manager.TaskManager;
import net.armcloud.paascenter.openapi.mapper.DevicePadMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadStatusMapper;
import net.armcloud.paascenter.openapi.model.bo.AddBackupTaskBO;
import net.armcloud.paascenter.openapi.model.bo.RestoreBackupTaskBO;
import net.armcloud.paascenter.openapi.model.vo.DevicePadVO;
import net.armcloud.paascenter.openapi.service.IDeviceChangeInfoService;
import net.armcloud.paascenter.openapi.service.IPadBackupService;
import net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import net.armcloud.paascenter.common.model.entity.task.PadBackupTaskInfo;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.PAD_CODE_NOT_EXIST;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PROCESSING_FAILED;

@Service
public class PadBackupServiceImpl implements IPadBackupService {
    private final PadMapper padMapper;
    private final TaskManager taskManager;
    private final PadStatusMapper padStatusMapper;
    private final DevicePadMapper devicePadMapper;

    private final IDeviceChangeInfoService deviceChangeInfoService;
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Override
    public List<GeneratePadBackupTaskVO> backup(PadBackupDTO param) {
        List<String> padCodes = param.getPadCodes();
        List<DevicePadVO> padVOList = devicePadMapper.listByPadCode(padCodes);
        Map<String, DevicePadVO> devicePadVOS = padVOList.stream()
                .collect(Collectors.toMap(DevicePadVO::getPadCode, obj -> obj, (o1, o2) -> o1));
        if (padCodes.size() != devicePadVOS.size()) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }
        //验证当前实例IP是否支持备份功能
        List<String> queryDeviceIpList = padVOList.stream().map(DevicePadVO::getPadCode).distinct().collect(Collectors.toList());
        List<String> deviceIpList = deviceChangeInfoService.getMountVersionV1(queryDeviceIpList);
        //有板卡挂载方式不是V2版本，不支持备份功能
        if(queryDeviceIpList.size()>deviceIpList.size()){
            throw new BasicException(PadExceptionCode.MOUNT_VERSION_NOT_BACKUP);
        }
        List<PadStatus> padStatuses = padStatusMapper.listByPadCodes(padCodes);
        String noRunningPadCodes = padStatuses.stream()
                .filter(padStatus -> !Lists.newArrayList(PadStatusConstant.RUNNING,PadStatusConstant.ABNORMAL).contains(padStatus.getPadStatus()))
                .map(PadStatus::getPadCode).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(noRunningPadCodes)) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "实例[" + noRunningPadCodes + "]当前状态不支持备份");
        }

        List<GeneratePadBackupTaskVO> result = new ArrayList<>(padCodes.size());
        AddBackupTaskBO addBackupTaskBO = new AddBackupTaskBO();
        addBackupTaskBO.setCustomerId(param.getCustomerId());
        addBackupTaskBO.setTaskSource(param.getTaskSource());

        List<Pad> pads = padMapper.listByPadCodes(padCodes);
        Map<String, String> padCodeSpecificationCodeMap = pads.stream()
                .collect(Collectors.toMap(Pad::getPadCode, Pad::getDeviceLevel, (o1, o2) -> o1));

        List<AddBackupTaskBO.Pad> taskBoPads = new ArrayList<>(padCodes.size());
        String date = dateTimeFormatter.format(LocalDateTime.now());
        padCodes.forEach(padCode -> {
            AddBackupTaskBO.Pad taskBO = new AddBackupTaskBO.Pad();
            taskBO.setPadCode(padCode);
            String backupName = padCode + "_" + date;
            if (StringUtils.isNotBlank(param.getBackupNamePrefix())) {
                backupName = param.getBackupNamePrefix() + "_" + backupName;
            }

            taskBO.setBackupName(backupName);
            taskBO.setDeviceId(devicePadVOS.get(padCode).getDeviceId());
            taskBO.setSpecificationCode(padCodeSpecificationCodeMap.get(padCode));
            taskBoPads.add(taskBO);
        });
        addBackupTaskBO.setPads(taskBoPads);

        List<PadTask> padTasks = taskManager.addBackupTask(addBackupTaskBO);
        padTasks.forEach(padTask -> {
            GeneratePadBackupTaskVO generatePadTaskVO = new GeneratePadBackupTaskVO();
            generatePadTaskVO.setTaskId(padTask.getCustomerTaskId());
            String padCode = padTask.getPadCode();
            generatePadTaskVO.setPadCode(padCode);
            String backupName = addBackupTaskBO.getPads().stream()
                    .filter(pad -> Objects.equals(pad.getPadCode(), padCode))
                    .map(AddBackupTaskBO.Pad::getBackupName).findFirst()
                    .orElse("");
            generatePadTaskVO.setBackupName(backupName);
            result.add(generatePadTaskVO);
        });

        return result;
    }

    @Override
    public List<GeneratePadTaskVO> restore(PadRestoreDTO param) {
        List<String> padCodes = param.getPadCodes();
        List<DevicePadVO> padVOList = devicePadMapper.listByPadCode(padCodes);
        Map<String, DevicePadVO> padCodeDeviceMap = padVOList.stream()
                .collect(Collectors.toMap(DevicePadVO::getPadCode, obj -> obj, (o1, o2) -> o1));
        if (padCodes.size() != padCodeDeviceMap.size()) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }

        List<PadStatus> padStatuses = padStatusMapper.listByPadCodes(padCodes);
        String noRunningPadCodes = padStatuses.stream()
                .filter(padStatus -> !Lists.newArrayList(PadStatusConstant.RUNNING,PadStatusConstant.ABNORMAL).contains(padStatus.getPadStatus()))
                .map(PadStatus::getPadCode).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(noRunningPadCodes)) {
            throw new BasicException(PROCESSING_FAILED.getStatus(), "实例[" + noRunningPadCodes + "]当前状态不支持还原");
        }
        List<String> queryDeviceIpList = padVOList.stream().map(DevicePadVO::getPadCode).distinct().collect(Collectors.toList());
        //有板卡挂载方式不是V2版本，不支持备份还原功能
        List<String> deviceIpList = deviceChangeInfoService.getMountVersionV1(queryDeviceIpList);
        if(queryDeviceIpList.size()>deviceIpList.size()){
            throw new BasicException(PadExceptionCode.MOUNT_VERSION_NOT_BACKUP);
        }
        Long backupId = param.getBackupId();
        String backupName = param.getBackupName();
        if (StringUtils.isBlank(backupName) && backupId == null) {
            throw new BasicException("backupName and backupId cannot null");
        }

        Long customerId = param.getCustomerId();
        PadBackupTaskInfo padBackupTaskInfo;
        // 目前管理员的ID固定为0，这样处理是为兼容管理员的操作，如果是管理员操作则无需校验用户权限
        if (customerId <= 0) {
            padBackupTaskInfo = taskManager.getCustomerLatestPadBackupData(backupName, backupId, null);
        } else {
            padBackupTaskInfo = taskManager.getCustomerLatestPadBackupData(backupName, backupId, customerId);
        }

        if (padBackupTaskInfo == null) {
            throw new BasicException("备份数据不存在");
        }

        String specificationCode = padBackupTaskInfo.getSpecificationCode();
        String notSupportPadCodes = padMapper.listByPadCodes(padCodes).stream()
                .filter(pad -> !Objects.equals(pad.getDeviceLevel(), specificationCode))
                .map(Pad::getPadCode)
                .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(notSupportPadCodes)) {
            throw new BasicException("实例[" + notSupportPadCodes + "]不支持恢复此备份数据");
        }

        List<GeneratePadTaskVO> taskVOS = new ArrayList<>(padCodes.size());
        RestoreBackupTaskBO addBackupTaskBO = new RestoreBackupTaskBO();
        addBackupTaskBO.setCustomerId(customerId);
        addBackupTaskBO.setTaskSource(param.getTaskSource());

        List<RestoreBackupTaskBO.Pad> taskBoPads = new ArrayList<>(padCodes.size());
        padCodes.forEach(padCode -> {
            RestoreBackupTaskBO.Pad taskBO = new RestoreBackupTaskBO.Pad();
            taskBO.setPadCode(padCode);
            taskBO.setBackupId(padBackupTaskInfo.getId());
            taskBO.setDeviceId(padCodeDeviceMap.get(padCode).getDeviceId());
            taskBoPads.add(taskBO);
        });
        addBackupTaskBO.setPads(taskBoPads);

        List<PadTask> padTasks = taskManager.addRestoreTask(addBackupTaskBO);
        padTasks.forEach(padTask -> {
            GeneratePadTaskVO generatePadTaskVO = new GeneratePadTaskVO();
            generatePadTaskVO.setTaskId(padTask.getCustomerTaskId());
            generatePadTaskVO.setPadCode(padTask.getPadCode());
            taskVOS.add(generatePadTaskVO);
        });

        return taskVOS;
    }

    @Override
    public List<DataDelDTO> dataDel(DataDelMasterDTO param) {
       return taskManager.delPadBackupData(param);
    }

    public PadBackupServiceImpl(PadMapper padMapper, PadStatusMapper padStatusMapper, TaskManager taskManager,
                                DevicePadMapper devicePadMapper,IDeviceChangeInfoService deviceChangeInfoService) {
        this.padMapper = padMapper;
        this.taskManager = taskManager;
        this.padStatusMapper = padStatusMapper;
        this.devicePadMapper = devicePadMapper;
        this.deviceChangeInfoService = deviceChangeInfoService;
    }

}
