package net.armcloud.paascenter.openapi.utils;

import net.armcloud.paascenter.openapi.service.impl.NumberUtil;

public class SimUtils {

    /**
     * 使用 Luhn 算法计算校验位
     * 
     * @param number 不包含校验位的 ICCID 数字串
     * @return 校验位数字
     */
    public static int computeLuhnCheckDigit(String number) {
        int sum = 0;
        boolean alternate = true;
        // 从右向左遍历数字
        for (int i = number.length() - 1; i >= 0; i--) {
            int n = number.charAt(i) - '0';
            if (alternate) {
                n *= 2;
                if (n > 9) {
                    n = (n % 10) + 1; // 等同于 n - 9
                }
            }
            sum += n;
            alternate = !alternate;
        }
        int checkDigit = (10 - (sum % 10)) % 10;
        return checkDigit;
    }

    /**
     * 生成手机号码
     * @param phoneRegex 手机号码正则表达式
     * @param phoneCode 手机号前缀
     * @return 手机号码
     */
    public static String generatePhoneNumber(String phoneRegex, String phoneCode) {
        String phone = "";
        // 如果phoneRegex不为空，则使用phoneRegex
        if (null != phoneRegex && phoneRegex.length() > 0) {
            phone = NumberUtil.generateValue(phoneRegex);
        } else {
            // 手机编码加上7-10位数字
            phone = phoneCode + NumberUtil.generateValue("[0-9]{7,10}");
        }
        // 如果手机号码长度大于20，则截取前20位
        if (phone.length() > 20) {
            phone = phone.substring(0, 20);
        }
        return phone;
    }
    /**
     * 生成ICCID
     * 
     * @param iccidPrefix 前缀，可以为空
     * @param phoneCode   手机号前缀，可以为空
     * @return ICCID
     */
    public static String generateICCID(String iccidRegex, String phoneCode) {
        String iccid = "";
        // 如果iccidRegex不为空，则使用iccidRegex
        if (null != iccidRegex && iccidRegex.length() > 0 && iccidRegex.startsWith("89")) {
            iccid = NumberUtil.generateValue(iccidRegex);
        } else { // 如果iccidRegex为空，则生成89开头，phoneCode为1位，就在前面补零
            // phoneCode只有一位，就在前面补零
            if (phoneCode.length() == 1) {
                phoneCode = "0" + phoneCode;
            }
            iccid = "89" + phoneCode;
        }
        // 不足19位，随机生成数字填充
        if (iccid.length() < 19) {
            iccid = iccid + NumberUtil.generateValue("[0-9]{" + (19 - iccid.length()) + "}");
        }
        // 如果超过19位，截取到19位, 确保iccid是19位
        if (iccid.length() > 19) {
            iccid = iccid.substring(0, 19);
        }
        // 计算校验位
        int checkDigit = computeLuhnCheckDigit(iccid);
        iccid = iccid + checkDigit;
        
        return iccid;
    }

    /**
     * 生成IMSI
     * 
     * @param mcc 移动国家代码
     * @param mnc 移动网络代码
     * @return IMSI
     */
    public static String generateIMSI(String mcc, String mnc) {
        // mnc长度小于2，前面补零
        if (mnc.length() < 2) {
            mnc = "0" + mnc;
        }
        String imsi = mcc + mnc;
        imsi += NumberUtil.generateValue("[0-9]{" + (15 - imsi.length()) + "}");
        return imsi;
    }


    public static void main(String[] args) {
        // System.out.println(generateICCID(null, "86"));
        System.out.println(generateIMSI("355", "068")); 
        // System.out.println(NumberUtil.generateValue("355 6[7-9]\\d{6}"));
        System.out.println(NumberUtil.generateValue("1(403|587|825|780|867|236|250|604|778|204|431|506|709|902|905|418|438|450|514|579|581|819|873|902|905|226|249|289|343|365|416|437|519|613|647|705|807|905|226|249|289|343|365|416|437|519|613|647|705|807|902|905)\\d{7}"));
    }
}