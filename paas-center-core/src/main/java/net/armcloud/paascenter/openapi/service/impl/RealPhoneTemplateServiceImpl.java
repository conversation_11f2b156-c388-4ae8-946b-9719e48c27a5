package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.common.utils.CustomerUtils;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.MISSING_NECESSARY_CUSTOMER_INFORMATION;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.filecenter.service.FileStorageService;
import net.armcloud.paascenter.filecenter.service.OssService;
import net.armcloud.paascenter.filecenter.service.UserFileService;
import net.armcloud.paascenter.openapi.mapper.AdiTemplateCustomerMapper;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.RealPhoneTemplateMapper;
import net.armcloud.paascenter.openapi.mapper.ScreenLayoutMapper;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.enums.AdiTemplateIsOfficialEnum;
import net.armcloud.paascenter.openapi.model.enums.AdiTemplateOperationTypeEnum;
import net.armcloud.paascenter.openapi.model.vo.*;
import net.armcloud.paascenter.openapi.service.IAdiTemplateLogService;
import net.armcloud.paascenter.openapi.service.IRealPhoneTemplateService;
import net.armcloud.paascenter.openapi.service.IScreenLayoutService;
import net.armcloud.paascenter.openapi.utils.AdiParserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RealPhoneTemplateServiceImpl implements IRealPhoneTemplateService {

    private final RealPhoneTemplateMapper realPhoneTemplateMapper;

    public RealPhoneTemplateServiceImpl(RealPhoneTemplateMapper realPhoneTemplateMapper){
        this.realPhoneTemplateMapper = realPhoneTemplateMapper;
    }


    @Resource
    private IAdiTemplateLogService adiTemplateLogService;

    @Resource
    private RedisService redisService;

    @Resource
    private FileStorageService fileStorageService;

    @Resource
    private UserFileService userFileService;

    @Resource
    private OssService ossService;
    @Resource
    private IScreenLayoutService screenLayoutService;
    @Resource
    private ScreenLayoutMapper screenLayoutMapper;

    @Resource
    private CustomerMapper customerMapper;

    @Resource
    private AdiTemplateCustomerMapper adiTemplateCustomerMapper;

    //ADI版本号缓存
    public static final String CUSTOMER_ADI_VERSION_SERIAL_NO = "adi:adi_version_serial_no:";

    private static final String UPLOAD_STATUS_SUCCESS = "success";
    private static final String FILE_STATUS_VALID = "valid";
    private static final String ANDROID_MODEL_PREFIX = "realdevice_";

    // 模板公共状态：1-公共模板，0-非公共
    private static final Integer TEMPLATE_PUBLIC = 1;
    private static final Integer TEMPLATE_NON_PUBLIC = 0;

    public static final String ADI_ZIP_PARSE_DIRECTORY = "adi:adi_fingerprintMd5_zip:";
    private static final Random random = new Random();

    @Override
    public List<RealPhoneTemplateVO> pageList(RealPhoneTemplateDTO realPhoneTemplateDTO) {
        PageHelper.startPage(realPhoneTemplateDTO.getPageIndex(), realPhoneTemplateDTO.getPageSize(),false);
        Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        // 创建基本查询条件
        QueryWrapper<RealPhoneTemplate> realPhoneTemplate = new QueryWrapper<RealPhoneTemplate>()
                .eq("delete_flag", 0)
                .eq("status", 1);
                
        // 添加规格编号和安卓镜像版本的过滤条件
//        if(!StringUtils.isEmpty(realPhoneTemplateDTO.getResourceSpecificationCode())){
//            realPhoneTemplate.eq("resource_specification_code", realPhoneTemplateDTO.getResourceSpecificationCode());
//        }
        if(!StringUtils.isEmpty(realPhoneTemplateDTO.getAndroidImageVersion())){
            realPhoneTemplate.eq("android_image_version", realPhoneTemplateDTO.getAndroidImageVersion());
        }
        
        boolean isAdmin = redisService.isAdmin(customerId);
        // 管理员可以查看所有模板
        if (!isAdmin) {
            realPhoneTemplate.and(wrapper -> 
                wrapper.eq("is_public", TEMPLATE_PUBLIC)
                    .or()
                    .inSql("id", "SELECT template_id FROM adi_template_customer WHERE customer_id = " + customerId)
            );
        }
        
        // 执行查询并转换结果
        List<RealPhoneTemplate> realPhoneTemplateList = realPhoneTemplateMapper.selectList(realPhoneTemplate);
        List<RealPhoneTemplateVO> realPhoneTemplateVOList = null;
        if(CollectionUtils.isNotEmpty(realPhoneTemplateList)){
            realPhoneTemplateVOList = BeanUtil.copyToList(realPhoneTemplateList, RealPhoneTemplateVO.class);
            return realPhoneTemplateVOList.stream()
                    .collect(Collectors.groupingBy(RealPhoneTemplateVO.TemplateUniqueKey::build))
                    .values()
                    .stream()
                    .map(templates -> templates.get(random.nextInt(templates.size())))
                    .collect(Collectors.toList());
        }
        return realPhoneTemplateVOList;
    }


    @Override
    public Page<AdiTemplateVO> queryAdiTemplates(AdiTemplateQueryDTO queryDTO) {
        try {
            PageHelper.startPage(queryDTO.getPage(), queryDTO.getRows());
            long customerId =CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
            boolean isAdmin = redisService.isAdmin(customerId);
            
            if (queryDTO.getCustomerIds() == null) {
                queryDTO.setCustomerIds(new ArrayList<>());
            }
            
            if (!isAdmin && !queryDTO.getCustomerIds().contains(customerId)) {
                queryDTO.getCustomerIds().add(customerId);
            }
            
            Integer templateType = queryDTO.getTemplateType();
            if (templateType != null) {
                if (templateType == 1) { // 公共模板
                    queryDTO.setCustomerIds(new ArrayList<>());
                } else if (templateType == 2 && !isAdmin) { // 自定义模板（非管理员）
                    queryDTO.getCustomerIds().clear();
                    queryDTO.getCustomerIds().add(customerId);
                }
            }

            List<AdiTemplateVO> voList = realPhoneTemplateMapper.queryAdiTemplates(queryDTO, isAdmin);
            for (AdiTemplateVO vo : voList) {
                String layout = vo.getScreenWidth() + "x" + vo.getScreenHigh() + "px | " 
                              + vo.getPixelDensity() + "dpi | " 
                              + vo.getScreenRefreshRate() + "fps";
                vo.setScreenLayout(layout);
            }
            
            return new Page<>(voList);
        } catch (Exception e) {
            log.error("查询ADI模板失败", e);
            throw new BasicException("查询adi模板失败");
        }
    }


    @Override
    public AdiTemplateVO getAdiTemplateDetail(Long id) {
        if (id == null) {
            return null;
        }
        // 获取当前用户ID
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        // 查询模板基本信息
        RealPhoneTemplate template = realPhoneTemplateMapper.selectById(id);
        if (template == null) {
            return null;
        }
        // 检查权限: 判断当前用户是否有权限查看此模板
        if (!hasTemplateAccessPermission(id, customerId)) {
            log.warn("用户 {} 无权限查看模板 {}", customerId, id);
            return null;
        }
        // 有权限，转换为VO并返回
        AdiTemplateVO vo = convertToVO(template);

        // 获取关联的客户ID列表并设置到VO中
        if (template.getIsPublic() == null || !Objects.equals(template.getIsPublic(), TEMPLATE_PUBLIC)) {
            List<Long> customerIds = getTemplateCustomerIds(id);
            vo.setCustomerIds(customerIds);
        }

        // 获取关联的实例数
        Integer instanceCount = getTemplateInstanceCount(id);
        vo.setInstanceCount(instanceCount);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> createAdiTemplate(AdiTemplateCreateDTO dto) {
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        CustomerInfoVo customerInfoVo = customerMapper.getCustomerInfoById(customerId);
        String fingerprintMd5 = dto.getFingerprintMd5();
        RealPhoneTemplate template = new RealPhoneTemplate();
        template.setBrand(dto.getBrand());
        template.setModel(dto.getModel());
        template.setResourceSpecificationCode(dto.getResourceSpecificationCode());
        template.setScreenLayoutCode(dto.getScreenLayoutCode());
        template.setAdiTemplateDownloadUrl(dto.getPublicUrl());
        template.setAdiTemplatePwd(dto.getAdiTemplatePwd());
        template.setPropertyJSON(dto.getProperty());
        template.setAndroidImageVersion(dto.getAndroidImageVersion());
        template.setFingerprint(dto.getFingerprint());
        template.setFingerprintMd5(fingerprintMd5);
        template.setIsPublic(0);
        template.setStatus(0);
        template.setDeviceName(dto.getDeviceName());
        template.setModelCode(dto.getModelCode());
        template.setAospVersion(dto.getAospVersion());
        template.setAdiTemplateVersion(generateAdiVersion());
        template.setTestCasesDownloadUrl(dto.getTestCasesDownloadUrl());
        // 3. 保存实体
        int addRow = realPhoneTemplateMapper.insert(template);
        if(addRow > 0){
            try {
                SpringUtil.getBean(RealPhoneTemplateServiceImpl.class).addScreenLayout(template.getScreenLayoutCode(), customerInfoVo);
            } catch (Exception e) {
                log.error("新增屏幕布局失败", e);
                throw new BasicException("屏幕布局处理失败");
            }
            // 如果有客户授权列表，则添加授权关系
            if (Objects.equals(template.getIsPublic(), TEMPLATE_NON_PUBLIC) && CollUtil.isNotEmpty(dto.getCustomerIds())) {
                adiTemplateCustomerMapper.batchInsertTemplateCustomers(template.getId(), dto.getCustomerIds(),customerId);
            }

            // 4. 记录操作日志
            adiTemplateLogService.recordLog(
                    template.getId(),
                    AdiTemplateOperationTypeEnum.UPLOAD.getCode(),
                    customerInfoVo.getCustomerName(),
                    "新增ADI模板: " + template.getBrand()+"+"+template.getModel()+"+"+template.getAndroidImageVersion()
            );
        }
        return Result.ok(template.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void addScreenLayout(String screenLayoutCode, CustomerInfoVo customerInfoVo) {
        // 查询screen_layout表，判断是否存在对应的屏幕布局。如果不存在则新增。
        ScreenLayoutVO screenLayoutVO = screenLayoutService.getByCode(screenLayoutCode);

        if(Objects.isNull(screenLayoutVO)){
            ScreenLayoutVO screenLayout = getScreenLayoutVO(screenLayoutCode);
            screenLayoutService.addScreenLayout(screenLayout, customerInfoVo.getCustomerName());
        } else {
            if (screenLayoutVO.getStatus() == 1 && screenLayoutVO.getDeleteFlag() == 0) {
                // 原屏幕布局数据状态正常
                return;
            }
            // 更新status和delete_flag
            UpdateWrapper<ScreenLayout> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("code", screenLayoutCode)
                    .set("status", 1)
                    .set("delete_flag", 0);
            screenLayoutMapper.update(updateWrapper);
        }
    }

    @NotNull
    private static ScreenLayoutVO getScreenLayoutVO(String screenLayoutCode) {
        ScreenLayoutVO screenLayout = new ScreenLayoutVO();
        screenLayout.setCode(screenLayoutCode);
        // 按这个realdevice_1080x2244x408 格式分割 layoutCode, 格式为realdevice_宽x高xDPI
        String[] layoutInfo = screenLayoutCode.split("_")[1].split("x");
        screenLayout.setScreenWidth(layoutInfo[0]);
        screenLayout.setScreenHigh(layoutInfo[1]);
        screenLayout.setScreenRefreshRate("60");
        screenLayout.setPixelDensity(layoutInfo[2]);
        return screenLayout;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> createCustomAdiTemplate(AdiCustomTemplateCreateDTO createDTO) {
        // 通过 传入的鉴权参数获取对应的customId
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        AdiTemplateCreateDTO newCreateDto = new AdiTemplateCreateDTO(createDTO);
        newCreateDto.setCustomerIds(Collections.singletonList(customerId));
        return SpringUtil.getBean(RealPhoneTemplateServiceImpl.class).createAdiTemplate(newCreateDto);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateAdiTemplate(AdiTemplateUpdateDTO updateDTO) {
        RealPhoneTemplate template = BeanUtil.copyProperties(updateDTO, RealPhoneTemplate.class);

        // 获取当前操作用户
        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        CustomerInfoVo customerInfoVo = customerMapper.getCustomerInfoById(customerId);

        // 检查原模板是否存在
        RealPhoneTemplate existsRealPhoneTemplate = realPhoneTemplateMapper.selectById(updateDTO.getId());
        if (Objects.isNull(existsRealPhoneTemplate)) {
            throw new BasicException("ADI模板不存在");
        }
        
        // 检查是否为公共模板，且不是管理员
        if (TEMPLATE_PUBLIC.equals(existsRealPhoneTemplate.getIsPublic()) && !redisService.isAdmin(customerId)) {
            return Result.fail("公共模板不可修改");
        }
        // 检查权限: 判断当前用户是否有权限操作此模板
        if (!hasTemplateAccessPermission(updateDTO.getId(), customerId)) {
            return Result.fail("无权操作该模板");
        }

        // 检查是否有指定的客户已有相同指纹MD5的模板（排除当前模板自身）
        List<Long> customerIdsToCheck = updateDTO.getCustomerIds();
        if (CollUtil.isNotEmpty(customerIdsToCheck)) {
            // 存储已存在模板的客户名称列表
            List<String> existingCustomerNames = new ArrayList<>();

            // 遍历每个客户ID，检查是否已存在相同指纹MD5且ID不同的模板
            for (Long cid : customerIdsToCheck) {
                RealPhoneTemplate dupTemplate = realPhoneTemplateMapper.checkFingerprintMd5Exists(updateDTO.getFingerprintMd5(), cid);
                if (Objects.nonNull(dupTemplate) && !dupTemplate.getId().equals(updateDTO.getId())) {
                    // 获取客户名称
                    CustomerInfoVo customerInfo = customerMapper.getCustomerInfoById(cid);
                    String customerName = customerInfo != null ? customerInfo.getCustomerName() : "客户ID:" + cid;
                    existingCustomerNames.add(customerName);
                }
            }

            // 如果存在冲突，抛出异常并提供详细信息
            if (!existingCustomerNames.isEmpty()) {
                String errorCustomers = String.join("、", existingCustomerNames);
                log.error("指定客户下已存在相同指纹MD5的其他模板: {}", errorCustomers);
                throw new BasicException("客户(" + errorCustomers + ")已存在相同的ADI模板，请重新上传源文件");
            }
        } else {
            // 如果没有指定客户ID，则视为公共模板，检查是否已存在相同指纹MD5的公共模板（排除当前模板自身）
            QueryWrapper<RealPhoneTemplate> wrapper = new QueryWrapper<RealPhoneTemplate>()
                    .eq("fingerprint_md5", updateDTO.getFingerprintMd5())
                    .eq("is_public", TEMPLATE_PUBLIC)
                    .eq("delete_flag", 0)
                    .ne("id", updateDTO.getId());
            if (null != updateDTO.getResourceSpecificationCode()) {
                wrapper.eq("resource_specification_code", updateDTO.getResourceSpecificationCode());
            }
            RealPhoneTemplate existsPublicTemplate = realPhoneTemplateMapper.selectOne(wrapper);

            if (Objects.nonNull(existsPublicTemplate)) {
                log.error("已存在相同指纹MD5的其他公共ADI模板:{}", existsPublicTemplate.getId());
                throw new BasicException("已存在相同的公共ADI模板，请重新上传源文件");
            }
        }
        RealPhoneTemplate realMd5 = realPhoneTemplateMapper.selectOne(new QueryWrapper<RealPhoneTemplate>().eq("fingerprint_md5",updateDTO.getFingerprintMd5()).last("limit 1"));
        // 需要判断是否重新提交了ADI模板
        if(Objects.isNull(realMd5)){
            parseAndUploadTemplate(template, updateDTO.getPublicUrl(), customerInfoVo.getCustomerName());
        }else if(!realMd5.getAdiTemplateDownloadUrl().equals(updateDTO.getPublicUrl())){
            parseAndUploadTemplate(template, updateDTO.getPublicUrl(), customerInfoVo.getCustomerName());
        }else{
            template.setAdiTemplateDownloadUrl(realMd5.getAdiTemplateDownloadUrl());
            template.setAdiTemplatePwd(realMd5.getAdiTemplatePwd());
        }
        int updateRow = realPhoneTemplateMapper.updateById(template);
        if(updateRow >0){
            // 更新模板与客户的关联关系
            updateTemplateCustomerRelationships(template.getId(), updateDTO.getCustomerIds(), customerId);

            // 3. 记录操作日志
            StringBuilder detail = new StringBuilder("编辑ADI模板: ");
            adiTemplateLogService.recordLog(
                    template.getId(),
                    AdiTemplateOperationTypeEnum.EDIT.getCode(),
                    customerInfoVo.getCustomerName(),
                    detail.append(JSONUtil.toJsonStr(updateDTO)).toString()
            );

        }
        return Result.ok();
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateAdiTemplateStatus(AdiTemplateStatusDTO statusDTO) {
        if (statusDTO.getId() == null) {
            return Result.fail("模板ID不能为空");
        }

        // 验证状态值是否合法，状态只能是0(DISABLE)或1(ENABLE)
        if ((!Objects.equals(statusDTO.getStatus(), AdiTemplateOperationTypeEnum.DISABLE.getCode()) &&
                !Objects.equals(statusDTO.getStatus(), AdiTemplateOperationTypeEnum.ENABLE.getCode()))) {
            return Result.fail("无效的状态值，只能是0(禁用)或1(启用)");
        }

        // 1. 检查模板是否存在
        RealPhoneTemplate existingTemplate = realPhoneTemplateMapper.selectById(statusDTO.getId());
        if (existingTemplate == null) {
            return Result.fail("模板不存在: " + statusDTO.getId());
        }

        // 如果当前状态与要设置的状态相同，直接返回成功
        if (existingTemplate.getStatus() != null && existingTemplate.getStatus().equals(statusDTO.getStatus())) {
            return Result.ok("状态未发生变化");
        }

        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());

        // 检查是否为公共模板，且不是管理员
        if (TEMPLATE_PUBLIC.equals(existingTemplate.getIsPublic()) && !redisService.isAdmin(customerId)) {
            return Result.fail("公共模板不可修改");
        }

        // 检查权限: 判断当前用户是否有权限操作此模板
        if (!hasTemplateAccessPermission(statusDTO.getId(), customerId)) {
            return Result.fail("无权操作该模板");
        }

        CustomerInfoVo customerInfoVo = customerMapper.getCustomerInfoById(customerId);

        // 2. 更新状态
        RealPhoneTemplate template = new RealPhoneTemplate();
        template.setId(statusDTO.getId());
        template.setStatus(statusDTO.getStatus());
        template.setUpdateTime(new Date());
        template.setUpdateBy(customerInfoVo.getCustomerName());

        realPhoneTemplateMapper.updateById(template);

        // 3. 记录操作日志
        String statusDesc = statusDTO.getStatus() == 1 ? AdiTemplateOperationTypeEnum.ENABLE.getDesc() : AdiTemplateOperationTypeEnum.DISABLE.getDesc();
        adiTemplateLogService.recordLog(
                template.getId(),
                statusDTO.getStatus() == 1 ?
                        AdiTemplateOperationTypeEnum.ENABLE.getCode() :
                        AdiTemplateOperationTypeEnum.DISABLE.getCode(),
                customerInfoVo.getCustomerName(),
                statusDesc + "ADI模板" + existingTemplate.getBrand() + "+" + existingTemplate.getModel() + "+" + existingTemplate.getAndroidImageVersion()
        );

        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateAdiTemplateOfficial(AdiTemplateOfficialDTO officialDTO) {
        if (officialDTO.getId() == null) {
            return Result.fail("模板ID不能为空");
        }

        // 验证正式版标识值是否合法，只能是0(测试版)或1(正式版)
        if ((!Objects.equals(officialDTO.getIsOfficial(), AdiTemplateIsOfficialEnum.TEST_VERSION.getCode()) &&
                !Objects.equals(officialDTO.getIsOfficial(), AdiTemplateIsOfficialEnum.OFFICIAL_VERSION.getCode()))) {
            return Result.fail("无效的正式版标识值，只能是0(测试版)或1(正式版)");
        }

        // 1. 检查模板是否存在
        RealPhoneTemplate existingTemplate = realPhoneTemplateMapper.selectById(officialDTO.getId());
        if (existingTemplate == null) {
            return Result.fail("模板不存在: " + officialDTO.getId());
        }

        // 如果当前状态与要设置的状态相同，直接返回成功
        if (existingTemplate.getIsOfficial() != null && existingTemplate.getIsOfficial().equals(officialDTO.getIsOfficial())) {
            return Result.ok("版本状态未发生变化");
        }

        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());

        // 检查是否为公共模板，且不是管理员
        if (TEMPLATE_PUBLIC.equals(existingTemplate.getIsPublic()) && !redisService.isAdmin(customerId)) {
            return Result.fail("公共模板不可修改");
        }

        // 检查权限: 判断当前用户是否有权限操作此模板
        if (!hasTemplateAccessPermission(officialDTO.getId(), customerId)) {
            return Result.fail("无权操作该模板");
        }

        CustomerInfoVo customerInfoVo = customerMapper.getCustomerInfoById(customerId);

        // 2. 更新正式版状态
        RealPhoneTemplate template = new RealPhoneTemplate();
        template.setId(officialDTO.getId());
        template.setIsOfficial(officialDTO.getIsOfficial());
        template.setUpdateTime(new Date());
        template.setUpdateBy(customerInfoVo.getCustomerName());

        realPhoneTemplateMapper.updateById(template);

        // 3. 记录操作日志
        String officialDesc = officialDTO.getIsOfficial() == 1 ?
                AdiTemplateIsOfficialEnum.OFFICIAL_VERSION.getDesc() : AdiTemplateIsOfficialEnum.TEST_VERSION.getDesc();
        String detail = "设置版本:"+officialDesc + ": " +  existingTemplate.getBrand() + "+" + existingTemplate.getModel() + "+" + existingTemplate.getAndroidImageVersion();

        adiTemplateLogService.recordLog(
                template.getId(),
                AdiTemplateOperationTypeEnum.EDIT.getCode(),
                customerInfoVo.getCustomerName(),
                detail
        );

        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteAdiTemplate(Long id) {
        // 1. 检查模板是否存在
        RealPhoneTemplate existingTemplate = realPhoneTemplateMapper.selectById(id);
        if (existingTemplate == null) {
            return Result.fail("模板不存在: " + id);
        }

        long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());

        // 检查是否为公共模板，且不是管理员
        if (TEMPLATE_PUBLIC.equals(existingTemplate.getIsPublic()) && !redisService.isAdmin(customerId)) {
            return Result.fail("公共模板不可修改");
        }

        // 检查权限: 判断当前用户是否有权限操作此模板
        if (!hasTemplateAccessPermission(id, customerId)) {
            log.warn("用户 {} 无权删除模板 {}", customerId, id);
            return Result.fail("无权操作该模板");
        }

        // 2. 检查模板是否被使用
        Integer instanceCount = getTemplateInstanceCount(id);
        if (instanceCount > 0) {
            log.warn("尝试删除已关联实例的模板，模板ID: {}，关联实例数: {}", id, instanceCount);
            return Result.fail("该模板已关联" + instanceCount + "个实例，无法删除。请先解除关联关系后再删除模板。");
        }

        CustomerInfoVo customerInfoVo = customerMapper.getCustomerInfoById(customerId);

        try {
            log.info("开始删除模板，模板ID: {}", id);

            // 3. 先删除模板与客户的关联关系
            adiTemplateCustomerMapper.deleteByTemplateId(id);
            log.debug("删除模板客户关联关系, 模板ID: {}", id);
            // 4. 再删除模板本身
            RealPhoneTemplate deleteTemplate = new RealPhoneTemplate();
            deleteTemplate.setId(id);
            deleteTemplate.setDeleteFlag(true);
            deleteTemplate.setUpdateTime(new Date());
            deleteTemplate.setUpdateBy(customerInfoVo.getCustomerName());
            int count = realPhoneTemplateMapper.updateById(deleteTemplate);
            if (count <= 0) {
                log.warn("模板软删除失败，可能已被其他操作删除，模板ID: {}", id);
                throw new BasicException("模板删除失败，请确认模板是否存在");
            }

            // 5. 记录操作日志
            String detail = "删除ADI模板: " + existingTemplate.getBrand() + "+" + existingTemplate.getModel() + "+" + existingTemplate.getAndroidImageVersion();

            adiTemplateLogService.recordLog(
                    id,
                    AdiTemplateOperationTypeEnum.DELETE.getCode(),
                    customerInfoVo.getCustomerName(),
                    detail
            );

            log.info("模板删除成功，模板ID: {}", id);
            return Result.ok();
        } catch (Exception e) {
            log.error("删除模板异常, 模板ID: {}", id, e);
            throw new BasicException("删除模板失败: " + e.getMessage());
        }
    }


    @Override
    public Result<AdiParseResultVO> parseAdiFile(String fileUrl,String id) {
        if (StringUtils.isEmpty(fileUrl)) {
            return Result.fail("文件URL不能为空");
        }

        File tempFile = null;
        InputStream inputStream = null;
        try {
            // 1. 从OSS获取文件输入流
            inputStream = ossService.getFileInputStream(fileUrl);
            if (inputStream == null) {
                return Result.fail("无法从OSS获取文件：" + fileUrl);
            }

            // 获取文件名 - 从URL提取
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }

            // 创建临时文件
            tempFile = File.createTempFile("adi_download_", ".zip");
            log.info("从OSS下载文件: {}, 临时文件路径: {}", fileUrl, tempFile.getAbsolutePath());

            // 将输入流写入临时文件
            FileUtils.copyInputStreamToFile(inputStream, tempFile);

            // 2. 将下载的文件转换为MultipartFile进行解析
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    "application/zip",
                    FileUtils.readFileToByteArray(tempFile)
            );

            // 3.支持原始包和生成后的包
            AdiParseVO parseResult = AdiParserUtil.parseAdiFile(multipartFile, true);
            // 4. 如果解析不成功，直接返回错误信息
            if (Boolean.FALSE.equals(parseResult.getSuccess())) {
                return Result.fail(parseResult.getMessage());
            }
            parseResult.setPublicUrl(fileUrl);
            if(id != null){
                RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(id);
                if(Objects.isNull(realPhoneTemplate)){
                    return Result.fail("模板id不存在");
                }
                parseResult.setScreenLayoutCode(realPhoneTemplate.getScreenLayoutCode());
                parseResult.setAdiTemplateVersion(StringUtils.isBlank(realPhoneTemplate.getAdiTemplateVersion()) ? 
                    generateAdiVersion() : realPhoneTemplate.getAdiTemplateVersion());
            }else{
                if(StringUtils.isBlank(parseResult.getScreenWidth()) || StringUtils.isBlank(parseResult.getScreenHeight())
                        || StringUtils.isBlank(parseResult.getScreenDensity())){
                    return Result.fail("请检查包的数据是否完整");
                }
                String screenLayoutCode = ANDROID_MODEL_PREFIX + parseResult.getScreenWidth() + "x" + parseResult.getScreenHeight() + "x" + parseResult.getScreenDensity();
                parseResult.setScreenLayoutCode(screenLayoutCode);
                parseResult.setAdiTemplateVersion(generateAdiVersion());
                // 5. 保存完整的parseResult对象到Redis中
                String cacheKey = ADI_ZIP_PARSE_DIRECTORY + parseResult.getFingerprintMd5();
                redisService.setIfAbsentExpire(cacheKey, JSONUtil.toJsonStr(parseResult), 5L, TimeUnit.MINUTES);
                log.info("已缓存完整的ADI解析结果，fingerprintMd5: {}, workDir: {}, cacheKey: {}",
                        parseResult.getFingerprintMd5(), parseResult.getWorkDir(), cacheKey);
            }
            AdiParseResultVO adiParseResultVO = BeanUtil.copyProperties(parseResult, AdiParseResultVO.class);
            return Result.ok(adiParseResultVO);
        } catch (IOException e) {
            log.error("从OSS下载或解析文件失败: {}", fileUrl, e);
            return Result.fail("从OSS下载或解析文件失败: " + e.getMessage());
        } finally {
            // 关闭输入流并删除临时文件
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流失败", e);
                }
            }
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                    log.debug("临时文件已删除: {}", tempFile.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", tempFile.getAbsolutePath(), e);
                }
            }
        }
    }


    @Override
    public List<AdiTemplateVO> listForSelection(AdiTemplateSelectionDTO selectionDTO) {
        // 将单个customerId转换为List
        List<Long> customerIds = null;
        if (selectionDTO.getCustomerId() != null) {
            customerIds = CollUtil.newArrayList(selectionDTO.getCustomerId());
        }
        // 直接调用优化后的查询，获取VO对象列表
        return realPhoneTemplateMapper.listForSelectionOptimized(selectionDTO.getStatus(), selectionDTO.getIsOfficial(), customerIds);
    }


    /**
     * 处理模板与客户的关系
     * 规则  说明
     * R1(多选) 一个 ADI 模板可授权给个客户（租户）。
     * R2(公共模板) 当授权列表为空 ⇒ 视为「公共」，任何客户均可在实例创建时选择。
     * R3(移除客户) 在模板编辑界面把某客户勾选去掉：
     * • 已创建实例 继续引用旧版本，不受影响；
     * • 未来新建实例 时，该客户将不再看到此模板。
     * R4(移除全部) 若授权列表最终为空 → 模板自动转为「公共模板」。
     * 根据R1-R4规则设置isPublic状态
     * @param template 模板实体
     * @param customerIds 客户ID列表
     */
    private void handleTemplateCustomerRelationship(RealPhoneTemplate template, List<Long> customerIds) {
        // 判断客户列表是否为空
        if (customerIds == null || customerIds.isEmpty()) {
            // R2: 授权列表为空，设置为公共模板
            template.setIsPublic(TEMPLATE_PUBLIC);
        } else {
            // 授权列表不为空，设置为非公共模板
            template.setIsPublic(TEMPLATE_NON_PUBLIC);
        }
    }

    private void parseAndUploadTemplate(RealPhoneTemplate template, String fileUrl, String customerName) {
        File tempFile = null;
        InputStream inputStream = null;
        try {
            // 检查Redis中是否已经有此文件的解析结果缓存
            String fingerprintMd5 = template.getFingerprintMd5();
            String cacheKey = ADI_ZIP_PARSE_DIRECTORY + fingerprintMd5;
            AdiParseVO parseResult = null;

            // 尝试从缓存中获取完整的parseResult对象
            Object cachedResult = redisService.getCacheObject(cacheKey);
            if (cachedResult instanceof AdiParseVO) {
                parseResult = (AdiParseVO) cachedResult;
                log.info("从缓存中获取到完整的AdiParseVO对象，fingerprintMd5: {}, workDir: {}",
                        fingerprintMd5, parseResult.getWorkDir());

                // 验证工作目录是否仍然存在
                File workDir = new File(parseResult.getWorkDir());
                if (!workDir.exists() || !workDir.isDirectory()) {
                    log.warn("缓存的工作目录不存在，需要重新解析: {}", parseResult.getWorkDir());
                    redisService.deleteObject(cacheKey);
                    parseResult = null;
                }
            }

            // 如果没有缓存或缓存无效，则重新下载和解析
            if (parseResult == null) {
                // 1. 从OSS获取文件输入流
                inputStream = ossService.getFileInputStream(fileUrl);
                if (inputStream == null) {
                    throw new BasicException("无法从OSS获取文件：" + fileUrl);
                }

                // 获取文件名 - 从URL提取
                String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
                if (fileName.contains("?")) {
                    fileName = fileName.substring(0, fileName.indexOf("?"));
                }

                // 创建临时文件
                tempFile = File.createTempFile("adi_download_", ".zip");
                log.info("从OSS下载文件: {}, 临时文件路径: {}", fileUrl, tempFile.getAbsolutePath());

                // 将输入流写入临时文件
                FileUtils.copyInputStreamToFile(inputStream, tempFile);

                // 2. 将下载的文件转换为MultipartFile进行解析
                MultipartFile multipartFile = new MockMultipartFile(
                        fileName,
                        fileName,
                        "application/zip",
                        FileUtils.readFileToByteArray(tempFile)
                );

                parseResult = AdiParserUtil.parseAdiFile(multipartFile, true);
                if(template.getId() != null){
                    RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectById(template.getId());
                    if(Boolean.TRUE.equals(parseResult.getSuccess()) && parseResult.getPackageType() == 2
                            && (!realPhoneTemplate.getBrand().equals(parseResult.getBrand())
                            || !realPhoneTemplate.getModel().equals(parseResult.getModel())
                            || !realPhoneTemplate.getAospVersion().equals(parseResult.getAospVersion())
                            || !realPhoneTemplate.getAndroidImageVersion().equals(parseResult.getAndroidImageVersion()))){
                        parseResult.setSuccess(false);
                        parseResult.setMessage("新文件机型品牌参数不符");
                    }
                }

            }
            if(Boolean.FALSE.equals(parseResult.getSuccess())){
                throw new BasicException(parseResult.getMessage());
            }
            RealPhoneTemplate realPhoneTemplate = realPhoneTemplateMapper.selectOne(new QueryWrapper<RealPhoneTemplate>()
                    .eq("fingerprint_md5",parseResult.getFingerprintMd5())
                    .last("limit 1"));
            String publicUrl;
            if(Objects.nonNull(realPhoneTemplate)){
                publicUrl = realPhoneTemplate.getAdiTemplateDownloadUrl();
                parseResult.setFinalZipPassword(realPhoneTemplate.getAdiTemplatePwd());
            }else{
                // 处理加密
                AdiParserUtil.createAESPasswordZip(new File(parseResult.getWorkDir()), parseResult);
                try {
                    publicUrl = uploadFile(parseResult);
                } catch (IOException e) {
                    log.error("上传文件失败",e);
                    throw new RuntimeException(e);
                }finally{
                    safeDeleteTempDirectory(parseResult);
                }
            }

            String screenLayoutCode;
            // 处理更新时上传非原始包无法解析屏幕信息的场景
            if(parseResult.getPackageType() == 2){
                screenLayoutCode = template.getScreenLayoutCode();
            }else{
                screenLayoutCode = ANDROID_MODEL_PREFIX + parseResult.getScreenWidth() + "x" + parseResult.getScreenHeight() + "x" + parseResult.getScreenDensity();
                ScreenLayoutVO screenLayoutVO = screenLayoutService.getByCode(screenLayoutCode);
                if(Objects.isNull(screenLayoutVO)){
                    screenLayoutVO = new ScreenLayoutVO();
                    screenLayoutVO.setCode(screenLayoutCode);
                    screenLayoutVO.setScreenWidth(parseResult.getScreenWidth());
                    screenLayoutVO.setScreenHigh(parseResult.getScreenHeight());
                    screenLayoutVO.setScreenRefreshRate("60");
                    screenLayoutVO.setPixelDensity(parseResult.getScreenDensity());
                    screenLayoutService.addScreenLayout(screenLayoutVO, customerName);
                }
            }
            // 用户可能会更改deviceName,解析后可能造成不一致
            String deviceName = template.getDeviceName();
            // 创建模板实体
            BeanUtils.copyProperties(parseResult, template);
            template.setDeviceName(deviceName);
            template.setAdiTemplatePwd(parseResult.getFinalZipPassword());
            template.setAdiTemplateDownloadUrl(publicUrl);
            template.setScreenLayoutCode(screenLayoutCode);
            template.setPropertyJSON("{}");
            template.setCreateBy(customerName);
            template.setUpdateBy(customerName);
            template.setCreateTime(new Date());
            template.setUpdateTime(new Date());
        } catch (IOException e) {
            log.error("从OSS下载或解析文件失败: {}", fileUrl, e);
            throw new BasicException("从OSS下载或解析文件失败: " + e.getMessage());
        } finally {
            // 关闭输入流并删除临时文件
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn("关闭输入流失败", e);
                }
            }
            if (tempFile != null && tempFile.exists()) {
                try {
                    Files.delete(tempFile.toPath());
                    log.debug("临时文件已删除: {}", tempFile.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", tempFile.getAbsolutePath(), e);
                }
            }
        }
    }

    private static void safeDeleteTempDirectory(AdiParseVO parseResult) {
        Path workDir = Paths.get(parseResult.getWorkDir());
        if (Files.exists(workDir)) {
            try {
                Path dir = null;
                if (Files.isDirectory(workDir)) {
                    dir = workDir;
                } else if (Files.isRegularFile(workDir)) {
                    dir = workDir.getParent();  // 传进来的是 .zip，删除它的父目录
                    Files.deleteIfExists(workDir); // 先把文件本身删掉
                }
                // 保险：只清理我们自己创建的工作目录，防止误删 /tmp
                if (dir != null && dir.getFileName().toString().startsWith("adi_work_")) {
                    FileUtils.deleteDirectory(dir.toFile());
                    log.debug("临时目录已删除: {}", dir);
                } else {
                    log.warn("跳过目录删除，路径不符合前缀规则: {}", dir);
                }
            } catch (IOException ex) {
                log.warn("删除临时目录失败: {}", workDir, ex);
            }
        }
    }

    private String uploadFile(AdiParseVO parseResult) throws IOException {
        // 1. 获取指纹MD5并检查是否已存在
        String fingerprintMd5 = parseResult.getFingerprintMd5();

        // 2. 上传到OSS
        String fileName = parseResult.getBrand() + "_" + parseResult.getModel() + "_" + parseResult.getAndroidImageVersion() + ".zip";

        // 生成OSS存储路径
        String ossPath = ossService.generateStoragePathByDirectory(
                "adi",
                fileName,
                fingerprintMd5);

        // 上传文件到OSS
        String contentType = "application/zip";
        String publicUrl = ossService.uploadFile(
                ossPath,
                Files.newInputStream(new File(parseResult.getZipFile()).toPath()),
                contentType,
                parseResult.getAdiSizeBytes());

        if (publicUrl == null) {
            throw new BasicException("上传文件到OSS失败");
        }
        return publicUrl;
    }

    /**
     * 将实体对象转换为VO
     */
    private AdiTemplateVO convertToVO(RealPhoneTemplate template) {
        if (template == null) {
            return null;
        }
        AdiTemplateVO vo = new AdiTemplateVO();
        BeanUtils.copyProperties(template, vo);

        // 状态描述
        vo.setStatusDesc(template.getStatus() != null && template.getStatus() == 1 ?
                AdiTemplateOperationTypeEnum.ENABLE.getDesc() : AdiTemplateOperationTypeEnum.DISABLE.getDesc());

        // 是否正式版描述
        vo.setIsOfficialDesc(template.getIsOfficial() != null && template.getIsOfficial() == 1 ?
                AdiTemplateIsOfficialEnum.OFFICIAL_VERSION.getDesc() : AdiTemplateIsOfficialEnum.TEST_VERSION.getDesc());

        return vo;
    }


    /**
     * 创建用户文件记录
     */
    private UserFile createUserFileRecord(Long fileStorageId, Long customerId, String fileType,
                                          String fileName, String fileUniqueId,
                                          String fileStatus, String fileComment) {
        UserFile userFile = new UserFile();
        userFile.setFileStorageId(fileStorageId);
        userFile.setCustomerId(customerId);
        userFile.setFileType(fileType);
        userFile.setFileName(fileName);
        userFile.setFileUniqueId(fileUniqueId);
        userFile.setFileStatus(fileStatus);
        userFile.setFileComment(fileComment);
        userFile.setSourceType("upload");
        userFile.setCreatedTime(new Date());
        userFile.setUpdatedTime(new Date());
        userFile.setCreatedBy(customerId);
        userFile.setUpdatedBy(customerId);
        return userFile;
    }

    /**
     * 生成镜像版本号
     * 格式 yyyyMMdd_SerialNo ; SerialNo为序号 由redis保证当天唯一
     * 每个客户维护一个 公共镜像的customerId定为0
     * @return
     */
    private String generateAdiVersion(){
        long customerId = 0L;
        String nowDate = DateUtil.format(new Date(),"yyyyMMdd");
        String cacheKey = CUSTOMER_ADI_VERSION_SERIAL_NO + nowDate + ":" + customerId;
        Integer no = redisService.increment(cacheKey);
        if(no <= 1){
            redisService.expire(cacheKey,1, TimeUnit.DAYS);
        }
        return nowDate + "_" + no;
    }

    /**
     * 检查用户是否有权限访问指定模板
     *
     * @param templateId 模板ID
     * @param customerId 客户ID
     * @return 是否有权限
     */
    private boolean hasTemplateAccessPermission(Long templateId, Long customerId) {
        // 查询模板基本信息
        RealPhoneTemplate template = realPhoneTemplateMapper.selectById(templateId);
        if (template == null) {
            return false;
        }

        // 情况1: 公共模板，所有人可见
        if (Objects.equals(template.getIsPublic(), TEMPLATE_PUBLIC)) {
            return true;
        }

        // 情况2: 非公共模板，检查用户是否在授权列表中
        List<Long> authorizedCustomerIds = getTemplateCustomerIds(templateId);
        return (CollUtil.isNotEmpty(authorizedCustomerIds) && authorizedCustomerIds.contains(customerId)) || redisService.isAdmin(customerId);
    }

    /**
     * 更新模板与客户的关联关系
     *
     * @param templateId 模板ID
     * @param newCustomerIds 新的客户ID列表
     */
    private void updateTemplateCustomerRelationships(Long templateId, List<Long> newCustomerIds,Long customerId) {
        // 1. 先获取当前的关联关系
        List<Long> existingCustomerIds = adiTemplateCustomerMapper.selectCustomerIdsByTemplateId(templateId);

        // 2. 检查是否需要更新关联关系
        if (CollUtil.isEmpty(existingCustomerIds) && CollUtil.isEmpty(newCustomerIds)) {
            // 无需更新，都为空
            return;
        }

        // 3. 清除现有关联关系
        adiTemplateCustomerMapper.deleteByTemplateId(templateId);

        // 4. 处理新的关联关系
        if (CollUtil.isNotEmpty(newCustomerIds)) {
            // 添加新的关联关系
            adiTemplateCustomerMapper.batchInsertTemplateCustomers(templateId, newCustomerIds,customerId);
            // 如果添加了客户关系，设置为非公共模板
            RealPhoneTemplate template = new RealPhoneTemplate();
            template.setId(templateId);
            template.setIsPublic(TEMPLATE_NON_PUBLIC);
            realPhoneTemplateMapper.updateById(template);
        } else {
            // R4: 若授权列表为空，模板自动转为公共模板
            RealPhoneTemplate template = new RealPhoneTemplate();
            template.setId(templateId);
            template.setIsPublic(TEMPLATE_PUBLIC);
            realPhoneTemplateMapper.updateById(template);
        }
    }

    /**
     * 获取模板关联的客户ID列表
     *
     * @param templateId 模板ID
     * @return 客户ID列表
     */
    private List<Long> getTemplateCustomerIds(Long templateId) {
        return adiTemplateCustomerMapper.selectCustomerIdsByTemplateId(templateId);
    }

    /**
     * 获取模板关联的实例数
     *
     * @param templateId 模板ID
     * @return 关联的实例数
     */
    private Integer getTemplateInstanceCount(Long templateId) {
        if (templateId == null) {
            log.warn("获取模板关联实例数时templateId为空");
            return 0;
        }

        try {
            // 查询pad表中status=1且使用该模板的记录数
            Integer count = realPhoneTemplateMapper.countTemplateInstances(templateId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取模板关联实例数失败，templateId: {}", templateId, e);
            return 0;
        }
    }

    private boolean isAdiTemplateDataConsistent(AdiTemplateUpdateDTO updateDTO, RealPhoneTemplate realMd5) {
        return Objects.equals(updateDTO.getBrand(), realMd5.getBrand()) &&
                Objects.equals(updateDTO.getModel(), realMd5.getModel()) &&
                Objects.equals(updateDTO.getAndroidImageVersion(), realMd5.getAndroidImageVersion());
    }
}
