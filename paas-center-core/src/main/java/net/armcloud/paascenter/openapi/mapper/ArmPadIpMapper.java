package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.common.model.entity.paas.ArmPadIp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ArmPadIpMapper {
    void updateArmPad();

    void batchSaveArmPadIp(@Param("id") Long serverId, @Param("netPadIds") List<Long> netPadIds);

    void deleteByArmServerIdOrNetPadId(@Param("armServerId")Long armServerId, @Param("netPadId") Long netPadId);

    List<ArmPadIp> selectByArmServerIdOrNetPadId(@Param("armServerId")Long armServerId, @Param("netPadId") Long netPadId);
}
