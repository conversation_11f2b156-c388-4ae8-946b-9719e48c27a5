package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 黑白名单保存对象
 */
@Data
public class AppClassifyPadSaveDTO implements Serializable {
    @ApiModelProperty(value = "id")
    @NotNull(message = "id cannot null")
    private Long id;
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long customerId;
    @ApiModelProperty(value = "关联实例集合")
    @Size(max = 500, message = "实例数量不能超过500个")
    private List<AppPadInfo> appPadInfos;

    @Data
    public static class AppPadInfo{
        @ApiModelProperty(value = "实例编号")
        @NotEmpty(message = "padCode cannot null")
        private String padCode;
        @ApiModelProperty(value = "实例规格")
        private String deviceLevel;
        @ApiModelProperty(value = "实例ip")
        private String padIp;
    }
}
