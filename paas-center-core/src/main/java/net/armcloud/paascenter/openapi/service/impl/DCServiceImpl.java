package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.MD5Utils;
import net.armcloud.paascenter.openapi.manager.DcInfoManager;
import net.armcloud.paascenter.openapi.mapper.DcInfoMapper;
import net.armcloud.paascenter.openapi.mapper.EdgeClusterMapper;
import net.armcloud.paascenter.openapi.mapper.GameServerVersionMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.dto.GameServeVersionDTO;
import net.armcloud.paascenter.openapi.model.dto.GameServerGetPadDcInfoDTO;
import net.armcloud.paascenter.openapi.model.dto.GetPadDcInfoDTO;
import net.armcloud.paascenter.openapi.model.vo.DcInfoVO;
import net.armcloud.paascenter.openapi.model.vo.GameServerVersionInfoVo;
import net.armcloud.paascenter.openapi.model.vo.PadDcInfoVO;
import net.armcloud.paascenter.openapi.service.ICustomerService;
import net.armcloud.paascenter.openapi.service.IDCService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.common.model.entity.paas.GameServerVersion;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.common.core.constant.Constants.ENABLE;
import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PERMISSION_DENIED_EXCEPTION;
import static org.springframework.util.ObjectUtils.isEmpty;

@Service
@Slf4j
public class DCServiceImpl implements IDCService {
    private final PadMapper padMapper;
    private final DcInfoManager padManager;
    private final DcInfoMapper dcInfoMapper;
    private final ICustomerService customerService;
    private final GameServerVersionMapper gameServerVersionMapper;
    private final RedisService redisService;
    private final EdgeClusterMapper edgeClusterMapper;
    private final IEdgeClusterConfigurationService edgeClusterConfigurationService;

    @Override
    public DcInfo getByPadCode(String padCode) {
        String key = RedisKeyPrefix.DC_INFO_KEY + padCode;
        Object dcInfoObject = redisService.getCacheObject(key);
        if (ObjectUtil.isNull(dcInfoObject)) {
            DcInfo dcInfo = dcInfoMapper.getByPadCode(padCode);
            String dcInfoJson = com.alibaba.fastjson.JSON.toJSONString(dcInfo);
            redisService.setCacheObject(key, dcInfoJson, RedisKeyTime.day_1, TimeUnit.DAYS);
            return dcInfo;
        }
        return JSON.parseObject(dcInfoObject.toString(), DcInfo.class);
    }

//    @Override
//    public List<DcInfo> listByCustomerId(long customerId) {
//        return dcInfoMapper.listByCustomerId(customerId);
//    }

//    @Override
//    public DcInfoVO getDcInfo(GetPadDcInfoDTO dto) {
//        String padCode = dto.getPadCode();
//        return padManager.getDcInfoCache(padCode);
//    }

    @Override
    public PadDcInfoVO getDcInfoBySDK(GetPadDcInfoDTO dto, String sdkToken, String uuid) {
//        VerifyAndGetSDKCustomerDTO verifyAndGetSDKCustomerDTO = new VerifyAndGetSDKCustomerDTO();
//        verifyAndGetSDKCustomerDTO.setSdkToken(sdkToken);
//        verifyAndGetSDKCustomerDTO.setUuid(uuid);
//
//        customerService.verifyAndGetInfo(verifyAndGetSDKCustomerDTO);
//        return PadDcInfoVO.builder(getDcInfo(dto));
        return null;
    }

    @Override
    public PadDcInfoVO getDcInfoByGameServer(GameServerGetPadDcInfoDTO dto, String requestAuth) {
        String correctRequestAuth = MD5Utils.generateMD5(dto.getCloudVendorType() + dto.getPadOutCode());
        if (!correctRequestAuth.equals(requestAuth)) {
            throw new BasicException(PERMISSION_DENIED_EXCEPTION);
        }

        Pad pad = padMapper.getByCloudVendorTypeAndPadOutCode(dto.getCloudVendorType(), dto.getPadOutCode());
        if (Objects.isNull(pad)) {
            return null;
        }
        DcInfoVO dcInfoCache = padManager.getDcInfoCache(pad.getPadCode());

        String edgeClusterCode = edgeClusterMapper.selectEdgeClusterCodeByPadCodeSingle(pad.getPadCode());
        String ossEndpointInternal = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.OSS_ENDPOINT_INTERNAL);
        String ossEndpoint = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.OSS_ENDPOINT);

        PadDcInfoVO vo = new PadDcInfoVO();
        vo.setDcName(dcInfoCache.getDcName());
        vo.setOssEndpoint(ossEndpoint);
        vo.setOssEndpointInternal(ossEndpointInternal);
        return vo;
    }

    @Override
    public DcInfo getByDcId(long dcId) {
        return dcInfoMapper.getById(dcId);
    }

    /**
     * 查询机房gameServer最新版本
     * @param param
     * @return
     */
    @Override
    public GameServerVersionInfoVo getGameServeVersion(GameServeVersionDTO param) {
        Integer dcId = padMapper.getDcIdByPadOutCode(param.getPadOutCode());
        if (isEmpty(dcId)) {
            return null;
        }
        GameServerVersion gameServerVersion = gameServerVersionMapper.selectOne(new QueryWrapper<GameServerVersion>().lambda()
                .eq(GameServerVersion::getDcId, dcId)
                .eq(GameServerVersion::getStatus, ENABLE)
                .orderByDesc(GameServerVersion::getVersionCode)
                .last("LIMIT 1"));
        return GameServerVersionInfoVo.builder(gameServerVersion);
    }

    @Override
    public List<DcInfo> list() {
        return dcInfoMapper.list();
    }

    public DCServiceImpl(DcInfoMapper dcInfoMapper, DcInfoManager padManager, ICustomerService customerService, PadMapper padMapper, GameServerVersionMapper gameServerVersionMapper, RedisService redisService,EdgeClusterMapper edgeClusterMapper,IEdgeClusterConfigurationService edgeClusterConfigurationService) {
        this.dcInfoMapper = dcInfoMapper;
        this.padManager = padManager;
        this.customerService = customerService;
        this.padMapper = padMapper;
        this.gameServerVersionMapper = gameServerVersionMapper;
        this.redisService = redisService;
        this.edgeClusterMapper = edgeClusterMapper;
        this.edgeClusterConfigurationService = edgeClusterConfigurationService;
    }

}
