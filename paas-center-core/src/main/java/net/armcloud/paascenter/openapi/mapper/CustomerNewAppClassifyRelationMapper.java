package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerNewAppClassifyRelation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 用户应用分类应用关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Mapper
public interface CustomerNewAppClassifyRelationMapper extends BaseMapper<CustomerNewAppClassifyRelation> {

    List<NewAppClassifyNameVO> selectAppListByAppIds(QueryNewAppClassifyNameDTO param);
}
