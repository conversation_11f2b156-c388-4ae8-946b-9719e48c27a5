package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class GpsInjectInfoDTO extends BaseDTO {
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @NotNull(message = "latitude cannot null")
    @Range(min = -90, max = 90, message = "经纬度格式不合法，纬度范围应在 -90 到 90 之间")
    private Float latitude;

    @NotNull(message = "longitude cannot null")
    @Range(min = -180, max = 180, message = "经纬度格式不合法，经度范围应在 -180 到 180 之间")
    private Float longitude;
    
    private Float altitude;
}
