package net.armcloud.paascenter.openapi.service;

import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import net.armcloud.paascenter.openapi.model.dto.GameServeVersionDTO;
import net.armcloud.paascenter.openapi.model.dto.GameServerGetPadDcInfoDTO;
import net.armcloud.paascenter.openapi.model.dto.GetPadDcInfoDTO;
import net.armcloud.paascenter.openapi.model.vo.DcInfoVO;
import net.armcloud.paascenter.openapi.model.vo.GameServerVersionInfoVo;
import net.armcloud.paascenter.openapi.model.vo.PadDcInfoVO;

import java.util.List;

public interface IDCService {
    DcInfo getByPadCode(String padCode);

//    List<DcInfo> listByCustomerId(long customerId);

//    DcInfoVO getDcInfo(GetPadDcInfoDTO dto);

    PadDcInfoVO getDcInfoBySDK(GetPadDcInfoDTO dto, String sdkToken, String uuid);

    PadDcInfoVO getDcInfoByGameServer(GameServerGetPadDcInfoDTO dto, String requestAuth);

    DcInfo getByDcId(long dcId);

    GameServerVersionInfoVo getGameServeVersion(GameServeVersionDTO param);

    List<DcInfo> list();
}
