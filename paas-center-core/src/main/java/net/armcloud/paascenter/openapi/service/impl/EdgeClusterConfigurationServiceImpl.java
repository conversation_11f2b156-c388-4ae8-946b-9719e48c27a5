package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfiguration;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.mapper.EdgeClusterConfigurationMapper;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.EDGE_CLUSTER_CONFIGURATION;

@Slf4j
@Service
public class EdgeClusterConfigurationServiceImpl extends ServiceImpl<EdgeClusterConfigurationMapper, EdgeClusterConfiguration> implements IEdgeClusterConfigurationService {
    @Autowired
    private RedisService redisService;

    @Override
    public String getEdgeClusterConfigurationByKey(String clusterCode, EdgeClusterConfigurationEnum edgeClusterConfigurationEnum) {
        String key = edgeClusterConfigurationEnum.getKey();
        String redisKey = EDGE_CLUSTER_CONFIGURATION + clusterCode + "_" + key;
        String value = redisService.getCacheObject(redisKey);
        if (StrUtil.isNotBlank(value)) {
           return value;
        }

        String result = this.getBaseMapper().queryEdgeClusterConfigurationByKey(clusterCode, key);
        if (StrUtil.isNotBlank(result)) {
            redisService.setCacheObject(redisKey, result, 5L, TimeUnit.MINUTES);
        }else{
            log.warn("clusterCode:{},key:{},result:{}", clusterCode, key, result);
        }
        log.info("clusterCode:{},key:{},result:{}", clusterCode, key, result);
        return result;
    }

}