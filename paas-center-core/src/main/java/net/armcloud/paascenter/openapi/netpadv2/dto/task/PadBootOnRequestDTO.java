package net.armcloud.paascenter.openapi.netpadv2.dto.task;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.base.TaskBaseRequestDTO;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * 开机请求DTO
 * CBS执行开机任务时需要的参数
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "开机请求DTO", description = "下发到CBS的开机任务参数")
public class PadBootOnRequestDTO extends TaskBaseRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "容器网络配置", required = true)
    private ContainerNetwork containerNetwork;

    @ApiModelProperty(value = "资源限制配置", required = true)
    private ResourceLimit resourceLimit;

    @ApiModelProperty(value = "安卓属性")
    private JSONObject androidProps;

    @ApiModelProperty(value = "安卓启动参数")
    private AndroidBootParams androidBootParams;

    @ApiModelProperty(value = "实例类型", required = true, notes = "virtual：虚拟机；real：真机")
    private String type;

    @ApiModelProperty(value = "网存实例标记", notes = "1 网存实例 没值或者为0 本地实例")
    private Integer netStorageResFlag;

    @ApiModelProperty(value = "镜像配置", required = true)
    @Valid
    private Image image;

    @ApiModelProperty(value = "扩展设置")
    private ExtraSettings extraSettings;

    @ApiModelProperty(value = "任务超时时间(秒)")
    private Integer timeout;

    /*以下参数为后端存储需要，非开机任务需要。*/
    /* ---------- Start ---------- */
    @ApiModelProperty(value = "板卡IP")
    private String deviceIp;

    @ApiModelProperty(value = "服务器编码")
    private String armServerCode;

    @ApiModelProperty(value = "板卡规格")
    private String deviceLevel;

    @ApiModelProperty(value = "集群编号")
    private String clusterCode;
    /* ---------- End ---------- */

    /**
     * 容器网络配置
     */
    @Data
    @ApiModel(value = "容器网络配置")
    public static class ContainerNetwork implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "设备名称", required = true)
        private String deviceName;

        @ApiModelProperty(value = "网关地址", required = true)
        private String gateway;

        @ApiModelProperty(value = "IP范围", required = true)
        private String ipRange;

        @ApiModelProperty(value = "子网", required = true)
        private String subnet;

        @ApiModelProperty(value = "IP地址", required = true)
        private String ip;

        @ApiModelProperty(value = "MAC地址", required = true)
        private String mac;

        @ApiModelProperty(value = "Macvlan名称", required = true)
        private String macvlanName;
    }

    /**
     * 资源限制配置
     */
    @Data
    @ApiModel(value = "资源限制配置")
    public static class ResourceLimit implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "CPU限制", required = true)
        private Integer cpu;

        @ApiModelProperty(value = "内存限制(MB)", required = true)
        private Integer memory;

        @ApiModelProperty(value = "存储限制(GB)", required = true)
        private Integer storage;

        @ApiModelProperty(value = "宿主机预留存储空间(GB)", required = true)
        private Integer hostStorageSize;
    }

    /**
     * 安卓启动参数
     */
    @Data
    @ApiModel(value = "安卓启动参数")
    public static class AndroidBootParams implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "屏幕宽度")
        private Integer width;

        @ApiModelProperty(value = "屏幕DPI")
        private Integer dpi;

        @ApiModelProperty(value = "帧率")
        private Integer fps;

        @ApiModelProperty(value = "屏幕高度")
        private Integer height;

        @ApiModelProperty(value = "DNS1")
        private String dns1;

        @ApiModelProperty(value = "DNS2")
        private String dns2;
    }

    /**
     * 镜像配置
     */
    @Data
    @ApiModel(value = "镜像配置")
    public static class Image implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "仓库地址", required = true)
        private String repository;

        @ApiModelProperty(value = "镜像标签", required = true)
        private String tag;

        // 后端存储需要，镜像ID
        private String imageId;
    }

    /**
     * 扩展设置
     */
    @Data
    @ApiModel(value = "扩展设置")
    public static class ExtraSettings implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "存储ID")
        private String storageId;

        @ApiModelProperty(value = "存储大小")
        private Long storageSize;

        @ApiModelProperty(value = "ADI配置")
        private Adi adi;

        /**
         * ADI配置
         */
        @Data
        @ApiModel(value = "ADI配置")
        public static class Adi implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "安卓证书数据")
            private String androidCertData;

            @ApiModelProperty(value = "ADI下载URL")
            private String downloadUrlOfADI;

            @ApiModelProperty(value = "ADI密码")
            private String passwordOfADI;

            // 后端存储需要，ADI模板ID
            private Long realTemplateId;
        }
    }

    @Override
    public String toJSONString() {
        return JSONObject.toJSONString(this);
    }
}
