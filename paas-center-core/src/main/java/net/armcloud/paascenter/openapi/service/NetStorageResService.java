package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageRes;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.common.model.entity.paas.Pad;

import java.util.List;
/**
 * <AUTHOR>
 * @Date 2025/3/17 10:50
 * @Description:
 */


/**
 * <AUTHOR>
 * @Date 2025/3/6 20:51
 * @Description: 网络存储资源服务类
 */
public interface NetStorageResService extends IService<NetStorageRes> {

    /**
     * 根据用户id扣减网存余额
     * @param customerId
     * @return true扣减成功，false扣减失败
     */
    Boolean deductsTheSizeOfTheResourcesUsed(Long customerId,String clusterCode,  List<Pad> padList);


    /**
     * 根据用户id扣减网存余额
     * @param customerId
     * @param size
     * @return true扣减成功，false扣减失败
     */
    Boolean deductsTheSizeOfTheResourcesUsedByResUnit(Long customerId,  List<NetStorageResUnit> padList);

    /**
     * 根据用户id扣减网存余额
     * @param customerId 用户id
     * @param size 扣减大小
     * @return true扣减成功，false扣减失败
     */
    Boolean deductTheSpecifiedBalanceSize (Long customerId, Long size);


    /**
     * 当前用户网存余额是否可以足额扣减
     * @param customerId
     * @param size
     * @return
     */
    Boolean canDeductSize(Long customerId,String clusterCode, Long size);
}

