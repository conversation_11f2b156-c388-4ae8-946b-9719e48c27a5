package net.armcloud.paascenter.openapi.netpadv2.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 网存实例V2批量删除DTO
 * 支持传入多个实例进行批量删除操作
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Data
public class NetPadV2BatchDelDTO {

    @ApiModelProperty(value = "实例编码列表", required = true)
    @NotNull(message = "padCodes cannot be null")
    @Size(min = 1, max = 200, message = "实例数量范围1-200")
    private List<String> padCodes;

    // 以下字段由系统自动填充，不需要前端传递
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "超时时间（秒）")
    @Max(value = 120 * 60, message = "超时时间不能超过120分钟")
    @Min(value = 5 * 60, message = "超时时间不能小于5分钟")
    private Integer timeout;
}
