package net.armcloud.paascenter.openapi.netpadv2.service;

import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2BatchBootOnDTO;
import net.armcloud.paascenter.openapi.netpadv2.service.impl.NetPadV2ComputeMatchServiceImpl;

import java.util.List;

/**
 * 网存实例V2算力匹配服务接口
 * 优化版本的算力匹配逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
public interface NetPadV2ComputeMatchService {

    /**
     * 批量匹配算力资源
     * 使用分布式锁和批量操作优化性能
     * 
     * @param clusterCode 集群编码
     * @param detailsVOList 实例详情列表
     * @param customerId 客户id
     * @return 匹配结果
     */
    List<NetPadV2ComputeMatchServiceImpl.NetPadRelation> batchMatchComputeAndStorage(String clusterCode,
                                                                                     List<PadDetailsVO> detailsVOList,
                                                                                     long customerId);

    /**
     * 清除板卡缓存
     *
     * @param deviceCodes 板卡编码
     * @return 是否成功
     */
    boolean removeDeviceCodes(List<String> deviceCodes);
}
