package net.armcloud.paascenter.openapi.manager;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.mapper.DcInfoMapper;
import net.armcloud.paascenter.openapi.model.vo.DcInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.CLOUD_VENDOR_PAD_DC_KEY_PREFIX;
import static net.armcloud.paascenter.openapi.constants.CacheKeyConstants.PAD_DC_KEY_PREFIX;


@Component
public class DcInfoManager {
    private final RedisService redisService;
    private final DcInfoMapper dcInfoMapper;

    public DcInfoVO getDcInfoCache(String padCode) {
        return getDcInfoCache(padCode, () -> dcInfoMapper.getVoByPadCode(padCode));
    }


    public DcInfoVO getDcInfoCache(String padCode, Supplier<DcInfoVO> cacheSupplier) {
        String key = PAD_DC_KEY_PREFIX + padCode;
        String objJson = redisService.getCacheObject(key);
        if (StringUtils.isNoneBlank(objJson)) {
            return JSON.parseObject(objJson, DcInfoVO.class);
        }

        DcInfoVO padDcInfoVO = cacheSupplier.get();
        redisService.setCacheObject(key, JSON.toJSONString(padDcInfoVO), 3L, TimeUnit.DAYS);
        return padDcInfoVO;
    }

    public DcInfoManager(RedisService redisService, DcInfoMapper dcInfoMapper) {
        this.redisService = redisService;
        this.dcInfoMapper = dcInfoMapper;
    }

}
