package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * ADI文件上传解析DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI文件上传解析对象", description = "用于上传并解析ADI ZIP文件")
public class AdiUploadDTO {

    @ApiModelProperty(value = "ADI文件OSS临时地址", required = true, example = "https://oss.xx.com/temp/xxx.zip")
    @NotBlank(message = "ADI文件地址不能为空")
    private String fileUrl;
    
    @ApiModelProperty(value = "文件解压密码", example = "123456")
    private String password;
    
    @ApiModelProperty(value = "是否强制解析（即使存在风险项）", example = "false")
    private Boolean forceAnalysis = false;
    
    @ApiModelProperty(value = "是否自动创建模板", example = "true")
    private Boolean autoCreate = true;
    
    @ApiModelProperty(value = "是否正式版(0:测试版 1:正式版)", example = "0")
    private Integer isOfficial = 0;
    
    @ApiModelProperty(value = "状态(0:禁用 1:启用)", example = "0")
    private Integer status = 0;
    
    @ApiModelProperty(value = "客户ID", example = "999")
    private Long customerId;
    
    @ApiModelProperty(value = "备注", example = "测试版本")
    private String remark;
} 