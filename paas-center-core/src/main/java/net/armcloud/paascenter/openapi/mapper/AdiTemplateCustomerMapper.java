package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.AdiTemplateCustomer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AdiTemplateCustomerMapper extends BaseMapper<AdiTemplateCustomer> {
    
    /**
     * 批量插入模板与客户的关联关系
     *
     * @param templateId  模板ID
     * @param customerIds 客户ID列表
     */
    void batchInsertTemplateCustomers(@Param("templateId") Long templateId, @Param("customerIds") List<Long> customerIds,@Param("operationId") Long operationId);
    
    /**
     * 删除指定模板的所有客户关联
     *
     * @param templateId 模板ID
     */
    void deleteByTemplateId(@Param("templateId") Long templateId);
    
    /**
     * 查询模板关联的客户ID列表
     *
     * @param templateId 模板ID
     * @return 客户ID列表
     */
    List<Long> selectCustomerIdsByTemplateId(@Param("templateId") Long templateId);
}
