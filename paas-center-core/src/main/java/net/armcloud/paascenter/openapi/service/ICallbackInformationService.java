package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.CallbackInformation;
import net.armcloud.paascenter.common.model.vo.api.CallbackUrlVO;

public interface ICallbackInformationService extends IService<CallbackInformation> {

    /**
     * 根据客户ID和类型获取回调地址
     *
     * @param customerId 客户ID
     * @param type       回调类型
     * @return CallbackUrlVO
     */
    CallbackUrlVO getCallbackUrlService(Long customerId, Integer type);

}
