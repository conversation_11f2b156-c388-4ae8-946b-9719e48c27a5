package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ADI模板选择参数DTO
 */
@Data
@ApiModel(value = "ADI模板选择参数DTO")
public class AdiTemplateSelectionDTO {

    @ApiModelProperty(value = "状态(0-关闭，1-启用)")
    private Integer status;

    @ApiModelProperty(value = "是否正式版(0-测试版，1-正式版)")
    private Integer isOfficial;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;
}