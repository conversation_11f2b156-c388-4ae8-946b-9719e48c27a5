package net.armcloud.paascenter.openapi.rocketmq;

import com.alibaba.fastjson2.JSON;
import net.armcloud.paascenter.common.model.dto.api.UpdateDcImageStatusDTO;
import net.armcloud.paascenter.common.model.mq.container.PushImageTaskResultMQ;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.service.IDcImageService;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@Service
@AliRocketMQMsgListener(topic = "${mq.container-push-image-result-message.topic}", consumerGroup = "${mq.container-push-image-result-message.group}")
public class PushImageTaskResultConsumer implements AliRocketMQListener<MessageView> {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;
    @Resource
    private IDcImageService dcImageService;

    @Override
    public void onMessage(MessageView messageView) throws Exception {
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("PushImageTaskResultConsumer onMessage:{},MessageId:{}", str, messageView.getMessageId());

        PushImageTaskResultMQ dto = JSON.parseObject(str, PushImageTaskResultMQ.class);
        //消息幂等
        String key = RedisKeyPrefix.PUSH_IMAGE_TASK_RESULT_MSG_LOCK + messageView.getMessageId().toString();
        RLock lock = redissonDistributedLock.tryLock(key, 0, 5);
        if (Objects.isNull(lock)) {
            log.info("PushImageTaskResultConsumer not get lock onMessage:{},MessageId:{}", str, messageView.getMessageId());
            return;
        }
        try {
            UpdateDcImageStatusDTO updateDcImageStatusDTO = new UpdateDcImageStatusDTO();
            updateDcImageStatusDTO.setDcId(dto.getDcId());
            updateDcImageStatusDTO.setImageId(dto.getImageId());
            updateDcImageStatusDTO.setResult(dto.getResult());
            updateDcImageStatusDTO.setErrorMsg(dto.getErrorMsg());
            dcImageService.updateDcImageStatus(updateDcImageStatusDTO);
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }
}
