package net.armcloud.paascenter.openapi.model.vo;

import net.armcloud.paascenter.common.model.entity.paas.GameServerVersion;
import lombok.Data;

import java.util.Objects;

@Data
public class GameServerVersionInfoVo {

    // 版本下载链接
    private String downloadUrl;

    // 版本名称
    private String version;

    // 版本编码
    private Long versionCode;

    public static GameServerVersionInfoVo builder(GameServerVersion gameServerVersion) {
        if (Objects.isNull(gameServerVersion)) {
            return null;
        }

        GameServerVersionInfoVo gameServerVersionInfo = new GameServerVersionInfoVo();
        gameServerVersionInfo.setDownloadUrl(gameServerVersion.getDownloadUrl());
        gameServerVersionInfo.setVersion(gameServerVersion.getVersion());
        gameServerVersionInfo.setVersionCode(gameServerVersion.getVersionCode());
        return gameServerVersionInfo;
    }
}
