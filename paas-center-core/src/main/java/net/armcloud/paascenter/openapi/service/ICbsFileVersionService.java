package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.CbsFileVersion;
import net.armcloud.paascenter.openapi.model.vo.CbsFileVersionVO;

/**
 * cbs文件版本管理 - 接口
 */
public interface ICbsFileVersionService extends IService<CbsFileVersion> {

    /**
     * 获取最新的cbs
     * @return
     */
    CbsFileVersionVO getLastCbs();

}
