package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.vo.AdiParseResultVO;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateVO;
import net.armcloud.paascenter.openapi.model.vo.RealPhoneTemplateVO;
import net.armcloud.paascenter.openapi.service.IRealPhoneTemplateService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/openapi/open/realPhone/template")
@Api(tags = "真机模板")
@Slf4j
@RefreshScope
public class RealPhoneTemplateController {
    private final IRealPhoneTemplateService realPhoneTemplateService;
    @Value("${armcloud.adi.download.path}")
    public String downloadPath;

    public RealPhoneTemplateController(IRealPhoneTemplateService realPhoneTemplateService) {
        this.realPhoneTemplateService = realPhoneTemplateService;
    }

    @PostMapping("list")
    @ApiOperation(value = "分页获取真机模板")
    public Result<List<RealPhoneTemplateVO>> list(@Valid @RequestBody RealPhoneTemplateDTO param) {
        param.checkParam();
        return Result.ok(realPhoneTemplateService.pageList(param));
    }

    @PostMapping(value = "/adi-list")
    @ApiOperation(value = "查询ADI模板列表", httpMethod = "POST", notes = "支持多条件查询")
    public Result<Page<AdiTemplateVO>> list(@RequestBody @Valid AdiTemplateQueryDTO queryDTO) {
        log.info("Query ADI templates with params: {}", queryDTO);
        return Result.ok(realPhoneTemplateService.queryAdiTemplates(queryDTO));
    }

    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取ADI模板详情", httpMethod = "GET", notes = "根据ID获取ADI模板详情")
    public Result<AdiTemplateVO> detail(@PathVariable("id") Long id) {
        log.info("Get ADI template detail, id: {}", id);
        return Result.ok(realPhoneTemplateService.getAdiTemplateDetail(id));
    }

    @PostMapping(value = "/create")
    @ApiOperation(value = "创建ADI模板", httpMethod = "POST", notes = "创建新的ADI模板")
    public Result<?> create(@RequestBody @Valid AdiTemplateCreateDTO createDTO) {
        log.info("Create ADI template: {}", createDTO);
        return realPhoneTemplateService.createAdiTemplate(createDTO);
    }

    @PostMapping(value = "/custom/create")
    @ApiOperation(value = "创建ADI模板", httpMethod = "POST", notes = "创建新的ADI模板")
    public Result<?> createCustom(@RequestBody @Valid AdiCustomTemplateCreateDTO createDTO) {
        log.info("Create ADI template: {}", createDTO);
        return realPhoneTemplateService.createCustomAdiTemplate(createDTO);
    }

    @PutMapping(value = "/update")
    @ApiOperation(value = "更新ADI模板", httpMethod = "PUT", notes = "更新已有的ADI模板")
    public Result<?> update(@RequestBody @Valid AdiTemplateUpdateDTO updateDTO) {
        log.info("Update ADI template: {}", updateDTO);
        return realPhoneTemplateService.updateAdiTemplate(updateDTO);
    }

    @PutMapping(value = "/change-status")
    @ApiOperation(value = "修改ADI模板状态", httpMethod = "PUT", notes = "启用或禁用ADI模板")
    public Result<?> changeStatus(@RequestBody @Valid AdiTemplateStatusDTO statusDTO) {
        log.info("Change ADI template status: {}", statusDTO);
        return realPhoneTemplateService.updateAdiTemplateStatus(statusDTO);
    }

    @PutMapping(value = "/change-official")
    @ApiOperation(value = "修改ADI模板正式版标记", httpMethod = "PUT", notes = "将ADI模板设为测试版或正式版")
    public Result<?> changeOfficial(@RequestBody @Valid AdiTemplateOfficialDTO officialDTO) {
        log.info("Change ADI template official flag: {}", officialDTO);
        return realPhoneTemplateService.updateAdiTemplateOfficial(officialDTO);
    }

    @PutMapping(value = "/delete/{id}")
    @ApiOperation(value = "删除ADI模板", httpMethod = "DELETE", notes = "根据ID删除ADI模板")
    public Result<?> delete(@PathVariable("id") Long id) {
        log.info("Delete ADI template, id: {}", id);
        return realPhoneTemplateService.deleteAdiTemplate(id);
    }

    @GetMapping(value = "/parse-file")
    @ApiOperation(value = "解析ADI文件", httpMethod = "GET", notes = "解析OSS中的ADI文件")
    public Result<AdiParseResultVO> parse(@RequestParam("fileUrl") String fileUrl,@RequestParam(name = "id",required = false) String id) {
        log.info("Parse ADI file from URL: {},id:{}", fileUrl,id);
        return realPhoneTemplateService.parseAdiFile(fileUrl,id);
    }


    @PostMapping(value = "/selection")
    @ApiOperation(value = "查询ADI模板下拉列表", httpMethod = "POST", notes = "用于下拉框选择")
    public Result<List<AdiTemplateVO>> selectionPost(@RequestBody AdiTemplateSelectionDTO selectionDTO) {
        log.info("Query ADI templates for selection, params: {}", selectionDTO);
        return Result.ok(realPhoneTemplateService.listForSelection(selectionDTO));
    }

    @GetMapping(value = "/app-download-path")
    @ApiOperation(value = "应用下载地址", httpMethod = "GET", notes = "ADI应用下载地址")
    public Result<?> getDownloadPath() {
        Map<String, String> data = Collections.singletonMap("downloadPath", downloadPath);
        return Result.ok(data);
    }

}
