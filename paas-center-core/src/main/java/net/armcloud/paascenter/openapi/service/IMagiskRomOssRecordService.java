package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.model.dto.MagiskRomOssRecordCreateDTO;
import net.armcloud.paascenter.openapi.model.dto.MagiskRomOssRecordQueryDTO;
import net.armcloud.paascenter.openapi.model.entity.MagiskRomOssRecord;
import net.armcloud.paascenter.openapi.model.vo.MagiskRomOssRecordVO;

/**
 * Magisk ROM OSS记录服务接口
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
public interface IMagiskRomOssRecordService extends IService<MagiskRomOssRecord> {

    /**
     * 新增或更新记录
     * 如果版本已存在，则更新oss_url和replace_time；否则新增记录
     *
     * @param createDTO 创建请求DTO
     * @return 操作结果
     */
    Result<Void> createOrUpdate(MagiskRomOssRecordCreateDTO createDTO);

    /**
     * 查询记录
     * 如果version为空，返回最新创建的记录；否则根据version查询
     *
     * @param queryDTO 查询请求DTO
     * @return 查询结果
     */
    Result<MagiskRomOssRecordVO> queryRecord(MagiskRomOssRecordQueryDTO queryDTO);
}
