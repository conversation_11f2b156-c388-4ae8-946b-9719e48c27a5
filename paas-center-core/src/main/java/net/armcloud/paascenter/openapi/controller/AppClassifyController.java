package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyDetailVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyPadDetailVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyVO;
import net.armcloud.paascenter.openapi.service.IAppClassifyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/openapi/open/appClassify")
@Api(tags = "黑白名单")
public class AppClassifyController {
    @Resource
    private IAppClassifyService appClassifyService;

    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单列表", httpMethod = "POST", notes = "黑白名单列表")
    public Result<List<AppClassifyVO>> list(@RequestBody AppClassifyQueryDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(appClassifyService.list(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单详情", httpMethod = "POST", notes = "黑白名单详情")
    public Result<AppClassifyDetailVO> detail(@RequestParam("id") Long id) {
        Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        return Result.ok(appClassifyService.detail(id,customerId));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单保存", httpMethod = "POST", notes = "黑白名单保存")
    public Result<?> save(@Valid @RequestBody AppClassifySaveDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(appClassifyService.save(param));
    }

    @RequestMapping(value = "/addApp", method = RequestMethod.POST)
    @ApiOperation(value = "添加黑白名单app", httpMethod = "POST", notes = "添加黑白名单app")
    public Result<?> addApp(@Valid @RequestBody AppClassifyAddAppDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        appClassifyService.addApp(param);
        return Result.ok();
    }

    @RequestMapping(value = "/padDetail", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单实例关联详情", httpMethod = "POST", notes = "黑白名单实例关联详情")
    public Result<AppClassifyPadDetailVO> padDetail(@RequestParam("id") Long id) {
        Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        return Result.ok(appClassifyService.padDetail(id,customerId));
    }

    @RequestMapping(value = "/padSave", method = RequestMethod.POST)
    @ApiOperation(value = "黑白名单实例关联保存", httpMethod = "POST", notes = "黑白名单实例关联保存")
    public Result<?> padSave(@Valid @RequestBody AppClassifyPadSaveDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        appClassifyService.padSave(param);
        return Result.ok();
    }

    @RequestMapping(value = "/addPad", method = RequestMethod.POST)
    @ApiOperation(value = "增加黑白名单实例关联", httpMethod = "POST", notes = "增加黑白名单实例关联")
    public Result<?> addPad(@Valid @RequestBody AppClassifyAddPadDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        appClassifyService.addPad(param);
        return Result.ok();
    }

    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ApiOperation(value = "删除黑白名单", httpMethod = "POST", notes = "删除黑白名单")
    public Result<?> del(@RequestParam("id") Long id) {
        Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        appClassifyService.del(id,customerId);
        return Result.ok();
    }

    @RequestMapping(value = "/delPad", method = RequestMethod.POST)
    @ApiOperation(value = "删除黑白名单实例关联", httpMethod = "POST", notes = "删除黑白名单实例关联")
    public Result<?> delPad(@Valid @RequestBody AppClassifyDelPadDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        appClassifyService.delPad(param);
        return Result.ok();
    }

}
