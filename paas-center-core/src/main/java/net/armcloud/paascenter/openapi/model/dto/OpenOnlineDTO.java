package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class OpenOnlineDTO extends BaseDTO {
    @ApiModelProperty(value = "实例id", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, max = 200, message = "实例数量不多余200个")
    private List<String> padCodes;

    @ApiModelProperty(hidden = false)
    private Long customerId;

    @ApiModelProperty(value = "开启关闭ADB状态(1开启 0或者不传默认关闭)", required = true)
    private Integer openStatus;

}
