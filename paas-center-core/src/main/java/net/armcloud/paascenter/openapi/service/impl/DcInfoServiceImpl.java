package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.mapper.DcInfoMapper;
import net.armcloud.paascenter.openapi.model.vo.DcInfoVO;
import net.armcloud.paascenter.openapi.service.IDcInfoService;
import net.armcloud.paascenter.common.model.entity.paas.DcInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DcInfoServiceImpl extends ServiceImpl<DcInfoMapper, DcInfo> implements IDcInfoService {

    @Resource
    private DcInfoMapper dcInfoMapper;



}
