package net.armcloud.paascenter.openapi.netpadv2.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 网存实例V2创建DTO
 * 优化版本的网存实例创建接口参数
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Data
public class NetPadV2CreateDTO {

    @ApiModelProperty(value = "CPU是否隔离", hidden = true)
    private Boolean isolateCpu = false;

    @ApiModelProperty(value = "存储是否隔离", hidden = true)
    private Boolean isolateStorage = false;

    @ApiModelProperty(value = "内存是否隔离", hidden = true)
    private Boolean isolateMemory = false;

    @ApiModelProperty(value = "集群编码", required = true)
    @NotNull(message = "集群编码不能为空")
    private String clusterCode;

    @ApiModelProperty(value = "是否随机ADI模板")
    private Boolean randomADITemplates = false;

    @ApiModelProperty(value = "规格代码", required = true)
    @NotNull(message = "规格代码不能为空")
    private String specificationCode;

    @ApiModelProperty(value = "镜像ID", required = true)
    @NotNull(message = "镜像ID不能为空")
    private String imageId;

    @ApiModelProperty(value = "屏幕布局编码", required = true)
    private String screenLayoutCode;

    @ApiModelProperty(value = "实例数量", required = true)
    @NotNull(message = "实例数量不能为空")
    @Min(value = 1, message = "实例数量不能小于1")
    @Max(value = 100, message = "实例数量不能大于100")
    private Integer number = 1;

    @ApiModelProperty(value = "DNS配置")
    private String dns;

    @ApiModelProperty(value = "存储大小(GB)", required = true)
    @NotNull(message = "存储规格不能为空")
    private Integer storageSize;

    @ApiModelProperty(value = "真机模板ID")
    private Long realPhoneTemplateId;

    @ApiModelProperty(value = "国家代码")
    private String countryCode;

    @ApiModelProperty(value = "机型属性")
    private JSONObject androidProp;

    // 以下字段由系统自动填充，不需要前端传递
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(hidden = true)
    private String oprBy;

    @ApiModelProperty(hidden = true)
    private String sourceCode;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;
}
