package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.openapi.model.vo.SelectImageInfoVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.CustomerUploadImageParameterDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CustomerUploadImageMapper extends BaseMapper<CustomerUploadImage> {


    

    // 根据镜像唯一标识查询安卓版本
    public Integer getAndroidVersionByImageUniqueId(String uniqueId);

    SelectImageInfoVO getCustomerUploadImageInfoByUniqueId(@Param("uniqueId") String uniqueId,@Param("customerId")Long customerId);

    // 根据镜像唯一标识查询镜像参数
    String getImageParameterByImageUniqueId(@Param("uniqueId") String uniqueId);

    List<CustomerUploadImageParameterDTO> getImageParameterByImageUniqueIdList(@Param("imageIdList") List<String> imageIdList);
}
