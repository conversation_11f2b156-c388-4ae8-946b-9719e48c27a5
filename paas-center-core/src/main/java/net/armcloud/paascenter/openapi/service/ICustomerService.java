package net.armcloud.paascenter.openapi.service;


import net.armcloud.paascenter.common.client.internal.dto.CheckSdkTokenPadDTO;
import net.armcloud.paascenter.common.client.internal.dto.VerifyAndGetSDKCustomerDTO;
import net.armcloud.paascenter.common.client.internal.vo.SDKCustomerVO;

public interface ICustomerService {
    SDKCustomerVO verifyAndGetInfo(VerifyAndGetSDKCustomerDTO dto);

    /**
     * sdkToken bind uuid
     *
     * @param dto
     * @return Boolean
     */
    Boolean sdkTokenBindUuidService(VerifyAndGetSDKCustomerDTO dto);

    /**
     * checkSdkTokenBindCustomerAndPad
     * @param dto
     * @return Boolean
     */
    Boolean checkSdkTokenBindCustomerAndPad(CheckSdkTokenPadDTO dto);

    /**
     * 验证sdkToken 是否绑定客户和pad
     * @param dto
     * @return
     */

    Boolean checkSdkTokenBindCustomerAndPadV2(CheckSdkTokenPadDTO dto);
}
