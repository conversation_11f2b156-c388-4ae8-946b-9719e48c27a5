package net.armcloud.paascenter.openapi.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.ArmServerStatusDTO;
import net.armcloud.paascenter.common.client.internal.facade.ArmServerInternalFacade;
import net.armcloud.paascenter.common.model.dto.api.AddDeviceTaskDTO;
import net.armcloud.paascenter.openapi.service.IArmService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class ArmServerInternalController implements ArmServerInternalFacade {
    @Resource
    private IArmService armService;
    @Resource
    private IDeviceService deviceService;
    @Override
    public Result<?> armServerStatusCallback(ArmServerStatusDTO armServerStatusDTO) {
        return armService.armServerStatusCallback(armServerStatusDTO.getServerSn(), armServerStatusDTO.getStatus());
    }

    @Override
    public Result<?> createDevice(AddDeviceTaskDTO addDeviceTaskDTO) {
        return Result.ok(deviceService.pullModeAddDeviceTask(addDeviceTaskDTO));
    }
}
