package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.constant.task.RedisTaskQueueConstants;
import net.armcloud.paascenter.common.core.constant.task.TaskStatusConstants;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.Task;
import net.armcloud.paascenter.common.model.entity.task.TaskRetry;
import net.armcloud.paascenter.openapi.model.dto.NetSyncTaskPayloadDTO;
import net.armcloud.paascenter.openapi.service.INetSyncService;
import net.armcloud.paascenter.task.enums.TaskTypeAndChannelEnum;
import net.armcloud.paascenter.task.manager.TaskQueueManager;
import net.armcloud.paascenter.task.mapper.PadTaskMapper;
import net.armcloud.paascenter.task.service.ITaskRetryService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import org.springframework.data.redis.core.RedisTemplate;
import javax.annotation.Resource;
import java.util.Date;
import java.util.Set;


@Slf4j
@Service
@RefreshScope
public class NetSyncServiceImpl implements INetSyncService {

    @Resource
    TaskQueueManager taskQueueManager;

    @Resource
    PadTaskMapper padTaskMapper;

    @Resource
    private ITaskRetryService taskRetryService;
    
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    // 每次推送至edge条目
    @Value("${task.net_push.count:20}")
    private Integer pushTaskCount;

    @Override
    public void pushTaskToEdge() {
        log.debug("Starting to process NET_SYNC tasks, max count: {}", pushTaskCount);

        ZSetOperations<String, String> zsetOps = redisTemplate.opsForZSet();
        HashOperations<String, String, String> hashOps = redisTemplate.opsForHash();
        // 从 ZSet 中取最老的 pushTaskCount 个 subTaskId（score 最小）
        Set<String> subTaskIds = zsetOps.range(RedisTaskQueueConstants.NET_SYNC_FIFO_TASKID_QUEUE, 0, pushTaskCount - 1);
        if (CollUtil.isEmpty(subTaskIds)) {
            log.debug("No NET_SYNC tasks in ZSET queue");
            return;
        }
        log.info("Retrieved {} task IDs from ZSET", subTaskIds.size());

        int processedCount = 0;
        for (String subTaskId : subTaskIds) {
            String payload = hashOps.get(RedisTaskQueueConstants.NET_SYNC_FIFO_TASK_HASH, subTaskId);
            if (payload == null) {
                // 如果 Hash 中不存在对应 payload，就直接清理 ZSet 中的脏 ID
                zsetOps.remove(RedisTaskQueueConstants.NET_SYNC_FIFO_TASKID_QUEUE, subTaskId);
                log.warn("No payload found for subTaskId {}, removed from ZSET", subTaskId);
                continue;
            }

            try {
                NetSyncTaskPayloadDTO queueParam = JSON.parseObject(payload, NetSyncTaskPayloadDTO.class);

                Task masterTask = queueParam.getMasterTask();
                PadTask padTask = queueParam.getPadTask();
                String clusterCode = queueParam.getClusterCode();
                String edgeEventNoticeKey = queueParam.getEdgeEventNoticeKey();
                Object paramObj = queueParam.getParamObj();

                TaskTypeAndChannelEnum taskTypeEnum = TaskTypeAndChannelEnum.fromCode(masterTask.getType());
                if (taskTypeEnum == null) {
                    log.error("Unknown task type: {}, taskId: {}", masterTask.getType(), padTask.getId());
                    // 从队列中移除
                    zsetOps.remove(RedisTaskQueueConstants.NET_SYNC_FIFO_TASKID_QUEUE, subTaskId);
                    hashOps.delete(RedisTaskQueueConstants.NET_SYNC_FIFO_TASK_HASH, subTaskId);
                    continue;
                }

                // 发送到边缘
                log.info("Sending NET_SYNC task to edge: taskId={}, padCode={}, cluster={}",
                        padTask.getId(), padTask.getPadCode(), clusterCode);

                Boolean noticeResult = taskQueueManager.edgeEventNotice(
                        taskTypeEnum.getChannel(), clusterCode, edgeEventNoticeKey, paramObj);

                if (Boolean.TRUE.equals(noticeResult)) {
                    // 通知成功，更新状态并清理队列
                    handleTaskNotificationSuccess(masterTask, padTask,
                            taskTypeEnum, clusterCode, edgeEventNoticeKey, paramObj);
                    zsetOps.remove(RedisTaskQueueConstants.NET_SYNC_FIFO_TASKID_QUEUE, subTaskId);
                    hashOps.delete(RedisTaskQueueConstants.NET_SYNC_FIFO_TASK_HASH, subTaskId);
                    processedCount++;
                } else {
                    // 通知失败，做失败处理但保留在队列，将在下次扫描时重试
                    handleTaskNotificationFailure(masterTask, padTask,
                            taskTypeEnum.getChannel(), clusterCode, edgeEventNoticeKey, paramObj);
                }

            } catch (Exception ex) {
                log.error("Error processing NET_SYNC task {}, will retry later: {}", subTaskId, ex.getMessage(), ex);
                // 留在队列，下一轮重试
            }
        }

        if (processedCount > 0) {
            log.info("Successfully processed {} NET_SYNC tasks", processedCount);
        }
    }

    /**
     * 处理任务通知成功的情况
     * 根据任务类型决定设置为成功或执行中
     *
     * @param masterTask 主任务
     * @param padTask 实例任务
     * @param taskTypeEnum 任务类型枚举
     */
    private void handleTaskNotificationSuccess(Task masterTask, PadTask padTask,
                                               TaskTypeAndChannelEnum taskTypeEnum, String clusterCode,
                                               String edgeEventNoticeKey, Object paramObj) {
        try {
            // 某些任务通知后就算成功，而有些需要设置为执行中等待回调
            int padTaskStatus;

            // 检查任务类型是否需要设置为执行中
            if (Boolean.TRUE.equals(taskTypeEnum.getNeedWaitCallback())) {
                // 创建ACK重试任务
                createRetryTask(masterTask, padTask, taskTypeEnum.getChannel(), clusterCode, edgeEventNoticeKey, paramObj,"ack");
                // 需要等待回调的任务设置为执行中
                padTaskStatus = TaskStatusConstants.EXECUTING.getStatus();
                log.info("Task notified and set to EXECUTING (waiting for callback): taskId={}, taskType={}",
                        padTask.getId(), masterTask.getType());
            } else {
                // 不需要等待回调的任务设置为成功
                padTaskStatus = TaskStatusConstants.SUCCESS.getStatus();
                log.info("Task notified and set to SUCCESS: taskId={}, taskType={}",
                        padTask.getId(), masterTask.getType());
            }

            // 更新任务状态
            padTaskMapper.updateStatusAndTimeById(padTask.getId(), padTaskStatus, new Date());
        }catch (Exception e){
            log.error("handleTaskNotificationSuccess error: {}",masterTask,e);
        }

    }

    /**
     * 处理任务通知失败的情况
     * 判断是否需要重试，并进行相应处理
     *
     * @param masterTask 主任务
     * @param padTask 实例任务
     * @param taskChannelEnumCode 任务渠道编码
     * @param clusterCode 集群编码
     * @param edgeEventNoticeKey 通知Key
     * @param paramObj 参数对象
     */
    private void handleTaskNotificationFailure(Task masterTask, PadTask padTask,
                                               String taskChannelEnumCode, String clusterCode,
                                               String edgeEventNoticeKey, Object paramObj) {
        try {
            // 失败，检查是否需要重试
            TaskTypeAndChannelEnum taskTypeEnum = TaskTypeAndChannelEnum.fromCode(masterTask.getType());
            if (taskTypeEnum != null && Boolean.TRUE.equals(taskTypeEnum.getNeedRetry())) {
                log.info("Direct task failed and needs retry: taskId={}, taskType={}, padCode={}",
                        padTask.getId(), masterTask.getType(), padTask.getPadCode());

                // 创建重试任务
                createRetryTask(masterTask, padTask, taskChannelEnumCode, clusterCode, edgeEventNoticeKey, paramObj,"notify");

                // 将任务状态修改为待执行
                padTaskMapper.updateStatusAndTimeById(padTask.getId(), TaskStatusConstants.WAIT_EXECUTE.getStatus(), new Date());
            } else {
                // 不需要重试的任务直接设置为失败
                padTaskMapper.updateStatusAndTimeById(padTask.getId(), TaskStatusConstants.FAIL_ALL.getStatus(), new Date());
                log.info("Task failed and does not need retry: taskId={}, taskType={}",
                        padTask.getId(), masterTask.getType());
            }
        }catch (Exception e){
            log.error("handleTaskNotificationFailure error: {}",masterTask,e);
        }

    }

    /**
     * 创建任务重试记录
     *
     * @param masterTask 主任务
     * @param padTask 实例任务
     * @param taskChannelEnumCode 任务渠道编码
     * @param clusterCode 集群编码
     * @param edgeEventNoticeKey 通知Key
     * @param paramObj 参数对象
     * @param action 重试动作类型: notify-通知重试, ack-确认检查
     */
    private void createRetryTask(Task masterTask, PadTask padTask,
                                 String taskChannelEnumCode, String clusterCode,
                                 String edgeEventNoticeKey, Object paramObj, String action) {
        // 创建任务参数对象，包含edgeEventNotice所需的所有参数
        JSONObject retryParam = new JSONObject();
        retryParam.put("taskChannel", taskChannelEnumCode);
        retryParam.put("clusterCode", clusterCode);
        retryParam.put("noticeKey", edgeEventNoticeKey);
        retryParam.put("paramObj", paramObj);

        // 创建任务重试对象
        TaskRetry taskRetry = new TaskRetry();
        taskRetry.setMasterTaskId(masterTask.getId());
        taskRetry.setSubTaskId(padTask.getId());
        taskRetry.setPadCode(padTask.getPadCode());
        taskRetry.setTaskType(masterTask.getType());
        taskRetry.setTaskParam(retryParam.toJSONString());
        taskRetry.setAction(action);
        // 创建任务重试记录
        taskRetryService.createTaskRetry(taskRetry);
        log.info("Created {} retry task for: taskId={}, taskType={}", action, padTask.getId(), masterTask.getType());
    }
}
