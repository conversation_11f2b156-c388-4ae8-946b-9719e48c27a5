package net.armcloud.paascenter.openapi.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;
import java.io.File;

/**
 * ADI模板相关配置
 */
@Slf4j
@Data
@Configuration
@RefreshScope
public class AdiConfig {

    /**
     * ADI临时目录路径
     */
    @Value("${armcloud.adi.temp.dir}")
    private String adiTempDir;
    
    @PostConstruct
    public void init() {
        // 设置临时目录
        if (StringUtils.isNotBlank(adiTempDir)) {
            // 设置系统属性，供AdiParserUtil使用
            System.setProperty("armcloud.adi.temp.dir", adiTempDir);
            
            // 确保目录存在
            ensureDirectoryExists(adiTempDir, "ADI临时目录");
        } else {
            log.info("未配置ADI临时目录，将使用系统默认临时目录");
        }
        
    }
    
    /**
     * 确保目录存在，如果不存在则创建
     * 
     * @param dirPath 目录路径
     * @param dirDesc 目录描述（用于日志）
     * @return 目录是否有效
     */
    private boolean ensureDirectoryExists(String dirPath, String dirDesc) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (created) {
                log.info("已创建{}：{}", dirDesc, dirPath);
                return true;
            } else {
                log.warn("无法创建{}：{}", dirDesc, dirPath);
                return false;
            }
        } else if (!dir.isDirectory()) {
            log.warn("配置的{}不是一个有效的目录：{}", dirDesc, dirPath);
            return false;
        } else {
            log.info("使用配置的{}：{}", dirDesc, dirPath);
            return true;
        }
    }
} 