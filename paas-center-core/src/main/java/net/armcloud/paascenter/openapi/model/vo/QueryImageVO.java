package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class QueryImageVO implements Serializable {

    @ApiModelProperty(value = "镜像ID")
    private String imageId;

    @ApiModelProperty(value = "镜像名称")
    private String imageName;

    @ApiModelProperty(value = "镜像Tag")
    private String imageTag;

    @ApiModelProperty(value = "SOC类型")
    private String serverType;

    @ApiModelProperty(value = "Rom版本")
    private String romVersion;

    @ApiModelProperty(value = "描述")
    private String imageDesc;

    @ApiModelProperty(value = "发版版本 1测试版 2正式版")
    private Integer releaseType;
}
