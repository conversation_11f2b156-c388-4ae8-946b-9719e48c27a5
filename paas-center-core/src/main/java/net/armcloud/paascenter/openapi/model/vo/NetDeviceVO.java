package net.armcloud.paascenter.openapi.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/2/11 14:17
 * @Version 1.0
 */
@Data
public class NetDeviceVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "可用ip")
    private Integer availableIp;

    @ApiModelProperty(value = "ipv4Cidr")
    private String ipv4Cidr;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "状态")
    private String status;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "可用IP")
    private Integer availableIpNum;

    @ApiModelProperty(value = "未用IP")
    private Integer noUsedIpNum;

    @ApiModelProperty(value = "总数")
    private Integer countIpNum;

    @ApiModelProperty(value = "绑定状态")
    private String bindName;
}
