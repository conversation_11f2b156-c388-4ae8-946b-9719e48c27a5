package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.client.internal.UpdateDeviceVersionDTO;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.openapi.service.CbsService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/2/17 17:42
 * @Version 1.0
 */
@RestController
@RequestMapping("/openapi/open/cbs")
@Api(tags = "CBS")
@Slf4j
public class CbsController {


    private final CbsService cdsService;

    public CbsController(CbsService cdsService) {
        this.cdsService = cdsService;
    }

    @RequestMapping("/update/device/version")
    public Result<?> updateDeviceVersion( @RequestBody UpdateDeviceVersionDTO updateDeviceVersionDTO) {
        cdsService.updateDevice(updateDeviceVersionDTO);
        return Result.ok();
    }


}
