package net.armcloud.paascenter.openapi.model.dto.padnetowrk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


@Data
public class SetPadNetworkProxyDTO {
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @Size(min = 1, message = "padCodes cannot null")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    private String account;

    private String password;

    private String ip;

    private Integer port;

    @NotNull(message = "enable cannot null")
    private Boolean enable;
}
