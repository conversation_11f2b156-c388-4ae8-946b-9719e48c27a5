package net.armcloud.paascenter.openapi.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.armcloud.paascenter.common.client.internal.vo.CustomerAppFileVO;
import net.armcloud.paascenter.common.client.internal.vo.CustomerFileVO;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.filecenter.mapper.UserFileMapper;

@Component
public class FileManager {

    @Autowired
    private UserFileMapper userFileMapper;

    /**
     * 查询用户拥有的应用
     *
     * @throws BasicException 应用不存在异常
     */
    public CustomerAppFileVO getCustomerAppFile(Long customerId, String appId, String appName, String pkgName) throws BasicException {
        return userFileMapper.queryCustomerAppFile(customerId, appId, appName, pkgName);
    }


    public CustomerFileVO getCustomerExistingFile(Long customerId, String fileUniqueId) throws BasicException {
        CustomerFileVO customerFileVO = userFileMapper.queryCustomerFile(customerId, null, fileUniqueId);
        return customerFileVO;
    }

    public void refreshLastUseTime(long fileId) {
        // 暂时关闭文件中心刷新文件使用时间
        // FeignUtils.getContent(fileCenterFileInternalStub.refreshLastUseTime(fileId));
    }


    public CustomerFileVO getFileIdByCustomerMd5(Long customerId, String md5) {
        CustomerFileVO customerFileVO = userFileMapper.queryCustomerFile(customerId, md5, null);
        return customerFileVO;

    }

}
