package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.GameServeVersionDTO;
import net.armcloud.paascenter.openapi.model.dto.GameServerGetPadDcInfoDTO;
import net.armcloud.paascenter.openapi.model.vo.DefaultRootAppVO;
import net.armcloud.paascenter.openapi.model.vo.GameServerVersionInfoVo;
import net.armcloud.paascenter.openapi.model.vo.PadDcInfoVO;
import net.armcloud.paascenter.openapi.service.IDCService;
import net.armcloud.paascenter.openapi.service.IDefaultRootAppConfigService;
import net.armcloud.paascenter.openapi.service.IKeepAliveAppPadService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.common.client.internal.dto.AppBlacklistDTO;
import net.armcloud.paascenter.common.client.internal.dto.StartAppListDTO;
import net.armcloud.paascenter.common.client.internal.vo.BlacklistVO;
import net.armcloud.paascenter.common.client.internal.vo.StartAppListVO;
import net.armcloud.paascenter.common.client.internal.vo.WhitelistVO;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static net.armcloud.paascenter.common.core.constant.Constants.REQUEST_AUTH;

@RestController
@RequestMapping("/openapi/open/pad/game-server")
@Api(tags = "SDK接口")
public class GameServerPadController {
    private final IDCService dcService;

    private final IPadService padService;

    private final IDefaultRootAppConfigService defaultRootAppConfigService;
    private final IKeepAliveAppPadService keepAliveAppPadService;

    @GetMapping("getDcInfo")
    @ApiOperation(value = "查询实例所在机房信息")
    public Result<PadDcInfoVO> getDcInfo(@Valid GameServerGetPadDcInfoDTO param) {
        HttpServletRequest request = RequestUtils.getCurrentRequest();
        return Result.ok(dcService.getDcInfoByGameServer(param, request.getHeader(REQUEST_AUTH)));
    }

    @PostMapping("getGameServeVersion")
    @ApiOperation(value = "查询机房gameServer最新版本")
    public Result<GameServerVersionInfoVo> getGameServeVersion(@Valid @RequestBody GameServeVersionDTO param) {
        return Result.ok(dcService.getGameServeVersion(param));
    }

    @PostMapping("padBlacklists")
    @ApiOperation(value = "gameServer获取黑名单列表")
    public Result<BlacklistVO> padBlacklists(@Valid @RequestBody AppBlacklistDTO param) {
        return Result.ok(padService.getBlacklistService(param));
    }

    @PostMapping("padWhitelists")
    @ApiOperation(value = "gameServer获取白名单列表")
    public Result<WhitelistVO> padWhitelists(@Valid @RequestBody AppBlacklistDTO param) {
        return Result.ok(padService.getWhitelistService(param));
    }

    /**
     * 获取默认root应用列表
     * 此处24小时缓存 目前是通过直接更新数据库 所以更新时需要删除缓存 key为default_root_app_config:
     * @return
     */
    @PostMapping("padRootlists")
    @ApiOperation(value = "获取默认root应用列表")
    public Result<DefaultRootAppVO> padRootLists() {
        return Result.ok(defaultRootAppConfigService.getDefaultRootAppList());
    }

    @PostMapping("padStartAppLists")
    @ApiOperation(value = "gameServer获取获取保活应用列表")
    public Result<StartAppListVO> padStartAppLists(@Valid @RequestBody StartAppListDTO param) {
        return Result.ok(keepAliveAppPadService.getPadStartAppLists(param));
    }

    public GameServerPadController(IDCService dcService, IPadService padService,IDefaultRootAppConfigService defaultRootAppConfigService,
                                   IKeepAliveAppPadService keepAliveAppPadService) {
        this.dcService = dcService;
        this.padService = padService;
        this.defaultRootAppConfigService = defaultRootAppConfigService;
        this.keepAliveAppPadService = keepAliveAppPadService;
    }

}
