package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResOffLog;
import net.armcloud.paascenter.openapi.mapper.NetStorageResOffLogMapper;
import net.armcloud.paascenter.openapi.service.INetStorageResOffLogService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Author: weimj
 */
@Service
@Slf4j
public class NetStorageResOffLogServiceImpl extends ServiceImpl<NetStorageResOffLogMapper, NetStorageResOffLog> implements INetStorageResOffLogService {


    @Override
    public NetStorageResOffLog getByPadCode(String padCode) {
        return baseMapper.getByPadCode(padCode);
    }

    @Override
    public List<NetStorageResOffLog> getByPadCodeList(List<String> padCodes) {
        return baseMapper.getByPadCodeList(padCodes);
    }
}
