package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchPadPropertiesDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1, max = 200, message = "实例数量不多余200个")
    private List<String> padCodes;
}
