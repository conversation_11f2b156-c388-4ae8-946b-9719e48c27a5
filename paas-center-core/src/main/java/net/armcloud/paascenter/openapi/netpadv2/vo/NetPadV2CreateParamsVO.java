package net.armcloud.paascenter.openapi.netpadv2.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网存实例V2创建参数VO
 * 用于封装创建实例时传入的参数信息
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Data
public class NetPadV2CreateParamsVO {

    @ApiModelProperty(value = "实例编码")
    private String padCode;

    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    @ApiModelProperty(value = "DNS配置")
    private String dns;

    @ApiModelProperty(value = "真机模板ID")
    private Long realPhoneTemplateId;

    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @ApiModelProperty(value = "安卓属性")
    private JSONObject androidProp;
}
