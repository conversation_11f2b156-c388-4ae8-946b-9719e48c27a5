package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.service.IAppBlackService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.SetUpBlackListDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@RestController
@RequestMapping("/openapi/open/appBlack")
@Api(tags = "应用黑名单")
@Slf4j
public class AppBlackController {

    @Resource
    private IAppBlackService appBlackService;

    @ApiOperation(value = "设置应用黑名单")
    @PostMapping("/setUpBlackList")
    public Result<?> setUpBlackList(@RequestBody @Valid SetUpBlackListDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return appBlackService.setUpBlackList(param);
    }



}
