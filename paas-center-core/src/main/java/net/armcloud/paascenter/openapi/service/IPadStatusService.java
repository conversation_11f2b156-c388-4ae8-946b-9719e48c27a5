package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.dto.api.UpdatePadOnlineDTO;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;

import java.util.List;

public interface IPadStatusService extends IService<PadStatus> {

    /**
     * @param padCodes  实例编号
     * @param padStatus 实例状态
     * @return Boolean
     */
    Boolean updatePadStatusService(List<String> padCodes, Integer padStatus);

    void updatePadOnline(UpdatePadOnlineDTO param);

    /**
     * 修改实例状态，发送实例状态回调消息
     */
    Boolean updatePadStatusAndSendPadStatusCallback(List<String> padCodes, Integer status, Long customerId, String oprBusiness);

    /**
     * 仅修改实例状态
     * @param padCode
     * @param status
     */
    void updatePadStatus(String padCode, Integer status);
}
