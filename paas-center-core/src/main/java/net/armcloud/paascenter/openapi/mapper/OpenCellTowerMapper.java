package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import net.armcloud.paascenter.common.model.entity.paas.OpenCellTower;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 基站信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Mapper
public interface OpenCellTowerMapper extends BaseMapper<OpenCellTower> {

   // 根据mcc随机一个基站信息
   OpenCellTower selectRandomOpenCellTower(@Param("mcc") Integer mcc);

   Integer selectRandomIdOpenCellTower(@Param("mcc") Integer mcc);

   List<Integer> selectIdsByMcc(@Param("mcc") Integer mcc);

   OpenCellTower queryById(@Param("id") Integer id);

}
