package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.model.entity.paas.AdiTemplateLog;
import net.armcloud.paascenter.openapi.mapper.AdiTemplateLogMapper;
import net.armcloud.paascenter.openapi.model.dto.AdiTemplateLogQueryDTO;
import net.armcloud.paascenter.openapi.model.enums.AdiTemplateOperationTypeEnum;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateLogVO;
import net.armcloud.paascenter.openapi.service.IAdiTemplateLogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * ADI模板操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Slf4j
@Service
public class AdiTemplateLogServiceImpl extends ServiceImpl<AdiTemplateLogMapper, AdiTemplateLog> implements IAdiTemplateLogService {

    @Resource
    private AdiTemplateLogMapper adiTemplateLogMapper;

    @Override
    public Page<AdiTemplateLogVO> queryAdiTemplateLogs(AdiTemplateLogQueryDTO queryDTO) {
        // 使用高级查询获取带模板名称和操作者名称的日志列表
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("templateId", queryDTO.getTemplateId());
            params.put("operationType", queryDTO.getOperationType());
            params.put("operator", queryDTO.getOperator());
            params.put("startTime", queryDTO.getStartTime());
            params.put("endTime", queryDTO.getEndTime());

            PageHelper.startPage(queryDTO.getPage(), queryDTO.getRows());
            List<Map<String, Object>> logMaps = adiTemplateLogMapper.selectAdiTemplateLogsWithNames(params);

            // 转换为VO
            List<AdiTemplateLogVO> voList = convertToVOListFromMaps(logMaps);


            return new Page<>(voList);
        } catch (Exception e) {
            log.error("Failed to query AdiTemplateLogs with joins, falling back to simple query", e);
            // 如果高级查询失败，回退到简单查询
            return queryAdiTemplateLogsSimple(queryDTO);
        }
    }

    /**
     * 简单查询方式（不带关联查询）
     */
    private Page<AdiTemplateLogVO> queryAdiTemplateLogsSimple(AdiTemplateLogQueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPage(), queryDTO.getRows());
        
        LambdaQueryWrapper<AdiTemplateLog> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (queryDTO.getTemplateId() != null) {
            wrapper.eq(AdiTemplateLog::getTemplateId, queryDTO.getTemplateId());
        }
        if (queryDTO.getOperationType() != null) {
            wrapper.eq(AdiTemplateLog::getOperationType, queryDTO.getOperationType());
        }
        if (queryDTO.getOperator() != null && !StringUtils.isEmpty(queryDTO.getOperator())) {
            wrapper.eq(AdiTemplateLog::getOperator, queryDTO.getOperator());
        }
        if (queryDTO.getStartTime() != null) {
            wrapper.ge(AdiTemplateLog::getOperationTime, queryDTO.getStartTime());
        }
        if (queryDTO.getEndTime() != null) {
            wrapper.le(AdiTemplateLog::getOperationTime, queryDTO.getEndTime());
        }
        
        // 按操作时间降序排序
        wrapper.orderByDesc(AdiTemplateLog::getOperationTime);
        
        List<AdiTemplateLog> logs = adiTemplateLogMapper.selectList(wrapper);
        PageInfo<AdiTemplateLog> pageInfo = new PageInfo<>(logs);
        
        // 转换为VO
        List<AdiTemplateLogVO> voList = convertToVOList(logs);
        
        Page<AdiTemplateLogVO> page = new Page<>();
        page.setPage(pageInfo.getPageNum());
        page.setRows(pageInfo.getPageSize());
        page.setTotal(pageInfo.getTotal());
        page.setTotalPage(pageInfo.getPages());
        page.setSize(pageInfo.getSize());
        page.setPageData(voList);
        
        return page;
    }

    @Override
    public AdiTemplateLogVO getAdiTemplateLogDetail(Long id) {
        AdiTemplateLog log = adiTemplateLogMapper.selectById(id);
        if (log == null) {
            return null;
        }
        
        return convertToVO(log);
    }

    @Override
    public boolean recordLog(Long templateId, Integer operationType, String operator, String operationDetail) {
        AdiTemplateLog log = new AdiTemplateLog();
        log.setTemplateId(templateId);
        log.setOperationType(operationType);
        log.setOperator(operator);
        log.setOperationDetail(operationDetail);
        log.setOperationTime(new Date());
        log.setCreateBy(operator);
        log.setCreateTime(new Date());
        log.setUpdateBy(operator);
        log.setUpdateTime(new Date());
        return this.save(log);
    }

    @Override
    public boolean recordLog(AdiTemplateLog log) {
        if (log.getOperationTime() == null) {
            log.setOperationTime(new Date());
        }
        return this.save(log);
    }
    
    /**
     * 将实体对象转换为VO
     */
    private AdiTemplateLogVO convertToVO(AdiTemplateLog log) {
        if (log == null) {
            return null;
        }
        AdiTemplateLogVO vo = new AdiTemplateLogVO();
        BeanUtils.copyProperties(log, vo);
        
        // 操作类型描述
        vo.setOperationTypeDesc(AdiTemplateOperationTypeEnum.getDescByCode(log.getOperationType()));
        
        return vo;
    }
    
    /**
     * 批量将实体对象转换为VO
     */
    private List<AdiTemplateLogVO> convertToVOList(List<AdiTemplateLog> logs) {
        List<AdiTemplateLogVO> voList = new ArrayList<>();
        if (logs == null || logs.isEmpty()) {
            return voList;
        }
        
        for (AdiTemplateLog log : logs) {
            voList.add(convertToVO(log));
        }
        
        return voList;
    }
    
    /**
     * 从Map列表转换为VO列表（用于处理连表查询结果）
     */
    private List<AdiTemplateLogVO> convertToVOListFromMaps(List<Map<String, Object>> logMaps) {
        List<AdiTemplateLogVO> voList = new ArrayList<>();
        if (logMaps == null || logMaps.isEmpty()) {
            return voList;
        }
        
        for (Map<String, Object> map : logMaps) {
            AdiTemplateLogVO vo = new AdiTemplateLogVO();
            
            vo.setId(getLongValue(map, "id"));
            vo.setTemplateId(getLongValue(map, "template_id"));
            vo.setTemplateName((String) map.get("template_name"));
            vo.setOperationType((Integer) map.get("operation_type"));
            vo.setOperationTypeDesc(AdiTemplateOperationTypeEnum.getDescByCode(vo.getOperationType()));
            vo.setOperator(getLongValue(map, "operator"));
            vo.setOperatorName((String) map.get("operator_name"));
            vo.setOperationDetail((String) map.get("operation_detail"));
            vo.setOperationTime((Date) map.get("operation_time"));
            
            voList.add(vo);
        }
        
        return voList;
    }
    
    /**
     * 从Map中安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}
