package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.PadGroupNew;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【pad_group(用户分组表)】的数据库操作Mapper
* @createDate 2025-01-16 22:01:30
* @Entity generator.domain.PadGroup
*/

@Mapper
public interface PadGroupNewMapper extends BaseMapper<PadGroupNew> {

    int deleteByPrimaryKey(Long id);

    int insert(PadGroupNew record);

    int insertSelective(PadGroupNew record);

    PadGroupNew selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PadGroupNew record);

    int updateByPrimaryKey(PadGroupNew record);

}
