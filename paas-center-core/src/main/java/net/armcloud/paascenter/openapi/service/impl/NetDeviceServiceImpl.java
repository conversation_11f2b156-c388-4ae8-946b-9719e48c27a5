package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.NetDeviceMapper;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.service.INetDeviceService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.NetDevice;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.NAME_OR_IPV4CIDR_EXIST;

@Service
public class NetDeviceServiceImpl implements INetDeviceService {
    @Resource
    private NetDeviceMapper netDeviceMapper;

    @Resource
    private CustomerMapper customerMapper;



    @Override
    public Result<?> saveNetDevice(NetDevice param) {
        List<NetDevice> netDevices = netDeviceMapper.selectNetDeviceByIpv4OrNameExcludingId(param.getIpv4Cidr(), param.getName(), null);
        if (CollUtil.isNotEmpty(netDevices)) {
            throw new BasicException(NAME_OR_IPV4CIDR_EXIST);
        }
        String customerName = getCustomerInfoById(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest())).getCustomerName();
        param.setBindFlag(ClusterAndNetConstant.NOT_BOUND);
        param.setDeleteFlag(ClusterAndNetConstant.NOT_DELETED);
        param.setCreateBy(customerName);
        param.setCreateTime(new Date());
        netDeviceMapper.saveNetDevice(param);
        return Result.ok();
    }
    private CustomerInfoVo getCustomerInfoById(Long customerId) {
        return customerMapper.getCustomerInfoById(customerId);
    }
}
