package net.armcloud.paascenter.openapi.scheduled;

import net.armcloud.paascenter.common.utils.DingTalkRobotClient;
import org.apache.catalina.connector.Connector;
import org.apache.catalina.startup.Tomcat;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
@Component
public class TomcatMetricsScheduled implements ApplicationContextAware {

    private static ApplicationContext applicationContext;
    @Value("${spring.profiles.active:unknown}")
    private String springProfilesActive;

    /**
     * 放大一百倍,单位%
     */
    @Value("${spring.tomcat.warning.max:80}")
    private Integer maxThread;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Scheduled(fixedRate = 1 * 60 * 1000) // 每 1 分钟执行一次
    public void monitorTomcat() {
        // 从 Spring 上下文中获取 WebServer
        ServletWebServerApplicationContext webServerContext = (ServletWebServerApplicationContext) applicationContext;

        // 获取 Tomcat 实例
        TomcatWebServer tomcatWebServer = (TomcatWebServer) webServerContext.getWebServer();
        Tomcat tomcat = tomcatWebServer.getTomcat();

        // 获取 Tomcat 的连接器
        Connector[] connectors = tomcat.getService().findConnectors();

        for (Connector connector : connectors) {
            // 获取连接器的线程池 Executor
            ThreadPoolExecutor executor = (ThreadPoolExecutor) connector.getProtocolHandler().getExecutor();
            int size = executor.getQueue().size();
            // 获取最大线程数
            int maxThreads = executor.getMaximumPoolSize();

            // 获取当前活动线程数
            int currentThreadsBusy = executor.getActiveCount();

            // 获取已完成的线程数
            long completedThreads = executor.getCompletedTaskCount();

            // 获取线程池的核心线程数
            int coreThreads = executor.getCorePoolSize();

            // 如果当前活动线程数超过最大线程数的 80%，则发送告警消息
            if(currentThreadsBusy*100/maxThreads>=maxThread){
                DingTalkRobotClient.sendMessage(springProfilesActive,"Tomcat 监控信息.\n"+"等待队列大小："+size+"\n"+"允许最大线程数："+maxThreads+"\n"+"当前连接数量："+currentThreadsBusy);
            }
        }
    }
}
