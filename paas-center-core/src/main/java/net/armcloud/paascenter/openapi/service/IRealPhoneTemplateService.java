package net.armcloud.paascenter.openapi.service;


import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.vo.AdiParseResultVO;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateVO;
import net.armcloud.paascenter.openapi.model.vo.RealPhoneTemplateVO;
import java.util.List;

public interface IRealPhoneTemplateService {

    List<RealPhoneTemplateVO> pageList(RealPhoneTemplateDTO realPhoneTemplateDTO);

    /**
     * 查询ADI模板列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<AdiTemplateVO> queryAdiTemplates(AdiTemplateQueryDTO queryDTO);

    /**
     * 获取ADI模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    AdiTemplateVO getAdiTemplateDetail(Long id);

    /**
     * 创建ADI模板
     *
     * @param createDTO 创建参数
     * @return 结果
     */
    Result<?> createAdiTemplate(AdiTemplateCreateDTO createDTO);

    /**
     * 创建客户自定义ADI模板
     *
     * @param createDTO 创建参数
     * @return 结果
     */
    Result<?> createCustomAdiTemplate(AdiCustomTemplateCreateDTO createDTO);

    /**
     * 更新ADI模板
     *
     * @param updateDTO 更新参数
     * @return 结果
     */
    Result<?> updateAdiTemplate(AdiTemplateUpdateDTO updateDTO);

    /**
     * 更新ADI模板状态
     *
     * @param statusDTO 状态参数
     * @return 结果
     */
    Result<?> updateAdiTemplateStatus(AdiTemplateStatusDTO statusDTO);

    /**
     * 更新ADI模板正式版标记
     *
     * @param officialDTO 正式版参数
     * @return 结果
     */
    Result<?> updateAdiTemplateOfficial(AdiTemplateOfficialDTO officialDTO);

    /**
     * 删除ADI模板
     *
     * @param id 模板ID
     * @return 结果
     */
    Result<?> deleteAdiTemplate(Long id);


    /**
     * 解析ADI文件
     * 
     * @param fileUrl OSS中的文件URL
     * @param id
     * @return 解析结果
     */
    Result<AdiParseResultVO> parseAdiFile(String fileUrl,String id);


    /**
     * 下拉列表查询
     * @param selectionDTO
     * @return 模板列表
     */
    List<AdiTemplateVO> listForSelection(AdiTemplateSelectionDTO selectionDTO);
}
