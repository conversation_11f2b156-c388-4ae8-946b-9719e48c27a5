package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.entity.NetStorageResUnitDeleted;
import org.apache.ibatis.annotations.Mapper;

/**
 * 网络存储单元删除记录Mapper
 * <AUTHOR>
 * @Date 2025/6/25
 * @Description: 网络存储单元删除记录操作mapper
 */
@Mapper
public interface NetStorageResUnitDeletedMapper extends BaseMapper<NetStorageResUnitDeleted> {

}
