package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.mapper.CbsFileVersionMapper;
import net.armcloud.paascenter.openapi.model.vo.CbsFileVersionVO;
import net.armcloud.paascenter.openapi.service.ICbsFileVersionService;
import net.armcloud.paascenter.common.model.entity.paas.CbsFileVersion;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * cbs文件版本管理
 */
@Slf4j
@Service
@RefreshScope
public class CbsFileVersionServiceImpl extends ServiceImpl<CbsFileVersionMapper, CbsFileVersion> implements ICbsFileVersionService {

    @Resource
    private RedisService redisService;

    @Value("${cbsFileDomain:}")
    private String cbsFileDomain;

    /**
     * 获取最新的cbs
     * @return
     */
    @Override
    public CbsFileVersionVO getLastCbs() {
        CbsFileVersionVO cbsFileVersionVO = null;
        String key = RedisKeyPrefix.CBS_LAST_FILE;
        Object cbsFileVersionObject = redisService.getCacheObject(key);
        if (ObjectUtil.isNotNull(cbsFileVersionObject)) {
            cbsFileVersionVO = JSON.parseObject(cbsFileVersionObject.toString(),CbsFileVersionVO.class);
        }else{
            CbsFileVersion cbsFileVersion = baseMapper.selectOne(new QueryWrapper<>(CbsFileVersion.class).select("file_url","version_title","version_num")
                    .eq("status",1)
                    .orderByDesc("version_num").orderByDesc("id").last("limit 1"));
            if(cbsFileVersion != null){
                cbsFileVersionVO = new CbsFileVersionVO();
                cbsFileVersionVO.setVersionName(cbsFileVersion.getVersionTitle());
                cbsFileVersionVO.setFileUrl(cbsFileVersion.getFileUrl());
                cbsFileVersionVO.setVersionNum(cbsFileVersion.getVersionNum());
                String cbsFileVersionVOJson = JSON.toJSONString(cbsFileVersionVO);
                //这里无需设置过期时间 这个数据更新频率极低 且由管理平台进行版本管理并维护缓存；就算缓存有问题也不会影响当前的cbs
                redisService.setCacheObject(key, cbsFileVersionVOJson);
            }
        }
        if(cbsFileVersionVO != null && StrUtil.isNotEmpty(cbsFileVersionVO.getFileUrl())){
            // 如果不是http或https开头，则添加cbsFileDomain
            String fileUrl = cbsFileVersionVO.getFileUrl();
            if (!fileUrl.startsWith("http://") && !fileUrl.startsWith("https://")) {
                cbsFileVersionVO.setFileUrl(cbsFileDomain + fileUrl);
            }
        }
        return cbsFileVersionVO;
    }
}
