package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.BaseDTO;

@Data
public class UploadImageFromUrlDTO extends BaseDTO {
    @ApiModelProperty("TAR文件下载地址")
    private String imageUrl;

    @ApiModelProperty("类型")
    private String serverType;

    @ApiModelProperty("romVersion")
    private String romVersion;

    @ApiModelProperty(value = "发版版本 1测试版 2正式版")
    private Integer releaseType;

    @ApiModelProperty("测试用例地址")
    private String testCaseFilePath;

    private Long customerId;

    @ApiModelProperty("镜像描述")
    private String imageDesc;

}

