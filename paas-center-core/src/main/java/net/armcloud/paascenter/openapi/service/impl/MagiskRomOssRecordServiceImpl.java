package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.MagiskRomOssRecordMapper;
import net.armcloud.paascenter.openapi.model.dto.MagiskRomOssRecordCreateDTO;
import net.armcloud.paascenter.openapi.model.dto.MagiskRomOssRecordQueryDTO;
import net.armcloud.paascenter.openapi.model.entity.MagiskRomOssRecord;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.model.vo.MagiskRomOssRecordVO;
import net.armcloud.paascenter.openapi.service.IMagiskRomOssRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * Magisk ROM OSS记录服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Service
public class MagiskRomOssRecordServiceImpl extends ServiceImpl<MagiskRomOssRecordMapper, MagiskRomOssRecord> 
        implements IMagiskRomOssRecordService {

    @Resource
    private MagiskRomOssRecordMapper magiskRomOssRecordMapper;

    @Resource
    private CustomerMapper customerMapper;

    @Override
    public Result<Void> createOrUpdate(MagiskRomOssRecordCreateDTO createDTO) {
        try {
            String customerName = getCustomerName();

            // 检查版本是否已存在
            MagiskRomOssRecord existingRecord = magiskRomOssRecordMapper.selectByVersion(createDTO.getVersion());

            if (existingRecord != null) {
                // 版本冲突，更新记录
                int updateCount = magiskRomOssRecordMapper.updateOssUrlAndReplaceTime(
                        createDTO.getVersion(), 
                        createDTO.getOssUrl(), 
                        customerName
                );
                
                if (updateCount > 0) {
                    log.info("版本冲突替换成功: version={}, oldOssUrl={}, newOssUrl={}, updateBy={}", 
                            createDTO.getVersion(), 
                            existingRecord.getOssUrl(), 
                            createDTO.getOssUrl(), 
                            customerName);
                } else {
                    log.error("版本冲突替换失败: version={}, ossUrl={}", 
                            createDTO.getVersion(), createDTO.getOssUrl());
                }
            } else {
                // 新增记录
                MagiskRomOssRecord newRecord = new MagiskRomOssRecord();
                newRecord.setOssUrl(createDTO.getOssUrl());
                newRecord.setVersion(createDTO.getVersion());
                newRecord.setCreateTime(new Date());
                newRecord.setCreateBy(customerName);

                magiskRomOssRecordMapper.insert(newRecord);
                log.info("新增Magisk ROM OSS记录成功: version={}, ossUrl={}, createBy={}", 
                        createDTO.getVersion(), createDTO.getOssUrl(), customerName);
            }

            return Result.ok();
        } catch (Exception e) {
            log.error("创建或更新Magisk ROM OSS记录失败", e);
            return Result.fail("操作失败: " + e.getMessage());
        }
    }

    private String getCustomerName() {
        // 获取当前用户信息
        String customerName = null;
        try {
            Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
            CustomerInfoVo customerInfo = customerMapper.getCustomerInfoById(customerId);
            customerName = customerInfo.getCustomerName();
        } catch (BasicException e) {
            if (Objects.equals(e.getExceptionCode(), BasicExceptionCode.MISSING_NECESSARY_CUSTOMER_INFORMATION)) {
                customerName = "no_auth";
            }
        }
        return customerName;
    }

    @Override
    public Result<MagiskRomOssRecordVO> queryRecord(MagiskRomOssRecordQueryDTO queryDTO) {
        try {
            MagiskRomOssRecord record;

            if (queryDTO.getVersion() == null || queryDTO.getVersion().trim().isEmpty()) {
                // 查询最新创建的记录
                record = magiskRomOssRecordMapper.selectLatest();
            } else {
                // 根据版本查询
                record = magiskRomOssRecordMapper.selectByVersion(queryDTO.getVersion());
            }

            if (record == null) {
                return Result.fail("未找到相关记录");
            }

            // 转换为VO
            MagiskRomOssRecordVO vo = new MagiskRomOssRecordVO();
            BeanUtils.copyProperties(record, vo);

            return Result.ok(vo);
        } catch (Exception e) {
            log.error("查询Magisk ROM OSS记录失败", e);
            return Result.fail("查询失败: " + e.getMessage());
        }
    }
}
