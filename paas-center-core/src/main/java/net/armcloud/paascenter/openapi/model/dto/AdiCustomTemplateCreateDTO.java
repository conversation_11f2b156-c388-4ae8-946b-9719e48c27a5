package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * ADI模板创建DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "ADI自定义模板创建对象", description = "用于创建新的ADI模板")
public class AdiCustomTemplateCreateDTO extends AdiTemplateCreateBaseDTO {


}