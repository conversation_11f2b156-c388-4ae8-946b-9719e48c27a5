package net.armcloud.paascenter.openapi.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CustomerDeviceMapper {
    int countNotExpiredByDeviceIdAndCustomerId(@Param("customerId") long customerId, @Param("deviceIds") List<Long> deviceIds);

    Long getCustomerIdByDeviceId(@Param("deviceId") Long deviceId);
}
