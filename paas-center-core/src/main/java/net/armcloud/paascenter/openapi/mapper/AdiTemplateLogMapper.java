package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.AdiTemplateLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ADI模板操作日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface AdiTemplateLogMapper extends BaseMapper<AdiTemplateLog> {

    /**
     * 查询ADI模板操作日志列表（带模板名称和操作者名称）
     *
     * @param params 查询参数
     * @return 日志列表
     */
    List<Map<String, Object>> selectAdiTemplateLogsWithNames(@Param("params") Map<String, Object> params);
    
    /**
     * 获取指定模板ID的最新操作日志
     *
     * @param templateId 模板ID
     * @return 最新日志
     */
    AdiTemplateLog selectLatestLogByTemplateId(@Param("templateId") Long templateId);
}
