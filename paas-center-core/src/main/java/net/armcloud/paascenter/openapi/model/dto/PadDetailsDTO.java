package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PadDetailsDTO implements Serializable {
    /**
     * 实例编号
     */
    @ApiModelProperty(value = "实例编号")
    private List<String> padCodes;

    @ApiModelProperty(value = "实例ip")
    private List<String> padIps;

    @ApiModelProperty(value = "实例在线状态：0-离线，1-在线")
    private String vmStatus;

    @ApiModelProperty(value = "受控状态（推流状态）：1-非受控，2-受控")
    private String controlStatus;

    @ApiModelProperty(value = "实例运行状态 14-异常 其他-正常")
    private String faultStatus;

    @ApiModelProperty(value = "物理机在线状态：0-离线，1-在线")
    private String deviceStatus;

    @ApiModelProperty(value = "实例状态：0-禁用，1-启用， -1 删除")
    private Integer status;

    @ApiModelProperty(value = "分组id")
    private Integer groupId;

    @ApiModelProperty(value = "机房编号")
    private String idcCode;

    @ApiModelProperty(value = "用户id")
    private Long customerId;

    @Min(value = 1, message = "offset最小值为1")
    @ApiModelProperty(value = "起始页,默认1")
    @NotNull(message = "page cannot null")
    private Integer page = 1;

    @Max(value = 100, message = "count最大值为100")
    @ApiModelProperty(value = "查询数量,默认10")
    @NotNull(message = "page cannot null")
    private Integer rows = 10;

}
