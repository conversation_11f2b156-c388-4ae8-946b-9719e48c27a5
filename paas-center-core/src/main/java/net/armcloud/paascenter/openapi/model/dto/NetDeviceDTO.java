package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/2/11 14:18
 * @Version 1.0
 */
@Data
public class NetDeviceDTO extends PageDTO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "查询")
    private String query;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;
    @ApiModelProperty(value = "ipv4 CIDR")
    @NotBlank(message = "ipv4 CIDR不能为空")
    @Pattern(
            regexp = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12]?[0-9])$",
            message = "IPv4 CIDR 格式错误"
    )
    private String ipv4Cidr;
    private String remarks;
}
