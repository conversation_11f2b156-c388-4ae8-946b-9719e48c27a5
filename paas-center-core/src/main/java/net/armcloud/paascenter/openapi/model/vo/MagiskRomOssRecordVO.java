package net.armcloud.paascenter.openapi.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Magisk ROM OSS记录响应VO
 *
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@ApiModel(value = "MagiskRomOssRecordVO", description = "Magisk ROM OSS记录响应")
public class MagiskRomOssRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    private Long id;

    /**
     * oss地址
     */
    @ApiModelProperty(value = "oss地址")
    private String ossUrl;

    /**
     * 包版本
     */
    @ApiModelProperty(value = "包版本")
    private String version;

    /**
     * 替换时间
     */
    @ApiModelProperty(value = "替换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replaceTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
}
