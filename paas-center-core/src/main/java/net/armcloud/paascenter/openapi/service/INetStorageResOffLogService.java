package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResOffLog;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface INetStorageResOffLogService extends IService<NetStorageResOffLog> {

    NetStorageResOffLog getByPadCode(String padCode);

    List<NetStorageResOffLog> getByPadCodeList(List<String> padCodes);
}
