package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ADI模板创建DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板创建对象", description = "用于创建新的ADI模板")
public class AdiTemplateCreateBaseDTO {


    @ApiModelProperty(value = "设备品牌", required = true, example = "HUAWEI")
    @NotBlank(message = "设备品牌不能为空")
    private String brand;

    @ApiModelProperty(value = "设备型号", required = true, example = "ANA-AN00")
    @NotBlank(message = "设备型号不能为空")
    private String model;

    @ApiModelProperty(value = "机型名称", example = "P40-5G")
    private String deviceName;

    @ApiModelProperty(value = "系统指纹", required = true, example = "HUAWEI/ANA-AN00/HWANA:11/...")
    @NotBlank(message = "系统指纹不能为空")
    private String fingerprint;

    @ApiModelProperty(value = "指纹MD5", required = true, example = "d41d8cd98f00b204e9800998ecf8427e")
    @NotBlank(message = "指纹MD5不能为空")
    private String fingerprintMd5;

    @ApiModelProperty(value = "Android版本", example = "11")
    private Integer androidImageVersion;

    @ApiModelProperty(value = "屏幕布局编码", example = "realdevice_1080x2340x480")
    @NotBlank(message = "屏幕布局编码不能为空")
    private String screenLayoutCode;

    @ApiModelProperty(value = "规格编码", example = "m2-3")
    // @NotBlank(message = "规格编码不能为空")
    private String resourceSpecificationCode;

    @ApiModelProperty(value = "ADI模板压缩包密码", example = "abcdef")
    private String adiPassword;

    @ApiModelProperty(value = "是否正式版(0:测试版 1:正式版)", required = true, example = "0")
    @NotNull(message = "是否正式版不能为空")
    private Integer isOfficial;

    @ApiModelProperty(value = "屏幕宽度(像素)")
    private String screenWidth;

    @ApiModelProperty(value = "屏幕高度(像素)")
    private String screenHeight;

    @ApiModelProperty(value = "屏幕密度")
    private String screenDensity;

    @ApiModelProperty(value = "机型标识")
    private String modelCode;

    @ApiModelProperty(value = "adi模板版本")
    private String adiTemplateVersion;

    @ApiModelProperty(value = "文件下载地址")
//    @NotBlank(message = "文件下载地址不能为空")
    private String publicUrl;

    @ApiModelProperty(value = "测试用例文件下载地址")
    private String testCasesDownloadUrl;

    /**
     * AOSP版本
     */
    @ApiModelProperty(value = "AOSP版本")
    private String aospVersion;

    @ApiModelProperty(value = "adi文件解压密码")
    @NotBlank(message = "adiTemplatePwd cannot null")
    private String adiTemplatePwd;

    @ApiModelProperty(value = "实例属性(JSON格式)")
    private String property;
}