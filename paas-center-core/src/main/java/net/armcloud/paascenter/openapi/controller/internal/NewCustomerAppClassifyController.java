package net.armcloud.paascenter.openapi.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.QueryNewAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.facade.CustomerNewAppClassifyFacade;
import net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.service.INewAppClassifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 应用分类
 */
@RestController
public class NewCustomerAppClassifyController implements CustomerNewAppClassifyFacade {

    @Resource
    private INewAppClassifyService newAppClassifyService;

    @Override
    public Result<List<NewAppClassifyNameVO>> queryAppNewClassifyName(QueryNewAppClassifyNameDTO dto) {
        return Result.ok(newAppClassifyService.appListByAppIds(dto));
    }
    
}
