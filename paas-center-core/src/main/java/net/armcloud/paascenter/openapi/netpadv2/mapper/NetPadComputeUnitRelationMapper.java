package net.armcloud.paascenter.openapi.netpadv2.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadComputeUnitRelationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实例与算力单元关联关系Mapper
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Mapper
public interface NetPadComputeUnitRelationMapper extends BaseMapper<NetPadComputeUnitRelationDO> {

    /**
     * 根据实例编码查询算力关联关系
     */
    NetPadComputeUnitRelationDO selectByPadCode(@Param("padCode") String padCode);

    /**
     * 根据实例编码列表查询算力关联关系
     */
    List<NetPadComputeUnitRelationDO> selectByPadCodes(@Param("padCodes") List<String> padCodes);

    /**
     * 批量插入关联关系
     */
    int batchInsert(@Param("relations") List<NetPadComputeUnitRelationDO> relations);

    /**
     * 删除关联关系
     */
    int deleteByPadCode(@Param("padCode") String padCode);

    /**
     * 批量删除关联关系
     */
    int deleteByPadCodes(@Param("padCodes") List<String> padCodes);

}
