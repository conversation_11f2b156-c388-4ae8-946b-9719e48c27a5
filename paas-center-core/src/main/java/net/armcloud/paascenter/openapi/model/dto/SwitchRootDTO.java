package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Getter
@Setter
public class SwitchRootDTO extends BaseDTO {

    @ApiModelProperty(value = "实例列表", required = true)
    @NotNull(message = "padCodes cannot null")
    @Size(min = 1,max=500, message = "实例数量不少于于1个")
    private List<String> padCodes;

    /**
     * 是否开启全局root权限，默认不开启
     */
    private Boolean globalRoot = false;

    /**
     * 应用包名
     */
    private String packageName;

    /**
     * root状态，0：关闭 1：开启
     */
    @ApiModelProperty(value = "root开启状态", required = true)
    @NotNull(message = "rootStatus cannot null")
    private Integer rootStatus;
}
