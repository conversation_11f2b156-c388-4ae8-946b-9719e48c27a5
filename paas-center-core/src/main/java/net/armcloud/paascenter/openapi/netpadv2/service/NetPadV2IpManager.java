package net.armcloud.paascenter.openapi.netpadv2.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.mapper.NetPadMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.service.netstorage.impl.NetStorageComputeUnitServiceImpl;
import net.armcloud.paascenter.openapi.utils.CIDRUtils;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;



/**
 * <AUTHOR>
 * @date 2025/07/06
 * @description 网存实例V2IP管理服务
 */
@Slf4j
@Service
public class NetPadV2IpManager {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private NetPadMapper netPadMapper;

    @Resource
    private PadMapper padMapper;

    @Resource
    private RedissonDistributedLock redissonDistributedLock;

    @Resource
    private DeviceMapper deviceMapper;

    // 白名单
    private final static List<Integer> ipSubNumWhiteList = new ArrayList<>();

    @Value("#{'${ip.sub.num.whitelist:}'.split(',')}")
    private List<String> ipSubNumStrings;

    // 可用IP key前缀
    private final static String availableIpKey = "netpadv2:ip:available";

    // 已使用IP key前缀（按deviceIp维度存储）
    private final static String usedIpByDeviceKey = "netpadv2:ip:used:device";

    // 随机数生成器，用于生成随机过期时间
    private final static Random random = new Random();

    @PostConstruct
    private void postConstruct() {
        for (String ipSubNumString : ipSubNumStrings) {
            try {
                if (StrUtil.isBlank(ipSubNumString)) {
                    continue;
                }
                int parsedNum = Integer.parseInt(ipSubNumString);
                if (parsedNum >= 0) {
                    ipSubNumWhiteList.add(parsedNum);
                }
            } catch (Exception exception) {
                log.error("ipSubNumWhiteList parse error", exception);
            }
        }
    }

    /**
     * 添加可用IP
     *
     * @param ipList      IP地址集合
     * @param armServerId 服务器编码
     */
    public void addAvailableIp(List<String> ipList, Long armServerId) {
        String availableIpCacheKey = getAvailableIpCacheKey(armServerId);
        ipList.forEach(ip -> {
            redisTemplate.opsForSet().add(availableIpCacheKey, ip);
        });
        redisTemplate.expire(availableIpCacheKey, 2 * 60 * 60, TimeUnit.SECONDS);
    }
    
    
    /**
     * 删除可用IP
     *
     * @param ip          IP地址
     * @param armServerId 服务器编码
     */
    public void removeAvailableIp(String ip, Long armServerId) {
        // 从redis中删除
        String availableIpCacheKey = getAvailableIpCacheKey(armServerId);
        redisTemplate.opsForSet().remove(availableIpCacheKey, ip);
    }

    @NotNull
    private static String getAvailableIpCacheKey(Long armServerId) {
        return RedisKeyUtils.cacheKey(availableIpKey, String.valueOf(armServerId));
    }

    @NotNull
    private static String getUsedIpByDeviceCacheKey(String deviceIp) {
        return RedisKeyUtils.cacheKey(usedIpByDeviceKey, deviceIp);
    }

    /**
     * 生成随机过期时间，防止缓存雪崩
     * 基础时间 + 随机时间（0-30分钟）
     *
     * @param baseSeconds 基础过期时间（秒）
     * @return 随机过期时间（秒）
     */
    private static long getRandomExpireTime(long baseSeconds) {
        // 添加0-30分钟的随机时间
        long randomSeconds = random.nextInt(30 * 60);
        return baseSeconds + randomSeconds;
    }

    /**
     * 获取可用IP
     *
     * @param armServerId 服务器编码
     * @return 可用IP
     */
    public String getAvailableIp(Long armServerId) {
        log.info("getAvailableIp start,armServerId:{}", armServerId);
        // 获取IP
        String availableIp = getIpCache(armServerId);
        if (StrUtil.isNotBlank(availableIp)) {
            return availableIp;
        }
        String availableIpLockKey = RedisKeyUtils.lockKey(NetPadV2IpManager.availableIpKey, String.valueOf(armServerId));
        RLock lock = redissonDistributedLock.tryLock(availableIpLockKey, 10, 30);
        if (lock == null) {
            log.warn("获取可用IP锁失败,armServerId:{}", armServerId);
            throw new BasicException(PadExceptionCode.GET_PAD_IP_EXCEPTION);
        }
        try {
            // 获取IP
            availableIp = getIpCache(armServerId);
            if (StrUtil.isNotBlank(availableIp)) {
                return availableIp;
            }
            // 缓存中没有，从数据库获取
            List<String> ipList = getIpList(armServerId);
            if (CollectionUtil.isEmpty(ipList)) {
                log.info("未查询到可用IP,armServerId:{}", armServerId);
                // 空值标记
                emptyFlag(armServerId);
                throw new BasicException(PadExceptionCode.PAD_IP_NOT_ENOUGH);
            }
            addAvailableIp(ipList, armServerId);
            availableIp = getIpCache(armServerId);
            if (StrUtil.isNotBlank(availableIp)) {
                return availableIp;
            }
        } finally {
            lock.unlock();
        }
        log.info("无可用IP,armServerId:{}", armServerId);
        throw new BasicException(PadExceptionCode.PAD_IP_NOT_ENOUGH);
    }

    private String getIpCache(Long armServerId) {
        // 判断空值标记
        if (isEmptyFlag(armServerId)) {
            log.info("命中空值标记,armServerId:{}", armServerId);
            throw new BasicException(PadExceptionCode.PAD_IP_NOT_ENOUGH);
        }
        // 先从缓存获取
        String availableIpCacheKey = getAvailableIpCacheKey(armServerId);
        return redisTemplate.opsForSet().pop(availableIpCacheKey);
    }

    private void emptyFlag(Long armServerId) {
        String emptyCacheKey = getEmptyFlagKey(armServerId);
        redisTemplate.opsForValue().set(emptyCacheKey, "1", 5, TimeUnit.SECONDS);
    }

    private boolean isEmptyFlag(Long armServerId) {
        String emptyCacheKey = getEmptyFlagKey(armServerId);
        return redisTemplate.hasKey(emptyCacheKey);
    }

    private static String getEmptyFlagKey(Long armServerId) {
        return RedisKeyUtils.cacheKey(availableIpKey, "empty", String.valueOf(armServerId));
    }

    private List<String> getIpList(Long armServerId) {
        // 获取服务器绑定的全部网段
        List<String> cidrList = netPadMapper.selectIpv4CidrsByArmServer(armServerId);
        if (CollectionUtil.isEmpty(cidrList)) {
            throw new BasicException(PadExceptionCode.ARM_SERVER_IP_RANGE_NOT_EXIST);
        }
        // 获取全部网段的IP地址
        List<String> ipList = cidrList.stream()
                .flatMap(cidr ->
                        Objects.requireNonNull(CIDRUtils.getIPAddressesFromCIDR(cidr, ipSubNumWhiteList)).stream()
                )
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ipList)) {
            throw new BasicException(PadExceptionCode.GET_PAD_IP_EXCEPTION);
        }

        // 从Redis中获取recordUsedIp记录的已使用IP（需要聚合该armServerId下所有deviceIp的IP）
        Set<String> allRecordedUsedIps = getAllRecordedUsedIpsByArmServerId(armServerId);
        if (!CollectionUtil.isEmpty(allRecordedUsedIps)) {
            log.info("从Redis中获取到已使用IP, armServerId:{}, IP数量:{}", armServerId, allRecordedUsedIps.size());
            ipList.removeAll(allRecordedUsedIps);
        }

        // 从pad表过滤掉已使用的IP
        List<String> usedIpList = padMapper.selectUsePadIpsByArmServer(armServerId);
        ipList.removeAll(usedIpList);

        // 从算力表过滤已使用IP
        List<String> usedIpListByArmServerId = SpringUtil.getBean(NetStorageComputeUnitServiceImpl.class).getUsedIpListByArmServerId(armServerId);
        ipList.removeAll(usedIpListByArmServerId);

        if (CollectionUtil.isEmpty(ipList)) {
            throw new BasicException(PadExceptionCode.PAD_IP_NOT_ENOUGH);
        }
        return ipList;
    }

    /**
     * 记录已使用的IP(CBS通过接口报上来的每张板卡上面的全部实例IP)
     * 高性能版本：使用纯Redis缓存，避免数据库查询
     *
     * @param deviceIp 服务器IP
     * @param ipList   IP地址集合
     */
    public void recordUsedIp(String deviceIp, List<String> ipList) {
        log.info("recordUsedIp start, deviceIp:{}, ipList size:{}", deviceIp,
                CollectionUtil.isEmpty(ipList) ? 0 : ipList.size());

        try {
            // 1. 生成Redis key（按deviceIp维度存储）
            String usedIpByDeviceCacheKey = getUsedIpByDeviceCacheKey(deviceIp);

            // 2. 使用Redis原子操作更新已使用IP集合
            if (CollectionUtil.isEmpty(ipList)) {
                // 当IP列表为空时，清除该deviceIp下记录的IP
                log.info("IP列表为空，清除deviceIp下已记录的IP, deviceIp:{}", deviceIp);
                redisTemplate.delete(usedIpByDeviceCacheKey);
            } else {

                // 批量写入临时key，提高性能
                String[] ipArray = ipList.toArray(new String[0]);
                redisTemplate.opsForSet().add(usedIpByDeviceCacheKey, ipArray);

                // 设置正式key过期时间，使用随机过期时间
                long randomExpireTime = getRandomExpireTime(60 * 60);
                redisTemplate.expire(usedIpByDeviceCacheKey, randomExpireTime, TimeUnit.SECONDS);

                log.info("记录已使用IP成功, deviceIp:{}, IP数量:{}", deviceIp, ipList.size());
            }
        } catch (Exception e) {
            log.error("recordUsedIp异常, deviceIp:{}", deviceIp, e);
        }
    }

    /**
     * 获取指定armServerId下所有deviceIp记录的已使用IP
     * 需要查询该armServerId下的所有deviceIp，然后聚合它们的已使用IP
     *
     * @param armServerId ARM服务器ID
     * @return 所有已使用的IP集合
     */
    private Set<String> getAllRecordedUsedIpsByArmServerId(Long armServerId) {
        Set<String> allUsedIps = new HashSet<>();

        try {
            // 1. 获取该armServerId下的所有deviceIp
            List<String> deviceIps = getDeviceIpsByArmServerId(armServerId);
            if (CollectionUtil.isEmpty(deviceIps)) {
                return allUsedIps;
            }

            // 2. 遍历每个deviceIp，获取其记录的已使用IP
            for (String deviceIp : deviceIps) {
                String usedIpByDeviceCacheKey = getUsedIpByDeviceCacheKey(deviceIp);
                Set<String> deviceUsedIps = redisTemplate.opsForSet().members(usedIpByDeviceCacheKey);
                if (!CollectionUtil.isEmpty(deviceUsedIps)) {
                    allUsedIps.addAll(deviceUsedIps);
                }
            }

            log.debug("聚合armServerId:{}下所有deviceIp的已使用IP, deviceIp数量:{}, 总IP数量:{}",
                    armServerId, deviceIps.size(), allUsedIps.size());

        } catch (Exception e) {
            log.error("获取armServerId:{}下所有已使用IP异常", armServerId, e);
        }

        return allUsedIps;
    }

    /**
     * 获取指定armServerId下的所有deviceIp列表
     * 使用缓存优化，避免频繁查询数据库
     *
     * @param armServerId ARM服务器ID
     * @return deviceIp列表
     */
    private List<String> getDeviceIpsByArmServerId(Long armServerId) {
        String deviceIpsKey = RedisKeyUtils.cacheKey("netpadv2:armserver:deviceips", String.valueOf(armServerId));

        // 先尝试从缓存获取
        Set<String> cachedDeviceIps = redisTemplate.opsForSet().members(deviceIpsKey);
        if (!CollectionUtil.isEmpty(cachedDeviceIps)) {
            return new ArrayList<>(cachedDeviceIps);
        }

        // 缓存未命中，查询数据库
        try {
            List<String> deviceIps = deviceMapper.getDeviceIpsByArmServerId(armServerId);
            if (!CollectionUtil.isEmpty(deviceIps)) {
                // 缓存查询结果，使用随机过期时间
                String[] deviceIpArray = deviceIps.stream().distinct().toArray(String[]::new);
                redisTemplate.opsForSet().add(deviceIpsKey, deviceIpArray);
                long randomExpireTime = getRandomExpireTime(30 * 60); // 基础30分钟 + 随机时间
                redisTemplate.expire(deviceIpsKey, randomExpireTime, TimeUnit.SECONDS);

                log.info("缓存armServerId:{}下的deviceIp列表, 数量:{}, 过期时间:{}s",
                        armServerId, deviceIps.size(), randomExpireTime);
            }
            return deviceIps;
        } catch (Exception e) {
            log.error("查询armServerId:{}下的deviceIp列表异常", armServerId, e);
            return new ArrayList<>();
        }
    }
}
