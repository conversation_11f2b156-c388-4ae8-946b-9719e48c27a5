package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RealPhoneTemplateDTO {

    @ApiModelProperty(value = "页码")
    private Integer pageIndex;

    @ApiModelProperty(value = "获取数量")
    private Integer pageSize;


    @ApiModelProperty(value = "规格编号")
    private String resourceSpecificationCode;

    @ApiModelProperty(value = "安卓镜像版本")
    private String androidImageVersion;
    public void checkParam(){
        if(pageIndex == null || pageIndex <= 0){
            pageIndex = 1;
        }
        if(pageSize == null || pageSize <= 0 || pageSize > 100){
            pageSize = 10;
        }
    }
}
