package net.armcloud.paascenter.openapi.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.net.InetAddress;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CIDRUtils {

    /**
     * 根据 CIDR 生成 IP 地址集合
     *
     * @param cidr CIDR 格式的地址，例如 "**********/24"
     * @return IP 地址的集合，如果发生异常则返回 null
     */
    public static Set<String> getIPAddressesFromCIDR(String cidr) {
        return getIPAddressesFromCIDR(cidr,Collections.emptyList());
    }


    public static Set<String> getIPAddressesFromCIDR(String cidr, List<Integer> subNetIpWhiteList) {
        try {
            String[] parts = cidr.split("/");
            String ip = parts[0];

            Integer subIpNum = getSubNetNum(ip);
            if (subIpNum == null) {
                log.error("getSubNetNum cidr error, ip:{}", ip);
                return null;
            }
            int prefixLength = Integer.parseInt(parts[1]);
            InetAddress inetAddress = InetAddress.getByName(ip);
            List<String> ipAddresses = getIPAddress(inetAddress, prefixLength);
            Set<String> ipAddressesFinal = new HashSet<>();
            if (!ipAddresses.isEmpty()) {
                for (String ipAddress : ipAddresses) {
                    if(StringUtils.isBlank(ipAddress)){
                        continue;
                    }
                    Integer subNetNum = getSubNetNum(ipAddress);
                    if (subNetNum == null) {
                        log.error("getSubNetNum ip error, ip:{}", ipAddress);
                        continue;
                    }
                    if (subNetNum >= subIpNum && !ipAddress.endsWith(".0")
                            && !ipAddress.endsWith(".255")) {
                        if (!subNetIpWhiteList.isEmpty() && subNetIpWhiteList.contains(subNetNum)) {
                            ipAddressesFinal.add(ipAddress);
                        } else if(subNetIpWhiteList.isEmpty()) {
                            ipAddressesFinal.add(ipAddress);
                        } else {
                            log.info("getIPAddressesFromCIDR subNetIpWhiteList not contains subNetNum, ip:{}, subNetNum:{}", ipAddress, subNetNum);
                        }
                    }
                }
            }

            return ipAddressesFinal;
        } catch (Exception e) {
            // 捕获所有异常并返回 null
            log.error("getIPAddressesFromCIDR error", e);
            return null;
        }
    }

    private static List<String> getIPAddress(InetAddress inetAddress, int prefixLength) {
        byte[] addressBytes = inetAddress.getAddress();

        int mask = 0xffffffff << (32 - prefixLength);
        int baseAddress = toInt(addressBytes) & mask;

        List<String> ipAddresses = new ArrayList<>();
        int numberOfAddresses = (prefixLength == 32) ? 1 : (1 << (32 - prefixLength));

        for (int i = 1; i < numberOfAddresses - 1; i++) {
            int currentAddress = baseAddress + i;
            ipAddresses.add(toIPString(currentAddress));
        }
        return ipAddresses;
    }

    private static Integer getSubNetNum(String ip) {
        // get the third  ip str
        String[] splitIps = ip.split("\\.");
        if (splitIps.length == 4) {
            String ipSubNetStartStr = splitIps[2];
            try {
                return Integer.parseInt(ipSubNetStartStr);
            } catch (Exception e) {
                // nothing
                log.error("getSubNetNum error", e);
            }
        }
        return null;
    }

    /**
     * 将字节数组形式的 IP 地址转换为整数
     *
     * @param address 字节数组形式的 IP 地址
     * @return 整数形式的 IP 地址
     */
    private static int toInt(byte[] address) {
        int result = 0;
        for (byte b : address) {
            result = (result << 8) | (b & 0xFF);
        }
        return result;
    }

    /**
     * 将整数形式的 IP 地址转换为点分十进制字符串
     *
     * @param address 整数形式的 IP 地址
     * @return 点分十进制形式的 IP 地址字符串
     */
    private static String toIPString(int address) {
        return ((address >> 24) & 0xFF) + "." +
                ((address >> 16) & 0xFF) + "." +
                ((address >> 8) & 0xFF) + "." +
                (address & 0xFF);
    }

    /**
     * 将点分十进制表示的 IP 地址转换为整数
     *
     * @param ipAddress 点分十进制表示的 IP 地址
     * @return 整数表示的 IP 地址
     */
    public static int ipToInt(String ipAddress) {
        String[] octets = ipAddress.split("\\.");
        int result = 0;
        for (String octet : octets) {
            result = (result << 8) | Integer.parseInt(octet);
        }
        return result;
    }

    /**
     * 对 IP 地址集合进行排序
     *
     * @param ips IP 地址集合
     * @return 排序后的 IP 地址集合
     */
    public static List<String> sortIPs(List<String> ips) {
        return ips.stream()
                .sorted(Comparator.comparingInt(CIDRUtils::ipToInt))
                .collect(Collectors.toList());
    }
//
//    public static void main(String[] args) {
//        List<String> ipAddressesFromCIDR = getIPAddressesFromCIDR("************/24");
//        for (String s : ipAddressesFromCIDR) {
//            System.out.print(s + " ");
//        }
//        System.out.println(ipAddressesFromCIDR.size());
//    }

}