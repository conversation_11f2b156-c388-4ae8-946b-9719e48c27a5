package net.armcloud.paascenter.openapi.utils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

import com.alibaba.fastjson.JSONObject;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.model.entity.paas.CountryInfo;
import net.armcloud.paascenter.common.model.entity.paas.OpenCellTower;
import net.armcloud.paascenter.common.model.entity.paas.SimInfo;
import net.armcloud.paascenter.openapi.mapper.CountryInfoMapper;
import net.armcloud.paascenter.openapi.mapper.OpenCellTowerMapper;
import net.armcloud.paascenter.openapi.mapper.SimInfoMapper;
import net.armcloud.paascenter.openapi.service.impl.NumberUtil;

@Slf4j
public class AndroidDeviceInfoUtils {

    private static final ConcurrentHashMap<Integer, List<Integer>> idCache = new ConcurrentHashMap<>();


    public static boolean ramdomSimAndGPSInfo(String countryCode, JSONObject props) {
        // 随机一个sim卡信息
        SimInfoMapper simInfoMapper = SpringUtil.getBean(SimInfoMapper.class);
        SimInfo simInfo = simInfoMapper.selectRandomSimInfo(countryCode);

        if (null == simInfo) {
            countryCode = "SG";
            simInfo = simInfoMapper.selectRandomSimInfo(countryCode);
        }
        if (null == simInfo) {
            return false;
        }

        String mcc = simInfo.getMcc() + "";
        String mnc = simInfo.getMnc() + "";
        String phoneCode = simInfo.getPhoneCode();
        String phoneRegex = simInfo.getPhoneRegex();
        // String imsiRegex = simInfo.getImsiRegex();
        String iccidRegex = simInfo.getIccidRegex();
        // String imeiRegex = simInfo.getImeiRegex();

        // 生成手机号
        props.put("persist.sys.cloud.phonenum", SimUtils.generatePhoneNumber(phoneRegex, phoneCode));

        log.info("ramdomSimAndGPSInfo countryCode:{},phoneCode:{},phoneRegex:{},phonenum:{}",countryCode,phoneCode,phoneRegex,props.get("persist.sys.cloud.phonenum"));

        // 生成imei
        props.put("persist.sys.cloud.imeinum", NumberUtil.generateValue("\\d{15}"));

        // 生成iccid
        props.put("persist.sys.cloud.iccidnum", SimUtils.generateICCID(iccidRegex, phoneCode));

        // 生成imsi
        props.put("persist.sys.cloud.imsinum", SimUtils.generateIMSI(mcc, mnc));

        // 随机一个基站信息
        OpenCellTowerMapper openCellTowerMapper = SpringUtil.getBean(OpenCellTowerMapper.class);
//        OpenCellTower openCellTower = openCellTowerMapper.selectRandomOpenCellTower(simInfo.getMcc());
        Integer openCellTowersId = getRandomId(simInfo.getMcc());
        OpenCellTower openCellTower = openCellTowerMapper.queryById(openCellTowersId);
        log.info("ramdomSimAndGPSInfo openCellTower:{}", openCellTower);
        if (null != openCellTower) {
            props.put("persist.sys.cloud.gps.lat", String.valueOf(openCellTower.getLat()));
            props.put("persist.sys.cloud.gps.lon", String.valueOf(openCellTower.getLon()));

            Integer area = openCellTower.getArea();
            Long cellid = openCellTower.getCell();
            Integer unit = openCellTower.getUnit();
            Integer range = openCellTower.getRange();
            Integer samples = openCellTower.getSamples();

            // 如果mnc小于2位，则补齐2位
            if (mnc.length() < 2) {
                mnc = "0" + mnc;
            }

            props.put("persist.sys.cloud.mobileinfo", mcc + "," + mnc);
            // type,mcc,mnc,tac(16进制),cellid(16进制),narfcn(16进制),pci(16进制)
            props.put("persist.sys.cloud.cellinfo", "9," + mcc + "," + mnc + ","
                    + Integer.toHexString(area).toUpperCase() + "," + Long.toHexString(cellid).toUpperCase()
                    + "," + Integer.toHexString(range).toUpperCase() + ","
                    + Integer.toHexString(samples).toUpperCase());

            // 语言信息暂不设置
            // props.put("persist.sys.language", languageMap.get("language"));
        } else {
            // 如果基站信息为空，获取国家信息，然后随机一个经纬度
            CountryInfoMapper countryInfoMapper = SpringUtil.getBean(CountryInfoMapper.class);
            CountryInfo countryInfo = countryInfoMapper.selectCountryInfoByCountryCode(countryCode);
            Random random = new Random();
            if (null != countryInfo) {
                double latMin = countryInfo.getSouth();
                double latMax = countryInfo.getNorth();
                double lonMin = countryInfo.getWest();
                double lonMax = countryInfo.getEast();
                
                double lat = latMin + (latMax - latMin) * random.nextDouble();
                double lon = lonMin + (lonMax - lonMin) * random.nextDouble();
                props.put("persist.sys.cloud.gps.lat", String.valueOf(lat));
                props.put("persist.sys.cloud.gps.lon", String.valueOf(lon));
            } else {
                String loc = NumberUtil.getRandomSingaporeCoordinates();
                String[] parts = loc.split(",");
                props.put("persist.sys.cloud.gps.lat", String.valueOf(parts[0]));
                props.put("persist.sys.cloud.gps.lon", String.valueOf(parts[1]));
            }
            props.put("persist.sys.cloud.gps.speed", "0." + (random.nextInt(8) + 1));
            props.put("persist.sys.cloud.gps.altitude", String.valueOf(random.nextInt(19) + 1));
            props.put("persist.sys.cloud.gps.bearing", String.valueOf(random.nextInt(90) + 1));

            props.put("persist.sys.cloud.mobileinfo", mcc + "," + mnc);
            // type,mcc,mnc,tac(16进制),cellid(16进制),narfcn(16进制),pci(16进制)
            props.put("persist.sys.cloud.cellinfo", "9," + mcc + "," + mnc
                    + "," + NumberUtil.decimalToHex(NumberUtil.generateValue("\\d{6}"))
                    + "," + NumberUtil.decimalToHex(NumberUtil.generateValue("\\d{9}"))
                    + "," + NumberUtil.decimalToHex(NumberUtil.generateValue("\\d{5}"))
                    + "," + NumberUtil.decimalToHex(NumberUtil.generateValue("\\d{2}")));
        }
        props.put("persist.sys.country", countryCode);
        return true;
    }

    // 获取随机ID
    public static Integer getRandomId(Integer mcc) {
        // 从缓存中获取随机 ID
        List<Integer> ids = idCache.get(mcc);
        if (ids == null || ids.isEmpty()) {
            // 如果缓存中没有相关 ID，则从数据库查询并缓存
            OpenCellTowerMapper openCellTowerMapper = SpringUtil.getBean(OpenCellTowerMapper.class);
            ids = openCellTowerMapper.selectIdsByMcc(mcc);
            loadIdsToCache(ids, mcc);
        }
        // 从缓存中获取随机 ID
        return getRandomFromList(ids);
    }

    // 从列表中获取随机 ID
    private static Integer getRandomFromList(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return null;
        }
        // 使用成员变量 random 来生成随机数
        int randomIndex = new Random().nextInt(ids.size());
        return ids.get(randomIndex);
    }

    // 加载 ID 到缓存
    private static void loadIdsToCache(List<Integer> ids, Integer mcc) {
        idCache.put(mcc, ids);
    }

    /**
     * 随机生成wifi信息
     * 
     * @param countryCode
     * @param props
     * @return
     */
    public static boolean ramdomWifiInfo(String countryCode, JSONObject props) {
        props.put("persist.sys.cloud.wifi.mac", AnddroidPropUtils.generateRandomBluetoothMac());

        // 随机一个ip段
        String ip_prefix = "192.168.";
        Random random = new Random();
        int ip_seg = random.nextInt(254);

        String gateway = ip_prefix + ip_seg + ".1";
        String ip = ip_prefix + ip_seg + "." + (random.nextInt(252) + 2);
        // 常见的海外dns
        String[] public_dns_array = { "*******", "*******" };
        String dns = public_dns_array[random.nextInt(public_dns_array.length)];

        // 随机一下90的概率使用内网的dns, 也就是等于网关
        if (random.nextInt(10) < 9) {
            dns = gateway;
        }

        props.put("persist.sys.cloud.wifi.ip", ip);
        props.put("persist.sys.cloud.wifi.gateway", gateway);
        props.put("persist.sys.cloud.wifi.dns1", dns);
        return true;
    }

    /**
     * 随机生成蓝牙信息
     * 
     * @param countryCode
     * @param props
     * @return
     */
    public static boolean ramdomBluetoothInfo(String countryCode, JSONObject props) {
        props.put("ro.bt.devicename", AnddroidPropUtils.generateRandomBluetoothName());
        props.put("persist.sys.cloud.bluetoothaddr", AnddroidPropUtils.generateRandomBluetoothMac());
        return true;
    }

    // 云真机需要特别处理以下两个属性，这两个属性的值应该最近过去的三个月，随机一个月份，日期为5日，比如现在是6月，那就是3,4,5月随机一个月份，2025-03-05,2025-04-05,2025-05-05
    // ro.build.version.security_patch
    // ro.vendor.build.security_patch
    // 生成最近三个月的5号日期
    public static boolean ramdomSecurityPatch(JSONObject props) {
        String randomPatchDate = getRandomPatchDate();
        props.put("ro.build.version.security_patch", randomPatchDate);
        props.put("ro.vendor.build.security_patch", randomPatchDate);

        return true;
    }

    public static String getRandomPatchDate() {
        List<String> lastThreeMonths = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        int currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH是0-11
        for (int i = 3; i >= 1; i--) {
            int month = currentMonth - i;
            int year = currentYear;
            if (month <= 0) {
                month += 12;
                year -= 1;
            }
            String monthStr = month < 10 ? "0" + month : String.valueOf(month);
            lastThreeMonths.add(year + "-" + monthStr + "-05");
        }
        // 随机选一个
        String randomPatchDate = lastThreeMonths.get(new Random().nextInt(lastThreeMonths.size()));
        return randomPatchDate;
    }


    /**
     * 随机生成电池信息
     * 
     * @param countryCode
     * @param props
     * @return
     */
    public static boolean ramdomBatteryInfo(String countryCode, JSONObject props) {
        int[] capacities = { 5000, 6000, 8000 };
        props.put("persist.sys.cloud.battery.capacity",
                String.valueOf(capacities[new Random().nextInt(capacities.length)]));
        props.put("persist.sys.cloud.battery.level", String.valueOf(50 + new Random().nextInt(39)));
        return true;
    }


}
