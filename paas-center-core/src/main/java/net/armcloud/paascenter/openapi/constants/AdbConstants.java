package net.armcloud.paascenter.openapi.constants;

import org.assertj.core.util.Lists;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * adb执行命令
 *
 */
public final class AdbConstants {

    // 私有化构造方法，防止实例化
    private AdbConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated ");
    }

    // 全局root开关
    public static final String SWITCH_GLOBAL_ROOT = "setprop persist.sys.cloud.root.global {}";
    // 单个应用root开关
    public static final String SWITCH_SIGNE_ROOT =
            "PACKAGE_NAMES=\"{PACKAGE_NAME}\"\n" +
                    "ROOT_STATUS={ROOT_STATUS} \n" +
                    "\n" +
                    "for pkg in $(echo $PACKAGE_NAMES | tr ',' ' '); do\n" +
                    "  UID=$(cmd package list packages -U | grep \"package:$pkg\" | awk -F \"uid:\" '{print $2}' | tr -d '[:space:]')\n" +
                    "\n" +
                    "  if [ -n \"$UID\" ]; then\n" +
                    "    if [ \"$ROOT_STATUS\" -eq 1 ]; then\n" +
                    "      setprop persist.sys.cloud.root.$UID 1\n" +
                    "      echo \"Root access enabled for UID: $UID ($pkg)\"\n" +
                    "    else\n" +
                    "      setprop persist.sys.cloud.root.$UID 0\n" +
                    "      echo \"Root access disabled for UID: $UID ($pkg)\"\n" +
                    "    fi\n" +
                    "  else\n" +
                    "    echo \"Package $pkg not found\"\n" +
                    "  fi\n" +
                    "done";
    /**
     * 替换 adbConstant 中的占位符 '{}'，使用 args 顺序替换。
     * 如果 args 不足以替换所有占位符，抛出 IllegalArgumentException 异常。
     *
     * @param adbConstant ADB 命令模板，包含占位符 '{}'
     * @param args 参数列表，用于替换占位符
     * @return 替换后的字符串
     * @throws IllegalArgumentException 如果 args 数量不足以替换所有占位符
     */
    public static String replaceAdbStr(String adbConstant, List<String> args) {
        if (adbConstant == null || args == null) {
            throw new IllegalArgumentException("adbConstant and args cannot be null");
        }

        // 正则表达式匹配 '{}' 占位符
        Pattern pattern = Pattern.compile("\\{}");
        Matcher matcher = pattern.matcher(adbConstant);

        StringBuffer result = new StringBuffer();
        int argsIndex = 0;

        // 替换占位符
        while (matcher.find()) {
            if (argsIndex >= args.size()) {
                throw new IllegalArgumentException("Insufficient arguments to replace placeholders. Expected more arguments.");
            }
            matcher.appendReplacement(result, args.get(argsIndex));
            argsIndex++;
        }
        matcher.appendTail(result);

        // 检查是否多余参数
        if (argsIndex < args.size()) {
            throw new IllegalArgumentException("Too many arguments provided. Some arguments were not used.");
        }

        return result.toString();
    }

    public static String replaceAdbStr(String adbConstant, String arg) {
      return replaceAdbStr(adbConstant, Lists.newArrayList(arg));
    }


}
