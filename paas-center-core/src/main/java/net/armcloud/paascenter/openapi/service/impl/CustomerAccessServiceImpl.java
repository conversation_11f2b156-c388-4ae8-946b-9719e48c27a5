package net.armcloud.paascenter.openapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.mapper.CustomerAccessMapper;
import net.armcloud.paascenter.openapi.service.ICustomerAccessService;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
public class CustomerAccessServiceImpl extends ServiceImpl<CustomerAccessMapper, CustomerAccess> implements ICustomerAccessService {
    @Resource
    private RedisService redisService;
    @Resource
    private CustomerAccessMapper customerAccessMapper;


    @Override
    public CustomerAccess getAccessByAccessKeyId(String accessKeyId) {
        // 从缓存中获取用户访问信息
        String customerAccessObj = redisService.getCacheObject(RedisKeyPrefix.CUSTOMER_ACCESS + accessKeyId);

        // 如果缓存中存在用户访问信息，则直接返回
        if (Objects.nonNull(customerAccessObj)) {
            return JSON.parseObject(customerAccessObj, CustomerAccess.class);
        }

        // 缓存中不存在用户访问信息，则从数据库中查询
        QueryWrapper<CustomerAccess> queryWrapper = new QueryWrapper<CustomerAccess>().eq("access_key_id", accessKeyId).eq("status", Constants.ENABLE);
        CustomerAccess customerAccess = customerAccessMapper.selectOne(queryWrapper);

        if (Objects.isNull(customerAccess)) {
            return null;
        }
        // 将查询结果放入缓存，并设置缓存时间为60分钟
        String customerAccessStr = JSON.toJSONString(customerAccess);
        redisService.setCacheObject(RedisKeyPrefix.CUSTOMER_ACCESS + accessKeyId, customerAccessStr, RedisKeyTime.minute_60, TimeUnit.MINUTES);

        return customerAccess;
    }

    @Override
    public CustomerAccess getAccessByCustomerId(Long customerId) {
        // 从缓存中获取用户访问信息
        String customerAccessObj = redisService.getCacheObject(RedisKeyPrefix.CUSTOMER_ACCESS_ID + customerId);

        // 如果缓存中存在用户访问信息，则直接返回
        if (Objects.nonNull(customerAccessObj)) {
            return JSON.parseObject(customerAccessObj, CustomerAccess.class);
        }

        // 缓存中不存在用户访问信息，则从数据库中查询
        QueryWrapper<CustomerAccess> queryWrapper = new QueryWrapper<CustomerAccess>().eq("customer_id", customerId).eq("status", Constants.ENABLE);
        CustomerAccess customerAccess = customerAccessMapper.selectOne(queryWrapper);

        if (Objects.isNull(customerAccess)) {
            return null;
        }
        // 将查询结果放入缓存，并设置缓存时间为60分钟
        String customerAccessStr = JSON.toJSONString(customerAccess);
        redisService.setCacheObject(RedisKeyPrefix.CUSTOMER_ACCESS_ID + customerId, customerAccessStr, RedisKeyTime.minute_60, TimeUnit.MINUTES);

        return customerAccess;
    }
}
