package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.PadRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PadRoomMapper extends BaseMapper<PadRoom> {

    /**
     * 批量插入房间
     */
    int batchInsertPadRooms(@Param("rooms") List<PadRoom> rooms);

}
