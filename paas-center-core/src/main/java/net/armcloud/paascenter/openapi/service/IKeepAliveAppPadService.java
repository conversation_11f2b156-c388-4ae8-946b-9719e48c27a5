package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.client.internal.dto.StartAppListDTO;
import net.armcloud.paascenter.common.client.internal.vo.StartAppListVO;
import net.armcloud.paascenter.common.model.entity.paas.KeepAliveAppPad;
import net.armcloud.paascenter.openapi.model.dto.SetKeepAliveAppSaveDTO;

/**
 * <p>
 * 保活应用实例 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface IKeepAliveAppPadService extends IService<KeepAliveAppPad> {

    /**
     * 设置保活应用
     * @param setKeepAliveAppSaveDTO
     */
    void setKeepAliveApp(SetKeepAliveAppSaveDTO setKeepAliveAppSaveDTO);

    /**
     * 查询实例的保活应用配置
     * @param param
     * @return
     */
    StartAppListVO getPadStartAppLists(StartAppListDTO param);
}
