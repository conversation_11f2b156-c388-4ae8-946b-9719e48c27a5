package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.dto.api.UploadImageDTO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.common.model.vo.api.UploadImageVO;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.vo.QueryImageVO;
import net.armcloud.paascenter.openapi.model.vo.SelectImageInfoVO;
import net.armcloud.paascenter.openapi.model.vo.UploadImageFromUrlVO;

import javax.validation.Valid;

public interface ICustomerUploadImageService extends IService<CustomerUploadImage> {


    /**
     * 推送镜像到镜像仓库（容器）
     * @param param
     * @return
     */
    UploadImageVO uploadImageContainer(UploadImageDTO param);

    IPage<QueryImageVO> queryImageList(@Valid QueryImageDTO param);

    UploadImageFromUrlVO uploadImageFromUrl(@Valid UploadImageFromUrlDTO param);

    void sendDataToDockerDownloadAndPush();

    void callBackResult(DownloadAndPushResultDTO dto);

    SelectImageInfoVO selectImageInfo(SelectImageInfoDTO param);
}
