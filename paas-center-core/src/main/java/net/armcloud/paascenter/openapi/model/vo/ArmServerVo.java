package net.armcloud.paascenter.openapi.model.vo;

import net.armcloud.paascenter.common.model.dto.PageDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class ArmServerVo extends PageDTO implements Serializable {

    /** 集群代码 */
    private String clusterCode;

    /** ARM服务器代码 */
    private String armServerCode;

    /** 机房Id */
    private String idc;

    /** ARM服务器序列号 */
    private String armSn;

    /** ARM服务器IP地址 */
    private String armIp;

    /** 设备子网 */
    private String deviceSubnet;

    /** 状态 */
    private String status;

    /** 是否在线 */
    private Integer online;

    /** MAC地址和VLAN信息 */
    private String macVlan;

    /** 机箱标签 */
    private String chassisLabel;

    /** 机箱中的机柜U位 */
    private String chassisCabinetU;

    /** 网关板卡ID */
    private String gatewayPadId;

    /** 网关设备ID */
    private String gatewayDeviceId;

}