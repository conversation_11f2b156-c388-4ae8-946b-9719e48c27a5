package net.armcloud.paascenter.openapi.netpadv2.service;

import com.alibaba.fastjson.JSONObject;
import net.armcloud.paascenter.common.core.constant.task.TaskTypeConstants;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.openapi.netpadv2.dto.*;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ResultVO;

import javax.validation.Valid;
import java.util.List;

/**
 * 网存实例V2服务接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
public interface NetPadV2Service {

    /**
     * 创建网存实例V2
     * 优化版本的网存实例创建接口
     *
     * @param dto 创建参数
     * @param sourceTarget 来源
     * @return 创建结果列表
     */
    List<NetPadV2CreateResponseDTO> createNetStorageInstances(NetPadV2CreateDTO dto, SourceTargetEnum sourceTarget);

    /**
     * 批量开机网存实例
     * 支持传入多个实例进行批量开机操作
     *
     * @param param 批量开机参数
     * @param sourceTargetEnum 来源类型
     * @return 任务列表
     */
    NetPadV2ResultVO batchBootOn(NetPadV2BatchBootOnDTO param, SourceTargetEnum sourceTargetEnum);

    /**
     * 获取实例开机参数(批量开机接口中会将开机参数放入redis中缓存)
     *
     * @param padCode 实例编码
     * @return 开机参数
     */
    JSONObject getTaskRequest(String padCode, TaskTypeConstants taskType);

    /**
     * 批量关机网存实例
     * 支持传入多个实例进行批量关机操作
     *
     * @param param 批量关机参数
     * @param sourceTargetEnum 来源类型
     * @return 任务列表
     */
    NetPadV2ResultVO batchBootOff(@Valid NetPadV2BatchOffDTO param, SourceTargetEnum sourceTargetEnum);

    /**
     * 处理实例关机后的逻辑
     *
     * @param padCode 实例编码
     */
    void padOffHandler(String padCode);

    /**
     * 处理实例删除后的逻辑
     *
     * @param padCode 实例编码
     */
    void padDelHandler(String padCode);

    /**
     * 批量删除网存实例
     * 支持传入多个实例进行批量删除操作
     *
     * @param param 批量删除参数
     * @param sourceTargetEnum 来源类型
     * @return 任务列表
     */
    NetPadV2ResultVO batchDelete(@Valid NetPadV2BatchDelDTO param, SourceTargetEnum sourceTargetEnum);
}
