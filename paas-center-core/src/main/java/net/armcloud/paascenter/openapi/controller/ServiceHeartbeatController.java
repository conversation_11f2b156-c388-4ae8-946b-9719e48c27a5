package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.core.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/openapi/open/heartbeat")
public class ServiceHeartbeatController {
    /**
     * 服务心跳检测
     */
    @GetMapping("status")
    public Result<?> stsToken() {
        return Result.ok();
    }
}
