package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.model.vo.api.StsTokenVO;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.ClearStsTokenDTO;
import net.armcloud.paascenter.openapi.model.dto.StsTokenByPadCodeDTO;
import net.armcloud.paascenter.openapi.service.ICustomerAccessService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.common.core.constant.Constants;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/openapi/open/token")
public class TokenController {
    @Resource
    private RedisService redisService;
    @Resource
    private ICustomerAccessService customerAccessService;

    @Resource
    private IPadService padService;

    /**
     * 获取SDK授权Token
     *
     * @return StsTokenVO
     */
    @GetMapping("stsToken")
    @ApiOperation(httpMethod = "POST", value = "获取SDK授权Token", notes = "获取SDK授权Token")
    public Result<StsTokenVO> stsToken() {
        //客户ID
        Long customerId = null;
        //客户密钥信息
        CustomerAccess customerAccess = null;

        long current = System.currentTimeMillis();
//        log.info("stsToken start thread:{} current:{}", Thread.currentThread().getId(), current);
//        log.info("获取SDK授权Token");
        customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
//        log.info("stsToken start thread:{} customerId:{} current:{}", Thread.currentThread().getId(), customerId, current);
        customerAccess = customerAccessService.getAccessByCustomerId(customerId);
        if (customerAccess == null) {
            throw new BasicException(BasicExceptionCode.INVALID_KEY);
        }
        StsTokenVO stsTokenVO = new StsTokenVO();
        String token = UUID.randomUUID().toString();
        String value = customerAccess.getCustomerId() + Constants.UNDERLINE + token;

        redisService.setCacheObject(RedisKeyPrefix.SDK_TOKEN + token, value, 24L, TimeUnit.HOURS);
        stsTokenVO.setToken(token);

        log.info("stsToken end thread:{} totalTime:{} customerId:{} current:{}", Thread.currentThread().getId(), customerId, System.currentTimeMillis() - current, current);
        return Result.ok(stsTokenVO);

    }
    /**
     * MoreLogin需要使用padCode生成token,该token只能用于当前padCode下用户生成rtc信息
     * @param dto
     * @return
     */
    @PostMapping(value = "/stsTokenByPadCode")
    @ApiOperation(httpMethod = "POST", value = "根据padCode获取SDK授权Token", notes = "根据padCode获取SDK授权Token")
    public Result<StsTokenVO> stsTokenByPadCode(@Valid @RequestBody StsTokenByPadCodeDTO dto ) {
        //客户ID
        Long customerId = null;
        //客户密钥信息
        CustomerAccess customerAccess = null;
        long current = System.currentTimeMillis();
        log.info("stsTokenByPadCode start thread:{} current:{}", Thread.currentThread().getId(), current);
        customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        log.info("stsTokenByPadCode start thread:{} customerId:{} current:{}", Thread.currentThread().getId(), customerId, current);
        customerAccess = customerAccessService.getAccessByCustomerId(customerId);
        if (customerAccess == null) {
            throw new BasicException(BasicExceptionCode.INVALID_KEY);
        }
        Boolean checkCustomerPadCode = padService.checkPadListOwnerService(customerId, Lists.newArrayList(dto.getPadCode()));
        if (!checkCustomerPadCode) {
            throw new BasicException(BasicExceptionCode.PAD_CODE_NOT_EXIST);
        }
        StsTokenVO stsTokenVO = new StsTokenVO();
        //为了向前兼容清除,在token后缀拼接指定字符串,并在value中拼接padCode信息.后续验证该token是否可以生成对应pad_code下的rtc信息,通过切割token跟value去判断
        String token = UUID.randomUUID() + Constants.USER_MORELOGIN;
        ;
        String value = customerAccess.getCustomerId() + Constants.UNDERLINE + token + Constants.UNDERLINE + dto.getPadCode();

        redisService.setCacheObject(RedisKeyPrefix.SDK_TOKEN + token, value, 24L, TimeUnit.HOURS);
        stsTokenVO.setToken(token);

//        log.info("stsTokenByPadCode end thread:{} totalTime:{} customerId:{} current:{}", Thread.currentThread().getId(), customerId, System.currentTimeMillis() - current, current);
        return Result.ok(stsTokenVO);

    }


    @PostMapping("clearStsToken")
    @ApiOperation(httpMethod = "POST", value = "清除SDK授权Token", notes = "清除SDK授权Token")
    public Result<?> clearStsToken(@RequestBody ClearStsTokenDTO clearStsTokenDTO) {
        String cacheKey = RedisKeyPrefix.SDK_TOKEN + clearStsTokenDTO.getToken();
        Long customerId = CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest());
        String cacheTokenVal = redisService.getCacheObject(cacheKey);
        if(StringUtils.isNotEmpty(cacheTokenVal)){
            String value =  customerId + Constants.UNDERLINE + clearStsTokenDTO.getToken();
            if(cacheTokenVal.startsWith(value)){
                redisService.deleteObject(cacheKey);
            }else{
                return Result.fail(BasicExceptionCode.TOKEN_NOT_BELONGING_CUSTOM);
            }
        }
        return Result.ok();
    }
}
