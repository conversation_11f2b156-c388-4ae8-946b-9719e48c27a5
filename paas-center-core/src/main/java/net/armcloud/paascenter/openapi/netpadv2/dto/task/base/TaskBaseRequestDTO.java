package net.armcloud.paascenter.openapi.netpadv2.dto.task.base;

import lombok.Data;

import java.io.Serializable;

/**
 * 任务基础请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025-07-07
 */
@Data
public abstract class TaskBaseRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    // 任务超时时间(秒)
    private Integer timeout;

    private String padCode;

    public abstract String toJSONString();
}
