package net.armcloud.paascenter.openapi.netpadv2.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 参数对比结果VO
 * 用于封装创建实例参数与开机参数的对比结果
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
@Data
public class ParamCompareResultVO {

    @ApiModelProperty(value = "实例编码")
    private String padCode;

    @ApiModelProperty(value = "是否需要更新")
    private boolean needUpdate = false;

    @ApiModelProperty(value = "DNS是否需要更新")
    private boolean dnsChanged = false;

    @ApiModelProperty(value = "新的DNS配置")
    private String newDns;

    @ApiModelProperty(value = "国家编码是否需要更新")
    private boolean countryCodeChanged = false;

    @ApiModelProperty(value = "新的国家编码")
    private String newCountryCode;

    @ApiModelProperty(value = "安卓属性是否需要更新")
    private boolean androidPropChanged = false;

    @ApiModelProperty(value = "新的安卓属性")
    private JSONObject newAndroidProp;
}
