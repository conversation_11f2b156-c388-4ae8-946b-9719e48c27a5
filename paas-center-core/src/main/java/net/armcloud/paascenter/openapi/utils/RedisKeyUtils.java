package net.armcloud.paascenter.openapi.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Redis Key生成工具类
 * 提供各种类型的Redis Key生成方法，支持可变参数，使用冒号分割
 * Key格式: {服务名}:{类型}:{参数1}:{参数2}:...
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Component
public class RedisKeyUtils {
    
    /**
     * 服务名称前缀，从Spring配置文件中读取
     */
    @Value("${spring.application.name:paas-center-core}")
    private String serviceName;
    
    /**
     * 静态服务名称，用于静态方法调用
     */
    private static String SERVICE_PREFIX;
    
    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";
    
    /**
     * 锁Key前缀
     */
    private static final String LOCK_PREFIX = "lock";
    
    /**
     * 数据Key前缀
     */
    private static final String DATA_PREFIX = "data";
    
    /**
     * 计数器Key前缀
     */
    private static final String COUNTER_PREFIX = "counter";
    
    /**
     * 缓存Key前缀
     */
    private static final String CACHE_PREFIX = "cache";
    
    /**
     * 初始化静态变量
     */
    @PostConstruct
    public void init() {
        SERVICE_PREFIX = this.serviceName;
    }
    
    /**
     * 生成锁Key
     * 格式: {服务名}:lock:{参数1}:{参数2}:...
     * 
     * @param params 可变参数
     * @return 锁Key
     */
    public static String lockKey(String... params) {
        return buildKey(LOCK_PREFIX, params);
    }
    
    /**
     * 生成数据Key
     * 格式: {服务名}:data:{参数1}:{参数2}:...
     * 
     * @param params 可变参数
     * @return 数据Key
     */
    public static String dataKey(String... params) {
        return buildKey(DATA_PREFIX, params);
    }
    
    /**
     * 生成计数器Key
     * 格式: {服务名}:counter:{参数1}:{参数2}:...
     * 
     * @param params 可变参数
     * @return 计数器Key
     */
    public static String counterKey(String... params) {
        return buildKey(COUNTER_PREFIX, params);
    }
    
    /**
     * 生成缓存Key
     * 格式: {服务名}:cache:{参数1}:{参数2}:...
     * 
     * @param params 可变参数
     * @return 缓存Key
     */
    public static String cacheKey(String... params) {
        return buildKey(CACHE_PREFIX, params);
    }
    
    /**
     * 生成自定义类型Key
     * 格式: {服务名}:{自定义类型}:{参数1}:{参数2}:...
     * 
     * @param type 自定义类型
     * @param params 可变参数
     * @return 自定义Key
     */
    public static String customKey(String type, String... params) {
        if (!StringUtils.hasText(type)) {
            throw new IllegalArgumentException("自定义Key类型不能为空");
        }
        return buildKey(type.trim(), params);
    }
    
    /**
     * 构建Redis Key
     * 
     * @param keyType Key类型
     * @param params 参数数组
     * @return 完整的Redis Key
     */
    private static String buildKey(String keyType, String... params) {
        StringBuilder keyBuilder = new StringBuilder();
        
        // 添加服务名前缀
        keyBuilder.append(getServicePrefix()).append(SEPARATOR);
        
        // 添加Key类型
        keyBuilder.append(keyType);
        
        // 处理参数
        if (params != null && params.length > 0) {
            String processedParams = Arrays.stream(params)
                    .filter(StringUtils::hasText)  // 过滤空值和空字符串
                    .map(String::trim)             // 去除前后空格
                    .collect(Collectors.joining(SEPARATOR));
            
            if (StringUtils.hasText(processedParams)) {
                keyBuilder.append(SEPARATOR).append(processedParams);
            }
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 获取服务名前缀
     * 如果静态变量未初始化，使用默认值
     * 
     * @return 服务名前缀
     */
    private static String getServicePrefix() {
        return StringUtils.hasText(SERVICE_PREFIX) ? SERVICE_PREFIX : "paas-center-core";
    }
    
    /**
     * 手动设置服务名前缀（用于测试）
     * 
     * @param serviceName 服务名
     */
    public static void setServicePrefix(String serviceName) {
        SERVICE_PREFIX = serviceName;
    }
}
