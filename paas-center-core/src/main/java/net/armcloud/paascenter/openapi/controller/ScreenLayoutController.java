package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.openapi.model.vo.ScreenLayoutVO;
import net.armcloud.paascenter.openapi.service.IScreenLayoutService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/openapi/open/screenLayout")
@Api(tags = "屏幕布局")
@Slf4j
public class ScreenLayoutController {
    private final IScreenLayoutService screenLayoutService;

    public ScreenLayoutController(IScreenLayoutService screenLayoutService) {
        this.screenLayoutService = screenLayoutService;
    }

    @PostMapping("publicList")
    @ApiOperation(value = "获取公共屏幕布局列表")
    public Result<List<ScreenLayoutVO>> publicList() {
        return Result.ok(screenLayoutService.list());
    }

}
