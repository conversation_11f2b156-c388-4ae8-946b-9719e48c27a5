package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadPropertiesMapper;
import net.armcloud.paascenter.openapi.service.IPadPropertiesService;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadProperties;
import net.armcloud.paascenter.common.model.entity.paas.PadPropertiesSub;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.NumberUtil;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.ZERO;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Slf4j
@Service
public class PadPropertiesServiceImpl implements IPadPropertiesService {

    @Resource
    RedisService redisService;
    @Resource
    private PadMapper padMapper;
    @Resource
    private PadPropertiesMapper padPropertiesMapper;

    @Override
    public Boolean saveOrUpdateService(PadProperties properties) {
        //开始时间
        long selectPadStartTime = System.currentTimeMillis();
        Pad pad = padMapper.selectOne(new QueryWrapper<Pad>().eq("pad_out_code", properties.getPadOutCode()));
        if (ObjectUtil.isNull(pad)) {
            return false;
        }
        // 如若上报了实例的存储数据，则变更
        List<PadPropertiesSub> propertiesValues = properties.getPropertiesValues();
        if (CollUtil.isNotEmpty(propertiesValues)) {
            Pad par = new Pad();
            par.setId(pad.getId());

            for (PadPropertiesSub propertiesSub : propertiesValues) {
                String propName = propertiesSub.getPropertiesName();
                switch (propName) {
                    case "dataSize":
                        par.setDataSize(Long.valueOf(propertiesSub.getPropertiesValue()));
                        break;
                    case "dataSizeUsed":
                        par.setDataSizeUsed(Long.valueOf(propertiesSub.getPropertiesValue()));
                        break;
                    case "dataSizeAvailable":
                        par.setDataSizeAvailable(Long.valueOf(propertiesSub.getPropertiesValue()));
                        break;
                    case "cloudVendorType":
                        par.setCloudVendorType(Integer.valueOf(propertiesSub.getPropertiesValue()));
                        break;
                    case "versionName":
                        par.setRtcVersionName(propertiesSub.getPropertiesValue());
                        break;
                    case "versionCode":
                        par.setRtcVersionCode(Integer.valueOf(propertiesSub.getPropertiesValue()));
                        break;
                    case "imageId":
                        par.setImageId(formatImageId(propertiesSub.getPropertiesValue()));
                        break;
                    case "persist.sys.cloud.madb_enable":
                        if (NumberUtil.isInteger(propertiesSub.getPropertiesValue())) {
                            par.setAdbOpenStatus(Integer.valueOf(propertiesSub.getPropertiesValue()));
                        }
                        break;
                    default:
                        // 处理其他属性或忽略未知属性
                        break;
                }
            }
            // 只在有变化时更新数据库
            if (isNotEmpty(par.getDataSize()) || isNotEmpty(par.getDataSizeUsed()) || isNotEmpty(par.getDataSizeAvailable())
                    || isNotEmpty(par.getCloudVendorType()) || isNotEmpty(par.getRtcVersionName()) || isNotEmpty(par.getRtcVersionCode())
                    || isNotEmpty(par.getImageId())) {
                padMapper.updateById(par);
            }
        }

        properties.setPadCode(pad.getPadCode());

        if (existenceProperties(properties.getPadCode()) > ZERO) {
            Boolean hasPadProperties = padPropertiesMapper.updateProperties(properties) > ZERO;
            //结束时间
            long selectPadEndTime = System.currentTimeMillis();
            if (selectPadEndTime - selectPadStartTime > 2000) {
                log.info("saveOrUpdateService-updateProperties,入参：{},响应时间: {} ms", JSON.toJSONString(properties), (selectPadEndTime - selectPadStartTime));
            }
            return hasPadProperties;
        }
        Boolean hasPadProperties= padPropertiesMapper.insertProperties(properties) > ZERO;
        //结束时间
        long selectPadEndTime = System.currentTimeMillis();
        if (selectPadEndTime - selectPadStartTime > 2000) {
            log.info("saveOrUpdateService-insertProperties,入参：{},响应时间: {} ms", JSON.toJSONString(properties), (selectPadEndTime - selectPadStartTime));
        }
        return hasPadProperties;
    }

    public Long existenceProperties(String padCode) {
        String key = RedisKeyPrefix.PAD_PROPERTIES_COUNT + padCode;
        String countObj = redisService.getCacheObject(key);

        if (ObjectUtil.isNotNull(countObj)) {
            return Long.parseLong(countObj);
        }

        long count = padPropertiesMapper.selectCount(new QueryWrapper<PadProperties>().eq("pad_code", padCode));
        if (count > ZERO) {
            redisService.setCacheObject(key, String.valueOf(count), RedisKeyTime.day_1, TimeUnit.DAYS);
        }
        return count;
    }

    private String formatImageId(String imageId) {
        if (StrUtil.isEmpty(imageId)) {
            return null;
        }

        if (!imageId.contains("/")) {
            return null;
        }

        String[] parts = imageId.split("/");
        String lastPart = parts[parts.length - 1];
        int colonIndex = lastPart.indexOf(":");
        if (colonIndex != -1) {
            return lastPart.substring(0, colonIndex);
        }
        return null;
    }

}
