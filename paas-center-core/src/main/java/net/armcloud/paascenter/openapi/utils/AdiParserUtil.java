package net.armcloud.paascenter.openapi.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.openapi.model.vo.AdiParseVO;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.enums.CompressionMethod;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.web.multipart.MultipartFile;
import net.lingala.zip4j.model.ZipParameters;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AdiParserUtil：用于完整复刻 adi_gen_auto_local.py 的核心逻辑
 * 包含：
 *   1) 解析ZIP并检查 debug_ramdisk 中的关键文件
 *   2) 解析 deviceInfo.txt / adb_debug.prop / cpuinfo / ...
 *   3) 生成各种配置文件 (adb_debug.prop, bootconfig, vcloud_settings.prop, sensor_etc)
 *   4) 复制 cpuinfo, libc.so, cpu/ 目录
 *   5) 使用AES加密生成最终ADI文件 (Zip4j替代Python的pyzipper)
 *
 * 说明：
 *   - DB操作(add_adi, add_screen_layout 等)和OSS上传可在Service层处理
 *   - 这里专注于local解析+生成, 并返回最终ZIP文件及解析结果
 *
 * 对应 Python 脚本中主要函数：
 *   - setup_clean_directories()
 *   - parse_uploaded_tar()  => parseUploadedZip()
 *   - gen_adb_debug_prop() => genAdbDebugProp()
 *   - gen_bootconfig(), gen_vcloud_settings(), gen_sensor_etc()
 *   - create_password_protected_zip() => createAESPasswordZip()
 */
public class AdiParserUtil {

    private static final Logger log = LoggerFactory.getLogger(AdiParserUtil.class);

    /**
     * Python里 REQUIRED_FILES = {
     *  "adb_debug.prop","deviceInfo.txt","cpuinfo","features.txt","libraries.txt","libc.so",
     *  "services.txt","am-getconfig.txt","cpu"
     * }
     * (其中 cpu 是目录，不完全相同; 这里拆分成 "cpu"(目录) 和 8个文件)
     */
    private static final List<String> REQUIRED_FILES = Arrays.asList(
            "adb_debug.prop",
            "deviceInfo.txt",
            "cpuinfo",
            "features.txt",
            "libraries.txt",
            "libc.so",
            "services.txt",
            "am-getconfig.txt"
            // "cpu" 目录额外处理
    );

    /**
     * 根据文件类型提取归档文件内容
     * 支持多种归档格式: ZIP, RAR, TAR, TAR.GZ, 7Z 等
     *
     * @param archiveFile 归档文件
     * @param destDir 解压目标目录
     * @throws IOException 解压失败时抛出异常
     */
    private static void extractArchive(File archiveFile, File destDir) throws IOException {
        String fileName = archiveFile.getName().toLowerCase();

        // 获取扩展名（处理复合扩展名如 .tar.gz）
        String extension = getFileExtension(fileName);
        log.info("正在解压文件: {}, 检测到文件类型: {}", fileName, extension);

        switch (extension) {
            case "zip":
                // 使用 Hutool 的 ZipUtil 解压 ZIP 文件
                cn.hutool.core.util.ZipUtil.unzip(archiveFile, destDir);
                break;

            case "rar":
                // 尝试使用命令行工具 unrar 解压 RAR 文件
                try {
                    log.info("尝试使用unrar命令行工具解压RAR文件");
                    extractUsingExternalCommand(archiveFile, destDir, "unrar", "x", "-o+", archiveFile.getAbsolutePath(), destDir.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("使用命令行解压RAR失败: {}，尝试使用第三方库解压", e.getMessage());
                    // 尝试使用Java-based RAR解压（需要添加junrar依赖）
                    try {
                        log.info("正在尝试备用解压方法");
                        // 判断是否有第三方库可用
                        try {
                            Class.forName("com.github.junrar.rarfile.FileHeader");
                            extractRarUsingThirdParty(archiveFile, destDir);
                        } catch (ClassNotFoundException cnfe) {
                            // 如果没有junrar依赖，使用7zip作为后备
                            log.info("junrar库不可用，尝试使用7zip作为备用解压程序");
                            extractUsingExternalCommand(archiveFile, destDir, "7z", "x", archiveFile.getAbsolutePath(), "-o" + destDir.getAbsolutePath(), "-y");
                        }
                    } catch (Exception e2) {
                        log.error("所有RAR解压方法都失败", e2);
                        throw new IOException("解压RAR文件失败: " + e.getMessage() +
                                "\n并且备用解压方法也失败: " + e2.getMessage() +
                                "\n请确保系统已安装 unrar 或 7z 命令行工具，或者使用ZIP格式上传文件", e);
                    }
                }
                break;

            case "tar":
                // 尝试使用命令行工具 tar 解压 TAR 文件
                try {
                    log.info("使用tar命令解压TAR文件");
                    // 添加参数h，忽略符号链接
                    extractUsingExternalCommand(archiveFile, destDir, "tar", "-xf", "--no-same-owner", "--warning=no-unknown-keyword", archiveFile.getAbsolutePath(), "-C", destDir.getAbsolutePath());
                } catch (IOException e) {
                    log.warn("使用命令行解压TAR失败: {}", e.getMessage());
                    // 尝试使用Java解压
                    try {
                        log.info("尝试使用Java IO手动解压TAR文件");
                        extractTarManually(archiveFile, destDir);
                    } catch (Exception e2) {
                        log.error("所有TAR解压方法都失败", e2);
                        throw new IOException("解压TAR文件失败: " + e.getMessage() +
                                "\n并且备用解压方法也失败: " + e2.getMessage() +
                                "\n请确保系统已安装 tar 命令行工具，或者使用ZIP格式上传文件", e);
                    }
                }
                break;
            default:
                // 默认尝试 ZIP 格式
                log.warn("未知的文件格式: {}, 尝试作为 ZIP 文件解压", extension);
                try {
                    cn.hutool.core.util.ZipUtil.unzip(archiveFile, destDir);
                } catch (Exception e) {
                    throw new IOException("不支持的归档文件格式: " + extension +
                            "\n请使用 ZIP, RAR, TAR, TAR.GZ 或 7Z 格式的文件", e);
                }
        }
        log.info("成功解压文件到: {}", destDir.getAbsolutePath());
    }

    /**
     * 获取文件扩展名，支持复合扩展名（如 .tar.gz）
     *
     * @param fileName 文件名
     * @return 文件扩展名（不含点）
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null) {
            return "";
        }

        // 处理常见的复合扩展名
        String lowerFileName = fileName.toLowerCase();

        // 检查是否是复合扩展名
        String[] compoundExtensions = {"tar.gz", "tar.bz2", "tar.xz"};
        for (String ext : compoundExtensions) {
            if (lowerFileName.endsWith("." + ext)) {
                return ext;
            }
        }

        // 处理普通扩展名
        int lastDotIndex = lowerFileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return lowerFileName.substring(lastDotIndex + 1);
        }

        // 没有扩展名
        return "";
    }

    /**
     * 使用外部命令行工具解压文件
     *
     * @param archiveFile 归档文件
     * @param destDir 目标解压目录
     * @param command 命令名称
     * @param args 命令参数
     * @throws IOException 解压失败时抛出异常
     */
    private static void extractUsingExternalCommand(File archiveFile, File destDir, String command, String... args) throws IOException {
        try {
            // 确保目标目录存在
            if (!destDir.exists()) {
                destDir.mkdirs();
            }

            // 构建命令
            List<String> cmdList = new ArrayList<>();

            // Windows 系统需要特殊处理
            if (SystemUtils.IS_OS_WINDOWS) {
                cmdList.add("cmd");
                cmdList.add("/c");
            }

            cmdList.add(command);
            cmdList.addAll(Arrays.asList(args));

            // 创建进程
            ProcessBuilder pb = new ProcessBuilder(cmdList);
            pb.redirectErrorStream(true);

            log.info("执行命令: {}", String.join(" ", cmdList));

            // 启动进程
            Process process = pb.start();

            // 读取输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("解压输出: {}", line);
                }
            }

            // 等待进程完成
            int exitCode = process.waitFor();

            if (exitCode != 0) {
                throw new IOException("解压命令执行失败，退出码: " + exitCode);
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("解压过程被中断", e);
        } catch (IOException e) {
            throw new IOException("执行解压命令失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用第三方库解压RAR文件
     * 注意：此方法基于junrar库，需要在pom.xml中添加依赖：
     * <dependency>
     *     <groupId>com.github.junrar</groupId>
     *     <artifactId>junrar</artifactId>
     *     <version>7.5.4</version>
     * </dependency>
     *
     * @param rarFile RAR文件
     * @param destDir 目标解压目录
     * @throws IOException 解压失败时抛出异常
     */
    private static void extractRarUsingThirdParty(File rarFile, File destDir) throws IOException {
        try {
            // 使用反射调用，避免编译期依赖
            Class<?> archiveClass = Class.forName("com.github.junrar.Archive");
            Object archive = archiveClass.getConstructor(File.class).newInstance(rarFile);

            try {
                // 获取getFileHeaders方法
                java.lang.reflect.Method getFileHeadersMethod = archiveClass.getMethod("getFileHeaders");
                @SuppressWarnings("unchecked")
                List<Object> fileHeaders = (List<Object>) getFileHeadersMethod.invoke(archive);

                if (fileHeaders != null) {
                    Class<?> fileHeaderClass = Class.forName("com.github.junrar.rarfile.FileHeader");
                    java.lang.reflect.Method getFileNameMethod = fileHeaderClass.getMethod("getFileNameString");
                    java.lang.reflect.Method isDirectoryMethod = fileHeaderClass.getMethod("isDirectory");
                    java.lang.reflect.Method extractFileMethod = archiveClass.getMethod("extractFile", fileHeaderClass, File.class);

                    for (Object header : fileHeaders) {
                        String fileName = (String) getFileNameMethod.invoke(header);
                        boolean isDirectory = (Boolean) isDirectoryMethod.invoke(header);

                        File outFile = new File(destDir, fileName);
                        if (isDirectory) {
                            if (!outFile.exists()) {
                                outFile.mkdirs();
                            }
                        } else {
                            // 确保父目录存在
                            File parent = outFile.getParentFile();
                            if (!parent.exists()) {
                                parent.mkdirs();
                            }

                            // 解压文件
                            extractFileMethod.invoke(archive, header, destDir);
                        }
                    }
                }

                // 关闭Archive
                java.lang.reflect.Method closeMethod = archiveClass.getMethod("close");
                closeMethod.invoke(archive);

                log.info("使用junrar库成功解压RAR文件");
            } catch (Exception e) {
                // 确保关闭Archive
                try {
                    java.lang.reflect.Method closeMethod = archiveClass.getMethod("close");
                    closeMethod.invoke(archive);
                } catch (Exception ex) {
                    // 忽略关闭错误
                }
                throw e;
            }
        } catch (Exception e) {
            throw new IOException("使用junrar库解压RAR文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Java IO手动解压TAR文件（不处理符号链接）
     *
     * @param tarFile TAR文件
     * @param destDir 目标解压目录
     * @throws IOException 解压失败时抛出异常
     */
    private static void extractTarManually(File tarFile, File destDir) throws IOException {
        // 检查commons-compress库是否可用
        try {
            Class.forName("org.apache.commons.compress.archivers.tar.TarArchiveInputStream");
            extractTarWithCommonsCompress(tarFile, destDir);
        } catch (ClassNotFoundException | NoClassDefFoundError e) {
            log.warn("commons-compress库不可用或版本不兼容，尝试使用命令行工具: {}", e.getMessage());
            // 尝试使用7z作为替代
            try {
                extractUsingExternalCommand(tarFile, destDir, "7z", "x", tarFile.getAbsolutePath(), "-o" + destDir.getAbsolutePath(), "-y");
            } catch (IOException ex) {
                // 如果7z也失败，尝试标准tar命令带不同参数
                extractUsingExternalCommand(tarFile, destDir, "tar", "-xf", "--ignore-zeros", "--no-same-permissions", tarFile.getAbsolutePath(), "-C", destDir.getAbsolutePath());
            }
        } catch (NoSuchMethodError e) {
            log.warn("检测到Commons IO或Commons Compress库版本不兼容: {}", e.getMessage());
            try {
                extractUsingExternalCommand(tarFile, destDir, "tar", "-xf", "--ignore-zeros", "--no-same-permissions", tarFile.getAbsolutePath(), "-C", destDir.getAbsolutePath());
            } catch (IOException ex) {
                throw new IOException("使用命令行解压TAR文件失败，可能需要升级Commons IO或Commons Compress库版本", ex);
            }
        }
    }

    /**
     * 使用Commons Compress库解压TAR文件
     * 注意：该方法可能因Commons IO版本不兼容而失败
     * 在较旧版本的Commons IO中，可能缺少org.apache.commons.io.file.attribute.FileTimes.fromUnixTime方法
     * 如果出现NoSuchMethodError，解压过程将回退到使用命令行工具
     */
    private static void extractTarWithCommonsCompress(File tarFile, File destDir) throws IOException {
        try (FileInputStream fis = new FileInputStream(tarFile);
             BufferedInputStream bis = new BufferedInputStream(fis);
             org.apache.commons.compress.archivers.tar.TarArchiveInputStream tis = new org.apache.commons.compress.archivers.tar.TarArchiveInputStream(bis)) {

            org.apache.commons.compress.archivers.tar.TarArchiveEntry entry;
            while ((entry = tis.getNextTarEntry()) != null) {
                if (entry.isSymbolicLink()) {
                    log.debug("跳过符号链接：{}", entry.getName());
                    continue;  // 跳过符号链接
                }

                File outputFile = new File(destDir, entry.getName());

                if (entry.isDirectory()) {
                    if (!outputFile.exists()) {
                        if (!outputFile.mkdirs()) {
                            throw new IOException("创建目录失败: " + outputFile.getAbsolutePath());
                        }
                    }
                } else {
                    // 确保父目录存在
                    File parent = outputFile.getParentFile();
                    if (!parent.exists()) {
                        if (!parent.mkdirs()) {
                            throw new IOException("创建父目录失败: " + parent.getAbsolutePath());
                        }
                    }

                    // 复制文件
                    try (FileOutputStream fos = new FileOutputStream(outputFile);
                         BufferedOutputStream bos = new BufferedOutputStream(fos)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = tis.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }
                    }
                    // 设置文件权限
                    try {
                        // TarArchiveEntry没有isExecutable方法，改用检查模式位
                        int mode = entry.getMode();
                        // 检查所有者执行位(0100)
                        boolean isExecutable = (mode & 0100) != 0;
                        outputFile.setExecutable(isExecutable);
                    } catch (SecurityException e) {
                        log.warn("无法设置可执行权限: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("手动解压TAR文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Java IO手动解压TAR.GZ文件（不处理符号链接）
     *
     * @param tarGzFile TAR.GZ文件
     * @param destDir 目标解压目录
     * @throws IOException 解压失败时抛出异常
     */
    private static void extractTarGzManually(File tarGzFile, File destDir) throws IOException {
        // 检查commons-compress库是否可用
        try {
            Class.forName("org.apache.commons.compress.archivers.tar.TarArchiveInputStream");
            extractTarGzWithCommonsCompress(tarGzFile, destDir);
        } catch (ClassNotFoundException | NoClassDefFoundError e) {
            log.warn("commons-compress库不可用或版本不兼容，尝试使用命令行工具: {}", e.getMessage());
            // 尝试使用7z作为替代
            try {
                extractUsingExternalCommand(tarGzFile, destDir, "7z", "x", tarGzFile.getAbsolutePath(), "-o" + destDir.getAbsolutePath(), "-y");
            } catch (IOException ex) {
                // 如果7z也失败，尝试标准tar命令带不同参数
                extractUsingExternalCommand(tarGzFile, destDir, "tar", "-xzf", "--ignore-zeros", "--no-same-permissions", tarGzFile.getAbsolutePath(), "-C", destDir.getAbsolutePath());
            }
        } catch (NoSuchMethodError e) {
            log.warn("检测到Commons IO或Commons Compress库版本不兼容: {}", e.getMessage());
            try {
                extractUsingExternalCommand(tarGzFile, destDir, "tar", "-xzf", "--ignore-zeros", "--no-same-permissions", tarGzFile.getAbsolutePath(), "-C", destDir.getAbsolutePath());
            } catch (IOException ex) {
                throw new IOException("使用命令行解压TAR.GZ文件失败，可能需要升级Commons IO或Commons Compress库版本", ex);
            }
        }
    }

    /**
     * 使用Commons Compress库解压TAR.GZ文件
     * 注意：该方法可能因Commons IO版本不兼容而失败
     * 在较旧版本的Commons IO中，可能缺少org.apache.commons.io.file.attribute.FileTimes.fromUnixTime方法
     * 如果出现NoSuchMethodError，解压过程将回退到使用命令行工具
     */
    private static void extractTarGzWithCommonsCompress(File tarGzFile, File destDir) throws IOException {
        try (FileInputStream fis = new FileInputStream(tarGzFile);
             BufferedInputStream bis = new BufferedInputStream(fis);
             java.util.zip.GZIPInputStream gis = new java.util.zip.GZIPInputStream(bis);
             org.apache.commons.compress.archivers.tar.TarArchiveInputStream tis = new org.apache.commons.compress.archivers.tar.TarArchiveInputStream(gis)) {

            org.apache.commons.compress.archivers.tar.TarArchiveEntry entry;
            while ((entry = tis.getNextTarEntry()) != null) {
                if (entry.isSymbolicLink()) {
                    log.debug("跳过符号链接：{}", entry.getName());
                    continue;  // 跳过符号链接
                }

                File outputFile = new File(destDir, entry.getName());

                if (entry.isDirectory()) {
                    if (!outputFile.exists()) {
                        if (!outputFile.mkdirs()) {
                            throw new IOException("创建目录失败: " + outputFile.getAbsolutePath());
                        }
                    }
                } else {
                    // 确保父目录存在
                    File parent = outputFile.getParentFile();
                    if (!parent.exists()) {
                        if (!parent.mkdirs()) {
                            throw new IOException("创建父目录失败: " + parent.getAbsolutePath());
                        }
                    }

                    // 复制文件
                    try (FileOutputStream fos = new FileOutputStream(outputFile);
                         BufferedOutputStream bos = new BufferedOutputStream(fos)) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = tis.read(buffer)) != -1) {
                            bos.write(buffer, 0, bytesRead);
                        }
                    }
                    // 设置文件权限
                    try {
                        // TarArchiveEntry没有isExecutable方法，改用检查模式位
                        int mode = entry.getMode();
                        // 检查所有者执行位(0100)
                        boolean isExecutable = (mode & 0100) != 0;
                        outputFile.setExecutable(isExecutable);
                    } catch (SecurityException e) {
                        log.warn("无法设置可执行权限: {}", e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("手动解压TAR.GZ文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 对应 python gen_adb_debug_prop()
     *    生成 adb_debug.prop
     *    python中有过滤: ro.boot., ro.lmk., ro.vendor. .... 只保留 ro.* + some persist keys
     *    这里简化演示, 直接写常见 ro.product.* & battery info
     */
    /**
     * 复刻 adi_gen_auto_local.py 的 gen_adb_debug_prop(...)
     *
     * @param propsMap      从 adb_debug.prop 解析出的属性 Map（key -> value）
     * @param deviceInfo deviceInfo.txt 解析后的 JSONObject
     * @param outDir     work_out 目录
     */
    @SuppressWarnings("unchecked")
    private static void genAdbDebugProp(File debugRamdisk, File outDir,Map<String,String> propsMap,JSONObject deviceInfo) {
        // 1. Python 中用于过滤/保留的常量
        final Set<String> NEED_FILTER_PROP_KEYS = new HashSet<>(Arrays.asList(
                "ro.preinstall.path",
                "ro.com.baidu.searchbox",
                "ro.com.google.gmsversion",
                "ro.vndk.version",
                "ro.product.vndk.version"
        ));
        final Set<String> NEED_KEEP_PROP_KEYS = new HashSet<>(Arrays.asList(
                "persist.radio.multisim.config",
                "gsm.version.baseband",
                "gsm.version.ril-impl"
        ));

        // 2. 收集满足条件的属性（保持插入顺序）
        Map<String, String> adbDebugProps = new LinkedHashMap<>();
        for (String key : propsMap.keySet()) {
            // 过滤 python 中列出的几大类别/前缀
            if (key.startsWith("ro.boot.")
                    || key.startsWith("ro.lmk.")
                    || key.startsWith("ro.vendor.")
                    || key.startsWith("ro.hardware.")
                    || NEED_FILTER_PROP_KEYS.contains(key)) {
                continue;
            }
            // 仅保留 (1) 以 "ro." 开头；(2) 非 ro. 但在需要保留列表
            boolean shouldKeep = key.startsWith("ro.") || NEED_KEEP_PROP_KEYS.contains(key);
            if (shouldKeep) {
                adbDebugProps.put(key, propsMap.get(key));
            }
        }

        /* 3. 写入文件：先全部属性，再附加 extra 信息（与 Python 完全一致） */
        File output = new File(outDir, "adb_debug.prop");
        try (PrintWriter pw = new PrintWriter(output, StandardCharsets.UTF_8.name())) {

            // 3‑1 先写过滤后的属性
            for (Map.Entry<String, String> entry : adbDebugProps.entrySet()) {
                pw.printf("%s=%s%n", entry.getKey(), entry.getValue());
            }

            // 3‑2 extra 段落
            pw.println();
            pw.println("# extra");

            // battery capacity
            String batteryCap = Optional.ofNullable(deviceInfo.getJSONObject("battery"))
                    .map(o -> o.getStr("cap", "0"))
                    .orElse("0");
            pw.printf("persist.sys.cloud.battery.capacity=%s%n", batteryCap);

            // surface flinger dpi_x / dpi_y
            JSONObject screen = deviceInfo.getJSONObject("screen");
            if (screen != null) {
                int xdpi = (int) Math.round(screen.getDouble("xdpi", 0.0) * 1000);
                int ydpi = (int) Math.round(screen.getDouble("ydpi", 0.0) * 1000);
                pw.printf("persist.device_config.surface_flinger_native_boot.display.dpi_x=%d%n", xdpi);
                pw.printf("persist.device_config.surface_flinger_native_boot.display.dpi_y=%d%n", ydpi);

                int width = screen.getInt("widthPixels", 0);
                int height = screen.getInt("heightPixels", 0);
                pw.printf("persist.vendor.framebuffer.main=%dx%d@60%n", width, height);
            }

            // GPU renderer
            String glRenderer = Optional.ofNullable(deviceInfo.getJSONObject("gpu"))
                    .map(o -> o.getStr("gles20.renderer", ""))
                    .orElse("");
            pw.printf("persist.sys.cloud.gpu.gl_renderer=%s%n", glRenderer);

            // disk datasize
            String dataSize = Optional.ofNullable(deviceInfo.getJSONObject("disk"))
                    .map(o -> o.getStr("data.totalSize", ""))
                    .orElse("");
            pw.printf("ro.sys.cloud.disk.datasize=%s%n", dataSize);

            // uname 相关
            JSONObject uname = deviceInfo.getJSONObject("uname");
            if (uname != null) {
                pw.printf("ro.sys.cloud.uname.release=%s%n", uname.getStr("release", ""));
                pw.printf("ro.sys.cloud.uname.version=%s%n", uname.getStr("version", ""));
            }

        } catch (IOException e) {
            throw new RuntimeException("genAdbDebugProp 出错: " + e.getMessage(), e);
        }
    }


    /**
     * 对应 python gen_bootconfig()
     *   生成 bootconfig
     *   python: for key in props if key.startswith("ro.boot."): => androidboot.*
     */
    private static void genBootconfig(File debugRamdisk, File outDir, Map<String,String> propsMap) {

        // 1. 和 Python 相同的过滤列表
        final Set<String> NEED_FILTER_KEYS = new HashSet<>(Arrays.asList(
                "ro.boot.ddr_size",
                "ro.boot.ddr_info",
                "ro.boot.hardware.coo",
                "ro.boot.hardware.ddr",
                "ro.boot.hardware.ufs"
        ));

        File output = new File(outDir, "bootconfig");
        try (PrintWriter pw = new PrintWriter(output, StandardCharsets.UTF_8.name())) {

            // 可选：保证输出顺序一致（按 key 字典序）
            List<String> keys = new ArrayList<>(propsMap.keySet());
            Collections.sort(keys);

            for (String key : keys) {
                if (!key.startsWith("ro.boot.") || NEED_FILTER_KEYS.contains(key)) {
                    continue;   // 完全复刻 Python 的 continue 逻辑
                }
                String newKey = "androidboot." + key.substring("ro.boot.".length());
                pw.printf("%s = \"%s\"%n", newKey, propsMap.get(key));
            }
        } catch (IOException e) {
            throw new RuntimeException("genBootconfig 失败: " + e.getMessage(), e);
        }
    }


    /**
     * 对应 python gen_vcloud_settings()
     *   读取 features.txt, libraries.txt => 写到 vcloud_settings.prop
     */
    private static void genVcloudSettings(File debugRamdisk, File outDir) {
        // ---------- 1. 读取 features.txt ----------
        File featuresFile = new File(debugRamdisk, "features.txt");
        String featuresText;
        try {
            if (featuresFile.exists() && featuresFile.length() > 0) {
                featuresText = FileUtil.readString(featuresFile, StandardCharsets.UTF_8);
            } else {
                // 文件不存在或为空
                featuresText = "android.hardware.sensor.accelerometer";
            }
        } catch (Exception ex) {
            // 读取异常时回退默认值
            featuresText = "android.hardware.sensor.accelerometer";
        }

        // ---------- 2. 读取 libraries.txt ----------
        File librariesFile = new File(debugRamdisk, "libraries.txt");
        String librariesText;
        try {
            if (librariesFile.exists() && librariesFile.length() > 0) {
                librariesText = FileUtil.readString(librariesFile, StandardCharsets.UTF_8);
            } else {
                librariesText = "libandroid.so\nliblog.so";
            }
        } catch (Exception ex) {
            librariesText = "libandroid.so\nliblog.so";
        }

        // ---------- 3. 换行替换并二次默认值保障 ----------
        featuresText = featuresText == null ? "" : featuresText.trim();
        librariesText = librariesText == null ? "" : librariesText.trim();

        if (featuresText.isEmpty()) {
            featuresText = "android.hardware.sensor.accelerometer";
        }
        if (librariesText.isEmpty()) {
            librariesText = "libandroid.so\nliblog.so";
        }

        featuresText = featuresText.replace("\n", "\\n");
        librariesText = librariesText.replace("\n", "\\n");

        // ---------- 4. Wi‑Fi 扫描默认 JSON（与 Python 完全一致） ----------
        String wifiScanResult =
                "[{\"SSID\":\"mywifi\",\"BSSID\":\"02:00:00:00:00:00\",\"MAC\":\"02:00:00:00:00:00\",\"IP\":\"**************\",\"gateway\":\"*************\",\"DNS1\":\"*******\",\"DNS2\":\"*******\",\"hessid\":0,\"anqpDomainId\":0,\"capabilities\":\"\",\"level\":0,\"linkSpeed\":400,\"txLinkSpeed\":400,\"rxLinkSpeed\":400,\"frequency\":2413,\"distance\":-1,\"distanceSd\":-1,\"channelWidth\":0,\"centerFreq0\":-1,\"centerFreq1\":-1,\"is80211McRTTResponder\":true}]";

        // ---------- 5. 写入 vcloud_settings.prop ----------
        File output = new File(outDir, "vcloud_settings.prop");
        try (PrintWriter pw = new PrintWriter(output, StandardCharsets.UTF_8.name())) {
            pw.println("pm_list_features=" + featuresText);
            pw.println("pm_list_libraries=" + librariesText);
            pw.println("wifi.scanresults=" + wifiScanResult);
        } catch (IOException e) {
            throw new RuntimeException("genVcloudSettings 失败: " + e.getMessage(), e);
        }
    }


    /**
     * 对应 python gen_sensor_etc()
     *   device_info["sensor"]["allSensor"] => name=xx type=xx ...
     */
    @SuppressWarnings("unchecked")
    private static void genSensorEtc(JSONObject deviceInfo, File outDir) {
        File output = new File(outDir, "sensor_etc");
        try (PrintWriter pw = new PrintWriter(output, StandardCharsets.UTF_8.name())) {
            // 根据 adi_gen_auto_local.py, 先判断是否存在 deviceInfo["sensor"]["allSensor"]
            if (deviceInfo.containsKey("sensor")) {
                JSONObject sensor = deviceInfo.getJSONObject("sensor");
                if (sensor.containsKey("allSensor")) {
                    Object arr = sensor.get("allSensor");
                    if (arr instanceof Collection) {
                        for (Object item : (Collection<?>) arr) {
                            // 将 item 强制转换为 Map<String,Object>
                            if (item instanceof Map) {
                                Map<String, Object> map = (Map<String, Object>) item;
                                pw.println("name=" + map.getOrDefault("name", ""));
                                pw.println("type=" + map.getOrDefault("type", ""));
                                pw.println("vendor=" + map.getOrDefault("vendor", ""));
                                pw.println("version=" + map.getOrDefault("version", ""));

                                pw.println("typeString=" + map.getOrDefault("typeString", ""));
                                pw.println("maxRange=" + formatAsPythonDouble(map.getOrDefault("maxRange", "")));
                                pw.println("resolution=" + formatAsPythonDouble(map.getOrDefault("resolution", "")));
                                pw.println("power=" + map.getOrDefault("power", ""));
                                pw.println("maxDelay=" + map.getOrDefault("maxDelay", ""));
                                pw.println("minDelay=" + map.getOrDefault("minDelay", ""));
                                pw.println("fifoMaxEventCount=" + map.getOrDefault("fifoMaxEventCount", ""));
                                pw.println("fifoReservedEventCount=" + map.getOrDefault("fifoReservedEventCount", ""));
                                pw.println();
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("genSensorEtc失败: " + e.getMessage(), e);
        }
    }

    private static String formatAsPythonDouble(Object value) {
        if (value instanceof Number) {
            double d = ((Number) value).doubleValue();
            // 使用 String.format 保留与 Python 一致的科学计数法
            return String.format("%." + 15 + "e", d).replace("E", "e");
        }
        return String.valueOf(value);
    }

    /**
     * 复制单个文件
     */
    private static void copyFile(File srcDir, String filename, File destDir) {
        File src = new File(srcDir, filename);
        if (src.exists() && src.isFile()) {
            FileUtil.copy(src, new File(destDir, filename), true);
        }
    }

    /**
     * 创建/清空临时工作目录
     * 对应python: setup_clean_directories()
     */
    private static File setupWorkDirectories() {
        // python中: tmp_dir = /work_tmp, work_out_dir= /work_out_tmp, final_out_dir= /adi_tars ...
        // 这里只建1个随机目录
        String suffix = RandomUtil.randomString(8);

        // 优先使用配置的临时目录，如果未配置则使用系统默认临时目录
        String baseTempDirPath = System.getProperty("armcloud.adi.temp.dir");
        if (StringUtils.isBlank(baseTempDirPath)) {
            baseTempDirPath = System.getProperty("java.io.tmpdir");
        }

        File baseDir = new File(baseTempDirPath, "adi_work_" + suffix);
        if (baseDir.exists()) {
            FileUtil.clean(baseDir);
        } else {
            if (!baseDir.mkdirs()) {
                log.warn("无法创建临时目录: {}, 将尝试使用系统默认临时目录", baseDir.getAbsolutePath());
                // 尝试使用系统默认临时目录
                baseDir = new File(System.getProperty("java.io.tmpdir"), "adi_work_" + suffix);
                baseDir.mkdirs();
            }
        }
        log.info("创建ADI临时工作目录: {}", baseDir.getAbsolutePath());
        return baseDir;
    }

    private static Map<String, String> parseGetProp(File adbPropFile) throws IOException {
    /*
      Python:
      - 读取 adb_debug.prop
      - 正则匹配  [key]: [value]
      - props[key] = value
    */
        Map<String, String> props = new HashMap<>();

        // 读取文件内容
        String content = new String(java.nio.file.Files.readAllBytes(adbPropFile.toPath()), StandardCharsets.UTF_8);

        // 判断是否有[]格式的数据
        String regexWithBrackets = "\\[([^]]+)\\]: \\[([^]]+)\\]";
        Pattern patternWithBrackets = Pattern.compile(regexWithBrackets);
        Matcher matcherWithBrackets = patternWithBrackets.matcher(content);

        if (matcherWithBrackets.find()) {
            // 如果有 [] 格式的数据，走现有的逻辑
            // 使用正则匹配 [key]: [value1,value2]
            while (matcherWithBrackets.find()) {
                String key = matcherWithBrackets.group(1).trim();
                String value = matcherWithBrackets.group(2).trim();
                props.put(key, value);
            }
        } else {
            // 如果没有 [] 格式的数据，按换行读取并处理
            String[] lines = content.split("\n");
            for (String line : lines) {
                if (line.contains("=")) {
                    String[] parts = line.split("=", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();
                        props.put(key, value);
                    }
                }
            }
        }

        return props;
    }

    /**
     * 对应 python create_password_protected_zip()
     *   不同点：python用 pyzipper.WZ_AES, 这里用 Zip4j
     *   AES256 => setEncryptionMethod(EncryptionMethod.AES), setAesKeyStrength(EncryptionStrength)
     *debug_ramdisk、raw_input.zip
     * @param workDir 处理完毕的文件都在 workDir 下
     * @param parseRes 解析结果, 需读取 adb_debug.prop md5 => finalZipName
     */
    public static File createAESPasswordZip(File workDir, AdiParseVO parseRes) {
        // python中： 先对 adb_debug.prop 做 md5 => adi_md5 => final zip filename
        File adbDebugProp = new File(workDir, "adb_debug.prop");
        if (!adbDebugProp.exists()) {
            throw new BasicException(" adb_debug.prop文件不存在，无法计算MD5 " );
        }
        String fileMd5 = DigestUtil.md5Hex(FileUtil.readBytes(adbDebugProp));
        // python中 rand_pwd = generate_random_string(20), 这边也随机
        String randPwd = RandomUtil.randomString(20);

        // final zip => {md5}.zip
        File finalZip = new File(workDir, fileMd5 + ".zip");
        log.info("准备创建加密ZIP文件: {}", finalZip.getAbsolutePath());
        parseRes.setZipFile(finalZip.getAbsolutePath()); // 先设置路径
        if (finalZip.exists()) {
            log.warn("发现上次残留的旧ZIP文件，正在尝试删除: {}", finalZip.getAbsolutePath());
            if (!finalZip.delete()) {
                // 删除失败通常是因为文件被其他进程占用或权限问题
                log.error("删除旧的压缩文件失败: {}", finalZip.getAbsolutePath());
                throw new RuntimeException("无法删除旧的压缩文件，请检查文件占用或权限: " + finalZip.getAbsolutePath());
            }
            log.info("旧ZIP文件删除成功: {}", finalZip.getAbsolutePath());
        }

        List<File> filesToAdd = new ArrayList<>();
        for (File f : Objects.requireNonNull(workDir.listFiles())) {
            String name = f.getName();
            // 排除原始输入文件（不仅限于 raw_input.zip）和 debug_ramdisk 目录以及临时提取目录
            if ("debug_ramdisk".equals(name) || name.startsWith("raw_input.") || name.startsWith("temp_extract")) {
                continue;          // 跳过
            }
            filesToAdd.add(f);
        }

        try {
            // 文件不存在时，Zip4j 应该会在这里创建它并写入头部
            ZipFile zip = new ZipFile(finalZip, randPwd.toCharArray());
            zip.setPassword(randPwd.toCharArray()); // 通常也在这里设置密码

            ZipParameters parameters = new ZipParameters();
            parameters.setCompressionMethod(CompressionMethod.DEFLATE);
            parameters.setEncryptFiles(true); // 对添加到压缩包的文件启用加密
            parameters.setEncryptionMethod(EncryptionMethod.AES);
            parameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);

            for (File f : filesToAdd) {
                if (f.isDirectory()) {
                    zip.addFolder(f, parameters);
                } else {
                    zip.addFile(f, parameters);
                }
            }
            parseRes.setFinalZipPassword(randPwd);
            parseRes.setAdiSizeBytes(finalZip.length());

        } catch (ZipException e) {
            throw new RuntimeException("Zip4j压缩AES失败: " + e.getMessage(), e);
        }
        return finalZip;
    }

    /**
     * 对应 python: parse_uploaded_tar()
     *   1) 解压各种归档格式 (ZIP, TAR, RAR, etc.)
     *   2) 检查 debug_ramdisk/
     *   3) 必须文件校验 + 风险文件
     *   4) 解析 deviceInfo.txt => brand, model, fingerprint => 校验
     *   5) 生成/复制相关文件( adb_debug.prop, bootconfig, sensor_etc... ) (python中是分函数)
     *   6) 计算 MD5
     */
    private static AdiParseVO parseUploadedArchive(File rawArchiveFile, File workDir) throws IOException {
        AdiParseVO result = new AdiParseVO();
        result.setPackageType(1);
        result.setSuccess(false);
        // ========== 1) 解压 ==========
        File debugRamdiskDir = new File(workDir, "debug_ramdisk");
        try {
            // 根据文件扩展名选择不同的解压方法
            extractArchive(rawArchiveFile, workDir);
        } catch (Exception e) {
            result.setMessage("解压文件失败！");
            log.error("解压文件失败！",e);
            return result;
        }
        if (!debugRamdiskDir.exists() || !debugRamdiskDir.isDirectory()) {
            result.setMessage("缺少debug_ramdisk目录");
            return result;
        }

        // ========== 2) 必须文件校验 + 风险项 ==========
        Set<String> filesInDebug = Arrays.stream(Optional.ofNullable(debugRamdiskDir.listFiles()).orElse(new File[0]))
                .filter(File::isFile)
                .map(File::getName)
                .collect(Collectors.toSet());

        // 检查 8个固定文件
        for (String req : REQUIRED_FILES) {
            if (!filesInDebug.contains(req)) {
                result.setMessage("缺少"+req+"文件");
                return result;
            }
        }
        // cpu 是目录 => 单独检查
        File cpuDir = new File(debugRamdiskDir, "cpu");
        if (!cpuDir.exists() || !cpuDir.isDirectory()) {
            result.setMessage("缺少cpu目录");
            return result;
        }

        // 检测多余文件 => 风险
        Set<String> extraFiles = new HashSet<>(filesInDebug);
        extraFiles.removeAll(REQUIRED_FILES);
        result.setHasRiskItems(!extraFiles.isEmpty());
        if (result.getHasRiskItems()) {
            result.setRiskFileList(new ArrayList<>(extraFiles));
        }

        // ========== 3) 解析 deviceInfo.txt ==========
        File deviceInfo = new File(debugRamdiskDir, "deviceInfo.txt");
        String deviceInfoContent = FileUtil.readString(deviceInfo, StandardCharsets.UTF_8);
        JSONObject deviceInfoJson = JSONUtil.parseObj(deviceInfoContent);
        JSONObject props = deviceInfoJson.getJSONObject("props");
        if (props == null) {
            result.setMessage("deviceInfo.props为空");
            return result;
        }
        // brand
        String brand = props.getStr("ro.product.brand");
        // model
        String model = props.getStr("ro.product.model");
        // fingerprint
        String fingerprint = props.getStr("ro.build.fingerprint");
        // python： verified_bootstate= deviceInfoObj["props"]["ro.boot.verifiedbootstate"]
        String verifiedBootState = props.getStr("ro.boot.verifiedbootstate");
        // build_tags
        String buildTags = props.getStr("ro.build.tags");
        // build_type
        String buildType = props.getStr("ro.build.type");

        Map<String,String> propsMap = parseGetProp(new File(debugRamdiskDir, "adb_debug.prop"));
        String androidVersion = propsMap.get("ro.build.version.release");
        String deviceName = propsMap.get("ro.product.marketname");

        //获取屏幕信息
        JSONObject screen = deviceInfoJson.getJSONObject("screen");
        if (screen == null) {
            result.setMessage("deviceInfo.screen为空");
            return result;
        }
        String screenWidth = screen.getStr("widthPixels");
        String screenHeight = screen.getStr("heightPixels");
        String densityDpi = screen.getStr("densityDpi");

        // python中的判断：
        // if verified_bootstate != "green": fail
        // if build_tags != "release-keys": fail
        // if build_type != "user": fail
        if(StringUtils.isBlank(verifiedBootState)){
            result.setMessage("verifiedbootstate为空");
            return result;
        }else if (!"green".equalsIgnoreCase(verifiedBootState)) {
            //print("current record verifiedbootstate[%s] not ok "%(verified_bootstate))
            result.setMessage("字段verifiedbootstate[值"+verifiedBootState+"]不符合要求");
            return result;
        }
        if(StringUtils.isBlank(buildTags)){
            result.setMessage("verifiedbootstate为空");
            return result;
        }else if (!"release-keys".equals(buildTags)) {
            // print("current record build_tags[%s] not ok " % (build_tags))
            result.setMessage("字段verifiedbootstate[值"+buildTags+"]不符合要求");
            return result;
        }
        if(StringUtils.isBlank(buildType)){
            result.setMessage("verifiedbootstate为空");
            return result;
        }else if (!"user".equalsIgnoreCase(buildType)) {
            result.setMessage("字段verifiedbootstate[值"+buildType+"]不符合要求");
            return result;
        }

        // 计算 fingerprint_md5
        String fingerprintMd5 = DigestUtil.md5Hex(fingerprint);

        // ========== 4) 生成/复制文件(对应 python gen_* 系列函数) ==========
        try {
            // gen_adb_debug_prop()
            genAdbDebugProp(debugRamdiskDir, workDir, propsMap, deviceInfoJson);

            // gen_bootconfig()
            genBootconfig(debugRamdiskDir, workDir, propsMap);

            // 复制 cpuinfo, libc.so, cpu 目录
            copyFile(debugRamdiskDir, "cpuinfo", workDir);
            copyFile(debugRamdiskDir, "libc.so", workDir);

            // 直接复制cpu目录
            File cpuSrcDir = new File(debugRamdiskDir, "cpu");
            if (cpuSrcDir.exists() && cpuSrcDir.isDirectory()) {
                // 创建目标CPU目录
                File cpuDestDir = new File(workDir, "cpu");
                if (!cpuDestDir.exists()) {
                    cpuDestDir.mkdirs();
                }

                // 遍历获取CPU目录下的CPU核心目录（cpu0, cpu1, ...）
                File[] cpuCores = cpuSrcDir.listFiles();
                if (cpuCores != null) {
                    for (File cpuCore : cpuCores) {
                        if (cpuCore.isDirectory()) {
                            // 为每个CPU核心创建对应目录
                            File cpuCoreDestDir = new File(cpuDestDir, cpuCore.getName());
                            if (!cpuCoreDestDir.exists()) {
                                cpuCoreDestDir.mkdirs();
                            }

                            // 复制CPU核心目录下的文件，跳过符号链接
                            File[] cpuCoreFiles = cpuCore.listFiles();
                            if (cpuCoreFiles != null) {
                                for (File cpuCoreFile : cpuCoreFiles) {
                                    try {
                                        // 忽略符号链接文件
                                        if (cpuCoreFile.isFile() && !java.nio.file.Files.isSymbolicLink(cpuCoreFile.toPath())) {
                                            FileUtil.copy(cpuCoreFile, new File(cpuCoreDestDir, cpuCoreFile.getName()), true);
                                        } else if (cpuCoreFile.isDirectory()) {
                                            // 对于子目录，使用FileUtil.copy方法并设置递归标志
                                            File targetDir = new File(cpuCoreDestDir, cpuCoreFile.getName());
                                            if (!targetDir.exists()) {
                                                targetDir.mkdirs();
                                            }
                                            FileUtil.copy(cpuCoreFile, targetDir, true);
                                        }
                                    } catch (Exception e) {
                                        log.warn("复制CPU文件时出错，已跳过: {}, 原因: {}", cpuCoreFile.getAbsolutePath(), e.getMessage());
                                    }
                                }
                            }
                        } else if (cpuCore.isFile()) {
                            // 直接复制CPU目录下的文件
                            try {
                                FileUtil.copy(cpuCore, new File(cpuDestDir, cpuCore.getName()), true);
                            } catch (Exception e) {
                                log.warn("复制CPU文件时出错，已跳过: {}, 原因: {}", cpuCore.getAbsolutePath(), e.getMessage());
                            }
                        }
                    }
                }
                log.debug("成功复制cpu目录到: {}", cpuDestDir.getAbsolutePath());
            } else {
                log.warn("源cpu目录不存在: {}", cpuSrcDir.getAbsolutePath());
            }

            // gen_vcloud_settings()
            genVcloudSettings(debugRamdiskDir, workDir);

            // gen_sensor_etc()
            genSensorEtc(deviceInfoJson, workDir);

        } catch (Exception e) {
            result.setMessage("生成文件时出错！");
            log.error("生成文件时出错！",e);
            return result;
        }

        // ========== 5) python最后还会 md5 adb_debug.prop, 生成 zip ==========
        // 这里只记录指纹信息
        result.setSuccess(true);
        result.setBrand(brand);
        result.setModel(model);
        result.setFingerprint(fingerprint);
        result.setFingerprintMd5(fingerprintMd5);
        // 安全解析 androidVersion
        if (androidVersion != null && !androidVersion.isEmpty()) {
            try {
                result.setAndroidImageVersion(Integer.parseInt(androidVersion));
                result.setAospVersion("android"+androidVersion);
            } catch (NumberFormatException e) {
                // 如果不是有效数字，设置默认值或记录错误
                log.warn("Invalid androidVersion format: {}", androidVersion);
                result.setAndroidImageVersion(0); // 设置默认值
                result.setAospVersion(androidVersion);
            }
        } else {
            // androidVersion为空的情况
            result.setAndroidImageVersion(0);
            result.setAospVersion("unknown");
        }
        result.setScreenWidth(screenWidth);
        result.setScreenHeight(screenHeight);
        result.setScreenDensity(densityDpi);
        result.setDeviceName(StringUtils.isBlank(deviceName)?model:deviceName);
//        机型标识取值逻辑：
//        优先获取ro.product.brand+ro.product.model+ro.product.marketname
//        如果ro.product.marketname不存在则：ro.product.brand+ro.product.model
        result.setModelCode(brand + "_" + model + (StringUtils.isNotBlank(deviceName) ? "_" + deviceName : ""));
        result.setWorkDir(workDir.getAbsolutePath());
        return result;
    }

    /**
     * 解析已生成的ADI包
     * 生成后的解压后包括以下目录和文件:
     * cpu
     * adb_debug.prop
     * bootconfig
     * cpuinfo
     * libc.so
     * sensor_etc
     * vcloud_settings.prop
     *
     * @param rawArchiveFile 压缩包
     * @param workDir 工作目录
     * @return 解析结果
     */
    public static AdiParseVO parseGeneratedAdiArchive(File rawArchiveFile, File workDir) {
        AdiParseVO result = new AdiParseVO();
        result.setSuccess(false);
        result.setPackageType(2);
        try {
            // 解压文件到工作目录
            extractArchive(rawArchiveFile, workDir);
        } catch (Exception e) {
            result.setMessage("解压文件失败");
            log.error("解压文件失败",e);
            return result;
        }
        
        // 检查生成后的ADI包必要文件和目录
        List<String> requiredGenFiles = Arrays.asList(
                "adb_debug.prop", "bootconfig", "cpuinfo", "libc.so", 
                "vcloud_settings.prop","sensor_etc");
        
        // 检查必要文件
        for (String req : requiredGenFiles) {
            File file = new File(workDir, req);
            if (!file.exists()) {
                result.setMessage("缺少"+req+"文件: ");
                return result;
            }
        }
        
        // 检查必要目录
        File cpuDir = new File(workDir, "cpu");
        if (!cpuDir.exists() || !cpuDir.isDirectory()) {
            result.setMessage("缺少cpu目录");
            return result;
        }

        // 解析 adb_debug.prop 获取关键信息
        File adbDebugPropFile = new File(workDir, "adb_debug.prop");
        Map<String, String> propsMap;
        try {
            propsMap = parseGetProp(adbDebugPropFile);
        } catch (IOException e) {
            result.setMessage("生成文件时出错！");
            log.error("解压文件失败",e);
            return result;
        }
        
        // 从 adb_debug.prop 中提取必要信息
        String brand = propsMap.get("ro.product.brand");
        String model = propsMap.get("ro.product.model");
        String fingerprint = propsMap.get("ro.build.fingerprint");
        String androidVersion = propsMap.get("ro.build.version.release");
        String deviceName = propsMap.get("ro.product.marketname");
        
        // 验证必要字段
        if (StringUtils.isBlank(brand)) {
            result.setMessage("brand为空");
            return result;
        }
        if (StringUtils.isBlank(model)) {
            result.setMessage("model为空");
            return result;
        }
        if (StringUtils.isBlank(fingerprint)) {
            result.setMessage("fingerprint为空");
            return result;
        }
        if (StringUtils.isBlank(androidVersion)) {
            result.setMessage("androidVersion为空");
            return result;
        }
        // 安全解析 androidVersion
        try {
            result.setAndroidImageVersion(Integer.parseInt(androidVersion));
            result.setAospVersion("android" + androidVersion);
        } catch (NumberFormatException e) {
            log.warn("Invalid androidVersion format: {}", androidVersion);
            result.setMessage("androidVersion解析失败："+androidVersion);
            return result;
        }

        // 计算 fingerprint_md5
        String fingerprintMd5 = DigestUtil.md5Hex(fingerprint);

        // 设置结果
        result.setSuccess(true);
        result.setBrand(brand);
        result.setModel(model);
        result.setFingerprint(fingerprint);
        result.setFingerprintMd5(fingerprintMd5);
        result.setDeviceName(StringUtils.isBlank(deviceName) ? model : deviceName);
        result.setModelCode(brand + "_" + model + (StringUtils.isNotBlank(deviceName) ? "_" + deviceName : ""));
        result.setWorkDir(workDir.getAbsolutePath());
        return result;
    }

    /**
     * 检查解压后的内容，判断是原始包还是生成后的包
     * @param workDir 解压后的目录
     * @return 1=原始包, 2=生成后的包, 0=无法识别
     */
    public static int detectAdiPackageType(File workDir) {
        if (new File(workDir, "debug_ramdisk").exists()) {
            return 1; // 原始包
        }
        
        // 检查生成后的包特征
        List<String> generatedFileMarkers = Arrays.asList(
                "adb_debug.prop", "bootconfig", "cpuinfo", "libc.so", 
                "vcloud_settings.prop", "cpu", "sensor_etc");
        
        int foundMarkers = 0;
        for (String marker : generatedFileMarkers) {
            if (new File(workDir, marker).exists()) {
                foundMarkers++;
            }
        }
        
        // 如果找到了大部分特征文件，认为是生成后的包
        if (foundMarkers >= 5) {
            return 2; // 生成后的包
        }
        
        return 0; // 无法识别
    }

    /**
     * 解析ADI文件包，支持原始包和生成后的包
     * @param file 上传的文件
     * @param checkType 是否需要检查类型 (true=自动检测类型，false=按照原始包处理)
     * @return 解析结果
     */
    public static AdiParseVO parseAdiFile(MultipartFile file, boolean checkType) {
        AdiParseVO result = new AdiParseVO();
        File workDir;

        try {
            if (file == null || file.isEmpty()) {
                result.setSuccess(false);
                result.setMessage("上传文件为空");
                return result;
            }

            // 创建临时工作目录
            workDir = setupWorkDirectories();
            log.info("创建临时工作目录: {}", workDir.getAbsolutePath());

            // 将用户上传的流保存为原始文件，保留原始扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename != null ? originalFilename : "");
            // 如果无法确定扩展名，使用文件内容前几个字节判断类型
            if (fileExtension.isEmpty()) {
                byte[] header = new byte[8];
                try (InputStream is = file.getInputStream()) {
                    is.read(header, 0, Math.min(8, (int)file.getSize()));
                }
                // 简单判断常见压缩格式的文件头
                if (header[0] == 'P' && header[1] == 'K') {
                    fileExtension = "zip"; // ZIP格式的文件头为PK
                } else if (header[0] == 0x1F && header[1] == (byte)0x8B) {
                    fileExtension = "tar.gz"; // GZIP格式的文件头
                } else if (header[0] == 'R' && header[1] == 'a' && header[2] == 'r') {
                    fileExtension = "rar"; // RAR格式的文件头
                } else {
                    fileExtension = "bin"; // 无法识别，使用通用后缀
                }
                log.info("通过文件头识别文件类型: {}", fileExtension);
            }

            // 使用原始扩展名保存文件
            File rawFile = new File(workDir, "raw_input." + fileExtension);
            file.transferTo(rawFile);

            if (!rawFile.exists() || rawFile.length() == 0) {
                result.setSuccess(false);
                result.setMessage("生成文件时出错！");
                return result;
            }

            log.info("保存上传文件到: {}, 大小: {} 字节, 类型: {}", rawFile.getAbsolutePath(), rawFile.length(), fileExtension);

            // 先解压文件以检测包类型
            if (checkType) {
                try {
                    // 创建临时目录用于检测
                    File tempDir = new File(workDir, "temp_extract");
                    tempDir.mkdirs();
                    extractArchive(rawFile, tempDir);
                    
                    // 检测包类型
                    int packageType = detectAdiPackageType(tempDir);
                    // 清理临时目录
                    FileUtil.clean(tempDir);
                    
                    // 根据类型选择解析方法
                    if (packageType == 1) {
                        // 原始包 - 使用原有解析方法
                        log.info("检测到原始ADI包，使用原始解析逻辑");
                        return parseUploadedArchive(rawFile, workDir);
                    } else if (packageType == 2) {
                        // 生成后的包 - 使用新的解析方法
                        log.info("检测到生成后的ADI包，使用生成后解析逻辑");
                        return parseGeneratedAdiArchive(rawFile, workDir);
                    } else {
                        // 无法识别
                        result.setSuccess(false);
                        result.setMessage("无法识别的ADI包类型，请确保上传正确的ADI包");
                        return result;
                    }
                } catch (Exception e) {
                    log.error("检测ADI包类型失败", e);
                    // 如果检测失败，回退到原始解析方法
                    return parseUploadedArchive(rawFile, workDir);
                }
            } else {
                // 不检查类型，直接使用原始解析方法
                return parseUploadedArchive(rawFile, workDir);
            }
        } catch (Exception e) {
            log.error("解析ADI文件出现异常", e);
            result.setSuccess(false);
            result.setMessage("解析ADI文件出现异常: " + e.getMessage());
            return result;
        }
    }

}
