package net.armcloud.paascenter.openapi.model.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/26 14:18
 * @Description: 算力使用详情
 */
@Data
public class NetStorageComputeVO implements Serializable {
    private static final long serialVersionUID = 1L; // 版本号，建议修改为唯一值


    /**
     * 已使用算力数量
     */
    private Long onNumber = 0L;

    /**
     * 总算力数量
     */
    private Long totalNumber = 0L;

    /**
     * 算力规格
     */
    private String deviceLevel;
}
