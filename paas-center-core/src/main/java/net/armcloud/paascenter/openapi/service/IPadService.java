package net.armcloud.paascenter.openapi.service;

import net.armcloud.paascenter.common.client.internal.dto.*;
import net.armcloud.paascenter.common.client.internal.vo.*;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.OptimizedPage;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.dto.api.*;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import net.armcloud.paascenter.common.model.vo.api.AsyncCmdVO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;
import net.armcloud.paascenter.common.model.vo.api.SyncCmdVO;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.dto.PadListOptimizedDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageNetWorkOffDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResPadBackupDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResPadDeleteDTO;
import net.armcloud.paascenter.openapi.model.vo.*;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageCreateVO;

import javax.validation.Valid;
import java.util.List;

public interface IPadService {
    /**
     * 检查Pad是否属于该客户
     *
     * @return Boolean
     */
    Boolean checkPadListOwnerService(Long customerId, List<String> padCodes);

    /**
     * 验证pad是否属于该客户且设备在线
     * @param customerId
     * @param padCodes
     * @param skipVerifyingTheOff 跳过离线状态验证
     * @return 如果实例不属于商户或者板卡离线,抛出异常
     */
    Boolean checkPadListAndDeviceOnlineOwnerService(Long customerId, List<String> padCodes,boolean skipVerifyingTheOff);

    /**
     * 本地截图
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> screenshotLocal(ScreenshotLocalDTO param);

    /**
     * 生成预览图
     */
    List<GeneratePreviewVO> generatePreview(ScreenshotLocalDTO param);

    /**
     * 获取长效预览图URL
     */
    List<LongPreviewVO> getLongGenerateUrl(LongPreviewDTO param);

    /**
     * 执行重启云机任务
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> restartService(RestartDTO param);

    /**
     * 执行重置云机任务
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> restService(ResetDTO param);

    /**
     * 异步执行命令
     *
     * @param param param
     * @return 任务id
     */
    List<AsyncCmdVO> asyncCmd(ExecuteADBDTO param);

    /**
     * 上传文件
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> uploadFile(PadDownloadFileDTO param);

    List<GeneratePadTaskVO> uploadFileV3(PadDownloadFileV3DTO param);

    List<GeneratePadTaskVO> uploadFileV2(PadDownloadFileV2DTO param);

    /**
     * 执行同步命令
     *
     * @return 命令结果内容
     */
    List<SyncCmdVO> syncCmd(SyncCmdDTO param, SourceTargetEnum sourceTarget);

    /**
     * 查询已安装应用列表
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> listApp(PadCodesDTO param);

    /**
     * 批量修改实例的wifi属性
     * @param param
     * @return
     */
    List<GeneratePadTaskVO> setWifiList(SetWifiListDTO param);

    /**
     * 实例属性
     *
     * @param param PadPropertiesDTO
     * @return PadPropertiesVO
     */
    PadPropertiesVO padPropertiesService(PadPropertiesDTO param);

    /**
     * 批量查询实例属性
     *
     * @param param BatchPadPropertiesDTO
     * @return List<PadPropertiesVO>
     */
    List<PadPropertiesVO> batchPadProperties(BatchPadPropertiesDTO param);

    /**
     * 更新实例属性
     *
     * @param param UpdatePadPropertiesDTO
     * @return Long 任务id
     */
    List<GeneratePadTaskVO> updatePadPropertiesService(UpdatePadPropertiesDTO param);

    /**
     * 批量一键新机
     *
     * @param param newPads
     * @return Long 任务id
     */
    List<GeneratePadTaskVO> newPads(NewPadsDTO param);

    /**
     * 实例详情
     *
     * @param param PadDetailsDTO
     * @return Long 任务id
     */
    Page<PadDetailsVO> padDetailsService(PadDetailsDTO param);

    /**
     * @param padCodes     实例编号集合
     * @param streamStatus 推流状态
     * @return Boolean
     */
    Boolean updatePadStreamStatusService(List<String> padCodes, Integer streamStatus);

    /**
     * 根据客户编号和实例查询实例信息
     *
     * @param padCustomerDTO padCustomerDTO
     * @return Pad
     */
    Pad getPadInfoByCustomerId(PadCustomerDTO padCustomerDTO);

    /**
     * 实例列表信息
     *
     * @param param PadListDTO
     * @return PadListVO
     */
    Page<PadListVO> padListService(PadListDTO param);

    /**
     * 实例列表信息（优化分页版本）
     *
     * @param param PadListOptimizedDTO
     * @return OptimizedPage<PadListVO>
     */
    OptimizedPage<PadListVO> padListOptimizedService(PadListOptimizedDTO param);

    /**
     * 根据云厂商实例信息查询pad实例
     */
    Pad getByCloudVendorTypeAndPadOutCode(GetPadByCloudVendorDTO dto);

    /**
     * 根据实例编号查询实例状态
     *
     * @param padCode 例编号
     * @return PadInfoVO
     */
    PadInfoVO getPadInfoByCode(String padCode);

    /**
     * 获取空闲实例列表
     *
     * @return
     */
    List<PadIdleListVO> padIdleListVOService();

    List<Pad> listAll();

//    List<ConsoleDcInfoVO> getDcIdGroupByPadCodes(List<String> padCodes);

    /**
     * 根据ip查询pad
     *
     * @param padIp 实例Ip
     * @return Pad
     */
    Pad getPadByOutCodeAndIp(String padOutCode, String padIp);

    /**
     * 批量获取实例机型信息
     *
     * @param param
     * @return
     */
    List<PadModelInfoVO> PadModelInfo(PadModelInfoDTO param);

    /**
     * 执行云机升级镜像任务
     *
     * @param param
     * @return
     */
    List<GeneratePadTaskInfoVO> upgradeImageService(UpgradeImageDTO param, SourceTargetEnum sourceTarget);

    /**
     * 根据padCode查询pad信息
     *
     * @param padCode
     * @return
     */
    Pad getPadByPadCode(String padCode);

    /**
     * 根据padCode查询pad信息
     *
     * @param padCodeList
     * @return
     */
    List<Pad> getPadListByPadCode(List<String> padCodeList);

    /**
     * 创建云机
     *
     * @param param
     * @param resourceSpecification
     * @param screenLayout
     * @param device
     * @param ips
     * @return
     */
    List<Pad> extractedPad(VirtualizeDeviceDTO param, ResourceSpecification resourceSpecification, ScreenLayout screenLayout, VirtualizeDeviceInfoVO device, List<String> ips);

    /**
     * 获取客户黑名单列表
     *
     * @param param
     * @return
     */
    BlacklistVO getBlacklistService(AppBlacklistDTO param);

    /**
     * 触发黑名单列表
     *
     * @param param
     * @return
     */
    List<GeneratePadTaskVO> triggeringBlackListService(TriggeringBlackDTO param);

    /**
     * 实例限速
     *
     * @param param LimitBandwidthDTO
     * @return 任务信息
     */
    List<GeneratePadTaskVO> limitBandwidthService(LimitBandwidthDTO param);

    /**
     * 更新实例限速
     *
     * @param param PadBandwidthDTO
     * @return Boolean
     */
    Boolean updatePadBandwidthService(PadBandwidthDTO param);

    /**
     * 更新实例时区
     */
    List<GeneratePadTaskVO> updateTimeZone(UpdatePadTimeZoneDTO param);

    /**
     * 更新实例语言
     */
    List<GeneratePadTaskVO> updateLanguage(UpdatePadLanguageDTO param);

    /**
     * 更新实例SIM卡信息
     */
    List<GeneratePadTaskVO> updateSIM(UpdatePadSIMDTO param);


    /**
     * 设置经纬度
     *
     * @param param
     * @return
     */
    Boolean gpsInjectInfo(GpsInjectInfoDTO param);

    List<PadEdgeClusterVO> listPadEdgeClusterInfo(PadCodesDTO padCodesDTO);

    List<GetStreamTypeVO> getStreamType(PadCodesDTO param);

    /**
     * 修改实例安卓改机属性
     */
    GeneratePadTaskVO updatePadAndroidProp(UpdatePadAndroidPropDTO param);

    /**
     * 一键新机
     * @param param
     * @return
     */
    List<GeneratePadTaskVO> replacePad(ReplacePadTaskDTO param, SourceTargetEnum sourceTargetEnum);

    List<GeneratePadTaskInfoVO> virtualRealSwitchUpgradeImageService(VirtualRealSwitchUpgradeImageDTO param);

    /**
     * 替换真机adb模板
     * @param param
     * @return
     */
    List<GeneratePadTaskInfoVO> replaceRealAdbTemplate(ReplaceRealAdbTemplateDTO param,SourceTargetEnum sourceTargetEnum);


    Boolean updatePadTypeService(PadTypeDTO padTypeDTO);

    /**
     * 修改实例属性
     * @param param
     * @return
     */
    GeneratePadTaskVO modifyPadProperties(ModifyPadInformationDTO param, SourceTargetEnum sourceTargetEnum);

    /**
     * 修改实例布局编码
     * @param param
     * @return
     */
    Integer updatePadLayoutCode(PadLayoutCodeDto param);
    /**
     * 验证实例是否处于数据恢复/数据备份状态
     *
     * @param padCodes 实例列表
     * @throws BasicException 实例处于数据恢复/数据备份状态则抛出此异常
     */
    void verifyPadBackupAndRestoreStatus(List<String> padCodes) throws BasicException;

    /**
     * 验证板卡上的实例是否处于数据恢复/数据备份状态
     */
    void verifyPadBackupAndRestoreStatusByIps(List<String> deviceIps) throws BasicException;

    /**
     * 开启关闭ADB
     * @param param
     * @param paas
     * @return
     */
    List<GeneratePadTaskInfoVO> openOnlineAdb(OpenOnlineAdbPadTaskDTO param, SourceTargetEnum paas);

    /**
     * 触发白名单列表
     *
     * @param param
     * @return
     */
    List<GeneratePadTaskVO> triggeringWhiteListService(TriggeringBlackDTO param);

    /**
     * 获取客户白名单列表
     *
     * @param param
     * @return
     */
    WhitelistVO getWhitelistService(AppBlacklistDTO param);

    /**
     * 修改pad表对应adb状态
     * @param padCode
     * @param adbOpenStatus
     */
    void updatePadAdbOpenStatus(List<String> padCode, Integer adbOpenStatus);

    /**
     * 查询那些实例存在当前指令任务，待执行或执行中
     */
    List<String>  existInstructionPadCode(ExistInstructionPadCodeDTO param);


    /**
     * 根据国家随机生成安卓属性并设置到实例上
     */
    GeneratePadTaskVO replacePadAndroidPropByCountry(ReplacePadAndroidPropDTO param);

    /**
     * 更新通讯录
     */
    List<GeneratePadTaskVO> updateContacts(UpdateContactsDTO param);

    void updatePadDns(String padCode, String dns);

    /**
     * 创建网存实例
     * @param param
     * @param sourceTargetEnum
     * @return
     */
    List<NetStorageCreateVO> virtualizeNetStorageRes(NetWorkVirtualizeDTO param, SourceTargetEnum sourceTargetEnum);

    List<GeneratePadTaskVO> netStorageResBootOn(NetWorkOnDTO param, SourceTargetEnum adminSystem);

    List<GeneratePadTaskVO> netStorageResBootOff(NetWorkOffDTO param, SourceTargetEnum adminSystem);

    List<GeneratePadTaskVO> netStorageResDelete(NetWorkDeleteDTO param, SourceTargetEnum paas);

    String netStorageResMoreCompatible(NetStorageResMoreCompatiblePaasDTO param,SourceTargetEnum sourceTargetEnum);

    /**
     * 解除实例跟板卡的绑定关系
     * @param param
     * @return
     */
    String unbindTheCardInformation(PadStatusDTO param);

    /**
     * 删除实例跟网存相关信息
     * @param param
     * @return
     */
    String deletePadInformation(PadStatusDTO param);

    /**
     * 获取网存可用容量
     * @param param
     * @return
     */
    StorageCapacityDetailVO getDetailStorageCapacityAvailable(NetStorageResDetailDTO param);

    /**
     * 获取网存可用容量
     * @param param
     * @return
     */
    List<StorageCapacityDetailVO> getDetailStorageCapacityAvailableList(NetStorageResDetailDTO param);
    /**
     * 获取网存实例使用详情
     * @param param
     */
    List<NetPadDeviceVO> groupNetPadByDeviceLevel(NetStorageResDetailDTO param);

    /**
     * 网存备份
     * @param param
     * @param paas
     * @return
     */

    List<GeneratePadTaskVO> netStoragePadBackup(NetStorageResPadBackupDTO param, SourceTargetEnum paas);

    GeneratePadTaskVO netStorageResSpecifiedCodeBootOn(NetStorageNetWorkOffDTO param, SourceTargetEnum paas);


    List<GeneratePadTaskVO> netStoragePadDelete(NetStorageResPadDeleteDTO param, SourceTargetEnum paas);

    List<GeneratePadTaskVO> simulateTouch(SimulateTouchDTO param);

    List<GeneratePadTaskVO> inputText(InputTextDTO param);

    List<GeneratePadTaskVO> addPhoneRecord(CallRecordsDTO param);

    void updatePadRealPhoneTemplate(String padCode, Long realPhoneTemplateId);

    List<GeneratePadTaskVO> resetGAID(ResetGaidDTO param);

    /**
     * 注入音频到实例麦克风
     * @param param
     * @return
     */
    List<GeneratePadTaskVO> injectAudioToMic(AudioToMicDTO param);

    void checkIp(String padCode, String useIp);

    List<GeneratePadTaskVO> unbindStatusAndResource(long andVerifyUserId, List<String> padCodes);
}