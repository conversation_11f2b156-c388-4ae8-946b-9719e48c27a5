package net.armcloud.paascenter.openapi.model.bo;

import lombok.Data;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;

import java.util.List;

@Data
public class AddBackupTaskBO {
    private Long customerId;

    private List<Pad> pads;

    private SourceTargetEnum taskSource;

    @Data
    public static class Pad {
        private String padCode;
        private String backupName;
        private Long deviceId;
        private String specificationCode;
    }
}
