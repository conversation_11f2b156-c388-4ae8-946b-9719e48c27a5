package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePadLanguageDTO extends PadCodesDTO {
    @NotBlank(message = "language cannot null")
    private String language;

    private String country;
}
