package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.api.PadUninstallAppFileDTO;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class UnInstallApp {
    @Size(min = 1, message = "实例数量不少于1个")
    @NotNull(message = "实例不能为空")
    @Valid
    List<PadUninstallAppFileDTO> apps;
}
