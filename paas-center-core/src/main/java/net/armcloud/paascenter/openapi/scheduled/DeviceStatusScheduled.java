package net.armcloud.paascenter.openapi.scheduled;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import net.armcloud.paascenter.cms.model.request.InstanceLifecycleStatusRequest;
import net.armcloud.paascenter.cms.model.response.InstanceLifecycleStatusResponse;
import net.armcloud.paascenter.common.client.internal.feign.ContainerPadFeignClient;
import net.armcloud.paascenter.common.client.internal.utils.ContainerFeignUtils;
import net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo;
import net.armcloud.paascenter.common.utils.FeignUtils;
import net.armcloud.paascenter.openapi.service.IDeviceChangeInfoService;
import net.armcloud.paascenter.openapi.service.IDeviceService;
import net.armcloud.paascenter.common.model.entity.paas.DeviceChangeInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DeviceStatusScheduled {

    @Resource
    private IDeviceService iDeviceService;
    @Resource
    private IDeviceChangeInfoService iDeviceChangeInfoService;

    @Resource
    private ContainerPadFeignClient containerPadFeignClient;
    /**
     * 定时更新板卡的挂载方式
     */
    //@Scheduled(fixedDelay = 30 * 60 * 1000)
    public void DeviceStatusGet(){
        long logSysCurrent = System.currentTimeMillis();
        log.info("DeviceStatusScheduled.DeviceStatusGet start. logSysCurrent:{} ",logSysCurrent);
        try {
            List<DeviceInfoVo> deviceIpList = iDeviceService.getDeviceMountVersionV1();
            if(!CollectionUtils.isNotEmpty(deviceIpList)){
                log.info("DeviceStatusScheduled.DeviceStatusGet run. deviceIpList result is empty ");
                return ;
            }
            //根据公网IP分组
            Map<String, List<DeviceInfoVo>> groupedByClusterPublicIp = deviceIpList.stream()
                    .collect(Collectors.groupingBy(DeviceInfoVo::getClusterPublicIp));

            groupedByClusterPublicIp.forEach((key, value) -> {
                URI host = ContainerFeignUtils.builderHost(key);
                List<List<DeviceInfoVo>> partitionedLists = Lists.partition(value, 50);
                partitionedLists.forEach(ipList-> {
                    List<DeviceChangeInfo>  resultList = requestCms(ipList,host);
                    if(CollectionUtils.isNotEmpty(resultList)){
                        resultList.forEach(deviceChangeInfo -> {
                            DeviceChangeInfo existingRecord = iDeviceChangeInfoService.getOne(
                                    new QueryWrapper<DeviceChangeInfo>().eq("pad_code", deviceChangeInfo.getPadCode())
                            );
                            if(Objects.isNull(existingRecord)) {
                                iDeviceChangeInfoService.save(deviceChangeInfo);
                                return;
                            };
                            deviceChangeInfo.setId(existingRecord.getId());
                            iDeviceChangeInfoService.updateById(deviceChangeInfo);

                        });


                    }
                });
            });
        }catch (Exception e){
            log.error("DeviceStatusGet error",e);
        }
        log.info("DeviceStatusScheduled.DeviceStatusGet end. logSysCurrent:{} ",logSysCurrent);

    }

    /**
     * 查询CMS获取板卡对应的动态信息
     * @param deviceIpList
     * @param host
     * @return
     */
    private List<DeviceChangeInfo> requestCms (List<DeviceInfoVo> deviceIpList,URI host ){
        log.info("requestCms start. deviceIpList:{}",deviceIpList);
        Set<String> ipSet = deviceIpList.stream()
                .map(DeviceInfoVo::getDeviceIp) // 提取 deviceIp
                .filter(Objects::nonNull)       // 过滤掉 null 值
                .filter(ip -> !ip.trim().isEmpty()) // 过滤掉空字符串或仅含空格的值
                .collect(Collectors.toSet());   // 收集为 Set

        ArrayList<DeviceChangeInfo> resultList = Lists.newArrayList();
        try {
            InstanceLifecycleStatusRequest request = new InstanceLifecycleStatusRequest();

            List<InstanceLifecycleStatusRequest.Instance> list = ipSet.stream().map(vo -> {
                InstanceLifecycleStatusRequest.Instance instance = new InstanceLifecycleStatusRequest.Instance();
                instance.setDeviceIp(vo);
                return instance;
            }).collect(Collectors.toList());
            request.setInstances(list);
            List<InstanceLifecycleStatusResponse> cmsResult = FeignUtils.getContent(containerPadFeignClient.instanceLifecycleStatus(host,request));
            log.info("containerPadFeignClient.instanceLifecycleStatus result:{}", cmsResult);
            //过滤掉版本不为2的板卡
            cmsResult.removeIf(device -> !Objects.equals(device.getVersion(), "2"));
            if(CollectionUtils.isNotEmpty(cmsResult)){
                cmsResult.forEach(instanceLifecycleStatusResponse -> {
                    DeviceChangeInfo info = new DeviceChangeInfo();
                    info.setDeviceIp(instanceLifecycleStatusResponse.getDeviceIp());
                    info.setMountVersion(instanceLifecycleStatusResponse.getVersion());
                    info.setPadCode(instanceLifecycleStatusResponse.getName());
                    resultList.add(info);
                });
            }
        }catch (Exception e){
            log.error("requestCms error",e);
        }
        log.info("requestCms end. resultList:{}",resultList);
        return resultList;

    }
}
