package net.armcloud.paascenter.openapi.model.dto.netstorage;

import lombok.Data;
import net.armcloud.paascenter.common.model.entity.paas.DevicePad;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageComputeUnit;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageComputeUnitPad;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/25 15:47
 * @Description: 网存开机DTO 存储开机的绑定关系
 */
@Data
public class NetStorageDTO {


    /**
     * 算力list
     */
    private List<NetStorageComputeUnit> netStorageComputeUnitList;

    /**
     * 算力实例绑定list
     */
    private List<NetStorageComputeUnitPad> netStorageComputeUnitPadList;


    /**
     * 板卡实例绑定list
     */
   private List<DevicePad> devicePadList;

    /**
     * 实例list
     */
   private List<PadDetailsVO> padDetailsVOList;

    /**
     * 板卡规格(算力规格-实例规格)
     */
   private String deviceLevel;

   /**
    * 集群编号
    */
   private String clusterCode;

    /**
     * 实例跟算力网存的映射
     */
   private List<NetStoragePadCodeDetailDTO> netStoragePadCodeDetailDTOList;




}
