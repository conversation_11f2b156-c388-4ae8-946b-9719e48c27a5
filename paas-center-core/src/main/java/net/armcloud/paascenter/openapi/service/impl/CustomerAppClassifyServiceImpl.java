package net.armcloud.paascenter.openapi.service.impl;

import net.armcloud.paascenter.common.client.internal.vo.AppClassifyNameVO;
import net.armcloud.paascenter.openapi.mapper.CustomerAppClassifyMapper;
import net.armcloud.paascenter.openapi.service.ICustomerAppClassifyService;
import net.armcloud.paascenter.common.client.internal.dto.QueryAppClassifyNameDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CustomerAppClassifyServiceImpl implements ICustomerAppClassifyService {

    @Resource
    private CustomerAppClassifyMapper customerAppClassifyMapper;

    @Override
    public List<AppClassifyNameVO> queryAppClassifyName(QueryAppClassifyNameDTO dto) {
        return customerAppClassifyMapper.getClassifyNameByAppIds(dto.getCustomerId(),dto.getPckNames());
    }
}
