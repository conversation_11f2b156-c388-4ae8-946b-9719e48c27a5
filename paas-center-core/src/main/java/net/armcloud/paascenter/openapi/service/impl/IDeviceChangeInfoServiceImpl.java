package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.openapi.mapper.IDeviceChangeInfoMapper;
import net.armcloud.paascenter.openapi.service.IDeviceChangeInfoService;
import net.armcloud.paascenter.common.model.entity.paas.DeviceChangeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IDeviceChangeInfoServiceImpl extends ServiceImpl<IDeviceChangeInfoMapper, DeviceChangeInfo> implements IDeviceChangeInfoService {


    @Override
    public List<String> getMountVersionV1(List<String> deviceIpList) {
        LambdaQueryWrapper <DeviceChangeInfo> wrapper = new LambdaQueryWrapper<>();
         wrapper.eq(DeviceChangeInfo::getMountVersion, "2");
        wrapper.in(DeviceChangeInfo::getPadCode,deviceIpList);
         return this.list(wrapper).stream().map(DeviceChangeInfo::getDeviceIp).collect(Collectors.toList());
    }
}