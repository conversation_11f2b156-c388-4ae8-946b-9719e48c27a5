package net.armcloud.paascenter.openapi.model.dto.padnetowrk;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class PadNetworkInterruptEnableDTO {
    @ApiModelProperty(hidden = true)
    private Long customerId;

    @ApiModelProperty(value = "实例列表", required = true)
    @Size(min = 1,max = 200, message = "实例数量范围1-200")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    @NotNull(message = "enable cannot null")
    private Boolean enable;
}
