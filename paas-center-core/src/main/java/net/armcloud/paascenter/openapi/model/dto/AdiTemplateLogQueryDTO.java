package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.util.Date;

/**
 * ADI模板操作日志查询条件DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板操作日志查询对象", description = "用于查询ADI模板操作日志的条件对象")
public class AdiTemplateLogQueryDTO extends PageDTO {

    @ApiModelProperty(value = "ADI模板ID", example = "123")
    private Long templateId;

    @ApiModelProperty(value = "操作类型(0:上传/1:启用/2:禁用/3:编辑/4:删除)", example = "0")
    private Integer operationType;

    @ApiModelProperty(value = "操作者", example = "admin")
    private String operator;

    @ApiModelProperty(value = "开始时间", example = "2024-01-01 00:00:00")
    private Date startTime;

    @ApiModelProperty(value = "结束时间", example = "2024-12-31 23:59:59")
    private Date endTime;
}