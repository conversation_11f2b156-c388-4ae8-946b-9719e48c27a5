package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class PadPropertiesDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "实例", required = true)
    @NotBlank(message = "padCode cannot null")
    private String padCode;

}
