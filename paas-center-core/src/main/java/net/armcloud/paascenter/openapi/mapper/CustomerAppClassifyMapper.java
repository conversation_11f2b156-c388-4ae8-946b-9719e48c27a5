package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.client.internal.vo.AppClassifyNameVO;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户黑白名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Mapper
public interface CustomerAppClassifyMapper extends BaseMapper<CustomerAppClassify> {

    int cusInsert(CustomerAppClassify customerAppClassify);

    int updatePadNum(@Param("id")Long id, @Param("padNum")Integer padNum);


    /**
     * 根据APPID查询分类名称
     * @param customerId
     * @param appIds
     * @return
     */
    List<AppClassifyNameVO> getClassifyNameByAppIds(@Param("customerId") Long customerId, @Param("pckNames") List<String> pckNames);
}
