package net.armcloud.paascenter.openapi.model.enums;

/**
 * ADI模板是否正式版本枚举
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public enum AdiTemplateIsOfficialEnum {

    /**
     * 测试版本
     */
    TEST_VERSION(0, "测试版"),

    /**
     * 正式版本
     */
    OFFICIAL_VERSION(1, "正式版");

    private final Integer code;
    private final String desc;

    AdiTemplateIsOfficialEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static AdiTemplateIsOfficialEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AdiTemplateIsOfficialEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        AdiTemplateIsOfficialEnum type = getByCode(code);
        return type == null ? "未知操作" : type.getDesc();
    }
} 