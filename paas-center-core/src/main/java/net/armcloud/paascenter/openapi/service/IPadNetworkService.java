package net.armcloud.paascenter.openapi.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;

import net.armcloud.paascenter.cms.model.response.ProxyDetectionResponse;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import net.armcloud.paascenter.openapi.model.dto.padnetowrk.PadNetworkInterruptEnableDTO;
import net.armcloud.paascenter.openapi.model.dto.padnetowrk.ProxyDetectionDTO;
import net.armcloud.paascenter.openapi.model.dto.padnetowrk.SetPadNetworkProxyDTO;

public interface IPadNetworkService {

    List<GeneratePadTaskVO> setProxy(SetPadNetworkProxyDTO param);

    List<GeneratePadTaskVO> setProxyV2(JSONObject param);

    List<GeneratePadTaskVO> proxyInfo(PadCodesDTO param);

    List<GeneratePadTaskVO> networkInterruptEnable(PadNetworkInterruptEnableDTO param);

    ProxyDetectionResponse proxyDetection(ProxyDetectionDTO param);
}
