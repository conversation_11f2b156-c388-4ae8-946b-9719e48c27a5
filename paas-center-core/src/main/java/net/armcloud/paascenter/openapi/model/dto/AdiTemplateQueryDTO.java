package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import java.util.List;

/**
 * ADI模板查询条件DTO
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板查询对象", description = "用于查询ADI模板的条件对象")
public class AdiTemplateQueryDTO extends PageDTO {


    @ApiModelProperty(value = "实例品牌", example = "HUAWEI")
    private String brand;

    @ApiModelProperty(value = "实例机型", example = "ANA-AN00")
    private String model;

    @ApiModelProperty(value = "android镜像版本", example = "11")
    private String androidImageVersion;

    @ApiModelProperty(value = "客户ID集合")
    private List<Long> customerIds;

    @ApiModelProperty(value = "adi模板版本")
    private String adiTemplateVersion;

    /**
     * 是否正式版（0:测试版 1:正式版）
     */
    @ApiModelProperty(value = "发布版本")
    private Integer isOfficial;

    @ApiModelProperty(value = "aosp版本")
    private String aospVersion;

    /**
     * 模板类型：1-公共模板，2-自定义模板
     */
    @ApiModelProperty(value = "模板类型：1-公共模板，2-自定义模板")
    private Integer templateType;

}