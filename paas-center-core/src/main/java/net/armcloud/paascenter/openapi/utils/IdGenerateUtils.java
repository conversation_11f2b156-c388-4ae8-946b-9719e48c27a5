package net.armcloud.paascenter.openapi.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

public class IdGenerateUtils {

    public static String generateImageUploadUniqueId() {
        // 获取当前日期，格式为yyMMdd
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        String dateStr = sdf.format(new Date());

        // 生成5位随机数
        Random random = new Random();
        int randomNum = 10000 + random.nextInt(90000); // 保证是5位数

        // 组合日期和随机数
        return "img-" + dateStr + randomNum;
    }

    public static String generateImageUploadUniqueIdPro() {
        // 当前时间日期部分（yyMMdd）
        String dateStr = new SimpleDateFormat("yyMMdd").format(new Date());

        // 当前时间戳的后6位（毫秒级别保证递增性）
        String timeMillisSuffix = String.valueOf(System.currentTimeMillis());
        String last6 = timeMillisSuffix.substring(timeMillisSuffix.length() - 6);

        // 生成 4 位随机数
        int randomNum = 1000 + new Random().nextInt(9000);

        return "img-" + dateStr + last6 + randomNum;
    }

    private IdGenerateUtils() {

    }
}
