package net.armcloud.paascenter.openapi.config;

import net.armcloud.paascenter.common.client.component.CommonPadCommandComponent;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class OpenApiPadCommandComponentConfig {

    @Bean
    public CommonPadCommandComponent openApiPadCommandComponent() {
        CommonPadCommandComponent padCommandComponent = new CommonPadCommandComponent();
        return padCommandComponent;
    }

}
