package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 黑白名单添加对象
 */
@Data
public class AppClassifyAddAppDTO implements Serializable {
    @ApiModelProperty(value = "id")
    @NotNull(message = "id cannot null")
    private Long id;
    @ApiModelProperty(value = "客户id",hidden = true)
    private Long customerId;
    @ApiModelProperty(value = "关联应用集合")
    @NotNull(message = "appInfos cannot null")
    @Size(min = 1,max = 500, message = "应用数量不能超过500个")
    @Valid
    private List<AppInfo> appInfos;

    @Data
    public static class AppInfo{
        @ApiModelProperty(value = "文件id")
        private Long fileId;
        @ApiModelProperty(value = "应用id")
        private Long appId;
        @ApiModelProperty(value = "应用名称")
        private String appName;
        @ApiModelProperty(value = "包名")
        @NotEmpty(message = "packageName cannot null")
        private String packageName;
        @ApiModelProperty(value = "版本号")
        private Long appVersionNo;
        @ApiModelProperty(value = "版本名称")
        private String appVersionName;
    }
}
