package net.armcloud.paascenter.openapi.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用分类响应对象
 */
@Data
public class NewAppClassifyVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;
    @ApiModelProperty(value = "应用数量")
    private Integer appNum;
    @ApiModelProperty(value = "分类状态 是否启用(1：是；0：否) 默认1")
    private Boolean enable;
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
