package net.armcloud.paascenter.openapi.aspect;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;
import net.armcloud.paascenter.common.model.dto.api.PadUninstallAppFileDTO;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.openapi.annotation.CustomerPadAuthValid;
import net.armcloud.paascenter.openapi.model.dto.InstallApp;
import net.armcloud.paascenter.openapi.model.dto.UnInstallApp;
import net.armcloud.paascenter.openapi.service.IPadService;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static net.armcloud.paascenter.common.core.exception.code.BasicExceptionCode.PAD_CODE_NOT_EXIST;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.DUPLICATE_INSTANCE_NUMBER;


@Aspect
@Component
public class CustomerPadAuthValidAspect {
    private final IPadService padService;

    @Before(value = "execution(* net.armcloud.paascenter.openapi.controller..*.*(..)) && @annotation(annotation)")
    public void requestAspectBefore(JoinPoint joinPoint, CustomerPadAuthValid annotation) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest httpServletRequest = attributes.getRequest();

        Object[] args = joinPoint.getArgs();
        if (Objects.isNull(args) || args.length == 0) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }

        List<String> padCodes = new ArrayList<>();
        HashMap<String, List<String>> map = new HashMap<>();
        if(args[0] instanceof InstallApp){
            InstallApp installApp = (InstallApp) args[0];
            List<PadDownloadAppFileDTO> apps = installApp.getApps();
            if (apps != null) {
                for (PadDownloadAppFileDTO app : apps) {
                    JSONObject jsonObject = (JSONObject) JSON.toJSON(app);
                    List<String> extractedPadCodes = getPadCodes(jsonObject, annotation);
                    padCodes.addAll(extractedPadCodes);
                    map.put(app.getAppId(), extractedPadCodes);
                }
            }
        }else if(args[0] instanceof UnInstallApp){
            UnInstallApp installApp = (UnInstallApp) args[0];
            List<PadUninstallAppFileDTO> apps = installApp.getApps();
            if (apps != null) {
                for (PadUninstallAppFileDTO app : apps) {
                    JSONObject jsonObject = (JSONObject) JSON.toJSON(app);
                    List<String> extractedPadCodes = getPadCodes(jsonObject, annotation);
                    padCodes.addAll(extractedPadCodes);
                    map.put(app.getAppId(), extractedPadCodes);
                }
            }
        }else {
            padCodes.addAll(getPadCodes(JSONObject.from(args[0]),annotation));
            map.put(JSONObject.from(args[0]).getString("appId"), getPadCodes(JSONObject.from(args[0]),annotation));
        }
        // 校验padCode是否有重复值
        if (hasDuplicates(map)) {
            throw new BasicException(DUPLICATE_INSTANCE_NUMBER);
        }
        padService.checkPadListAndDeviceOnlineOwnerService(CustomerUtils.getAndVerifyUserId(httpServletRequest), padCodes,annotation.skipVerifyingTheOff());
    }

    private List<String> getPadCodes(JSONObject jsonObject,CustomerPadAuthValid annotation) {
        List<String> padCodes = new ArrayList<>();
        Object fileValueObj = jsonObject.get(annotation.fieldName().getName());
        if (fileValueObj instanceof JSONArray) {
            padCodes = ((JSONArray) fileValueObj).toJavaList(String.class);
        }

        if (fileValueObj instanceof String) {
            padCodes.add((String) fileValueObj);
        }

        if (fileValueObj instanceof List) {
            padCodes = (List) fileValueObj;
        }

        return padCodes;
    }

    /**
     * 判断集合是否有重复值
     *
     * @param padCodes List<String>
     * @return boolean
     */
    public static boolean hasDuplicates(HashMap<String,List<String>> padCodes) {
        //检查list中是否有重复值
        for (List<String> padCodeList : padCodes.values()) {
            if (hasListDuplicates(padCodeList)) {
                return true;
            }
        }
        return false;
    }
    private static boolean hasListDuplicates(List<String> list) {
        Set<String> set = new HashSet<>();
        for (String item : list) {
            if (!set.add(item)) {
                return true;  // 有重复值
            }
        }
        return false;  // 没有重复值
    }

    public CustomerPadAuthValidAspect(IPadService padService) {
        this.padService = padService;
    }
}
