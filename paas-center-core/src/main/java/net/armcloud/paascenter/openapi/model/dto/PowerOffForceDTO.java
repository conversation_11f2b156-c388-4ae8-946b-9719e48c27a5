package net.armcloud.paascenter.openapi.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class PowerOffForceDTO implements Serializable {

    private final static long serialVersionUID = 1L;

    /**实例编号 必填*/
    @Size(min = 1, max = 200, message = "可传入的实例数量范围1-200")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;


}