package net.armcloud.paascenter.openapi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.model.vo.api.CallbackUrlVO;
import net.armcloud.paascenter.openapi.mapper.CallbackInformationMapper;
import net.armcloud.paascenter.openapi.service.ICallbackInformationService;
import net.armcloud.paascenter.common.model.entity.paas.CallbackInformation;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyTime;
import net.armcloud.paascenter.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CallbackInformationServiceImpl extends ServiceImpl<CallbackInformationMapper, CallbackInformation> implements ICallbackInformationService {

    @Resource
    private RedisService redisService;
    @Resource
    private CallbackInformationMapper callbackInformationMapper;

    @Override
    public CallbackUrlVO getCallbackUrlService(Long customerId, Integer type) {

        String key = RedisKeyPrefix.CUSTOMER_CALLBACK_URL + customerId + ":" + type;
        String callbackUrlObj = redisService.getCacheObject(key);

        // 如果缓存中存在，则直接返回
        if (Objects.nonNull(callbackUrlObj)) {
            return JSON.parseObject(callbackUrlObj, CallbackUrlVO.class);
        }

        // 缓存中不存在，则从数据库中查询
        CallbackUrlVO callbackUrlVO = callbackInformationMapper.selectCallbackUrl(customerId, type);

        if (Objects.isNull(callbackUrlVO)) {
            return null;
        }
        // 将查询结果放入缓存，并设置缓存时间为7天
        String callbackUrlVOStr = JSON.toJSONString(callbackUrlVO);
        redisService.setCacheObject(key, callbackUrlVOStr, RedisKeyTime.day_7, TimeUnit.DAYS);

        return callbackUrlVO;
    }
}
