package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.openapi.rocketmq.MqTopicConfig;
import net.armcloud.paascenter.common.model.entity.paas.ClientExceptionInfo;
import net.armcloud.paascenter.common.rocketmq.support.DefaultRocketMqProducerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/openapi/open/clientException")
public class ClientExceptionInfoController {

    @Resource
    private MqTopicConfig mqTopicConfig;
    @Resource
    private DefaultRocketMqProducerWrapper mqProducerService;
    @Autowired
    private HttpServletRequest request;

    @PostMapping("/sendInfo")
    public void clientExceptionInfo(@RequestBody ClientExceptionInfo clientExceptionInfo) {
        //获取请求的ip地址
        //TODO 跟文臣.光明沟通后,目前该接口暂时不需要,直接屏蔽.
//        String clientIpAddress = getClientIpAddress(request);
//        clientExceptionInfo.setCreateTime(new Date());
//        clientExceptionInfo.setIp(clientIpAddress);
//        String jsonString = JSON.toJSONString(clientExceptionInfo);
//        log.info("sendClientExceptionInfo jsonString={}", jsonString);
//
//        String msgId = mqProducerService.producerNormalMessage(mqTopicConfig.getClientExceptionTopic(), null, jsonString);
//        log.info("sendChangeDeviceStatusService msgId:{}", msgId);
    }
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        ip = request.getHeader("Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        return request.getRemoteAddr();
    }
}
