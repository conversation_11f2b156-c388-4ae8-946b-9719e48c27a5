package net.armcloud.paascenter.openapi.service.impl;

import com.mifmif.common.regex.Generex;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NumberUtil {
    private static String regex = "^1[3|4|5|6|7|8|9][0-9]\\d{4,8}$";

    private static String emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";


    private static String isIntegerRegex = "^[1-9]\\d*$";

    private static String EMAIL_REGEX = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    private static final Map<String, String> countryCodes = new HashMap<>();

    static {
        countryCodes.put("CN", "+86 ");
        countryCodes.put("US", "+1 ");
        countryCodes.put("KR", "+82 ");
        countryCodes.put("JP", "+81 ");
        countryCodes.put("DE", "+49 ");
        countryCodes.put("GB", "+44 ");
        countryCodes.put("FR", "+33 ");
        countryCodes.put("IT", "+39 ");
        countryCodes.put("ES", "+34 ");
        countryCodes.put("AU", "+61 ");
        countryCodes.put("CA", "+1 ");
        countryCodes.put("SG", "+65 ");
        countryCodes.put("IN", "+91 ");
        countryCodes.put("BR", "+55 ");
        countryCodes.put("RU", "+7 ");
        countryCodes.put("MX", "+52 ");
        countryCodes.put("TR", "+90 ");
        countryCodes.put("SA", "+966 ");
        countryCodes.put("ZA", "+27 ");
        countryCodes.put("NL", "+31 ");
        countryCodes.put("SE", "+46 ");
        countryCodes.put("NO", "+47 ");
        countryCodes.put("FI", "+358 ");
        countryCodes.put("CH", "+41 ");
        countryCodes.put("AT", "+43 ");
        countryCodes.put("BE", "+32 ");
    }

    public static String getCountryCode(String countryAbbreviation) {
        return countryCodes.getOrDefault(countryAbbreviation.toUpperCase(), "");
    }

    /**
     * 订单号生产
     *
     * @return
     */
    public static String getOrderId() {
        return "VMOS-CLOUD" + System.currentTimeMillis() + (int) ((Math.random() * 9 + 1) * 10000);
    }

    public static String generateValue(String regex) {
        // 去除开头和结尾的 ^ 和 $
        String modifiedRegex = regex.replaceAll("(^\\^|\\$$)", "");
        Generex generex = new Generex(modifiedRegex);
        return generex.random();
    }

    // 新加坡经纬度范围
    private static final double MIN_LATITUDE = 1.1700;
    private static final double MAX_LATITUDE = 1.4660;
    private static final double MIN_LONGITUDE = 103.5700;
    private static final double MAX_LONGITUDE = 104.1000;

    public static String getRandomSingaporeCoordinates() {
        Random random = new Random();

        // 生成随机经纬度
        double latitude = MIN_LATITUDE + (MAX_LATITUDE - MIN_LATITUDE) * random.nextDouble();
        double longitude = MIN_LONGITUDE + (MAX_LONGITUDE - MIN_LONGITUDE) * random.nextDouble();

        return String.format("%.4f,%.4f", latitude, longitude);
    }

    // 生成随机IP地址
    public static String generateRandomIP() {
        Random random = new Random();
        return random.nextInt(256) + "." +
                random.nextInt(256) + "." +
                random.nextInt(256) + "." +
                random.nextInt(256);
    }

    public static String extractTimestamp(String fingerprint) {
        Pattern pattern = Pattern.compile("/(\\d+):user/");
        Matcher matcher = pattern.matcher(fingerprint);
        if (matcher.find()) {
            return matcher.group(1);  // 返回匹配到的数字部分
        }
        return "9325679"; // 如果没有匹配到返回 null
    }


    public static String decimalToHex(String number) {
        Integer integer = Integer.valueOf(number);
        return Integer.toHexString(integer).toUpperCase(); // 转换为十六进制并转为大写
    }

    public static String extractModelWithRegex(String fingerprint) {
        String regex = "/([^/]+)/"; // 正则表达式：匹配两个斜杠之间的内容
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(fingerprint);

        if (matcher.find()) {
            return matcher.group(1); // 返回第一个捕获组，即两个斜杠之间的内容
        }
        return "raven"; // 如果没有找到，返回null
    }

    public static String generatePhone(String country, String regex) {
        // 去除开头和结尾的 ^ 和 $
        String modifiedRegex = regex.replaceAll("(^\\^|\\$$)", "");
        Generex generex = new Generex(modifiedRegex);
        return getCountryCode(country) + generex.random();
    }



    /**
     * 手机号码校验
     *
     * @param mobile
     * @return
     */
    public static boolean matchMobile(String mobile) {
        Pattern p = Pattern.compile(emailRegex);
        Matcher m = p.matcher(mobile);
        boolean isMatch = m.matches();
        if (isMatch) {
            return true;
        } else {
            return false;
        }
    }


}
