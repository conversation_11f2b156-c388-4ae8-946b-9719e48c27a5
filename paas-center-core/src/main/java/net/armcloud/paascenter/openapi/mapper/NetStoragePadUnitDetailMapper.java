package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.NetStoragePadUnitDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NetStoragePadUnitDetailMapper extends BaseMapper<NetStoragePadUnitDetail> {

    /**
     * 批量插入网存实例详情
     */
    int batchInsertNetStoragePadUnitDetails(@Param("details") List<NetStoragePadUnitDetail> details);

}
