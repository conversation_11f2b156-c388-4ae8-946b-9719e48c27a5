package net.armcloud.paascenter.openapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResPad;
import net.armcloud.paascenter.openapi.mapper.NetStorageResPadMapper;
import net.armcloud.paascenter.openapi.service.NetStorageResPadService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/3/9 20:52
 * @Description: NetStorageResServiceImpl
 */
@Service
public class NetStorageResPadServiceImpl extends ServiceImpl<NetStorageResPadMapper, NetStorageResPad> implements NetStorageResPadService {


}