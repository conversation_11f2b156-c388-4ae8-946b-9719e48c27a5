package net.armcloud.paascenter.openapi.netpadv2.dto.task;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.armcloud.paascenter.openapi.netpadv2.dto.task.base.TaskBaseRequestDTO;

import java.io.Serializable;

/**
 * 关机请求DTO
 * CBS执行关机任务时需要的参数
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "关机请求DTO", description = "下发到CBS的关机任务参数")
public class PadOffRequestDTO extends TaskBaseRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "板卡IP")
    private String deviceIp;

    @ApiModelProperty(value = "网存单元编码")
    private String netStorageResUnitCode;

    @ApiModelProperty(value = "是否强制删除（会强制删除实例的全部数据，不做任何备份。）")
    private Boolean forceDelete = false;

    @Override
    public String toJSONString() {
        return JSONObject.toJSONString(this);
    }
}
