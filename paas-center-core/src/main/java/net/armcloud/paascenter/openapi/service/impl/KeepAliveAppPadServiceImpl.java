package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.client.internal.dto.StartAppListDTO;
import net.armcloud.paascenter.common.client.internal.vo.StartAppListVO;
import net.armcloud.paascenter.openapi.mapper.KeepAliveAppPadMapper;
import net.armcloud.paascenter.openapi.mapper.KeepAliveAppRelationMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.dto.SetKeepAliveAppSaveDTO;
import net.armcloud.paascenter.openapi.service.IKeepAliveAppPadService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.KeepAliveAppPad;
import net.armcloud.paascenter.common.model.entity.paas.KeepAliveAppRelation;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 保活应用实例 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
public class KeepAliveAppPadServiceImpl extends ServiceImpl<KeepAliveAppPadMapper, KeepAliveAppPad> implements IKeepAliveAppPadService {

    @Resource
    private KeepAliveAppRelationMapper keepAliveAppRelationMapper;
    @Resource
    private PadMapper padMapper;

    /**
     * 设置保活应用
     * @param setKeepAliveAppSaveDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setKeepAliveApp(SetKeepAliveAppSaveDTO setKeepAliveAppSaveDTO) {
        boolean applyAllInstances = setKeepAliveAppSaveDTO.getApplyAllInstances()!=null && setKeepAliveAppSaveDTO.getApplyAllInstances();
        //生成关联id
        Long relationId = IdWorker.getId();

        //1.如果applyAllInstances=false 先用当前的实例编号获取到现在所有的关联id
        //如果applyAllInstances=true 清空该用户所有的keep_alive_app_pad和keep_alive_app_relation 否则只清空指定的实例的keep_alive_app_pad
        Set<Long> oldRelationIds = null;
        if(applyAllInstances){
            baseMapper.delete(new QueryWrapper<>(KeepAliveAppPad.class).eq("customer_id",setKeepAliveAppSaveDTO.getCustomerId()));
            keepAliveAppRelationMapper.delete(new QueryWrapper<>(KeepAliveAppRelation.class).eq("customer_id",setKeepAliveAppSaveDTO.getCustomerId()));
        }else{
            if(CollUtil.isEmpty(setKeepAliveAppSaveDTO.getPadCodes())){
                throw new BasicException("padCodes不能为空");
            }
            List<KeepAliveAppPad> keepAliveAppPads = baseMapper.selectList(new QueryWrapper<>(KeepAliveAppPad.class)
                    .eq("customer_id",setKeepAliveAppSaveDTO.getCustomerId())
                    .in("pad_code",setKeepAliveAppSaveDTO.getPadCodes()));
            if(CollUtil.isNotEmpty(keepAliveAppPads)){
                oldRelationIds = keepAliveAppPads.stream().map(KeepAliveAppPad::getRelationId).collect(Collectors.toSet());
            }
            List<String> padCodes = new ArrayList<>();
            if(CollUtil.isNotEmpty(setKeepAliveAppSaveDTO.getPadCodes())){
                padCodes = BeanUtil.copyToList(setKeepAliveAppSaveDTO.getPadCodes(),String.class);
            }
            padCodes.add("0");
            baseMapper.delete(new QueryWrapper<>(KeepAliveAppPad.class)
                    .eq("customer_id",setKeepAliveAppSaveDTO.getCustomerId()).in("pad_code",padCodes));
        }

        //批量插入keep_alive_app_relation
        if(CollUtil.isNotEmpty(setKeepAliveAppSaveDTO.getAppInfos())){
            List<KeepAliveAppRelation> saveKeepAliveAppRelations = new ArrayList<>();
            Map<String,String> map = new HashMap<>();
            for(SetKeepAliveAppSaveDTO.AppInfo appInfo : setKeepAliveAppSaveDTO.getAppInfos()){
                if(map.containsKey(appInfo.getServerName())){
                    continue;
                }
                KeepAliveAppRelation keepAliveAppRelation = new KeepAliveAppRelation();
                keepAliveAppRelation.setCustomerId(setKeepAliveAppSaveDTO.getCustomerId());
                keepAliveAppRelation.setServerName(appInfo.getServerName());
                keepAliveAppRelation.setStatus(true);
                keepAliveAppRelation.setRelationId(relationId);
                keepAliveAppRelation.setCreateTime(new Date());
                keepAliveAppRelation.setUpdateTime(keepAliveAppRelation.getCreateTime());
                keepAliveAppRelation.setCreateBy(setKeepAliveAppSaveDTO.getCustomerId()+"");
                keepAliveAppRelation.setUpdateBy(keepAliveAppRelation.getCreateBy());
                saveKeepAliveAppRelations.add(keepAliveAppRelation);
                //用于去重
                map.put(appInfo.getServerName(),"");
            }
            keepAliveAppRelationMapper.batchInsert(saveKeepAliveAppRelations);
        }
        //如果applyAllInstances=true则插入一条padCode=0的数据 否则批量插入keep_alive_app_pad
        if(applyAllInstances){
            KeepAliveAppPad keepAliveAppPad = new KeepAliveAppPad();
            keepAliveAppPad.setCustomerId(setKeepAliveAppSaveDTO.getCustomerId());
            keepAliveAppPad.setPadCode("0");
            keepAliveAppPad.setRelationId(relationId);
            keepAliveAppPad.setCreateTime(new Date());
            keepAliveAppPad.setUpdateTime(keepAliveAppPad.getCreateTime());
            keepAliveAppPad.setCreateBy(setKeepAliveAppSaveDTO.getCustomerId()+"");
            keepAliveAppPad.setUpdateBy(keepAliveAppPad.getCreateBy());
            baseMapper.insert(keepAliveAppPad);
        }else{
            List<KeepAliveAppPad> saveKeepAliveAppPads = new ArrayList<>();
            Set<String> padCodeSet = new HashSet<>(setKeepAliveAppSaveDTO.getPadCodes());
            for(String padCode : padCodeSet){
                KeepAliveAppPad keepAliveAppPad = new KeepAliveAppPad();
                keepAliveAppPad.setCustomerId(setKeepAliveAppSaveDTO.getCustomerId());
                keepAliveAppPad.setPadCode(padCode);
                keepAliveAppPad.setRelationId(relationId);
                keepAliveAppPad.setCreateTime(new Date());
                keepAliveAppPad.setUpdateTime(keepAliveAppPad.getCreateTime());
                keepAliveAppPad.setCreateBy(setKeepAliveAppSaveDTO.getCustomerId()+"");
                keepAliveAppPad.setUpdateBy(keepAliveAppPad.getCreateBy());
                saveKeepAliveAppPads.add(keepAliveAppPad);
            }
            baseMapper.batchInsert(saveKeepAliveAppPads);
        }
        //如果applyAllInstances=false 则校验1中的关联id是否还能在keep_alive_app_pad查询到 不能查到的 则清空指定关联id的keep_alive_app_relation
        if(!applyAllInstances && CollUtil.isNotEmpty(oldRelationIds)){
            List<KeepAliveAppPad> keepAliveAppPadList = baseMapper.selectList(new QueryWrapper<>(KeepAliveAppPad.class).in("relation_id",oldRelationIds));
            if(CollUtil.isNotEmpty(keepAliveAppPadList)){
                Set<Long> existRelationIds = keepAliveAppPadList.stream().map(KeepAliveAppPad::getRelationId).collect(Collectors.toSet());
                oldRelationIds.removeAll(existRelationIds);
            }
            if(CollUtil.isNotEmpty(oldRelationIds)){
                keepAliveAppRelationMapper.delete(new QueryWrapper<>(KeepAliveAppRelation.class).in("relation_id",oldRelationIds));
            }
        }
    }

    /**
     * 查询实例的保活应用配置
     * @param param
     * @return
     */
    @Override
    public StartAppListVO getPadStartAppLists(StartAppListDTO param) {
        StartAppListVO startAppListVO = new StartAppListVO();
        Pad pad = padMapper.selectOne(new QueryWrapper<>(Pad.class).eq("pad_code",param.getPadCode()).in("status", Arrays.asList(0,1)).last("limit 1"));
        if(pad != null){
            KeepAliveAppPad keepAliveAppPad = baseMapper.selectOne(new QueryWrapper<>(KeepAliveAppPad.class)
                    .eq("customer_id",pad.getCustomerId())
                    .in("pad_code",Arrays.asList("0",param.getPadCode())).orderByDesc("id").last("limit 1"));
            if(keepAliveAppPad != null){
                List<KeepAliveAppRelation> keepAliveAppRelationList = keepAliveAppRelationMapper.selectList(new QueryWrapper<>(KeepAliveAppRelation.class)
                        .eq("relation_id",keepAliveAppPad.getRelationId()).eq("status",1));
                if(CollUtil.isNotEmpty(keepAliveAppRelationList)){
                    Set<String> apps = new HashSet<>();
                    for(KeepAliveAppRelation keepAliveAppRelation : keepAliveAppRelationList){
                        apps.add(keepAliveAppRelation.getServerName());
                    }
                    startAppListVO.setStartAppList(new ArrayList<>(apps));
                }
            }
        }
        return startAppListVO;
    }
}
