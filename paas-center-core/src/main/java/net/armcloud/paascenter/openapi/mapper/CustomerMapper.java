package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import net.armcloud.paascenter.openapi.model.dto.QueryImageDTO;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.model.vo.QueryImageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CustomerMapper {
    /**
     * 根据id查询客户信息
     * @param customerId
     * @return
     */
    CustomerInfoVo getCustomerInfoById(@Param("customerId") long customerId);

    IPage<QueryImageVO> queryImageList(IPage<QueryImageVO> page, @Param("param")QueryImageDTO param);
}
