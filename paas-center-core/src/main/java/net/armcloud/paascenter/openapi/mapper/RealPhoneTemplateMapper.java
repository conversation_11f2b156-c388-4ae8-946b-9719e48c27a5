package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.RealPhoneTemplate;
import net.armcloud.paascenter.openapi.model.dto.AdiTemplateQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface RealPhoneTemplateMapper extends BaseMapper<RealPhoneTemplate> {


    /**
     * 检查指纹MD5是否已存在于特定客户下
     *
     * @param fingerprintMd5 指纹MD5
     * @param customerId 客户ID
     * @param resourceSpecificationCode 规格编号
     * @return 如果存在返回模板对象，否则返回null
     */
    RealPhoneTemplate checkFingerprintMd5Exists(@Param("fingerprintMd5") String fingerprintMd5, @Param("customerId") Long customerId);
    
    /**
     * 查询ADI模板列表，直接返回VO对象
     * 按照权限规则查询：
     * - 当customerIds为空时：只查询公共模板(is_public=1)
     * - 当customerIds不为空时：只查询用户有权限的模板(通过adi_template_customer表关联)
     * 
     * 结果同时包含关联的实例数量
     *
     * @param queryDTO 查询参数
     * @return 模板VO列表
     */
    List<AdiTemplateVO> queryAdiTemplates(@Param("query") AdiTemplateQueryDTO queryDTO,@Param("isAdmin")boolean isAdmin);
    
    /**
     * 根据条件查询模板列表用于选择
     * 按照权限规则查询：
     * - 当customerIds为空时：只查询公共模板(is_public=1)
     * - 当customerIds不为空时：只查询用户有权限的模板(通过adi_template_customer表关联)
     * 
     * 结果同时包含关联的实例数量
     *
     * @param status 状态
     * @param isOfficial 是否正式版
     * @param customerIds 客户ID列表
     * @return 模板VO列表
     */
    List<AdiTemplateVO> listForSelectionOptimized(@Param("status") Integer status, 
                                                 @Param("isOfficial") Integer isOfficial, 
                                                 @Param("customerIds") List<Long> customerIds);
    
    /**
     * 统计模板关联的实例数量
     * 仅统计状态为启用(status=1)的实例
     *
     * @param templateId 模板ID
     * @return 关联的实例数量
     */
    Integer countTemplateInstances(@Param("templateId") Long templateId);
}
