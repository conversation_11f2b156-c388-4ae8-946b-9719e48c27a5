package net.armcloud.paascenter.openapi.controller.internal;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.model.vo.netstorage.NetStorageResUnitVO;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageResUnitService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/3/27 20:57
 * @Description:
 */

@RestController
@RequestMapping("/internal/netStorage/resUnit")
@Api(tags = "存储单元对内接口")
@Slf4j
public class NetStorageResUnitInternalController {

    @Resource
    private NetStorageResUnitService netStorageResUnitService;

    @PostMapping("/getByCode")
    public Result<NetStorageResUnitVO> getByCode(@RequestParam("netStorageResUnitCode") String netStorageResUnitCode) {
        return Result.ok(netStorageResUnitService.getByCode(netStorageResUnitCode));
    }
}
