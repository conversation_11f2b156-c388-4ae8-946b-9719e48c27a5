package net.armcloud.paascenter.openapi.service.impl;

import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.GatewayPadMapper;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.service.IGatewayPadService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.GatewayPad;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant.ENABLE;
import static net.armcloud.paascenter.openapi.constants.ClusterAndNetConstant.NOT_DELETED;
import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.DUPLICATE_NAME;

@Service
public class GatewayPadServiceImpl implements IGatewayPadService {
    @Resource
    private GatewayPadMapper gatewayPadMapper;
    @Resource
    private CustomerMapper customerMapper;

    @Override
    public int insert(GatewayPad record) {
        if (gatewayPadMapper.countByNameAndNotDeleted(record.getGateway()) > 0) {
            throw new BasicException(DUPLICATE_NAME);
        }
        String customerName = getCustomerInfoById(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest())).getCustomerName();
        record.setCreateBy(customerName);
        record.setDeleteFlag(NOT_DELETED);
        record.setCreateTime(new Date());
        record.setStatus(ENABLE);
        return gatewayPadMapper.insert(record);
    }
    private CustomerInfoVo getCustomerInfoById(Long customerId) {
        return customerMapper.getCustomerInfoById(customerId);
    }
}
