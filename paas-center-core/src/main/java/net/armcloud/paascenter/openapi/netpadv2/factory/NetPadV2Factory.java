package net.armcloud.paascenter.openapi.netpadv2.factory;

import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2CreateDTO;
import net.armcloud.paascenter.openapi.netpadv2.dto.NetPadV2CreateResponseDTO;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadComputeUnitRelationDO;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadResUnitRelationDO;
import net.armcloud.paascenter.common.core.constant.pad.PadConstants;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import static net.armcloud.paascenter.common.core.constant.NumberConsts.*;

/**
 * 网存实例V2工厂类
 * 使用工厂方法模式创建实体对象
 *
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Component
public class NetPadV2Factory {


    /**
     * 创建Pad实例
     */
    public static Pad createPad(NetPadV2CreateDTO dto, ResourceSpecification resourceSpecification,
                                ScreenLayout screenLayout, String padCode, String netStorageResUnitCode,
                                int padSn, String dns, Integer streamType, String countryCode) {
        Pad pad = new Pad();

        // 主键ID使用数据库自增，不设置ID让数据库自动生成
        // pad.setId() 不设置，使用数据库自增
        
        // 基本信息
        pad.setPadCode(padCode);
        pad.setPadOutCode(padCode);
        pad.setCloudVendorType(1); // ARM_CLOUD_VENDOR_TYPE
        pad.setDeviceLevel(resourceSpecification.getSpecificationCode());
        pad.setCustomerId(dto.getCustomerId());
        pad.setGroupId(0L);
        pad.setStatus(ONE); // 网存实例创建后设为可用状态
        
        // DNS配置
        pad.setDns(dns);
        
        // 存储配置 - 转换成字节
        pad.setDataSize(dto.getStorageSize() * 1024L * 1024 * 1024);
        pad.setNetStorageResSize(dto.getStorageSize().longValue());
        
        // 镜像和布局
        pad.setImageId(dto.getImageId());
        pad.setScreenLayoutCode(screenLayout.getCode());
        
        // 网存相关
        pad.setNetStorageResId(netStorageResUnitCode);
        pad.setNetStorageResFlag(ONE);
        pad.setClusterCode(dto.getClusterCode());
        
        // 实例序号
        pad.setPadSn(padSn);

        // 推流类型
        pad.setStreamType(streamType);
        
        // 在线状态
        pad.setOnline(ZERO);
        pad.setStreamStatus(ZERO);
        
        // 资源隔离配置
        pad.setCpu(MINUS_ONE);
        pad.setMemory(dto.getIsolateMemory() ? resourceSpecification.getMemory() : MINUS_ONE);
        pad.setStorage(dto.getIsolateStorage() ? resourceSpecification.getStorage() : MINUS_ONE);
        
        // ADI模板配置
        pad.setRealPhoneTemplateId(dto.getRealPhoneTemplateId());
        
        // 实例类型：有ADI模板为真机，无ADI模板为虚拟机
        pad.setType(Objects.isNull(dto.getRealPhoneTemplateId()) ? 
                   PadConstants.Type.VIRTUAL.getValue() : PadConstants.Type.REAL.getValue());

        // SOC型号
        pad.setSocModel(resourceSpecification.getSocModel());
        
        // 创建信息
        pad.setCreateBy("virtualizeV2:" + dto.getOprBy());
        pad.setCreateTime(new Date());
        // 分组id
        if (Objects.nonNull(dto.getGroupId())) {
            pad.setGroupId(Long.valueOf(dto.getGroupId()));
        }

        // 国家代码
        pad.setCountryCode(countryCode);
        return pad;
    }

    /**
     * 创建网存单元
     */
    public static NetStorageResUnit createNetStorageResUnit(NetPadV2CreateDTO dto, String padCode,
                                                           String netStorageResUnitCode, Long storageSize) {
        NetStorageResUnit unit = new NetStorageResUnit();

        // 主键ID使用数据库自增，不设置ID让数据库自动生成
        // unit.setId() 不设置，使用数据库自增
        
        unit.setCustomerId(dto.getCustomerId());
        unit.setPadCode(padCode);
        unit.setCreateBy("virtualizeV2:" + dto.getOprBy());
        unit.setNetStorageResUnitCode(netStorageResUnitCode);
        unit.setNetStorageResUnitSize(storageSize);
        unit.setNetStorageResUnitUsedSize("0"); // 初始化为0
        unit.setClusterCode(dto.getClusterCode());
        unit.setCreateTime(new Date());
        unit.setUpdateTime(new Date());
        
        return unit;
    }

    /**
     * 创建响应DTO
     */
    public static NetPadV2CreateResponseDTO createResponseDTO(Pad pad) {
        return NetPadV2CreateResponseDTO.builder()
                .padCode(pad.getPadCode())
                .storageSize(pad.getNetStorageResSize())
                .clusterCode(pad.getClusterCode())
                .build();
    }

    /**
     * 创建实例与算力单元关联关系
     */
    public NetPadComputeUnitRelationDO createComputeRelation(String padCode, String computeUnitCode, String createBy) {
        NetPadComputeUnitRelationDO relation = new NetPadComputeUnitRelationDO();
        relation.setPadCode(padCode);
        relation.setNetStorageComputeUnitCode(computeUnitCode);
        relation.setCreateTime(new Date());
        relation.setUpdateTime(new Date());
        relation.setCreateBy(createBy);
        relation.setUpdateBy(createBy);
        return relation;
    }

    /**
     * 创建实例与存储关联关系
     */
    public static NetPadResUnitRelationDO createResUnitRelation(String padCode, String netStorageResUnitCode, String createBy) {
        NetPadResUnitRelationDO relation = new NetPadResUnitRelationDO();
        relation.setPadCode(padCode);
        relation.setNetStorageResUnitCode(netStorageResUnitCode);
        relation.setCreateTime(new Date());
        relation.setUpdateTime(new Date());
        relation.setCreateBy(createBy);
        relation.setUpdateBy(createBy);
        return relation;
    }

}
