package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PadGroupDTO implements Serializable {

    /**
     * old接口标记 兼容之前的payload
     */
    public interface old {}

    /**
     * 用于校验创建分组接口时候的参数
     */
    public interface create {}

    /**
     * 用于校验删除分组接口时候的参数
     */
    public interface delete {}


    /**
     * 用于校验移动分组接口时候的参数
     */
    public interface move {}


    private String remark;
    /**
     * 客户ID
     */
    @NotNull(groups = create.class,message = "客户ID不能为空")
    private Long customerId;


    @NotBlank(groups = create.class,message = "分组名称不能")
    private String groupName;

    @NotBlank(groups = {create.class,move.class},message = "创建用户不能为空")
    private String createUser;


    @NotNull(groups = {create.class,move.class},message = "分组Id不能为空")
    private Long groupId;
    /**
     * 实例分组Ids
     */
    @ApiModelProperty(value = "实例分组Id")
    private List<Integer> groupIds;

    /**
     * 实例编号
     */
    @NotNull(groups = {move.class},message = "实例编号不能为空")
    @ApiModelProperty(value = "实例编号")
    private String padCode;


    @NotNull(groups = delete.class,message = "唯一Id不能为空")
    @Size(min = 1, message = "分组主键Id 列表不能为空")  // 校验长度大于 0
    @ApiModelProperty(value = "分组主键Id")
    private List<Long> ids;
}
