package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.common.model.entity.paas.AdiCertificateRepository;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface AdiCertificateRepositoryMapper {

    List<AdiCertificateRepository> listByRandom(@Param("cert_level") int cert_level,  @Param("size") int size);

    void incrUseTotal(@Param("id") long id);

    void decrUseTotalByIds(@Param("ids") List<Long> ids);

    AdiCertificateRepository selectById(@Param("id") long id);

    Integer selectCertificateLevelById(@Param("id") Long id);
    
}