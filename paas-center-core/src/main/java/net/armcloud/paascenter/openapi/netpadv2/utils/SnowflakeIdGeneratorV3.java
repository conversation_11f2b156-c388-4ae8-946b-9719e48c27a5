package net.armcloud.paascenter.openapi.netpadv2.utils;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.redis.contstant.RedisKeyPrefix;
import net.armcloud.paascenter.common.redis.service.RedisService;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;

import java.util.Objects;

/**
 * 雪花算法ID生成器V3 - 16位优化版
 * 优化位数分配，生成16位以内的唯一ID
 * 
 * 位数分配：
 * - 时间戳: 30位 (秒级精度，可用34年，到2059年)
 * - 机器ID: 8位 (0-255，支持256台机器)
 * - 序列号: 16位 (0-65535，每秒最多65536个ID)
 * - 总位数: 54位，生成ID长度约15位
 *
 * <AUTHOR> Assistant
 * @date 2025-01-12
 */
@Slf4j
public class SnowflakeIdGeneratorV3 {

    /**
     * 起始时间戳 (2025-01-01 00:00:00) - 使用更近的起始时间减少位数
     */
    private static final long EPOCH = 1735689600000L;

    /**
     * 机器ID所占的位数 - 增加到8位支持更多机器
     */
    private static final long WORKER_ID_BITS = 8L;

    /**
     * 序列在ID中占的位数 - 增加到16位提升并发能力
     */
    private static final long SEQUENCE_BITS = 16L;

    /**
     * 时间戳占的位数 - 30位秒级时间戳
     */
    private static final long TIMESTAMP_BITS = 30L;

    /**
     * 支持的最大机器ID，结果是255
     */
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);

    /**
     * 机器ID向左移16位
     */
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;

    /**
     * 时间戳向左移24位(16+8)
     */
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;

    /**
     * 生成序列的掩码，这里为65535 (0xffff=65535)
     */
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);

    /**
     * 时间戳掩码，确保时间戳不超过30位
     */
    private static final long TIMESTAMP_MASK = ~(-1L << TIMESTAMP_BITS);

    /**
     * 工作机器ID(0~255)
     */
    @Getter
    private final long workerId;

    /**
     * 毫秒内序列(0~65535)
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间戳(秒级)
     */
    private long lastTimestamp = -1L;

    /**
     * 单例实例
     */
    private static volatile SnowflakeIdGeneratorV3 instance;

    /**
     * Redis服务，用于生成全局唯一的workerId
     */
    private static RedisService redisService;

    /**
     * 私有构造函数
     */
    private SnowflakeIdGeneratorV3(long workerId) {
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException(
                String.format("Worker ID can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        this.workerId = workerId;
        
        log.info("SnowflakeIdGeneratorV3 initialized with workerId: {} (16-bit optimized version)", workerId);
    }

    /**
     * 获取单例实例
     */
    public static SnowflakeIdGeneratorV3 getInstance() {
        if (instance == null) {
            synchronized (SnowflakeIdGeneratorV3.class) {
                if (Objects.isNull(redisService)) {
                    redisService = SpringUtil.getBean(RedisService.class);
                }
                if (instance == null) {
                    // 使用Redis计数器生成全局唯一的workerId
                    long workerId = getWorkerIdFromRedis();
                    instance = new SnowflakeIdGeneratorV3(workerId);
                }
            }
        }
        return instance;
    }

    /**
     * 从Redis获取全局唯一的workerId
     */
    private static long getWorkerIdFromRedis() {
        if (redisService == null) {
            log.warn("Redis service is not initialized, using fallback method to get workerId");
            return getDefaultWorkerId();
        }

        try {
            // 使用Redis计数器生成全局唯一的workerId
            String counterKey = RedisKeyUtils.counterKey(RedisKeyPrefix.SNOWFLAKE_WORKER_ID_COUNTER, "v3");
            Integer counter = redisService.increment(counterKey);

            if (counter == null) {
                log.warn("Failed to get workerId from Redis counter, using fallback method");
                return getDefaultWorkerId();
            }
            if (counter > 10 * 10000) {
                // 自增到10w以上时输出日志
                log.warn("WorkerId counter exceeds max value, using fallback method");
            }

            // 对最大workerId取模，确保workerId在有效范围内
            long workerId = counter % (MAX_WORKER_ID + 1);

            log.info("Generated workerId from Redis: {} (counter: {}) for V3", workerId, counter);
            return workerId;

        } catch (Exception e) {
            log.error("Error getting workerId from Redis, using fallback method", e);
            return getDefaultWorkerId();
        }
    }

    /**
     * 获取默认的workerId
     */
    private static long getDefaultWorkerId() {
        try {
            String hostAddress = java.net.InetAddress.getLocalHost().getHostAddress();
            int[] ints = new int[4];
            String[] parts = hostAddress.split("\\.");
            for (int i = 0; i < 4; i++) {
                ints[i] = Integer.parseInt(parts[i]);
            }
            return (ints[0] + ints[1] + ints[2] + ints[3]) % (MAX_WORKER_ID + 1);
        } catch (Exception e) {
            log.warn("Failed to get workerId from IP, using default value 1", e);
            return 1L;
        }
    }

    /**
     * 生成下一个ID
     */
    public synchronized long nextId() {
        long currentTimeMillis = timeGen();
        // 转换为秒级时间戳
        long timestamp = currentTimeMillis / 1000;

        // 时钟回拨检查
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                String.format("Clock moved backwards. Refusing to generate id for %d seconds", 
                    lastTimestamp - timestamp));
        }

        // 同一秒内，序列号递增
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & SEQUENCE_MASK;
            // 序列号溢出，等待下一秒
            if (sequence == 0) {
                timestamp = tilNextSecond(lastTimestamp);
            }
        } else {
            // 不同秒，序列号重置
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        // 计算相对时间戳并应用掩码确保不超过30位
        long relativeTimestamp = (timestamp - EPOCH / 1000) & TIMESTAMP_MASK;

        // 组装54位ID
        return (relativeTimestamp << TIMESTAMP_LEFT_SHIFT)
                | (workerId << WORKER_ID_SHIFT)
                | sequence;
    }

    /**
     * 生成字符串格式的ID
     */
    public static String nextIdStr() {
        return String.valueOf(getInstance().nextId());
    }

    /**
     * 生成长整型格式的ID
     */
    public static long nextIdLong() {
        return getInstance().nextId();
    }

    /**
     * 阻塞到下一秒，直到获得新的时间戳
     */
    protected long tilNextSecond(long lastTimestamp) {
        long timestamp = timeGen() / 1000;
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen() / 1000;
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 解析ID获取时间戳
     */
    public long getTimestampFromId(long id) {
        return ((id >> TIMESTAMP_LEFT_SHIFT) & TIMESTAMP_MASK) + (EPOCH / 1000);
    }

    /**
     * 解析ID获取workerId
     */
    public long getWorkerIdFromId(long id) {
        return (id >> WORKER_ID_SHIFT) & MAX_WORKER_ID;
    }

    /**
     * 解析ID获取序列号
     */
    public long getSequenceFromId(long id) {
        return id & SEQUENCE_MASK;
    }

    /**
     * 获取ID的位数长度
     */
    public static int getIdLength(long id) {
        return String.valueOf(id).length();
    }

    /**
     * 验证生成的ID是否在16位以内
     */
    public static boolean isWithin16Digits(long id) {
        return getIdLength(id) <= 16;
    }
}
