package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.model.vo.DevicePadVO;
import net.armcloud.paascenter.common.model.entity.paas.DevicePad;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DevicePadMapper extends BaseMapper<DevicePad> {

    @Select("select * from device_pad where device_id = #{deviceId}")
    List<DevicePad> selectList(Long deviceId);

    @Select("select * from device_pad where pad_id = #{padId} LIMIT 1")
    DevicePad selectByPadId(Long padId);

    List<DevicePadVO> listByPadCode(@Param("padCodes") List<String> padCodes);

    void batchInsert(List<DevicePad> devicePadList);
}
