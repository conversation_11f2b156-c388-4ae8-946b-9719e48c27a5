package net.armcloud.paascenter.openapi.helper;

import com.alibaba.fastjson.JSONObject;
import net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageResUnit;
import net.armcloud.paascenter.common.model.entity.paas.ResourceSpecification;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResPadDeleteDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageResUnitDeleteDTO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @Date 2025/3/25 10:07
 * @Description: 网存实例辅助类
 */
public interface NetStoragePadHelper {

    /**
     * 根据网存实例信息匹配计算单元和存储单元
     * @param padDetailsVOList
     * @return
     */

    List<NetStorageDTO> NetStorageMatchComputeAndStorage  (String clusterCode, List<PadDetailsVO> padDetailsVOList, String countryCode, JSONObject androidProp);

    /**
     * 获取规格信息
     * @param deviceLevel
     * @return
     * @throws ExecutionException
     */
    ResourceSpecification getResourceSpecification(String deviceLevel) throws ExecutionException;

    /**
     * 获取板卡信息
     * @param deviceId
     * @return
     * @throws ExecutionException
     */
     VirtualizeDeviceInfoVO getVirtualizeDevice(Long deviceId) throws ExecutionException;

    /**
     * 保存网存算力映射数据
     * @param netStorageDTOList
     */

    void saveNetStorageDTOList(List<NetStorageDTO> netStorageDTOList);

    /**
     * 执行网存实例备份操作
     * @param param
     */
    List<NetStorageResUnit> padBackup(List<NetStorageResUnit> netStorageResUnitList,Long totalSize,String clusterCode,Long customerId,String remark);

    /**
     * 处理网存克隆失败回调
     * @param netStorageResUnitCode
     */
    void processNetStorageBackupCallback(String netStorageResUnitCode);

    /**
     * 执行网存属性删除,包括数据验证
     * @param param
     */
    List<NetStorageResUnitDeleteDTO> processNetStoragePadDelete(NetStorageResPadDeleteDTO param);


    void processNetStoragePadOff(String padCode);

    /**
     * 处理网存存储删除成功回调
     * @param netStorageResUnitCode
     */
    void processNetStorageResUnitDeleteCallback(String netStorageResUnitCode);

    /**
     * 处理网存存储批量删除成功回调
     * @param netStorageResUnitCodes 网存存储单元编码集合
     */
    void processNetStorageResUnitBatchDeleteCallback(List<String> netStorageResUnitCodes);

    /**
     * 网存关机成功时先把存储状态设置未开机
     * @param padCode
     */
    void onlyNetStoragePadOff(String padCode);
}
