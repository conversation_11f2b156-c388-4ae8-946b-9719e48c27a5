package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAccess;

public interface ICustomerAccessService extends IService<CustomerAccess> {
    /**
     * @param accessKeyId 客户密钥ID
     * @return 密钥信息
     */
    CustomerAccess getAccessByAccessKeyId(String accessKeyId);

    /**
     * @param customerId 客户ID
     * @return 密钥信息
     */
    CustomerAccess getAccessByCustomerId(Long customerId);
}
