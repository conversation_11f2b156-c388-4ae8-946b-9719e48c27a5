package net.armcloud.paascenter.openapi.controller;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import net.armcloud.paascenter.common.core.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.*;

@Slf4j
@RestController
@RequestMapping("/openapi/open/test")
public class TestController {

    private final ExecutorService threadPool = new ThreadPoolExecutor(1, 1,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1), new ThreadFactoryBuilder()
            .setNameFormat("MessageNotifyThreadPool-%d").build(), new ThreadPoolExecutor.AbortPolicy());

    @PostMapping("thread")
    public Result<?> thread(@Param("num") String num) {
        try {
            threadPool.execute(() -> {
                System.out.println(Thread.currentThread().getName()+"-"+num);
                try {
                    Thread.sleep(50000L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
        } catch (RejectedExecutionException e) {
            log.error("队列已满任务丢弃", e);
        }
        return Result.ok();
    }

    @PostMapping("test1111")
    public Result<?> test1111() {
        log.info("test1111test1111test1111test1111");
        return Result.ok();
    }
}
