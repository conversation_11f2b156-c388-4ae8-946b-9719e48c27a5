package net.armcloud.paascenter.openapi.mapper;

import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.EdgeClusterConfiguration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface EdgeClusterConfigurationMapper extends BaseMapper<EdgeClusterConfiguration> {
    
    Map<String, String> queryEdgeClusterConfiguration(String clusterCode);

    String queryEdgeClusterConfigurationByKey(@Param("clusterCode") String clusterCode,@Param("key") String key);

}
