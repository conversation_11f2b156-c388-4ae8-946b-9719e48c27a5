package net.armcloud.paascenter.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import net.armcloud.paascenter.openapi.model.dto.NetPadDTO;
import net.armcloud.paascenter.openapi.service.INetPadService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.entity.paas.NetPad;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2025/2/11 15:19
 * @Version 1.0
 */

@RestController
@RequestMapping("/openapi/open/netPad")
@Api(tags = "实例网络")
public class NetPadController {


    @Resource
    private INetPadService netPadService;

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "实例网络创建", httpMethod = "POST", notes = "新增实例网络")
    public Result<?> saveNetPad(@Valid @RequestBody NetPadDTO param) {
        NetPad netPad = new NetPad();
        BeanUtil.copyProperties(param, netPad);
        return netPadService.saveNetPad(netPad);
    }
}
