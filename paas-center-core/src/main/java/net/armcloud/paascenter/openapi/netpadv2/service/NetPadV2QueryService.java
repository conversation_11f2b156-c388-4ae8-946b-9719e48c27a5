package net.armcloud.paascenter.openapi.netpadv2.service;

import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.entity.paas.*;

/**
 * 网存实例V2查询服务接口
 * 封装所有查询逻辑，避免在校验器中直接使用Mapper
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
public interface NetPadV2QueryService {

    /**
     * 根据规格编码查询资源规格
     */
    ResourceSpecification getResourceSpecificationByCode(String specificationCode);

    /**
     * 根据客户ID和集群编码查询网络存储资源
     */
    NetStorageRes getNetStorageResByCustomerAndCluster(Long customerId, String clusterCode);

    /**
     * 根据镜像ID、客户ID和来源查询客户上传镜像
     */
    CustomerUploadImage getCustomerUploadImageByIdAndCustomer(String imageId, Long customerId, SourceTargetEnum sourceTarget);

    /**
     * 根据ADI模板ID查询真机模板
     */
    RealPhoneTemplate getRealPhoneTemplateById(Long realPhoneTemplateId);

    /**
     * 随机查询可用的ADI模板
     */
    RealPhoneTemplate getRandomRealPhoneTemplate(Integer androidImageVersion);

    /**
     * 根据布局编码、客户ID和来源查询屏幕布局
     */
    ScreenLayout getScreenLayoutByCodeAndCustomer(String layoutCode, Long customerId, SourceTargetEnum sourceTarget);

    /**
     * 根据客户ID查询推流类型
     */
    Integer getStreamTypeByCustomerId(Long customerId);

    /**
     * 根据集群编码和配置键查询边缘集群配置
     */
    String getEdgeClusterConfigurationByKey(String clusterCode, EdgeClusterConfigurationEnum configKey);

    /**
     * 根据组ID和客户ID查询组信息
     */
    PadGroup getGroupInfo(Integer groupId, Long customerId);
}
