
package net.armcloud.paascenter.openapi.rocketmq;

import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQListener;
import net.armcloud.paascenter.common.rocketmq.anotation.AliRocketMQMsgListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Slf4j
@Service
@AliRocketMQMsgListener(consumerGroup = "${producer-topic.testTopic}_consumer", topic = "${producer-topic.testTopic}")
public class TestTopicConsumerListener implements AliRocketMQListener<MessageView> {
    @Override
    public void onMessage(MessageView messageView) throws Exception {
        log.info("收到消息:{}", messageView);
        String str = StandardCharsets.UTF_8.decode(messageView.getBody()).toString();
        log.info("收到消息内容:{}", str);
    }
}

