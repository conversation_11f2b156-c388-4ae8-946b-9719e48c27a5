package net.armcloud.paascenter.openapi.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.domain.Page;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.model.dto.AdiTemplateLogQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.AdiTemplateLogVO;
import net.armcloud.paascenter.openapi.service.IAdiTemplateLogService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * ADI模板操作日志管理控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Slf4j
@RestController
@RequestMapping("/manage/adi-template-log")
@Api(tags = "ADI模板操作日志管理")
public class AdiTemplateLogController {

    @Resource
    private IAdiTemplateLogService adiTemplateLogService;

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ApiOperation(value = "查询ADI模板操作日志列表", httpMethod = "POST", notes = "支持按模板ID、操作类型、操作人等条件筛选")
    public Result<Page<AdiTemplateLogVO>> list(@RequestBody @Valid AdiTemplateLogQueryDTO param) {
        log.info("Query ADI template logs with params: {}", param);
        return Result.ok(adiTemplateLogService.queryAdiTemplateLogs(param));
    }

    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    @ApiOperation(value = "查询ADI模板操作日志详情", httpMethod = "GET", notes = "根据日志ID查询详情")
    public Result<AdiTemplateLogVO> detail(Long id) {
        log.info("Query ADI template log detail, id: {}", id);
        return Result.ok(adiTemplateLogService.getAdiTemplateLogDetail(id));
    }

    @RequestMapping(value = "/listByTemplateId", method = RequestMethod.GET)
    @ApiOperation(value = "查询指定ADI模板的操作日志", httpMethod = "GET", notes = "根据模板ID查询其所有操作记录")
    public Result<Page<AdiTemplateLogVO>> listByTemplateId(Long templateId, Integer pageNo, Integer pageSize) {
        log.info("Query ADI template logs by templateId: {}, pageNo: {}, pageSize: {}", templateId, pageNo, pageSize);
        AdiTemplateLogQueryDTO param = new AdiTemplateLogQueryDTO();
        param.setTemplateId(templateId);
        param.setPage(pageNo != null ? pageNo : 1);
        param.setRows(pageSize != null ? pageSize : 10);
        return Result.ok(adiTemplateLogService.queryAdiTemplateLogs(param));
    }
}
