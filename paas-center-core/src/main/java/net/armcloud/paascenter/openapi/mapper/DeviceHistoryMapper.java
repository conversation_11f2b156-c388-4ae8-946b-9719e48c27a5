package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.DeviceHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceHistoryMapper extends BaseMapper<DeviceHistory> {

    /**
     * 批量插入设备历史记录
     * @param records 历史记录列表
     * @return 受影响的行数
     */
    int batchInsert(@Param("records") List<DeviceHistory> records);

}
