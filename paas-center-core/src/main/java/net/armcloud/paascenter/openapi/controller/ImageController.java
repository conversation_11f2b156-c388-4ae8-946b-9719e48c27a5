package net.armcloud.paascenter.openapi.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import net.armcloud.paascenter.common.model.dto.api.UploadImageDTO;
import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.manager.DictManager;
import net.armcloud.paascenter.openapi.model.dto.DownloadAndPushResultDTO;
import net.armcloud.paascenter.openapi.model.dto.QueryImageDTO;
import net.armcloud.paascenter.openapi.model.dto.SelectImageInfoDTO;
import net.armcloud.paascenter.openapi.model.dto.UploadImageFromUrlDTO;
import net.armcloud.paascenter.openapi.model.vo.SelectImageInfoVO;
import net.armcloud.paascenter.openapi.model.vo.UploadImageFromUrlVO;
import net.armcloud.paascenter.openapi.service.ICustomerUploadImageService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static net.armcloud.paascenter.common.core.constant.paas.DictConstants.Type.ROM_VERSION;

@RestController
@RequestMapping("/openapi/open/image")
@Api(tags = "镜像")
@Slf4j
public class ImageController {

    @Resource
    private ICustomerUploadImageService customerUploadImageService;

    @Resource
    private DictManager dictManager;


    @ApiOperation(value = "创建镜像上传任务")
    @PostMapping("/uploadImage")
    public Result<?> uploadImage(@RequestBody @Valid UploadImageDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        List<String> list = dictManager.selectValueByTypeList(ROM_VERSION);
        if (CollUtil.isEmpty(list) || StrUtil.isBlank(param.getRomVersion()) || !list.contains(param.getRomVersion())) {
            return Result.fail("romVersion 参数格式错误");
        }
        return Result.ok(customerUploadImageService.uploadImageContainer(param));
    }

    @ApiOperation(value = "镜像上传任务")
    @PostMapping("/uploadImageFromUrl")
    public Result<UploadImageFromUrlVO> uploadImageFromUrl(@RequestBody @Valid UploadImageFromUrlDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));

        List<String> list = dictManager.selectValueByTypeList(ROM_VERSION);
        if (CollUtil.isEmpty(list) || StrUtil.isBlank(param.getRomVersion()) || !list.contains(param.getRomVersion())) {
            return Result.fail("romVersion 参数格式错误");
        }
        return Result.ok(customerUploadImageService.uploadImageFromUrl(param));
    }


    @PostMapping("/selectImageInfo")
    public Result<SelectImageInfoVO> selectImageInfo(@RequestBody @Valid SelectImageInfoDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(customerUploadImageService.selectImageInfo(param));
    }

    @PostMapping("/sendDataToDockerDownloadAndPush")
    public Result<?> sendDataToDockerDownloadAndPush() {
        customerUploadImageService.sendDataToDockerDownloadAndPush();
        return Result.ok();
    }

    @PostMapping("/callBackResult")
    public Result<?> callBackResult(@RequestBody DownloadAndPushResultDTO dto) {
        customerUploadImageService.callBackResult(dto);
        return Result.ok();
    }




    @ApiOperation(value = "获取镜像列表")
    @PostMapping("/queryImageList")
    public Result<?> queryImageList(@Valid @RequestBody QueryImageDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(customerUploadImageService.queryImageList(param));
    }
}
