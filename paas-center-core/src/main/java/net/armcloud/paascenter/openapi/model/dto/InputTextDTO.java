package net.armcloud.paascenter.openapi.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 云机文本信息输入
 */
@Data
public class InputTextDTO {

    private Long customerId;
    /**实例编号 必填*/
    @Size(min = 1,max = 200,message = "实例数量不少于1个")
    @NotNull(message = "padCodes cannot null")
    private List<String> padCodes;

    /**点击坐标组*/
    @NotNull(message = "text cannot null")
    private String text;



}
