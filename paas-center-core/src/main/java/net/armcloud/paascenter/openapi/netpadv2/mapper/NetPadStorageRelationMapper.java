package net.armcloud.paascenter.openapi.netpadv2.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadResUnitRelationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 实例与存储关联关系Mapper
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Mapper
public interface NetPadStorageRelationMapper extends BaseMapper<NetPadResUnitRelationDO> {

    /**
     * 批量插入关联关系
     */
    int batchInsert(@Param("relations") List<NetPadResUnitRelationDO> relations);

    NetPadResUnitRelationDO getByPadCode(@Param("padCode") String padCode);

    List<NetPadResUnitRelationDO> selectByPadCodes(@Param("padCodes") List<String> padCodes);
}
