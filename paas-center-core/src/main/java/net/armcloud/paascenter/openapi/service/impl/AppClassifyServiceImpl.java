package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import net.armcloud.paascenter.common.client.internal.vo.PadInfoVO;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.common.model.dto.api.TriggeringBlackDTO;
import net.armcloud.paascenter.openapi.model.dto.*;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyDetailVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyPadDetailVO;
import net.armcloud.paascenter.openapi.model.vo.AppClassifyVO;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.service.IAppClassifyService;
import net.armcloud.paascenter.openapi.service.IPadService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyPadRelation;
import net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyRelation;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;

/**
 * 黑白名单业务层
 */
@Service
public class AppClassifyServiceImpl extends ServiceImpl<CustomerAppClassifyMapper, CustomerAppClassify> implements IAppClassifyService {
    @Resource
    private CustomerAppClassifyMapper customerAppClassifyMapper;
    @Resource
    private CustomerAppClassifyRelationMapper customerAppClassifyRelationMapper;
    @Resource
    private CustomerAppClassifyPadRelationMapper customerAppClassifyPadRelationMapper;
    @Resource
    private PadMapper padMapper;
    @Resource
    private IPadService padServiceImpl;
    @Resource
    private CustomerMapper customerMapper;

    /**
     * 设置黑白名单线程池
     */
    private final ExecutorService whiteAndBlackThreadPool = new ThreadPoolExecutor(5, 10,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000), new ThreadFactoryBuilder()
            .setNameFormat("SendNewAppWhiteAndBlackListThreadPool-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());

    private int getAppNum(Long id) {
        Long count = customerAppClassifyRelationMapper.selectCount(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                .eq("app_classify_id", id));
        return count != null ? count.intValue() : 0;
    }

    /**
     * 分页获取黑白名单列表
     * @param param
     * @return
     */
    @Override
    public List<AppClassifyVO> list(AppClassifyQueryDTO param) {
        PageHelper.startPage(param.getPage(), param.getRows());
        List<CustomerAppClassify> appMarketVOList = customerAppClassifyMapper.selectList(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("delete_flag",0)
                .eq(param.getCustomerId() != null,"customer_id",param.getCustomerId())
                .eq(param.getClassifyType() != null,"classify_type",param.getClassifyType())
                .like(StrUtil.isNotEmpty(param.getClassifyName()),"classify_name","%" + param.getClassifyName() + "%")
                .orderByDesc("id"));
        for (CustomerAppClassify customerAppClassify : appMarketVOList) {
            customerAppClassify.setAppNum(getAppNum(customerAppClassify.getId()));
        }
        List<AppClassifyVO> appClassifyVOList = BeanUtil.copyToList(appMarketVOList,AppClassifyVO.class);
        return appClassifyVOList;
    }


    /**
     * 获取黑白名单详情
     * @param id
     * @return
     */
    @Override
    public AppClassifyDetailVO detail(Long id,Long customerId) {
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper.selectById(id);
        if(customerAppClassify == null || customerAppClassify.getDeleteFlag()
                || !customerAppClassify.getCustomerId().equals(customerId)){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                .eq("app_classify_id",customerAppClassify.getId())
                .eq("customer_id",customerAppClassify.getCustomerId()));
        AppClassifyDetailVO appClassifyDetailVO = buildAppClassifyDetailVO(customerAppClassify,customerAppClassifyRelationList);
        return appClassifyDetailVO;
    }
    /**
     * 保存编辑黑白名单
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(AppClassifySaveDTO param) {
        Boolean isInsert = param.getId() == null;
        CustomerAppClassify appClassifyStatus = null;
        if(!isInsert){
            //编辑
            //判断该客户应用市场配置是否正常
            appClassifyStatus = customerAppClassifyMapper.selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                    .eq("id",param.getId()).eq("delete_flag",0));
            if(appClassifyStatus == null){
                throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
            }
        }else{
            //新增时 关联所有实例的模式下 一个类型只能存一条数据
            if(param.getApplyAllInstances() == null || param.getApplyAllInstances()){
                boolean exists = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("customer_id",param.getCustomerId()).eq("classify_type",param.getClassifyType()).eq("delete_flag",0));
                if(exists){
                    throw new BasicException(CUSTOMER_APP_CLASSIF_LIMIT_ONE);
                }
            }else{
                //同一个类型不能同时存在两个模式(由于模式1只限制一条 所以不用额外校验)
                boolean exists = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                        .eq("customer_id",param.getCustomerId()).eq("classify_type",param.getClassifyType()).eq("apply_all_instances",1).eq("delete_flag",0));
                if(exists){
                    throw new BasicException(CUSTOMER_APP_CLASSIF_LIMIT_MODE);
                }
            }
        }
        //判断分类名在该客户下是否重复
        Boolean classifyNameExist = customerAppClassifyMapper.exists(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("delete_flag",0)
                .eq("customer_id",param.getCustomerId())
                .eq("classify_name",param.getClassifyName())
                .eq("classify_type",param.getClassifyType())
                .notIn(!isInsert,"id",param.getId()));
        if(classifyNameExist){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NAME_REPEAT);
        }
        String customerAccount = getCustomerAccount(param.getCustomerId());
        //构建黑白名单对象
        CustomerAppClassify customerAppClassifySave = buildCustomerAppClassify(isInsert,param,customerAccount);
        if(isInsert){
            customerAppClassifyMapper.cusInsert(customerAppClassifySave);
        }else{
            //清除该用户所有关联分类应用
            customerAppClassifyRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                    .eq("app_classify_id",param.getId()).eq("customer_id",param.getCustomerId()));
            customerAppClassifyMapper.updateById(customerAppClassifySave);
        }

        if(CollUtil.isNotEmpty(param.getAppInfos())){
            //构建黑白名单关联应用对象
            List<CustomerAppClassifyRelation> customerAppClassifyRelationSaveList = buildCustomerAppClassifyRelation(param,customerAppClassifySave.getId(),customerAccount);
            if(CollUtil.isNotEmpty(customerAppClassifyRelationSaveList)){
                customerAppClassifyRelationMapper.batchInsert(customerAppClassifyRelationSaveList);
            }
        }

        boolean isBlack = param.getClassifyType() == 2;
        if(param.getApplyAllInstances() != null && !param.getApplyAllInstances()){
            //修改时发送黑白名单指令 新增时还未配置padCode则无需发送
            if(!isInsert){
                //先获取原先的padCode
                List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                        .eq("customer_id",param.getCustomerId()).eq("app_classify_id",param.getId()));
                //触发黑白名单
                sendAppWhiteAndBlackList(param.getCustomerId(),oldCustomerAppClassifyPadRelationList,isBlack);
            }
        }else{
            //黑白名单 每次新增和编辑都要触发该客户下所有实例设置黑白名单
            List<PadInfoVO> pads = padMapper.selectValidPadCodeByCustomerId(param.getCustomerId());
            if(CollUtil.isNotEmpty(pads)){
                sendNewAppWhiteAndBlackList(param.getCustomerId(), pads,isBlack);
            }
        }
        return customerAppClassifySave.getId();
    }

    /**
     * 添加黑白名单app
     * @param param
     * @return
     */
    @Override
    public void addApp(AppClassifyAddAppDTO param) {
        //判断黑白名单是否存在
        CustomerAppClassify appClassifyStatus = customerAppClassifyMapper.selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("id",param.getId())
                .eq("customer_id",param.getCustomerId())
                .eq("delete_flag",0));
        if(appClassifyStatus == null){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        Set<Long> appIds = new HashSet<>();
        for(AppClassifyAddAppDTO.AppInfo appInfo : param.getAppInfos()){
            appIds.add(appInfo.getAppId());
        }
        //判断关联应用是否存在
        List<CustomerAppClassifyRelation> customerAppClassifyRelationList = customerAppClassifyRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                .in("app_id",appIds)
                .eq("customer_id",param.getCustomerId())
                .eq("app_classify_id",param.getId()));
        if(CollUtil.isNotEmpty(customerAppClassifyRelationList)){
            //剔除已存在的app
            Map<Long,String> customerAppClassifyRelationExistMap = customerAppClassifyRelationList.stream().collect(Collectors.toMap(CustomerAppClassifyRelation::getAppId, CustomerAppClassifyRelation::getAppName,(key1 , key2)-> key2 ));
            List<AppClassifyAddAppDTO.AppInfo> newAppInfos = new ArrayList<>();
            for(AppClassifyAddAppDTO.AppInfo appInfo : param.getAppInfos()){
                if(!customerAppClassifyRelationExistMap.containsKey(appInfo.getAppId())){
                    newAppInfos.add(appInfo);
                }
            }
            param.setAppInfos(newAppInfos);
        }
        //添加
        //构建黑白名单关联应用对象
        String customerAccount = getCustomerAccount(param.getCustomerId());
        List<CustomerAppClassifyRelation> customerAppClassifyRelationSaveList = buildCustomerAppClassifyRelation(param,customerAccount);
        if(CollUtil.isNotEmpty(customerAppClassifyRelationSaveList)){
            customerAppClassifyRelationMapper.batchInsert(customerAppClassifyRelationSaveList);

            boolean isBlack = appClassifyStatus.getClassifyType() == 2;
            if(appClassifyStatus.getApplyAllInstances() != null && !appClassifyStatus.getApplyAllInstances()){
                //先获取该分类下的padCode
                List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                        .eq("customer_id",param.getCustomerId()).eq("app_classify_id",param.getId()));
                sendAppWhiteAndBlackList(param.getCustomerId(),oldCustomerAppClassifyPadRelationList,isBlack);
            }else{
                //黑白名单 每次新增和编辑都要触发该客户下所有实例设置黑白名单
                List<PadInfoVO> pads = padMapper.selectValidPadCodeByCustomerId(param.getCustomerId());
                if(CollUtil.isNotEmpty(pads)){
                    sendNewAppWhiteAndBlackList(param.getCustomerId(), pads,isBlack);
                }
            }
        }
    }

    /**
     * 黑白名单关联实例详情
     * @param id
     * @return
     */
    @Override
    public AppClassifyPadDetailVO padDetail(Long id,Long customerId) {
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper.selectById(id);
        if(customerAppClassify == null || customerAppClassify.getDeleteFlag()
                || !customerAppClassify.getCustomerId().equals(customerId)){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                .eq("app_classify_id",customerAppClassify.getId())
                .eq("customer_id",customerAppClassify.getCustomerId()));
        AppClassifyPadDetailVO appClassifyDetailVO = buildAppClassifyPadDetailVO(customerAppClassify, customerAppClassifyPadRelationList);
        return appClassifyDetailVO;
    }
    /**
     * 黑白名单关联实例保存编辑
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void padSave(AppClassifyPadSaveDTO param) {
        //判断该客户应用市场配置是否正常
        CustomerAppClassify appClassifyStatus = customerAppClassifyMapper.selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("id",param.getId()).eq("delete_flag",0));
        if(appClassifyStatus == null){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }

        //使用所有实例的模式不允许这里添加实例
        if(appClassifyStatus.getApplyAllInstances() != null && appClassifyStatus.getApplyAllInstances()){
            throw new BasicException(NOW_MODE_NOT_ALLOW_ADD_PAD_CODE);
        }

        //先获取原先的padCode
        List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                .eq("customer_id",param.getCustomerId()).eq("app_classify_id",param.getId()));

        //清除该用户分类下所有关联实例
        customerAppClassifyPadRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                .eq("customer_id",param.getCustomerId()).eq("app_classify_id",param.getId()));

        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationSaveList = null;
        if(CollUtil.isNotEmpty(param.getAppPadInfos())){
            //构建黑白名单关联应用对象
            String customerAccount = getCustomerAccount(param.getCustomerId());
            customerAppClassifyPadRelationSaveList = buildCustomerAppClassifyPadRelation(param,customerAccount);
            if(CollUtil.isNotEmpty(customerAppClassifyPadRelationSaveList)){
                customerAppClassifyPadRelationMapper.batchInsert(customerAppClassifyPadRelationSaveList);
                CustomerAppClassify customerAppClassifyUpdate = new CustomerAppClassify();
                customerAppClassifyUpdate.setId(param.getId());
                customerAppClassifyUpdate.setPadNum(customerAppClassifyPadRelationSaveList.size());
                customerAppClassifyUpdate.setUpdateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
                customerAppClassifyMapper.updateById(customerAppClassifyUpdate);
            }
        }

        //分离出新增的padCode和未新增的padCode
        List<CustomerAppClassifyPadRelation> addCustomerAppClassifyPadRelationList = new ArrayList<>();
        List<CustomerAppClassifyPadRelation> notAddCustomerAppClassifyPadRelationList = new ArrayList<>();
        Map<String, Long> newMap = null;
        if (CollUtil.isNotEmpty(customerAppClassifyPadRelationSaveList)) {
            addCustomerAppClassifyPadRelationList.addAll(customerAppClassifyPadRelationSaveList);
            newMap = customerAppClassifyPadRelationSaveList.stream().collect(Collectors.toMap(CustomerAppClassifyPadRelation::getPadCode, CustomerAppClassifyPadRelation::getAppClassifyId,(key1 , key2) -> key1));
        }
        if (CollUtil.isNotEmpty(oldCustomerAppClassifyPadRelationList)) {
            List<CustomerAppClassifyPadRelation> notAdd = new ArrayList<>();
            if(CollUtil.isNotEmpty(newMap)){
                //剥离出未新增的
                for(CustomerAppClassifyPadRelation customerAppClassifyPadRelation : oldCustomerAppClassifyPadRelationList){
                    if(newMap.get(customerAppClassifyPadRelation.getPadCode()) == null){
                        notAddCustomerAppClassifyPadRelationList.add(customerAppClassifyPadRelation);
                    }
                }
            }else{
                notAddCustomerAppClassifyPadRelationList.addAll(oldCustomerAppClassifyPadRelationList);
            }
        }
        //发送黑白名单指令任务
        boolean isBlack = appClassifyStatus.getClassifyType() == 2;
        sendAppWhiteAndBlackList(param.getCustomerId(),addCustomerAppClassifyPadRelationList,isBlack);
        sendAppWhiteAndBlackList(param.getCustomerId(),notAddCustomerAppClassifyPadRelationList,isBlack);
    }

    /**
     * 增加黑白名单实例关联
     * @param param
     * @return
     */
    @Override
    public void addPad(AppClassifyAddPadDTO param) {
        //判断黑白名单是否存在
        CustomerAppClassify appClassifyStatus = customerAppClassifyMapper.selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("id",param.getId())
                .eq("customer_id",param.getCustomerId())
                .eq("delete_flag",0));
        if(appClassifyStatus == null){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        //使用所有实例的模式不允许这里添加实例
        if(appClassifyStatus.getApplyAllInstances() != null && appClassifyStatus.getApplyAllInstances()){
            throw new BasicException(NOW_MODE_NOT_ALLOW_ADD_PAD_CODE);
        }
        Set<String> padCodes = new HashSet<>();
        for(AppClassifyAddPadDTO.AppPadInfo appPadInfo : param.getAppPadInfos()){
            padCodes.add(appPadInfo.getPadCode());
        }
        //判断关联实例是否存在
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                .in("pad_code",padCodes)
                .eq("customer_id",param.getCustomerId())
                .eq("app_classify_id",param.getId()));
        if(CollUtil.isNotEmpty(customerAppClassifyPadRelationList)){
            //剔除已存在的实例
            Map<String,String> customerAppClassifyPadRelationExistMap = customerAppClassifyPadRelationList.stream().collect(Collectors.toMap(CustomerAppClassifyPadRelation::getPadCode, CustomerAppClassifyPadRelation::getPadCode,(key1 , key2)-> key2 ));
            List<AppClassifyAddPadDTO.AppPadInfo> newPadInfos = new ArrayList<>();
            for(AppClassifyAddPadDTO.AppPadInfo appPadInfo : param.getAppPadInfos()){
                if(!customerAppClassifyPadRelationExistMap.containsKey(appPadInfo.getPadCode())){
                    newPadInfos.add(appPadInfo);
                }
            }
            param.setAppPadInfos(newPadInfos);
        }
        //添加
        //构建黑白名单关联应用对象
        String customerAccount = getCustomerAccount(param.getCustomerId());
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationSaveList = buildCustomerAppClassifyPadRelation(param,customerAccount);
        if(CollUtil.isNotEmpty(customerAppClassifyPadRelationSaveList)){
            customerAppClassifyPadRelationMapper.batchInsert(customerAppClassifyPadRelationSaveList);
            customerAppClassifyMapper.updatePadNum(param.getId(),customerAppClassifyPadRelationSaveList.size());
        }

        //触发黑白名单
        boolean isBlack = appClassifyStatus.getClassifyType() == 2;
        sendAppWhiteAndBlackList(param.getCustomerId(),customerAppClassifyPadRelationSaveList,isBlack);
    }

    /**
     * 删除黑白名单
     * @param id
     * @return
     */
    @Override
    public void del(Long id,Long customerId) {
        CustomerAppClassify customerAppClassify = customerAppClassifyMapper.selectById(id);
        if(customerAppClassify == null || customerAppClassify.getDeleteFlag()
                || !customerAppClassify.getCustomerId().equals(customerId)){
            throw new BasicException(APP_CLASSIFY_NOT_EXIST_OR_DEL);
        }
        //应用类型逻辑删
        String customerAccount = getCustomerAccount(customerId);
        CustomerAppClassify customerAppClassifyUpdate = new CustomerAppClassify();
        customerAppClassifyUpdate.setId(customerAppClassify.getId());
        customerAppClassifyUpdate.setDeleteFlag(true);
        customerAppClassifyUpdate.setUpdateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(customerId));
        customerAppClassifyUpdate.setUpdateTime(new Date());
        int customerAppClassifyStatus = customerAppClassifyMapper.updateById(customerAppClassifyUpdate);
        if(customerAppClassifyStatus > 0){
            //先获取原先的padCode
            List<CustomerAppClassifyPadRelation> oldCustomerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                    .eq("customer_id",customerAppClassify.getCustomerId()).eq("app_classify_id",customerAppClassify.getId()));
            //关联数据物理删
            customerAppClassifyRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyRelation.class)
                    .eq("customer_id",customerAppClassify.getCustomerId())
                    .eq("app_classify_id",customerAppClassify.getId()));
            customerAppClassifyPadRelationMapper.delete(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                    .eq("customer_id",customerAppClassify.getCustomerId())
                    .eq("app_classify_id",customerAppClassify.getId()));

            //发送黑白名单指令任务
            boolean isBlack = customerAppClassify.getClassifyType() == 2;
            if(customerAppClassify.getApplyAllInstances() != null && !customerAppClassify.getApplyAllInstances()){
                sendAppWhiteAndBlackList(customerAppClassify.getCustomerId(),oldCustomerAppClassifyPadRelationList,isBlack);
            }else{
                //黑白名单 每次删除都要触发该客户下所有实例设置黑白名单
                List<PadInfoVO> pads = padMapper.selectValidPadCodeByCustomerId(customerAppClassify.getCustomerId());
                if(CollUtil.isNotEmpty(pads)){
                    sendNewAppWhiteAndBlackList(customerAppClassify.getCustomerId(), pads,isBlack);
                }
            }
        }
    }

    /**
     * 删除黑白名单实例关联
     * @param param
     * @return
     */
    @Override
    public void delPad(AppClassifyDelPadDTO param) {
        //判断黑白名单是否存在
        CustomerAppClassify appClassifyStatus = customerAppClassifyMapper.selectOne(new QueryWrapper<>(CustomerAppClassify.class)
                .eq("id",param.getId())
                .eq("customer_id",param.getCustomerId())
                .eq("delete_flag",0));
        if(appClassifyStatus == null){
            throw new BasicException(CUSTOMER_APP_CLASSIF_NOT_EXISTS);
        }
        //使用所有实例的模式不允许这里删除实例
        if(appClassifyStatus.getApplyAllInstances() != null && appClassifyStatus.getApplyAllInstances()){
            throw new BasicException(NOW_MODE_NOT_ALLOW_DEL_PAD_CODE);
        }
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = customerAppClassifyPadRelationMapper.selectList(new QueryWrapper<>(CustomerAppClassifyPadRelation.class)
                .eq("app_classify_id",param.getId())
                .in("pad_code",param.getPadCodes()));
        //如果入参的实例不存在则无需继续执行
        if(CollUtil.isEmpty(customerAppClassifyPadRelationList)){
            return;
        }
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationDelList = new ArrayList<>();
        Set<Long> padCodeIds = new HashSet<>();
        for(CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList){
            customerAppClassifyPadRelationDelList.add(customerAppClassifyPadRelation);
            padCodeIds.add(customerAppClassifyPadRelation.getId());
        }
        //删除实例关联表中的这一批padcode并更新黑白名单中的实例数量字段
        int delCount = customerAppClassifyPadRelationMapper.deleteBatchIds(padCodeIds);
        if(delCount > 0){
            customerAppClassifyMapper.updatePadNum(param.getId(),-delCount);
        }
        //触发黑白名单
        boolean isBlack = appClassifyStatus.getClassifyType() == 2;
        sendAppWhiteAndBlackList(param.getCustomerId(),customerAppClassifyPadRelationDelList,isBlack);
    }


    /**
     * 构建黑白名单详情对象
     * @param customerAppClassify
     * @param customerAppClassifyRelationList
     * @return
     */
    private AppClassifyDetailVO buildAppClassifyDetailVO(CustomerAppClassify customerAppClassify, List<CustomerAppClassifyRelation> customerAppClassifyRelationList){
        AppClassifyDetailVO appClassifyDetailVO = BeanUtil.copyProperties(customerAppClassify,AppClassifyDetailVO.class);
        if(CollUtil.isNotEmpty(customerAppClassifyRelationList)){
            List<AppClassifyDetailVO.AppInfo> appInfos = BeanUtil.copyToList(customerAppClassifyRelationList,AppClassifyDetailVO.AppInfo.class);
            appClassifyDetailVO.setAppInfos(appInfos);
        }
        return appClassifyDetailVO;
    }

    /**
     * 构建黑白名单关联实例对象
     * @param customerAppClassify
     * @param customerAppClassifyPadRelationList
     * @return
     */
    private AppClassifyPadDetailVO buildAppClassifyPadDetailVO(CustomerAppClassify customerAppClassify
            , List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList) {
        AppClassifyPadDetailVO appClassifyPadDetailVO = BeanUtil.copyProperties(customerAppClassify,AppClassifyPadDetailVO.class);
        if(CollUtil.isNotEmpty(customerAppClassifyPadRelationList)){
            List<AppClassifyPadDetailVO.AppPadInfo> appInfos = BeanUtil.copyToList(customerAppClassifyPadRelationList,AppClassifyPadDetailVO.AppPadInfo.class);
            appClassifyPadDetailVO.setAppPadInfos(appInfos);
        }
        return appClassifyPadDetailVO;
    }

    /**
     * 构建黑白名单对象
     * @param param
     * @return
     */
    private CustomerAppClassify buildCustomerAppClassify(Boolean isInsert, AppClassifySaveDTO param,String customerAccount){
        //计算应用总数
        Integer appNum = 0;
        if(CollUtil.isNotEmpty(param.getAppInfos())){
            appNum += param.getAppInfos().size();
        }
        CustomerAppClassify customerAppClassify = new CustomerAppClassify();
        customerAppClassify.setCustomerId(param.getCustomerId());
        customerAppClassify.setClassifyName(param.getClassifyName());
        customerAppClassify.setAppNum(appNum);
        customerAppClassify.setRemark(param.getRemark());
        if(isInsert){
            customerAppClassify.setClassifyType(param.getClassifyType());
            if(param.getApplyAllInstances() != null){
                customerAppClassify.setApplyAllInstances(param.getApplyAllInstances());
            }
            customerAppClassify.setCreateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
            customerAppClassify.setCreateTime(new Date());
        }else{
            customerAppClassify.setId(param.getId());
        }
        customerAppClassify.setUpdateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
        customerAppClassify.setUpdateTime(new Date());
        customerAppClassify.setDeleteFlag(false);
        return customerAppClassify;
    }

    /**
     * 构建黑白名单关联应用对象
     * @param param
     * @param customerAppClassifyId
     * @return
     */
    private List<CustomerAppClassifyRelation> buildCustomerAppClassifyRelation(AppClassifySaveDTO param,Long customerAppClassifyId,String customerAccount){
        List<CustomerAppClassifyRelation> customerAppClassifyRelationList = BeanUtil.copyToList(param.getAppInfos(),CustomerAppClassifyRelation.class);
        if(CollUtil.isNotEmpty(customerAppClassifyRelationList)){
            for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                customerAppClassifyRelation.setCustomerId(param.getCustomerId());
                customerAppClassifyRelation.setAppClassifyId(customerAppClassifyId);
                customerAppClassifyRelation.setCreateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
                customerAppClassifyRelation.setCreateTime(new Date());
                customerAppClassifyRelation.setUpdateBy(customerAppClassifyRelation.getCreateBy());
                customerAppClassifyRelation.setUpdateTime(new Date());
            }
        }
        return customerAppClassifyRelationList;
    }

    /**
     * 构建黑白名单关联应用对象
     * @param param
     * @return
     */
    private List<CustomerAppClassifyRelation> buildCustomerAppClassifyRelation(AppClassifyAddAppDTO param,String customerAccount){
        List<CustomerAppClassifyRelation> customerAppClassifyRelationList = BeanUtil.copyToList(param.getAppInfos(),CustomerAppClassifyRelation.class);
        if(CollUtil.isNotEmpty(customerAppClassifyRelationList)){
            for (CustomerAppClassifyRelation customerAppClassifyRelation : customerAppClassifyRelationList) {
                customerAppClassifyRelation.setCustomerId(param.getCustomerId());
                customerAppClassifyRelation.setAppClassifyId(param.getId());
                customerAppClassifyRelation.setCreateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
                customerAppClassifyRelation.setCreateTime(new Date());
                customerAppClassifyRelation.setUpdateBy(customerAppClassifyRelation.getCreateBy());
                customerAppClassifyRelation.setUpdateTime(new Date());
            }
        }
        return customerAppClassifyRelationList;
    }

    /**
     * 构建黑白名单关联实例对象
     * @param param
     * @return
     */
    private List<CustomerAppClassifyPadRelation> buildCustomerAppClassifyPadRelation(AppClassifyPadSaveDTO param,String customerAccount){
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = BeanUtil.copyToList(param.getAppPadInfos(), CustomerAppClassifyPadRelation.class);
        if(CollUtil.isNotEmpty(customerAppClassifyPadRelationList)){
            for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList) {
                customerAppClassifyPadRelation.setCustomerId(param.getCustomerId());
                customerAppClassifyPadRelation.setAppClassifyId(param.getId());
                customerAppClassifyPadRelation.setCreateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
                customerAppClassifyPadRelation.setCreateTime(new Date());
                customerAppClassifyPadRelation.setUpdateBy(customerAppClassifyPadRelation.getCreateBy());
                customerAppClassifyPadRelation.setUpdateTime(new Date());
            }
        }
        return customerAppClassifyPadRelationList;
    }

    /**
     * 构建黑白名单关联实例对象
     * @param param
     * @return
     */
    private List<CustomerAppClassifyPadRelation> buildCustomerAppClassifyPadRelation(AppClassifyAddPadDTO param,String customerAccount){
        List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList = BeanUtil.copyToList(param.getAppPadInfos(), CustomerAppClassifyPadRelation.class);
        if(CollUtil.isNotEmpty(customerAppClassifyPadRelationList)){
            for (CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList) {
                customerAppClassifyPadRelation.setCustomerId(param.getCustomerId());
                customerAppClassifyPadRelation.setAppClassifyId(param.getId());
                customerAppClassifyPadRelation.setCreateBy(StrUtil.isNotEmpty(customerAccount)?customerAccount:String.valueOf(param.getCustomerId()));
                customerAppClassifyPadRelation.setCreateTime(new Date());
                customerAppClassifyPadRelation.setUpdateBy(customerAppClassifyPadRelation.getCreateBy());
                customerAppClassifyPadRelation.setUpdateTime(new Date());
            }
        }
        return customerAppClassifyPadRelationList;
    }

    /**
     * 发送黑白名单指令任务
     * @param customerId
     * @param customerAppClassifyPadRelationList
     * @param isBlack
     */
    private void sendAppWhiteAndBlackList(Long customerId, List<CustomerAppClassifyPadRelation> customerAppClassifyPadRelationList,boolean isBlack){
        if(CollUtil.isNotEmpty(customerAppClassifyPadRelationList)){
            //key为规格 value为实例编号
            Map<String,List<String>> map = new HashMap<>();
            //以规格的维度发送黑白名单
            for(CustomerAppClassifyPadRelation customerAppClassifyPadRelation : customerAppClassifyPadRelationList){
                List<String> padCodes = map.get(customerAppClassifyPadRelation.getDeviceLevel());
                if(padCodes == null){
                    padCodes = new ArrayList<>();
                    map.put(customerAppClassifyPadRelation.getDeviceLevel(),padCodes);
                }
                padCodes.add(customerAppClassifyPadRelation.getPadCode());
            }
            for(Map.Entry<String,List<String>> entry : map.entrySet()){
                TriggeringBlackDTO triggeringBlackDTO = new TriggeringBlackDTO();
                triggeringBlackDTO.setIsNewApi(true);
                triggeringBlackDTO.setIsMergeAppClassifyList(true);
                triggeringBlackDTO.setCustomerId(customerId);
                triggeringBlackDTO.setPadCodes(entry.getValue());
                triggeringBlackDTO.setPadGrade(entry.getKey());
                triggeringBlackDTO.setSourceCode(SourceTargetEnum.PAAS);
                if(isBlack){
                    padServiceImpl.triggeringBlackListService(triggeringBlackDTO);
                }else{
                    padServiceImpl.triggeringWhiteListService(triggeringBlackDTO);
                }
            }
        }
    }

    /**
     * 发送黑白名单指令任务
     * @param customerId
     * @param padInfoVOList
     * @param isBlack
     */
    private void sendNewAppWhiteAndBlackList(Long customerId, List<PadInfoVO> padInfoVOList,boolean isBlack){
        whiteAndBlackThreadPool.submit(() -> {
            if(CollUtil.isNotEmpty(padInfoVOList)){
                //key为规格 value为实例编号
                Map<String,List<String>> map = new HashMap<>();
                //以规格的维度发送黑白名单
                for(PadInfoVO padInfoVO : padInfoVOList){
                    List<String> padCodes = map.get(padInfoVO.getDeviceLevel());
                    if(padCodes == null){
                        padCodes = new ArrayList<>();
                        map.put(padInfoVO.getDeviceLevel(),padCodes);
                    }
                    padCodes.add(padInfoVO.getPadCode());
                }
                for(Map.Entry<String,List<String>> entry : map.entrySet()){
                    List<List<String>> padCodes = Lists.partition(entry.getValue(),200);
                    for(List<String> subPadCodes : padCodes){
                        TriggeringBlackDTO triggeringBlackDTO = new TriggeringBlackDTO();
                        triggeringBlackDTO.setIsNewApi(true);
                        triggeringBlackDTO.setIsMergeAppClassifyList(true);
                        triggeringBlackDTO.setCustomerId(customerId);
                        triggeringBlackDTO.setPadCodes(subPadCodes);
                        triggeringBlackDTO.setPadGrade(entry.getKey());
                        triggeringBlackDTO.setSourceCode(SourceTargetEnum.PAAS);
                        triggeringBlackDTO.setApplyAllInstances(true);
                        if(isBlack){
                            padServiceImpl.triggeringBlackListService(triggeringBlackDTO);
                        }else{
                            padServiceImpl.triggeringWhiteListService(triggeringBlackDTO);
                        }
                    }
                }
            }
        });
    }

    /**
     * 获取用户账号名称
     * @param customerId
     * @return
     */
    private String getCustomerAccount(Long customerId){
        CustomerInfoVo customerInfoVo = customerMapper.getCustomerInfoById(customerId);
        if(customerInfoVo != null){
            return customerInfoVo.getCustomerAccount();
        }
        return null;
    }
}
