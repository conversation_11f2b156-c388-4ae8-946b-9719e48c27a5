package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.PadProperties;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PadPropertiesMapper extends BaseMapper<PadProperties> {

    /**
     * 插入属性
     *
     * @param properties PadProperties
     * @return int
     */
    int insertProperties(PadProperties properties);

    /**
     * @param properties PadProperties
     * @return int
     */
    int updateProperties(PadProperties properties);

    /**
     * 根据实例编号查询属性
     *
     * @param  padCode
     * @return PadProperties
     */
    PadProperties selectPropertiesByCode(@Param("padCode") String padCode);


    /**
     * 批量查询示例属性
     * @param padCodes
     * @return
     */
    List<PadProperties> findPropertiesByCodes(@Param("padCodes") List<String> padCodes);

    /**
     * 根据实例编号集合查询属性
     *
     * @param  padCodes
     * @return PadProperties
     */
    List<PadProperties> selectPropertiesByCodes(@Param("padCodes") List<String> padCodes);
}
