package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import net.armcloud.paascenter.openapi.mapper.CustomerMapper;
import net.armcloud.paascenter.openapi.mapper.PadGroupMapper;
import net.armcloud.paascenter.openapi.mapper.PadGroupNewMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.model.dto.PadGroupDTO;
import net.armcloud.paascenter.openapi.model.vo.CustomerInfoVo;
import net.armcloud.paascenter.openapi.model.vo.PadGroupVO;
import net.armcloud.paascenter.openapi.service.IPadGroupService;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadGroup;
import net.armcloud.paascenter.common.model.entity.paas.PadGroupNew;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;

@Slf4j
@Service
public class PadGroupServiceImpl implements IPadGroupService {

    @Resource
    private PadGroupMapper padGroupMapper;

    /**
     * 不要问为什么生成两个同一实体的mapper
     */
    private final PadGroupNewMapper padGroupNewMapper;

    private final PadMapper padMapper;

    private final CustomerMapper customerMapper;
    public PadGroupServiceImpl(PadGroupNewMapper padGroupNewMapper, PadMapper padMapper, CustomerMapper customerMapper) {
        this.padGroupNewMapper = padGroupNewMapper;
        this.padMapper = padMapper;
        this.customerMapper = customerMapper;
    }

    @Override
    public List<PadGroupVO> padGroupListService(PadGroupDTO param) {
        if (CollUtil.isNotEmpty(param.getGroupIds())) {
            List<PadGroup> groupId = padGroupMapper.selectList(new QueryWrapper<PadGroup>().in("group_id", param.getGroupIds()));
            if (CollectionUtils.isEmpty(groupId)) {
                throw new BasicException(GROUP_NOT_EXIST);
            }
        }
        return padGroupMapper.selectPadGroupListVO(param.getCustomerId(), param.getGroupIds(), param.getPadCode());
    }

    /**
     * 新增pad分组
     *  1.过滤掉groupId和groupName相同的分组
     *       1.1 如果有相同的分组，则抛出异常
     *       1.2 如果没有相同的分组，则新增分组
     *
     * @param param
     */
    @Override
    public void addPadGroup(PadGroupDTO param) {
        log.info("PadGroupServiceImpl  addPadGroup param>>>>>>>>>>>>>>>>>>>:{}", param);
        CustomerInfoVo customerInfoById = customerMapper.getCustomerInfoById(param.getCustomerId());
        if(customerInfoById == null){
            log.error("PadGroupServiceImpl  addPadGroup customer not exist>:{}", param.getCustomerId());
            throw new BasicException(CUSTOMER_NOT_EXIST);
        }
        LambdaQueryWrapper<PadGroupNew> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PadGroupNew::getGroupId, param.getGroupId())
                .or()
                .eq(PadGroupNew::getGroupName, param.getGroupName());
        List<PadGroupNew> padGroupNews = padGroupNewMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(padGroupNews)) {
            log.warn("PadGroupServiceImpl  addPadGroup group already exist>>>>>>>>>>>>>>>>>>>:{}", padGroupNews);
            throw new BasicException(GROUP_ALREADY_EXIST);
        }
        PadGroupNew build = PadGroupNew.builder().groupId(param.getGroupId())
                .customerId(param.getCustomerId())
                .groupName(param.getGroupName())
                .deleteFlag(PadGroupNew.DELETE_FLAG_NORMAL)
                .createTime(new Date())
                .createBy(param.getCreateUser())
                .remark(param.getRemark()).build();
        if (padGroupNewMapper.insert(build) > 0) {
            log.info("PadGroupServiceImpl  addPadGroup insert success>>>>>>>>>>>>>>>>>>>>:{}", param);
            return;
        }
        log.error("PadGroupServiceImpl addPadGroup insert  error>>>>>>>>>>>>>>>>>>>>:{}", param);
    }

    /**
     * 删除分组
     * @param ids 分组ID集合
     *            1. 查询出所有分组集合
     *            2. 遍历分组集合，根据customerID以及groupId查询出pad集合
     *            3. 如果pad集合不为空，则抛出异常（不能删除有数据的分组）
     *               3.1如果pad集合为空，则删除分组
     */
    @Override
    public void deletePadGroup(List<Long> ids) {
        log.info("PadGroupServiceImpl deletePadGroup ids>:{}", ids);
        Set<String> deleteGroupNames = new HashSet<>();
        LambdaQueryWrapper<PadGroupNew> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PadGroupNew::getGroupId, ids);
        List<PadGroupNew> padGroupNews = padGroupNewMapper.selectList(wrapper);
        padGroupNews.forEach(s->{
            LambdaQueryWrapper<Pad> padWrapper = new LambdaQueryWrapper<>();
            padWrapper.eq(Pad::getGroupId, s.getGroupId()).eq(Pad::getCustomerId, s.getCustomerId());
            List<Pad> pads = padMapper.selectList(padWrapper);
            if (pads.size() > 0) {
                log.error("PadGroupServiceImpl deletePadGroup group has pad>:{}", s.getGroupName());
                throw new BasicException(GROUP_EXIST_PAD);
            }
           if(padGroupNewMapper.deleteById(s)>0){
               deleteGroupNames.add(s.getGroupName());
           }

        });
        log.info("PadGroupServiceImpl deletePadGroup results deleteGroupNames>:{}", deleteGroupNames);
    }

    /***
     * 移动pad分组
     * 1. 查询待分组的板卡，是否存在公共池 bypadCode and online and status
     * 2. 查询出分组对应的pad

     */
    @Override
    public void movePadGroup(PadGroupDTO param) {
        log.info("PadGroupServiceImpl movePadGroup groupId>:{} padCode>:{}", param.getPadCode(), param.getPadCode());
        LambdaQueryWrapper<Pad> padWrapper = new LambdaQueryWrapper<>();
        padWrapper.eq(Pad::getPadCode, param.getPadCode())
                .eq(Pad::getOnline, Pad.IS_ONLINE_TRUE)
                .eq(Pad::getStatus,Pad.STATUS_ENABLE);
        Pad pad = padMapper.selectOne(padWrapper);
        log.info("PadGroupServiceImpl movePadGroup pad>:{}", pad.toString());
        Long aLong = Optional.ofNullable(pad).map(Pad::getGroupId).orElse(null);
        LambdaQueryWrapper<PadGroupNew> padGroupWrapper = new LambdaQueryWrapper<>();
        padGroupWrapper.eq(PadGroupNew::getGroupId, aLong)
                .eq(PadGroupNew::getDeleteFlag, PadGroupNew.DELETE_FLAG_NORMAL)
                .eq(PadGroupNew::getCustomerId, pad.getCustomerId());
        // TODO: 2025/1/20 此处前置只能从公共池中移动，目前padGroup中无法区分公共池和私有池，所以只能从GroupName中判断是否为公共池
        PadGroupNew padGroupNews = padGroupNewMapper.selectOne(padGroupWrapper);
        if(Optional.ofNullable(padGroupNews).map(PadGroupNew::getGroupName).orElse(PadGroupNew.COMMON_POOL).equals(PadGroupNew.COMMON_POOL)){
            pad.setGroupId(param.getGroupId());
            pad.setUpdateTime(new Date());
            pad.setUpdateBy(param.getCreateUser());
            int update = padMapper.updateById(pad);
            if(update <= 0){
                log.error("PadGroupServiceImpl movePadGroup update data pad error>:{}", pad.toString());
                throw new BasicException(GROUP_MOVE_PAD_ERROR);
            }
            return;
        }
        throw new BasicException(GROUP_MOVE_ONLY_COMMON_POOL);

    }
}
