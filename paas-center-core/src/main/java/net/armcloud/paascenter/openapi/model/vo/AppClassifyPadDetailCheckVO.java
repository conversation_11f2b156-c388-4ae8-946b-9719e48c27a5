package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 黑白名单查询响应对象
 */
@Data
public class AppClassifyPadDetailCheckVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "客户id")
    private Long customerId;
    @ApiModelProperty(value = "分类名称")
    private String classifyName;
    @ApiModelProperty(value = "分类类型 1白名单 2黑名单")
    private Integer classifyType;
    @ApiModelProperty(value = "实例编号")
    private String padCode;
    @ApiModelProperty(value = "黑白名单id")
    private Long appClassifyId;
}
