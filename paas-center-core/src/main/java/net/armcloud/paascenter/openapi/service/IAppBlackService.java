package net.armcloud.paascenter.openapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.SetUpBlackListDTO;
import net.armcloud.paascenter.common.model.entity.paas.AppBlack;

public interface IAppBlackService extends IService<AppBlack> {

    Result<?> setUpBlackList(SetUpBlackListDTO param);
}
