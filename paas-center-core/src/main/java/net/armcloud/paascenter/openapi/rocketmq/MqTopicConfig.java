package net.armcloud.paascenter.openapi.rocketmq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "producer-topic")
public class MqTopicConfig {

    /**
     * 测试主题
     */
    private String testTopic;

    /**
     * 云手机PAAS平台-实例状态变更主题
     */
    private String vcpPodStatus;

    /**
     * 云手机PAAS平台-物理机状态变更主题
     */
    private String vcpDeviceStatus;

    /**
     * 客户端异常信息推送mq
     */
    private String clientExceptionTopic;

    /**
     * 云手机PAAS平台-物理机创建云机任务结果主题
     */
    private String virtualizeDeviceTask;
}
