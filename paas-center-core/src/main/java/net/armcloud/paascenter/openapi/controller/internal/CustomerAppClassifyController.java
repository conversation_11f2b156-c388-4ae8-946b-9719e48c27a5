package net.armcloud.paascenter.openapi.controller.internal;

import net.armcloud.paascenter.common.client.internal.dto.QueryAppClassifyNameDTO;
import net.armcloud.paascenter.common.client.internal.facade.CustomerAppClassifyFacade;
import net.armcloud.paascenter.common.client.internal.vo.AppClassifyNameVO;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.openapi.service.ICustomerAppClassifyService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class CustomerAppClassifyController implements CustomerAppClassifyFacade {

    @Resource
    private ICustomerAppClassifyService customerAppClassifyService;

    @Override
    public Result<List<AppClassifyNameVO>> queryAppClassifyName(QueryAppClassifyNameDTO dto) {
        return Result.ok(customerAppClassifyService.queryAppClassifyName(dto));
    }
}
