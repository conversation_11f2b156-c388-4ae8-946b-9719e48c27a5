package net.armcloud.paascenter.openapi.service;

import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.PadAppPackageNameDTO;
import net.armcloud.paascenter.common.model.dto.api.PadCodesDTO;
import net.armcloud.paascenter.common.model.dto.api.PadDownloadAppFileDTO;
import net.armcloud.paascenter.common.model.dto.api.PadUninstallAppFileDTO;
import net.armcloud.paascenter.common.model.vo.api.PadInstalledAppVO;

import java.util.List;

public interface IPadAppService {
    /**
     * 下载应用
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> installApp(PadDownloadAppFileDTO param);

    /**
     * 卸载应用
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> uninstallApp(PadUninstallAppFileDTO param);

    /**
     * 启动应用
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> startApp(PadAppPackageNameDTO param);

    /**
     * 停止应用
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> stopApp(PadAppPackageNameDTO param);

    /**
     * 重启应用
     *
     * @return 任务id
     */
    List<GeneratePadTaskVO> restartApp(PadAppPackageNameDTO param);

    List<PadInstalledAppVO> listInstalledApp(PadCodesDTO param);
}
