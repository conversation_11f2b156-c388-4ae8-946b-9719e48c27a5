package net.armcloud.paascenter.openapi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.model.dto.api.PadAdbDTO;
import net.armcloud.paascenter.common.model.dto.api.SshAdbConnectDTO;
import net.armcloud.paascenter.common.model.vo.api.PadAdbVO;
import net.armcloud.paascenter.common.model.vo.api.SshAdbConnectVO;
import net.armcloud.paascenter.openapi.mapper.EdgeClusterMapper;
import net.armcloud.paascenter.openapi.mapper.PadConnectMapper;
import net.armcloud.paascenter.openapi.mapper.PadMapper;
import net.armcloud.paascenter.openapi.mapper.PadStatusMapper;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.openapi.service.IPadConnectService;
import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.model.entity.paas.Pad;
import net.armcloud.paascenter.common.model.entity.paas.PadConnect;
import net.armcloud.paascenter.common.model.entity.paas.PadStatus;
import net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.*;
import static net.armcloud.paascenter.common.core.constant.Constants.CONNECTION_TYPE_ADB;
import static net.armcloud.paascenter.common.core.constant.NumberConsts.*;
import static net.armcloud.paascenter.common.core.constant.pad.PadStatusConstant.RUNNING;

@Slf4j
@Service
public class PadConnectServiceImpl extends ServiceImpl<PadConnectMapper, PadConnect> implements IPadConnectService {
    @Resource
    private PadConnectMapper padConnectMapper;
    @Resource
    private PadMapper padMapper;
    @Resource
    private PadStatusMapper padStatusMapper;
    @Resource
    private EdgeClusterMapper edgeClusterMapper;
    @Resource
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;

    @Override
    public Result<?> sshOrAdbConnect(SshAdbConnectDTO param) {
        List<String> padCodes = param.getPadCodes();
        // 批量查询所有Pad和PadStatus信息
        List<Pad> pads = padMapper.selectList(new QueryWrapper<Pad>().in("pad_code", padCodes).in("status", ZERO, ONE));
        List<PadStatus> padStatuses = padStatusMapper.selectList(new QueryWrapper<PadStatus>().in("pad_code", padCodes).eq("pad_status", RUNNING));
        // 将Pad和PadStatus信息转换为Map，方便后续查询
        Map<String, Pad> padMap = pads.stream().collect(Collectors.toMap(Pad::getPadCode, Function.identity()));
        Map<String, PadStatus> padStatusMap = padStatuses.stream().collect(Collectors.toMap(PadStatus::getPadCode, Function.identity()));
        for (String padCode : padCodes) {
            PadConnect connect = padConnectMapper.selectOne(new QueryWrapper<PadConnect>().eq("pad_code", padCode).eq("type", param.getType()).eq("delete_flag", ZERO));
            if (ObjectUtil.isNotNull(connect)) {
                throw new BasicException(CONNECTION_ALREADY_EXISTS);
            }
            Pad pad = padMap.get(padCode);
            if (pad == null) {
                throw new BasicException(PAD_CODE_NOT_EXIST);
            }
            PadStatus padStatus = padStatusMap.get(pad.getPadCode());
            if (padStatus == null) {
                throw new BasicException(PAD_NOT_RUNNING);
            }
            // 批量查询dcCode信息
            List<ConsoleDcInfoVO> dcIdGroupByPadCodes = padMapper.getDcIdGroupByPadCodes(Collections.singletonList(pad.getPadCode()));
            String dcCode = CollUtil.isNotEmpty(dcIdGroupByPadCodes) ? dcIdGroupByPadCodes.get(0).getDcCode() : null;
            String connectStr = StrUtil.builder().append(pad.getPadIp()).append("_").append(dcCode).append("_").append(RandomUtil.randomString(4)).toString();
            if (CONNECTION_TYPE_ADB.equals(param.getType())) {
                // 调用adb连接的接口获取命令跟密钥
                HashMap<String, Object> paramMap = new HashMap<>();
                paramMap.put("username", connectStr);
                String result = HttpUtil.post("https://adb.armcloud.net/createUser", JSONUtil.toJsonStr(paramMap));
                SshAdbConnectVO adbConnectVO = JSONUtil.toBean(result, SshAdbConnectVO.class);
                if (ObjectUtil.isNull(adbConnectVO) || !TWO_HUNDRED.equals(adbConnectVO.getStatus())) {
                    throw new BasicException(PAD_CONNECT_EXCEPTION);
                }
                // 将命令和密钥保存到数据库中
                PadConnect padConnect = new PadConnect();
                padConnect.setIpName(connectStr);
                padConnect.setPadCode(pad.getPadCode());
                padConnect.setType(CONNECTION_TYPE_ADB);
                padConnect.setTimeOut(DateUtil.offsetDay(DateUtil.beginOfMinute(DateUtil.date()), 1));
                padConnect.setConnectOrder(adbConnectVO.getCommand());
                padConnect.setConnectKey(adbConnectVO.getKey());
                padConnect.setAdbCommand(adbConnectVO.getCommand());
                padConnect.setDeleteFlag(ZERO);
                padConnect.setCreateBy(param.getUser());
                padConnect.setCreateTime(new Date());
                padConnectMapper.insert(padConnect);
                padMapper.update(new UpdateWrapper<Pad>().set("adb_status", TWO).eq("pad_code", padCode).in("status", ZERO, ONE));
            }
        }
        return Result.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PadAdbVO
    padAdbConnect(PadAdbDTO param) {
        Pad pad = padMapper.selectOne(new QueryWrapper<Pad>().in("pad_code", param.getPadCode()).in("status", ZERO, ONE));
        if (ObjectUtil.isNull(pad)) {
            throw new BasicException(PAD_CODE_NOT_EXIST);
        }

        PadStatus padStatus = padStatusMapper.selectOne(new QueryWrapper<PadStatus>().eq("pad_code", param.getPadCode()).eq("pad_status", RUNNING));
        if (ObjectUtil.isNull(padStatus)) {
            throw new BasicException(PAD_NOT_RUNNING);
        }

        if (param.getEnable()) {
            QueryWrapper<PadConnect> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("pad_code", param.getPadCode()).eq("type", CONNECTION_TYPE_ADB);
            queryWrapper.eq("delete_flag", ZERO);
            queryWrapper.gt("time_out", new Date()).last("limit 1");
            PadConnect padConnect = padConnectMapper.selectOne(queryWrapper);
            if (!ObjectUtil.isNull(padConnect)) {
                PadAdbVO padAdbVO = new PadAdbVO();
                padAdbVO.setPadCode(pad.getPadCode());
                padAdbVO.setCommand(padConnect.getConnectOrder());
                padAdbVO.setKey(padConnect.getConnectKey());
                //IP端口跟ssh返回得端口需要保持一致,目前都是返回5555,有返回就使用返回的
                padAdbVO.setAdb(StringUtils.isEmpty(padConnect.getAdbCommand())?"adb connect localhost:5555":padConnect.getAdbCommand());

                padAdbVO.setExpireTime(DateUtil.format(padConnect.getTimeOut(), DatePattern.NORM_DATETIME_PATTERN));
                padAdbVO.setEnable(Boolean.TRUE);
                return padAdbVO;
            }
            return getPadAdbVO(pad, param.getCustomerId().toString());
        } else {
            QueryWrapper<PadConnect> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("pad_code", param.getPadCode()).eq("type", CONNECTION_TYPE_ADB);
            queryWrapper.eq("delete_flag", ZERO);
            queryWrapper.gt("time_out", new Date());
            List<PadConnect> padConnectList = padConnectMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(padConnectList)) {
                throw new BasicException(INSTANCE_NOT_ENABLED_ADB);
            }
            AtomicReference<PadAdbVO> padAdbVO = new AtomicReference<>(new PadAdbVO());
            padConnectList.forEach(padConnect -> {
                Pad closePad = padMapper.selectOne(new QueryWrapper<Pad>().in("pad_code", param.getPadCode()).in("status", ZERO, ONE));
                padAdbVO.set(closeAdbConnect(closePad, padConnect));
            });
            return padAdbVO.get();
        }
    }


    private PadAdbVO getPadAdbVO(Pad pad, String createBy) {
        List<ConsoleDcInfoVO> dcIdGroupByPadCodes = padMapper.getDcIdGroupByPadCodes(Collections.singletonList(pad.getPadCode()));
        ConsoleDcInfoVO dcInfoVO = CollUtil.isNotEmpty(dcIdGroupByPadCodes) ? dcIdGroupByPadCodes.get(0) : null;
        if (ObjectUtil.isNull(dcInfoVO)) {
            throw new BasicException(DC_NOT_EXIST);
        }
        String edgeClusterCode = edgeClusterMapper.selectEdgeClusterCodeByPadCodeSingle(dcInfoVO.getPadCode());
        String edgeApiUrl = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.EDGE_API_BASE_URL);


        String username = StrUtil.builder().append(pad.getPadIp()).append("_").append(System.currentTimeMillis()).toString();
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("username", username);
        SshAdbConnectVO adbConnectVO = null;
        try {
            //切换成createADBUser 然后起个xxljob定时任务 每小时去清除超时的用户
            String result = HttpUtil.post(edgeApiUrl + "/createADBUser", JSONUtil.toJsonStr(paramMap));
            adbConnectVO = JSONUtil.toBean(result, SshAdbConnectVO.class);
            if (ObjectUtil.isNull(adbConnectVO) || !TWO_HUNDRED.equals(adbConnectVO.getStatus())) {
                throw new BasicException(PAD_CONNECT_EXCEPTION);
            }
        } catch (Exception e) {
            //createADBUser
            log.error("调用开启adb接口异常 url={} param={} e={}", edgeApiUrl + "/createADBUser", JSONUtil.toJsonStr(paramMap), e.getMessage());
            throw new BasicException(PAD_CONNECT_EXCEPTION);
        }
        // 将命令和密钥保存到数据库中
        Date expireTime = DateUtil.offsetDay(DateUtil.beginOfMinute(DateUtil.date()), ONE);
        PadConnect savePadConnect = new PadConnect();
        savePadConnect.setIpName(username);
        savePadConnect.setPadCode(pad.getPadCode());
        savePadConnect.setType(CONNECTION_TYPE_ADB);
        savePadConnect.setTimeOut(expireTime);
        savePadConnect.setConnectOrder(adbConnectVO.getCommand());
        savePadConnect.setConnectKey(adbConnectVO.getKey());
        savePadConnect.setAdbCommand(adbConnectVO.getAdbCommand());
        savePadConnect.setDeleteFlag(ZERO);
        savePadConnect.setCreateBy(createBy);
        savePadConnect.setCreateTime(new Date());
        padConnectMapper.insert(savePadConnect);
        padMapper.update(new UpdateWrapper<Pad>().set("adb_status", TWO).eq("pad_code", pad.getPadCode()).in("status", ZERO, ONE));

        PadAdbVO padAdbVO = new PadAdbVO();
        padAdbVO.setPadCode(pad.getPadCode());
        padAdbVO.setCommand(adbConnectVO.getCommand());
        padAdbVO.setKey(adbConnectVO.getKey());
        padAdbVO.setAdb(StringUtils.isEmpty(savePadConnect.getAdbCommand())?"adb connect localhost:5555":savePadConnect.getAdbCommand());

        padAdbVO.setEnable(Boolean.TRUE);
        padAdbVO.setExpireTime(DateUtil.format(savePadConnect.getTimeOut(), DatePattern.NORM_DATETIME_PATTERN));
        return padAdbVO;
    }

    private PadAdbVO closeAdbConnect(Pad closePad, PadConnect padConnect) {
        List<ConsoleDcInfoVO> dcIdGroupByPadCodes = padMapper.getDcIdGroupByPadCodes(Collections.singletonList(closePad.getPadCode()));
        ConsoleDcInfoVO dcInfoVO = CollUtil.isNotEmpty(dcIdGroupByPadCodes) ? dcIdGroupByPadCodes.get(0) : null;
        if (ObjectUtil.isNull(dcInfoVO)) {
            return null;
        }
        String edgeClusterCode = edgeClusterMapper.selectEdgeClusterCodeByPadCodeSingle(dcInfoVO.getPadCode());
        String edgeApiUrl = edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(edgeClusterCode, EdgeClusterConfigurationEnum.EDGE_API_BASE_URL);

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("username", padConnect.getIpName());
        String result = HttpUtil.post(edgeApiUrl + "/removeADBUser", JSONUtil.toJsonStr(paramMap));
        SshAdbConnectVO adbConnectVO = JSONUtil.toBean(result, SshAdbConnectVO.class);
        if (ObjectUtil.isNull(adbConnectVO) || !TWO_HUNDRED.equals(adbConnectVO.getStatus())) {
            throw new BasicException(PAD_CONNECT_EXCEPTION);
        }
        //修改
        PadConnect updatePadConnect = new PadConnect();
        updatePadConnect.setId(padConnect.getId());
        updatePadConnect.setDeleteFlag(ONE);
        updatePadConnect.setUpdateBy(closePad.getCustomerId().toString());
        padConnectMapper.updateById(updatePadConnect);

        PadAdbVO padAdbVO = new PadAdbVO();
        padAdbVO.setPadCode(padConnect.getPadCode());
        padAdbVO.setCommand(padConnect.getConnectOrder());
        padAdbVO.setExpireTime(DateUtil.format(padConnect.getTimeOut(), DatePattern.NORM_DATETIME_PATTERN));
        padAdbVO.setEnable(Boolean.FALSE);
        return padAdbVO;
    }
}
