package net.armcloud.paascenter.openapi.model.enums;

/**
 * ADI模板操作类型枚举
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public enum AdiTemplateOperationTypeEnum {


    /**
     * 禁用
     */
    DISABLE(0, "禁用"),

    /**
     * 启用
     */
    ENABLE(1, "启用"),


    /**
     * 上传
     */
    UPLOAD(2, "上传"),
    


    
    /**
     * 编辑
     */
    EDIT(3, "编辑"),
    
    /**
     * 删除
     */
    DELETE(4, "删除");
    
    private final Integer code;
    private final String desc;
    
    AdiTemplateOperationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static AdiTemplateOperationTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AdiTemplateOperationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        AdiTemplateOperationTypeEnum type = getByCode(code);
        return type == null ? "未知操作" : type.getDesc();
    }
} 