package net.armcloud.paascenter.openapi.mapper;

import net.armcloud.paascenter.openapi.model.dto.GatewayDeviceDTO;
import net.armcloud.paascenter.openapi.model.vo.GatewayDeviceVO;
import net.armcloud.paascenter.common.model.entity.paas.GatewayDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GatewayDeviceMapper {
    int insert(GatewayDevice record);

    GatewayDeviceVO selectById(Long id);

    int update(GatewayDevice record);

    int delete(@Param("status")Byte status, @Param("id") Long id);

    List<GatewayDeviceVO> selectList(GatewayDeviceDTO dto);

    int countByNameAndNotDeleted(@Param("gateway") String name);

    int updateGatewayDeviceStatus(GatewayDeviceDTO record);
}
