package net.armcloud.paascenter.openapi.netpadv2.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/07/06
 * @description 网存实例V2开机管理
 */
@Service
public class NetPadBootOnManager {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private final static String bootOnNumKey = "netpadv2:booton:num";

    private final static String bootOnPadKey = "netpadv2:booton:pad";

    @Resource
    private IEdgeClusterConfigurationService iEdgeClusterConfigurationService;

    /**
     * 当前可以开机的数量
     * @param clusterCode 集群编码
     * @return 可以开机的数量
     */
    public boolean canBootOn(String clusterCode, String padCode) {
        // 获取集群配置
        String bootingNumberConfig = iEdgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, EdgeClusterConfigurationEnum.NUMBER_OF_SIMULTANEOUS_BOOTING);
        if (StrUtil.isBlank(bootingNumberConfig) || !NumberUtil.isNumber(bootingNumberConfig)) {
            return true;
        }
        int bootingNumber = Integer.parseInt(bootingNumberConfig);
        if (incBootOnNum(clusterCode) <= bootingNumber) {
            cachePadCode(padCode);
            return true;
        } else {
            decBootOnNum(clusterCode, null);
            return false;
        }
    }

    private void cachePadCode(String padCode) {
        redisTemplate.opsForValue().set(getPadCacheKey(padCode), "1", 2, TimeUnit.HOURS);
    }

    public boolean isPadCodeBootOn(String padCode) {
        return redisTemplate.hasKey(getPadCacheKey(padCode));
    }

    public long incBootOnNum(String clusterCode) {
        String cntKey = getCntKey(clusterCode);
        Long res = redisTemplate.opsForValue().increment(cntKey);
        if (res == null) {
            return 0;
        }
        redisTemplate.expire(cntKey, 2, TimeUnit.HOURS);
        return res;
    }

    public void decBootOnNum(String clusterCode, String padCode) {
        String cntKey = getCntKey(clusterCode);
        redisTemplate.opsForValue().decrement(cntKey);
        redisTemplate.expire(cntKey, 2, TimeUnit.HOURS);
        if (Objects.nonNull(padCode)) {
            String padCacheKey = getPadCacheKey(padCode);
            redisTemplate.delete(padCacheKey);
        }
    }

    private String getCntKey(String clusterCode) {
        return RedisKeyUtils.counterKey(bootOnNumKey, clusterCode);
    }

    private String getPadCacheKey(String padCode) {
        return RedisKeyUtils.cacheKey(bootOnPadKey, padCode);
    }
}
