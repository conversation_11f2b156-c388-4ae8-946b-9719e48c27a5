package net.armcloud.paascenter.openapi.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * ADI模板返回对象
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@ApiModel(value = "ADI模板VO", description = "返回给前端的ADI模板信息")
public class AdiTemplateVO {

    @ApiModelProperty(value = "模板ID")
    private Long id;

    @ApiModelProperty(value = "设备品牌")
    private String brand;

    @ApiModelProperty(value = "设备型号")
    private String model;

    @ApiModelProperty(value = "机型名称")
    private String deviceName;

    @ApiModelProperty(value = "系统指纹")
    private String fingerprint;

    @ApiModelProperty(value = "指纹MD5")
    private String fingerprintMd5;

    @ApiModelProperty(value = "安卓镜像版本")
    private Integer androidImageVersion;

    @ApiModelProperty(value = "屏幕布局编码")
    private String screenLayoutCode;

    @ApiModelProperty(value = "屏幕布局")
    private String screenLayout;

    @ApiModelProperty(value = "规格编码")
    private String resourceSpecificationCode;

    @ApiModelProperty(value = "ADI模板OSS访问地址")
    private String adiTemplateDownloadUrl;

//    @ApiModelProperty(value = "ADI模板压缩包密码")
//    private String adiPassword;

    @ApiModelProperty(value = "是否正式版(0:测试版 1:正式版)")
    private Integer isOfficial;

    @ApiModelProperty(value = "是否正式版描述")
    private String isOfficialDesc;

    @ApiModelProperty(value = "状态(0:禁用 1:启用)")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;
    
    @ApiModelProperty(value = "是否公共模板(0:非公共 1:公共)")
    private Integer isPublic;
    
    @ApiModelProperty(value = "关联的客户ID列表")
    private List<Long> customerIds;
    
    @ApiModelProperty(value = "关联实例数量")
    private Integer instanceCount;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "ADI模板版本号")
    private String adiTemplateVersion;

    private Long screenWidth;

    private Long screenHigh;

    private Long pixelDensity;

    private Long screenRefreshRate;


    /**
     * 测试用例文件下载地址
     */
    @ApiModelProperty(value = "测试用例文件下载地址")
    private String testCasesDownloadUrl;

    @ApiModelProperty(value = "机型标识")
    private String modelCode;

    @ApiModelProperty(value = "AOSP版本")
    private String aospVersion;

}