package net.armcloud.paascenter.openapi.controller;

import net.armcloud.paascenter.common.utils.CustomerUtils;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.NewAppClassifyQueryDTO;
import net.armcloud.paascenter.openapi.model.vo.NewAppClassifyVO;
import net.armcloud.paascenter.openapi.service.INewAppClassifyService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/openapi/open/newAppClassify")
@Api(tags = "应用分类")
public class NewAppClassifyController {
    @Resource
    private INewAppClassifyService newAppClassifyService;

    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    @ApiOperation(value = "应用分类列表", httpMethod = "POST", notes = "应用分类列表")
    public Result<List<NewAppClassifyVO>> list(@Valid  @RequestBody NewAppClassifyQueryDTO param) {
        param.setCustomerId(CustomerUtils.getAndVerifyUserId(RequestUtils.getCurrentRequest()));
        return Result.ok(newAppClassifyService.list(param));
    }
}
