package net.armcloud.paascenter.openapi.service;

import net.armcloud.paascenter.common.core.domain.Result;
import net.armcloud.paascenter.common.model.dto.api.PadAdbDTO;
import net.armcloud.paascenter.common.model.dto.api.SshAdbConnectDTO;
import net.armcloud.paascenter.common.model.vo.api.PadAdbVO;

public interface IPadConnectService {

    /**
     * ssh和adb连接
     */
    Result<?> sshOrAdbConnect(SshAdbConnectDTO param);

    /**
     * 开启 / 关闭 adb
     *
     * @param param
     * @return
     */
    PadAdbVO padAdbConnect(PadAdbDTO param);

}
