package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/3/26 13:58
 * @Description:
 */
@Data
public class NetStorageComputeDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "用户ID")
    private Long customerId;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;
}
