package net.armcloud.paascenter.openapi.utils;


import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * <AUTHOR>
 * @Date 2025/3/12 20:17
 * @Description:
 */
public class IdGeneratorUtils {

    public static final String[] PAD_SUFFIX = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};
    private static final Random RANDOM = new Random();

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    /**
     * 网存id生成器
     * @return
     */
    public static String generateStorageId() {
        // 1. 获取当前日期（yyMMdd格式）
        String date = new SimpleDateFormat("yyMMdd").format(new Date());

        // 2. 随机生成7个大写字母或数字
        String randomPart = generateRandomString(7);

        // 3. 组合并返回结果
        return "ZSC" + date + randomPart;
    }
    // 生成指定长度的随机字母或数字字符串
    // 生成指定长度的随机字符（从PAD_SUFFIX数组中选取）

    private static String generateRandomString(int length) {
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            sb.append(PAD_SUFFIX[SECURE_RANDOM.nextInt(PAD_SUFFIX.length)]);
        }

        return sb.toString();
    }


    /**
     * 网存实例padCode生成器
     */
    public static String generateStoragePadCodeId() {
        // 1. 固定部分 "AC" 和 "N"
        String prefix = "ACN";

        // 2. 获取当前日期（yyMMdd格式）
        String date = new SimpleDateFormat("yyMMdd").format(new Date());

        // 3. 随机生成7个字符（从PAD_SUFFIX数组中选取）
        String randomPart = generateRandomString(7);

        // 4. 组合并返回结果
        return prefix + date + randomPart;
    }

    /**
     * 算力单元code生成器
     */
    public static String generateStorageComputeUnitCode() {
        // 1. 固定部分 "AC" 和 "N"
        String prefix = "ZCC";

        // 2. 获取当前日期（yyMMdd格式）
        String date = new SimpleDateFormat("yyMMdd").format(new Date());

        // 3. 随机生成7个字符（从PAD_SUFFIX数组中选取）
        String randomPart = generateRandomString(7);

        // 4. 组合并返回结果
        return prefix + date + randomPart;
    }
}
