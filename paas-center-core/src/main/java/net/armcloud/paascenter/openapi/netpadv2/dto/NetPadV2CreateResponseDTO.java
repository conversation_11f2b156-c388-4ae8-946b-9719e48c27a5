package net.armcloud.paascenter.openapi.netpadv2.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;

import java.util.Date;

/**
 * 网存实例V2创建响应DTO
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Data
@Builder
public class NetPadV2CreateResponseDTO {

    @ApiModelProperty(value = "实例编码")
    private String padCode;

    @ApiModelProperty(value = "存储大小(GB)")
    private Long storageSize;

    @ApiModelProperty(value = "集群编码")
    private String clusterCode;
}
