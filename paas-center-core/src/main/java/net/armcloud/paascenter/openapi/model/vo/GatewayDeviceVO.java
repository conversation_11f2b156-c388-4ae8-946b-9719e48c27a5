package net.armcloud.paascenter.openapi.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/2/11 14:29
 * @Version 1.0
 */
@Data
public class GatewayDeviceVO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "子网掩码")
    private String netmask;

    @ApiModelProperty(value = "网关")
    private String gateway;

    @ApiModelProperty(value = "dns")
    private String dns;

    @ApiModelProperty(value = "是否删除（1：已删除；0：未删除")
    private Byte deleteFlag;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "是否启用（1-启用 0--禁用")
    private Byte status;

    @ApiModelProperty(value = "是否启用（1-启用 0--禁用")
    private String statusName;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    private String updateTime;
}
