package net.armcloud.paascenter.openapi.netpadv2.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.core.constant.paas.ImageUploadStatus;
import net.armcloud.paascenter.common.enums.EdgeClusterConfigurationEnum;
import net.armcloud.paascenter.common.enums.SourceTargetEnum;
import net.armcloud.paascenter.common.model.entity.paas.*;
import net.armcloud.paascenter.openapi.mapper.*;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2QueryService;
import net.armcloud.paascenter.openapi.service.IEdgeClusterConfigurationService;
import net.armcloud.paascenter.openapi.service.NetStorageResService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 网存实例V2查询服务实现类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Slf4j
@Service
public class NetPadV2QueryServiceImpl implements NetPadV2QueryService {

    @Resource
    private ResourceSpecificationMapper resourceSpecificationMapper;

    @Resource
    private NetStorageResService netStorageResService;

    @Resource
    private CustomerUploadImageMapper customerUploadImageMapper;

    @Resource
    private RealPhoneTemplateMapper realPhoneTemplateMapper;

    @Resource
    private ScreenLayoutMapper screenLayoutMapper;

    @Resource
    private CustomerConfigMapper customerConfigMapper;

    @Resource
    private IEdgeClusterConfigurationService edgeClusterConfigurationService;
    @Autowired
    private PadGroupMapper padGroupMapper;

    @Override
    public ResourceSpecification getResourceSpecificationByCode(String specificationCode) {
        LambdaQueryWrapper<ResourceSpecification> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ResourceSpecification::getSpecificationCode, specificationCode)
               .eq(ResourceSpecification::getDeleteFlag, 0)
               .eq(ResourceSpecification::getStatus, 1)
               .last("LIMIT 1");
        return resourceSpecificationMapper.selectOne(wrapper);
    }

    @Override
    public NetStorageRes getNetStorageResByCustomerAndCluster(Long customerId, String clusterCode) {
        LambdaQueryWrapper<NetStorageRes> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NetStorageRes::getClusterCode, clusterCode)
               .eq(NetStorageRes::getCustomerId, customerId)
               .last("LIMIT 1");
        return netStorageResService.getOne(wrapper);
    }

    @Override
    public CustomerUploadImage getCustomerUploadImageByIdAndCustomer(String imageId, Long customerId, SourceTargetEnum sourceTarget) {
        LambdaQueryWrapper<CustomerUploadImage> wrapper = new LambdaQueryWrapper<>();
        
        // 如果不是管理员系统，需要验证客户权限
        if (!SourceTargetEnum.ADMIN_SYSTEM.equals(sourceTarget)) {
            wrapper.and(w -> w.eq(CustomerUploadImage::getCustomerId, customerId)
                           .or().isNull(CustomerUploadImage::getCustomerId));
        }
        
        wrapper.eq(CustomerUploadImage::getUniqueId, imageId)
               .eq(CustomerUploadImage::getDeleteFlag, 0)
               .eq(CustomerUploadImage::getStatus, ImageUploadStatus.SUCCESS.getStatus())
               .last("LIMIT 1");
        
        return customerUploadImageMapper.selectOne(wrapper);
    }

    @Override
    public RealPhoneTemplate getRealPhoneTemplateById(Long realPhoneTemplateId) {
        LambdaQueryWrapper<RealPhoneTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RealPhoneTemplate::getId, realPhoneTemplateId)
               .eq(RealPhoneTemplate::getDeleteFlag, "0")
               .last("LIMIT 1");
        return realPhoneTemplateMapper.selectOne(wrapper);
    }

    @Override
    public RealPhoneTemplate getRandomRealPhoneTemplate(Integer androidImageVersion) {
        LambdaQueryWrapper<RealPhoneTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RealPhoneTemplate::getDeleteFlag, "0")
               .eq(RealPhoneTemplate::getAndroidImageVersion, androidImageVersion)
               .eq(RealPhoneTemplate::getIsPublic, 1)
               .eq(RealPhoneTemplate::getStatus, 1)
               .last("ORDER BY RAND() LIMIT 1");
        return realPhoneTemplateMapper.selectOne(wrapper);
    }

    @Override
    public ScreenLayout getScreenLayoutByCodeAndCustomer(String layoutCode, Long customerId, SourceTargetEnum sourceTarget) {
        LambdaQueryWrapper<ScreenLayout> wrapper = new LambdaQueryWrapper<>();
        
        // 如果不是管理员系统，需要验证客户权限
        if (!SourceTargetEnum.ADMIN_SYSTEM.equals(sourceTarget)) {
            wrapper.and(w -> w.eq(ScreenLayout::getCustomerId, customerId)
                           .or().isNull(ScreenLayout::getCustomerId));
        }
        
        wrapper.eq(ScreenLayout::getCode, layoutCode)
               .eq(ScreenLayout::getDeleteFlag, 0)
               .eq(ScreenLayout::getStatus, 1)
               .last("LIMIT 1");
        
        return screenLayoutMapper.selectOne(wrapper);
    }

    @Override
    public Integer getStreamTypeByCustomerId(Long customerId) {
        Integer streamType = customerConfigMapper.getStreamTypeByCustomerId(customerId);
        if (streamType == null) {
            streamType = 1;
        }
        return streamType;
    }

    @Override
    public String getEdgeClusterConfigurationByKey(String clusterCode, EdgeClusterConfigurationEnum configKey) {
        return edgeClusterConfigurationService.getEdgeClusterConfigurationByKey(clusterCode, configKey);
    }

    @Override
    public PadGroup getGroupInfo(Integer groupId, Long customerId) {
        if (customerId == null) {
            return null;
        }
        if (groupId == null) {
            return null;
        }
        return padGroupMapper.getByCustomerIdAndGroupId(customerId, groupId);
    }
}
