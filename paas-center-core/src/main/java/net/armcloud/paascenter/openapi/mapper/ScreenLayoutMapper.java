package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.ScreenLayout;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 屏幕布局管理表表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Mapper
public interface ScreenLayoutMapper extends BaseMapper<ScreenLayout> {

    ScreenLayout getByCode(@Param("screenLayoutCode") String screenLayoutCode);
}
