package net.armcloud.paascenter.openapi.model.dto;

import net.armcloud.paascenter.common.model.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AdbConnectDTO extends BaseDTO {
    @ApiModelProperty(value = "实例id", required = true)
    @NotNull(message = "padCode cannot null")
    private String padCode;

}
