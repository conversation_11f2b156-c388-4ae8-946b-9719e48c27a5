package net.armcloud.paascenter.openapi.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.armcloud.paascenter.common.model.dto.PageDTO;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
public class QueryImageDTO extends PageDTO implements Serializable {

    @ApiModelProperty(value = "客户id",hidden = true)
    private Long customerId;


    @ApiModelProperty(value = "镜像类型, 1:公共镜像,2:自定义镜像")
    @Min(value = 1, message = "value 只能是 1 或 2")
    @Max(value = 2, message = "value 只能是 1 或 2")
    private Integer imageType;

    @ApiModelProperty(value = "镜像版本 1测试版 2正式版")
    private Integer releaseType;

    @ApiModelProperty(value = "rom版本")
    private String romVersion;

    @ApiModelProperty(value = "创建开始时间")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "时间格式必须为 yyyy-MM-dd HH:mm:ss")
   private String createTimeStart;

    @ApiModelProperty(value = "创建结束时间")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$", message = "时间格式必须为 yyyy-MM-dd HH:mm:ss")
    private String createTimeEnd;


}

