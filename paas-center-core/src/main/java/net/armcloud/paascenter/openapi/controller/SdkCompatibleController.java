package net.armcloud.paascenter.openapi.controller;

import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.utils.http.RequestUtils;
import net.armcloud.paascenter.openapi.model.dto.DcInfoCompatibleDTO;
import net.armcloud.paascenter.openapi.model.dto.GetPadDcInfoDTO;
import net.armcloud.paascenter.openapi.model.vo.PadDcInfoVO;
import net.armcloud.paascenter.openapi.service.IDCService;
import net.armcloud.paascenter.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static net.armcloud.paascenter.common.core.constant.Constants.SDK_TOKEN;
import static net.armcloud.paascenter.common.core.constant.Constants.SDK_UUID;

@RestController
@RequestMapping()
@Api(tags = "SDK兼容老版本")
@Slf4j
public class SdkCompatibleController {

    private final IDCService dcService;

    @GetMapping(value = "/openapi/open/pod/sdk/getDcInfo")
    @ApiOperation(value = "查询实例所在机房信息")
    public Result<PadDcInfoVO> getDcInfo(@Valid DcInfoCompatibleDTO param) {
//        HttpServletRequest request = RequestUtils.getCurrentRequest();
//        GetPadDcInfoDTO dto = new GetPadDcInfoDTO();
//        dto.setPadCode(param.getPodCode());
//        return Result.ok(dcService.getDcInfoBySDK(dto, request.getHeader(SDK_TOKEN), request.getHeader(SDK_UUID)));
        log.error("SdkCompatibleController getDcInfo ?????????????");
        return Result.ok();
    }

    public SdkCompatibleController(IDCService dcService) {
        this.dcService = dcService;
    }
}
