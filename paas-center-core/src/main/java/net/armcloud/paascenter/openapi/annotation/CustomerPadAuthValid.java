package net.armcloud.paascenter.openapi.annotation;

import net.armcloud.paascenter.common.core.exception.BasicException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验客户对pad的拥有权限
 * <p>
 * 此注解只扫描Controller上的方法
 *
 * <p>
 * 如未有pad的拥有权限则抛出 {@link BasicException}
 */
@Documented
@Target(METHOD)
@Retention(RUNTIME)
public @interface CustomerPadAuthValid {
    FieldName fieldName() default FieldName.PAD_CODES;

    //默认跳过
    boolean skipVerifyingTheOff()  default  true;

    @Getter
    @AllArgsConstructor
    enum FieldName {
        PAD_CODES("padCodes"),
        PAD_CODE("padCode"),
        RES_UNIT_CODE("netStorageResUnitCodes"),
        GROUP_ID("groupId");

        final String name;
    }
}
