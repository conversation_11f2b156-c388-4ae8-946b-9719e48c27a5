<assembly
	xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<formats>
		<format>dir</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<fileMode>775</fileMode>
			<directory>${project.basedir}/src/main/assembly</directory>
			<outputDirectory>.</outputDirectory>
			<lineEnding>unix</lineEnding>
			<includes>
				<include>*.env</include>
				<include>*.sh</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/target/classes</directory>
			<outputDirectory>.</outputDirectory>
			<includes>
				<include>*.xml</include>
				<include>*.yml</include>
				<include>*.properties</include>
			</includes>
		</fileSet>

<!--		<fileSet>-->
<!--			<fileMode>755</fileMode>-->
<!--			<directory>${project.basedir}</directory>-->
<!--			<includes>-->
<!--				<include>Dockerfile</include>-->
<!--			</includes>-->
<!--		</fileSet>-->

<!--		<fileSet>-->
<!--			<directory>${project.basedir}/target/</directory>-->
<!--			<outputDirectory>config</outputDirectory>-->
<!--			<includes>-->
<!--				<include>/*</include>-->
<!--			</includes>-->
<!--		</fileSet>-->
		<fileSet>
			<directory>${project.basedir}/target</directory>
			<outputDirectory>.</outputDirectory>
			<includes>
				<include>*.jar</include>
			</includes>
			<excludes>
				<exclude>*.jar.original</exclude>
				<exclude>*sources.jar</exclude>
			</excludes>
		</fileSet>
	</fileSets>
</assembly>