<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadStatusMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadStatus">
        <id column="id" property="id" />
        <result column="pad_code" property="padCode" />
        <result column="pad_out_code" property="padOutCode" />
        <result column="pad_status" property="padStatus" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, pad_code, pad_out_code, pad_status, update_by, update_time
    </sql>

    <select id="listByPadCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad_status
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>
    <update id="updateBatchPadStatus">
        <foreach collection="list" item="item" separator=";">
            UPDATE pad_status
            SET pad_status = #{item.padStatus}
            WHERE pad_code = #{item.padCode}
        </foreach>
    </update>

    <select id="listByDeviceIps" resultMap="BaseResultMap">
        select ps.id, ps.pad_code, ps.pad_out_code, ps.pad_status, ps.update_by, ps.update_time
        from pad_status ps
                 join pad p on ps.pad_code = p.pad_code
                 join device_pad dp on p.id = dp.pad_id
                 join device d on dp.device_id = d.id
        where d.delete_flag in
        <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
            #{deviceIp}
        </foreach>
    </select>

    <!-- 批量插入实例状态 -->
    <insert id="batchInsertPadStatus" parameterType="java.util.List">
        INSERT INTO pad_status (
            pad_code, pad_out_code, pad_status
        ) VALUES
        <foreach collection="statuses" item="status" separator=",">
            (
                #{status.padCode}, #{status.padOutCode}, #{status.padStatus}
            )
        </foreach>
    </insert>

    <update id="updatePadStatus">
        update pad_status
        set pad_status = #{status}
        where pad_code = #{padCode}
    </update>
</mapper>