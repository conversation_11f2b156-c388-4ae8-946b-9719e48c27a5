<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.AdiCertificateRepositoryMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.AdiCertificateRepository">
    <id column="id" property="id" />
    <result column="md5" property="md5" />
    <result column="root_cert_md5" property="rootCertMd5" />
    <result column="certificate" property="certificate" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id, md5, root_cert_md5, certificate, delete_flag, create_time, create_by, update_time,
    update_by
  </sql>

  <select id="listByRandom" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from adi_certificate_repository
    where delete_flag = false and certificate_level = #{cert_level}
    order by use_total
    limit #{size}
  </select>

  <update id="incrUseTotal">
    update adi_certificate_repository
    set use_total = use_total + 1
    where id = #{id}
      and delete_flag = false
  </update>

  <update id="decrUseTotalByIds">
    update adi_certificate_repository
    SET use_total = GREATEST(0, use_total - 1)
    where  id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
    and delete_flag = false
  </update>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from adi_certificate_repository
    where  id = #{id}
  </select>

  <select id="selectCertificateLevelById" resultType="java.lang.Integer">
    select
      certificate_level
    from adi_certificate_repository
    where  id = #{id}
  </select>
</mapper>