<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerAppClassifyPadRelationMapper">

    <insert id="batchInsert">
        INSERT INTO customer_app_classify_pad_relation (customer_id,app_classify_id, pad_code,device_level,pad_ip, create_time, create_by,update_time, update_by )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId},#{item.appClassifyId}, #{item.padCode},#{item.deviceLevel},#{item.padIp}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="selectPadList" resultType="net.armcloud.paascenter.openapi.model.vo.AppClassifyPadDetailCheckVO">
        SELECT
        cacpr.id,
        cacpr.customer_id as customerId,
        cacpr.pad_code as padCode,
        cac.classify_type as classifyType,
        cacpr.app_classify_id as appClassifyId
        FROM
        customer_app_classify_pad_relation cacpr
        LEFT JOIN customer_app_classify cac ON cacpr.app_classify_id = cac.id
        WHERE
        cacpr.customer_id = #{customerId}
        AND cacpr.pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="existsByClassifyType" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            customer_app_classify_pad_relation cacpr
                LEFT JOIN customer_app_classify cac ON cacpr.app_classify_id = cac.id
        WHERE
            cacpr.customer_id = #{customerId}
          AND cac.classify_type = #{classifyType}
          AND delete_flag = 0;
    </select>

    <select id="selectAppClassifyPadList" resultType="net.armcloud.paascenter.openapi.model.vo.TriggerAppClassifyPadVO">
        select cpr.pad_code as padCode,cr.package_name as packageName,cac.classify_type as classifyType
        from customer_app_classify_pad_relation cpr
        left join customer_app_classify_relation cr on cpr.app_classify_id = cr.app_classify_id
        left join customer_app_classify cac on cpr.app_classify_id = cac.id
        where  cpr.customer_id = #{customerId}
          and cpr.pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
         </foreach>
          and cac.classify_type = #{classifyType}
    </select>

    <select id="selectAppClassifyPadListByType" resultType="net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassifyPadRelation">
        select cpr.customer_id as customerId,cpr.app_classify_id as appClassifyId, cpr.pad_code as padCode,cpr.device_level as deviceLevel
        from customer_app_classify_pad_relation cpr
        left join customer_app_classify cac on cpr.app_classify_id = cac.id
        where  cpr.customer_id = #{customerId}
        and cpr.pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        and cac.classify_type = #{classifyType}
    </select>
</mapper>
