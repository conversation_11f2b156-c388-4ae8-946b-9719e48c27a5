<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.ServerMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.Server">
    <id column="id" property="id" />
    <result column="dc_id" property="dcId" />
    <result column="public_ip" property="publicIp" />
    <result column="internal_ip" property="internalIp" />
    <result column="public_port" property="publicPort" />
    <result column="internal_port" property="internalPort" />
    <result column="public_interface_port" property="publicInterfacePort" />
    <result column="internal_interface_port" property="internalInterfacePort" />
    <result column="enable" property="enable" />
    <result column="current_connect_total" property="currentConnectTotal" />
    <result column="connect_max_limit" property="connectMaxLimit" />
  </resultMap>

  <sql id="Base_Column_List">
    id, dc_id, public_ip, internal_ip, public_port, internal_port, public_interface_port, internal_interface_port,
    `enable`, current_connect_total, connect_max_limit
  </sql>

  <select id="getById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from server
    where id = #{id}
      and delete_flag = 0
  </select>

  <select id="chooseServerByDcId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from `server`
    where delete_flag = 0
      and current_connect_total + 1 &lt;= connect_max_limit
      and enable = 1
      and dc_id = #{dcId}
    order by current_connect_total
    limit 1
  </select>

  <update id="incrConnectionTotal">
    update `server`
    set current_connect_total = current_connect_total + 1
    where delete_flag = 0
      and current_connect_total + 1 &lt;= connect_max_limit
      and enable = 1
    and id = #{id}
  </update>

  <update id="decrConnectionTotal">
    update `server`
    set current_connect_total = current_connect_total - 1
    where current_connect_total - 1 >= 0
      and id = #{id}
      and delete_flag = 0
  </update>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from server
    where delete_flag = 0
  </select>
</mapper>