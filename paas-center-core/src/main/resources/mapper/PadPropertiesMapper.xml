<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadPropertiesMapper">
    <resultMap id="PropertiesResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadProperties">
        <result column="id" property="id"/>
        <result column="pad_code" property="padCode"/>
        <result column="properties_values" property="propertiesValues"
                typeHandler="net.armcloud.paascenter.openapi.mapper.typehandle.PadPropertiesTypeHandler"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="rom_version" property="romVersion"/>
        <result column="data_size" property="dataSize"/>
        <result column="data_size_used" property="dataSizeUsed"/>
        <result column="data_size_available" property="dataSizeAvailable"/>

    </resultMap>

   <insert id="insertProperties" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadProperties">
    INSERT INTO pad_properties(pad_code,properties_values)
        values (#{padCode},#{propertiesValues,typeHandler=net.armcloud.paascenter.openapi.mapper.typehandle.PadPropertiesTypeHandler})
   </insert>

    <insert id="updateProperties" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadProperties">
        UPDATE pad_properties
            <set>
                <if test="propertiesValues != null">
                    properties_values = #{propertiesValues,typeHandler=net.armcloud.paascenter.openapi.mapper.typehandle.PadPropertiesTypeHandler}
                </if>
            </set>
           where pad_code = #{padCode}
    </insert>

    <select id="selectPropertiesByCode" parameterType="java.lang.String"
            resultMap="PropertiesResultMap">
        select id, pad_code,properties_values,create_by,create_time,update_by,update_time
        from pad_properties
        where pad_code = #{padCode}
    </select>

    <select id="findPropertiesByCodes"
            resultMap="PropertiesResultMap">
        select id, pad_code,properties_values,create_by,create_time,update_by,update_time
        from pad_properties
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="selectPropertiesByCodes" resultMap="PropertiesResultMap">
        select a.id, a.pad_code,a.properties_values,c.rom_version,b.data_size ,b.data_size_used,b.data_size_available
        from pad_properties a
        inner join pad b on a.pad_code = b.pad_code
        inner join customer_upload_image c on b.image_id = c.unique_id
        where a.pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

</mapper>