<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.DictMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.Dict">
    <id column="id" property="id" />
    <result column="dict_label" property="dictLabel" />
    <result column="dict_type" property="dictType" />
    <result column="dict_value" property="dictValue" />
    <result column="dict_sort" property="dictSort" />
    <result column="status" property="status" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, dict_label, dict_type, dict_value, dict_sort, `status`, create_by, create_time,
    update_by, update_time
  </sql>

  <select id="getSingleValueByType" resultType="java.lang.String">
    select dict_value
    from dict
    where dict_type = #{type}
    limit 1
  </select>

    <select id="selectValueByTypeList" resultType="java.lang.String">
      select
      dict_value
      from dict
      where status = 0
      <if test="type != null and type != '' ">
        and dict_type = #{type}
      </if>
      order by dict_sort asc
    </select>
</mapper>