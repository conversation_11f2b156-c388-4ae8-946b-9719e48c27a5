<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.DeviceInstanceTaskMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.container.DeviceInstanceTask">
        <id column="id" property="id"/>
        <result column="master_task_id" property="masterTaskId"/>
        <result column="device_ip" property="deviceIp"/>
        <result column="instance_name" property="instanceName"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="msg" property="msg"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        master_task_id,
        device_ip,
        instance_name,
        status,
        start_time,
        end_time,
        msg,
        delete_flag,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into device_instance_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="masterTaskId != null">
                master_task_id,
            </if>
            <if test="deviceIp != null">
                device_ip,
            </if>
            <if test="instanceName != null and instanceName != ''">
                instance_name,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="masterTaskId != null">
                #{masterTaskId},
            </if>
            <if test="deviceIp != null">
                #{deviceIp},
            </if>
            <if test="instanceName != null and instanceName != ''">
                #{instanceName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <select id="listDeviceIpWithRunningTask" resultType="java.lang.String">
        select device_ip
        from device_instance_task
        where delete_flag = false
          and status in (2)
    </select>

    <select id="listWaitRunningTask" resultMap="BaseResultMap">
        select dit.id,
               dit.master_task_id,
               dit.device_ip,
               dit.instance_name,
               dit.status,
               dit.start_time,
               dit.end_time,
               dit.msg,
               dit.delete_flag,
               dit.create_by,
               dit.create_time,
               dit.update_by,
               dit.update_time
        from device_instance_task dit
            join
        (
        (
        select min(dt.id) as master_task_id
        from device_instance_task dit
                 join device_task dt on dit.master_task_id = dt.id and dt.delete_flag = false
        where dit.delete_flag = false
          and dit.status = 1
        <if test="excludeDeviceIps != null and excludeDeviceIps.size() != 0">
            and dit.device_ip not in
            <foreach collection="excludeDeviceIps" item="excludeDeviceIp" open="(" separator="," close=")">
                #{excludeDeviceIp}
            </foreach>
        </if>

        and dt.dc_id = #{dcId}
        group by dt.ip
        )
            limit #{handlerSize}
        )dtd on dit.master_task_id = dtd.master_task_id
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_instance_task
        where id = #{subTaskId}
          and delete_flag = false
    </select>

    <select id="listByMasterTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_instance_task
        where master_task_id = #{masterTaskId}
          and delete_flag = false
    </select>

    <update id="updateStatusById">
        update device_instance_task
        set status = #{status},
        <if test="startTime != null">
            start_time = #{startTime},
        </if>
        <if test="endTime != null">
            end_time = #{endTime},
        </if>
        msg = #{msg}
        where id = #{id}
          and delete_flag = false
        <if test="originStatus != null">
            and status = #{originStatus}
        </if>
    </update>

    <update id="batchUpdateStatusById">
        update device_instance_task
        set status = #{status},
        <if test="startTime != null">
            start_time = #{startTime},
        </if>
        <if test="endTime != null">
            end_time = #{endTime},
        </if>
        msg = #{msg}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and delete_flag = false
    </update>

    <select id="listWaitVerifyResultDeviceTask" resultMap="BaseResultMap">
        select dit.id,
               dit.master_task_id,
               dit.device_ip,
               dit.instance_name,
               dit.status,
               dit.start_time,
               dit.end_time,
               dit.msg,
               dit.delete_flag,
               dit.create_by,
               dit.create_time,
               dit.update_by,
               dit.update_time
        from device_instance_task dit
                 join device_task dt on dit.master_task_id = dt.id and dt.delete_flag = false
        where dit.status = 2
          and dit.delete_flag = false
          and dt.type = #{typeValue}
          and dt.status = 2
    </select>

    <update id="updateByMasterTaskId">
        update device_instance_task
        set status = #{status}
        <if test="startTime != null">
            , start_time = #{startTime}
        </if>
        <if test="endTime != null">
            , end_time = #{endTime}
        </if>
        <if test="msg != null and msg != ''">
            , msg = #{msg}
        </if>
        where master_task_id = #{masterTaskId}
          and delete_flag = false
    </update>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into device_instance_task
        (master_task_id, device_ip, instance_name, status, start_time, end_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.masterTaskId}, #{item.deviceIp}, #{item.instanceName}, #{item.status}, #{item.startTime},
             #{item.endTime})
        </foreach>
    </insert>

    <select id="listTimeout" resultMap="BaseResultMap">
        select dit.id,
               dit.master_task_id,
               dit.device_ip,
               dit.instance_name,
               dit.status,
               dit.start_time,
               dit.end_time,
               dit.msg,
               dit.delete_flag,
               dit.create_by,
               dit.create_time,
               dit.update_by,
               dit.update_time
        from device_instance_task dit
                 join device_task dt on dit.master_task_id = dt.id and dt.delete_flag = false
        where dit.delete_flag = false
          and dit.status = 2
          and dt.timeout_time &lt;= now()
          and dt.dc_id = #{dcId}
    </select>

    <select id="getLatestCreateTask" resultMap="BaseResultMap">
        select dit.id,
               dit.master_task_id,
               dit.device_ip,
               dit.instance_name,
               dit.status,
               dit.start_time,
               dit.end_time,
               dit.msg,
               dit.delete_flag,
               dit.create_by,
               dit.create_time,
               dit.update_by,
               dit.update_time
        from device_instance_task dit
                 join device_task dt on dt.id = dit.master_task_id and dit.delete_flag = false
        where dt.delete_flag = false
          and dt.dc_id = #{dcId}
          and dt.type = 100
          and dit.instance_name = #{instanceName}
        order by dt.id desc
        limit 1
    </select>
</mapper>