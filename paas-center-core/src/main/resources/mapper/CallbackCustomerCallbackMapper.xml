<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.callback.mapper.CallbackCustomerCallbackMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerCallback">
        <id column="id" property="id" />
        <result column="callback_id" property="callbackId" />
        <result column="customer_id" property="customerId" />
        <result column="callback_url" property="callbackUrl" />
        <result column="enable" property="enable" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <resultMap id="CustomerCallbackVOMap" type="net.armcloud.paascenter.callback.model.CustomerCallbackInfoVO">
        <result column="type" property="type" />
        <result column="customer_id" property="customerId" />
        <result column="host" property="host" />
        <result column="callback_url" property="callbackUrl" />
        <result column="task_business_type" property="taskBusinessType" />

    </resultMap>

    <sql id="Base_Column_List">
        id, callback_id, customer_id, callback_url, `enable`, delete_flag, create_time, create_by,
        update_time, update_by
    </sql>

    <select id="listByCustomerId" resultMap="CustomerCallbackVOMap">
        select pci.type,
               pcc.customer_id,
               pcc.host,
               pcc.callback_url
        from callback_information pci
                 join customer_callback pcc on pci.id = pcc.callback_id
        where pcc.customer_id = #{customerId}
          and pcc.enable = 1
          and pcc.delete_flag = 0
    </select>
    <select id="listAllTaskBusinessTypeNonEmpty"
            resultType="net.armcloud.paascenter.callback.model.CustomerCallbackInfoVO">
        SELECT id,type,description,task_business_type FROM callback_information where delete_flag = 0

    </select>

</mapper>