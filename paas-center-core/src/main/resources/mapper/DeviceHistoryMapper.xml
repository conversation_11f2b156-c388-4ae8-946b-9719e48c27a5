<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.DeviceHistoryMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.DeviceHistory">
    <id column="id" property="id" />
    <result column="dc_id" property="dcId" />
    <result column="device_level" property="deviceLevel" />
    <result column="device_code" property="deviceCode" />
    <result column="device_out_code" property="deviceOutCode" />
    <result column="cloud_vendor_type" property="cloudVendorType" />
    <result column="device_status" property="deviceStatus" />
    <result column="device_ip" property="deviceIp" />
    <result column="idc" property="idc" />
    <result column="arm_server_code" property="armServerCode" />
    <result column="soc_model" property="socModel" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
    <result column="pad_allocation_status" property="padAllocationStatus" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="cluster_code" property="clusterCode" />
    <result column="mac_address" property="macAddress" />
    <result column="init_status" property="initStatus" />
    <result column="debian_sys_info" property="debianSysInfo" />
    <result column="debian_boot_info" property="debianBootInfo" />
    <result column="cbs_info" property="cbsInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dc_id, device_level, device_code, device_out_code, cloud_vendor_type, device_status,
    device_ip, idc, arm_server_code, soc_model, create_by, create_time, update_by, update_time,
    pad_allocation_status, delete_flag, cluster_code, mac_address, init_status, debian_sys_info,
    debian_boot_info, cbs_info
  </sql>

  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO device_history (
    dc_id,device_level,device_code,device_out_code,cloud_vendor_type,device_status,device_ip,
    idc,arm_server_code,soc_model,pad_allocation_status,delete_flag,mac_address,init_status,
    debian_sys_info,debian_boot_info,ext_life_time_info,cbs_info,
    node_id,`position`,gateway,net_storage_res_flag,create_by,create_time,update_by,update_time
    ) VALUES
    <foreach collection="records" item="record" separator=",">
      (
      #{record.dcId}, #{record.deviceLevel}, #{record.deviceCode},
      #{record.deviceOutCode}, #{record.cloudVendorType}, #{record.deviceStatus},
      #{record.deviceIp}, #{record.idc}, #{record.armServerCode},
      #{record.socModel}, #{record.padAllocationStatus}, #{record.deleteFlag},
      #{record.macAddress}, #{record.initStatus}, #{record.debianSysInfo},
      #{record.debianBootInfo},#{record.extLifeTimeInfo},#{record.cbsInfo},
      #{record.nodeId},#{record.position},#{record.gateway},#{record.netStorageResFlag},
      #{record.createBy}, #{record.createTime},
      #{record.updateBy}, #{record.updateTime}
      )
    </foreach>
  </insert>
</mapper>