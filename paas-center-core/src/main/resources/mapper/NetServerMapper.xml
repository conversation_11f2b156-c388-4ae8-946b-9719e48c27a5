<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.NetServerMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.NetServer">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="ipv4_cidr" jdbcType="VARCHAR" property="ipv4Cidr" />
        <result column="net_type" jdbcType="VARCHAR" property="netType" />
        <result column="bind_flag" jdbcType="TINYINT" property="bindFlag" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <resultMap id="NetServerVO" type="net.armcloud.paascenter.openapi.model.vo.NetServerVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="ipv4_cidr" jdbcType="VARCHAR" property="ipv4Cidr" />
        <result column="net_type" jdbcType="VARCHAR" property="type" />
        <result column="bind_flag" jdbcType="TINYINT" property="status" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="availableIpNum" jdbcType="INTEGER" property="availableIpNum" />
    </resultMap>
    <sql id="Base_Column_List">
        id, `name`, ipv4_cidr,net_type,bind_flag, remarks, delete_flag, create_by, create_time,
    update_by, update_time
    </sql>
    <sql id="NetServerVO">
        id, `name`, ipv4_cidr,net_type,bind_flag, remarks, create_time,
        254-(select count(1) from arm_server where net_server_id = net_server.id and delete_flag = 0) as availableIpNum
    </sql>
    <update id="deleteNetServer">
        update net_server
        set delete_flag = 1
        where id = #{id}
    </update>
    <select id="listNetServer" resultMap="NetServerVO">
        SELECT
        ns.id,
        ns.`name`,
        ns.ipv4_cidr,
        ns.net_type,
        ns.bind_flag,
        ns.remarks,
        ns.create_time,
        COUNT(asv.id) AS availableIpNum,
        254 - COUNT(asv.id) AS noUsedIpNum,
        254 as countIpNum,
        dc.dict_label AS bindName
        FROM
        net_server ns
        LEFT JOIN
        arm_server asv ON ns.id = asv.net_server_id AND asv.delete_flag = 0
        LEFT JOIN
        dict AS dc ON dc.dict_value = ns.bind_flag AND dc.dict_type = 'net_bind_status'
        WHERE
        ns.delete_flag = 0
        <if test="name != null and name != ''">
            AND ns.name = #{name}
        </if>
        <if test="ipv4Cidr != null and ipv4Cidr != ''">
            AND ns.ipv4_cidr = #{ipv4Cidr}
        </if>
        GROUP BY
        ns.id,
        ns.`name`,
        ns.ipv4_cidr,
        ns.net_type,
        ns.bind_flag,
        ns.remarks,
        ns.create_time,
        dc.dict_label
    </select>
    <select id="selectListNetServer" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            net_server
        where delete_flag = 0
    </select>
    <select id="selectNetServerByIpv4" resultType="net.armcloud.paascenter.common.model.entity.paas.NetServer">
        select
            <include refid="Base_Column_List"/>
        from
            net_server
        where ipv4_cidr = #{ipv4Cidr} and delete_flag = 0
    </select>
    <select id="selectNetServerByIpv4OrNameExcludingId" resultType="net.armcloud.paascenter.common.model.entity.paas.NetServer">
        select
        <include refid="Base_Column_List"/>
        from
        net_server
        where (ipv4_cidr = #{ipv4Cidr} or name = #{name}) and delete_flag = 0
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <select id="selectById" resultType="net.armcloud.paascenter.common.model.entity.paas.NetServer">
        select
        <include refid="Base_Column_List"/>
        from
        net_server
        where id = #{id}
    </select>
    <select id="selectVoById" resultMap="NetServerVO">
        select
        <include refid="NetServerVO"/>
        from
        net_server
        where id = #{id}
    </select>
    <select id="listNetServerNotBind" resultMap="NetServerVO">
        select
        <include refid="NetServerVO"/>
        from
        net_server
        where bind_flag = 0 and delete_flag = 0
    </select>

    <insert id="saveNetServer" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.NetServer" useGeneratedKeys="true">
        insert into net_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="ipv4Cidr != null">
                ipv4_cidr,
            </if>
            <if test="netType != null">
                net_type,
            </if>
            <if test="bindFlag != null">
                bind_flag,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="ipv4Cidr != null">
                #{ipv4Cidr,jdbcType=VARCHAR},
            </if>
            <if test="netType != null">
                #{netType,jdbcType=TINYINT},
            </if>
            <if test="bindFlag != null">
                #{bindFlag,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateNetServer" parameterType="net.armcloud.paascenter.common.model.entity.paas.NetServer">
        update net_server
        <set>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="ipv4Cidr != null">
                ipv4_cidr = #{ipv4Cidr,jdbcType=VARCHAR},
            </if>
            <if test="netType != null">
                net_type = #{netType,jdbcType=TINYINT},
            </if>
            <if test="bindFlag != null">
                bind_flag = #{bindFlag,jdbcType=TINYINT},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateNetServerBindFlag">
        update net_server set bind_flag = #{bindFlag} where ipv4_cidr = #{deviceSubnet} and delete_flag=0
    </update>
</mapper>