<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.SimInfoMapper">


    <select id="selectRandomSimInfo" resultType="net.armcloud.paascenter.common.model.entity.paas.SimInfo">
       select * from sim_info where country_code = #{countryCode} and delete_flag = 0 order by rand() limit 1
    </select>


</mapper>