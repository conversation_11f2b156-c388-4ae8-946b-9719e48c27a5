<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.InstanceTaskMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.container.InstanceTask">
        <id column="id" property="id"/>
        <result column="dc_id" property="dcId"/>
        <result column="type" property="type"/>
        <result column="device_ip" property="deviceIp"/>
        <result column="instance_name" property="instanceName"/>
        <result column="status" property="status"/>
        <result column="clear_disk_data" property="clearDiskData"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="timeout_time" property="timeoutTime"/>
        <result column="msg" property="msg"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        dc_id,
        type,
        device_ip,
        instance_name,
        `status`,
        `clear_disk_data`,
        start_time,
        end_time,
        timeout_time,
        msg,
        delete_flag,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into instance_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dcId != null">
                dc_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="deviceIp != null">
                device_ip,
            </if>
            <if test="instanceName != null">
                instance_name,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="clearDiskData != null">
                clear_disk_data,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="msg != null">
                msg,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dcId != null">
                #{dcId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="deviceIp != null">
                #{deviceIp},
            </if>
            <if test="instanceName != null">
                #{instanceName},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="clearDiskData != null">
                #{clearDiskData},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="msg != null">
                #{msg},
            </if>
        </trim>
    </insert>

    <select id="listDeviceIpWithRunningTask" resultType="java.lang.String">
        select device_ip
        from instance_task
        where delete_flag = false
          and status in (2)
    </select>

    <select id="listWaitRunTasks" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task where id in(
        select min(id)
        from instance_task
        where delete_flag = false
          and status = 1
        <if test="excludeDeviceIps != null and excludeDeviceIps.size() != 0">
            and device_ip not in
            <foreach collection="excludeDeviceIps" item="excludeDeviceIp" open="(" separator="," close=")">
                #{excludeDeviceIp}
            </foreach>
        </if>

        <if test="excludeInstanceNames != null and excludeInstanceNames.size() != 0">
            and instance_name not in
            <foreach collection="excludeInstanceNames" item="excludeInstanceName" open="(" separator="," close=")">
                #{excludeInstanceName}
            </foreach>
        </if>
        and dc_id = #{dcId}
        group by device_ip, instance_name
        )
    </select>

    <select id="getIdsByInstanceNameAndDeviceIp" resultType="java.lang.Long">
        select min(id)
        from instance_task
        where delete_flag = false
        and status = 1
        <if test="excludeDeviceIps != null and excludeDeviceIps.size() != 0">
            and device_ip not in
            <foreach collection="excludeDeviceIps" item="excludeDeviceIp" open="(" separator="," close=")">
                #{excludeDeviceIp}
            </foreach>
        </if>

        <if test="excludeInstanceNames != null and excludeInstanceNames.size() != 0">
            and instance_name not in
            <foreach collection="excludeInstanceNames" item="excludeInstanceName" open="(" separator="," close=")">
                #{excludeInstanceName}
            </foreach>
        </if>
        and dc_id = #{dcId}
        group by device_ip, instance_name
    </select>

    <select id="listWaitRunTasksNew" resultType="net.armcloud.paascenter.common.model.entity.container.InstanceTask">
        select
           <include refid="Base_Column_List"/>
        from instance_task where id in
        <foreach collection="minIds" item="minId" open="(" separator="," close=")">
            #{minId}
        </foreach>
    </select>

    <update id="batchUpdateStatusById">
        update instance_task
        set status = #{status},
        <if test="startTime != null">
            start_time = #{startTime},
        </if>
        <if test="endTime != null">
            end_time = #{endTime},
        </if>
        <if test="timeoutTime != null">
            timeout_time = #{timeoutTime},
        </if>
        msg = #{msg}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and delete_flag = false
    </update>

    <select id="listByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task
        where id = #{id}
          and delete_flag = false
    </select>

    <select id="listByStatusAndTargetTaskTypes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task
        where status = #{status}
          and delete_flag = false
          and dc_id = #{dcId}
          and type in
        <foreach collection="typeValues" item="typeValue" open="(" separator="," close=")">
            #{typeValue}
        </foreach>
    </select>

    <select id="listByMasterTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task
        where id = #{masterTaskId}
          and delete_flag = false
    </select>

    <update id="updateByMasterTaskId">
        update instance_task
        set status = #{status}
        <if test="startTime != null">
            , start_time = #{startTime}
        </if>
        <if test="endTime != null">
            , end_time = #{endTime}
        </if>
        <if test="timeoutTime != null">
            , timeout_time = #{timeoutTime}
        </if>
        <if test="msg != null and msg != ''">
            , msg = #{msg}
        </if>
        where id = #{masterTaskId}
          and delete_flag = false
    </update>

    <select id="listById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and delete_flag = false
    </select>

    <select id="listTimeout" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task
        where delete_flag = false
          and status = 2
          and timeout_time &lt; now()
          and dc_id = #{dcId}
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from instance_task
        where id = #{id}
          and delete_flag = false
    </select>

    <update id="updateStatusById">
        update instance_task
        set status = #{status}
        <if test="startTime != null">
            , start_time = #{startTime}
        </if>
        <if test="endTime != null">
            , end_time = #{endTime}
        </if>
        <if test="timeoutTime != null">
            , timeout_time = #{timeoutTime}
        </if>
        <if test="msg != null and msg != ''">
            , msg = #{msg}
        </if>
        where id = #{id}
          and delete_flag = false
          and status != #{status}
        <if test="originStatus != null">
            and status = #{originStatus}
        </if>
    </update>

    <select id="listInstanceNameByDcIdAndStatus" resultType="java.lang.String">
        select instance_name
        from instance_task
        where dc_id = #{dcId}
          and status = #{status}
          and delete_flag = false
    </select>

    <select id="listDeviceIpByDcIdAndStatus" resultType="java.lang.String">
        select device_ip
        from instance_task
        where dc_id = #{dcId}
          and status in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        and delete_flag = false
    </select>

    <select id="selectIdByCreateTime" resultType="java.lang.Long">
        SELECT
            id
        FROM
            instance_task
        WHERE
            create_time &lt; #{date}
    </select>

    <delete id="delByIds">
        DELETE FROM
        instance_task
        WHERE
        id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>