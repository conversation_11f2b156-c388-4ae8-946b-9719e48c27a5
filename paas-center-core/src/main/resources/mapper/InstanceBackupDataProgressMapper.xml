<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.InstanceBackupDataProgressMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress">
    <id column="id" property="id" />
    <result column="instance_task_id" property="instanceTaskId" />
    <result column="instance_name" property="instanceName" />
    <result column="path" property="path" />
    <result column="package_done" property="packageDone" />
    <result column="upload_done" property="uploadDone" />
  </resultMap>

  <sql id="Base_Column_List">
    id, instance_task_id, instance_name, `path`, package_done, upload_done
  </sql>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.container.InstanceBackupDataProgress" useGeneratedKeys="true">
    insert into instance_backup_data_progress
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="instanceTaskId != null">
        instance_task_id,
      </if>
      <if test="instanceName != null">
        instance_name,
      </if>
      <if test="path != null">
        `path`,
      </if>
      <if test="packageDone != null">
        package_done,
      </if>
      <if test="uploadDone != null">
        upload_done,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="instanceTaskId != null">
        #{instanceTaskId},
      </if>
      <if test="instanceName != null">
        #{instanceName},
      </if>
      <if test="path != null">
        #{path},
      </if>
      <if test="packageDone != null">
        #{packageDone},
      </if>
      <if test="uploadDone != null">
        #{uploadDone},
      </if>
    </trim>
  </insert>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into instance_backup_data_progress
    (instance_task_id, instance_name, `path`, package_done, upload_done)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.instanceTaskId}, #{item.instanceName}, #{item.path}, #{item.packageDone}, #{item.uploadDone}
        )
    </foreach>
  </insert>

  <select id="getByInstanceTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from instance_backup_data_progress
    where instance_task_id = #{instanceTaskId}
  </select>

  <update id="update">
    update instance_backup_data_progress
    <set>
      <if test="instanceTaskId != null">
        instance_task_id = #{instanceTaskId},
      </if>
      <if test="instanceName != null">
        instance_name = #{instanceName},
      </if>
      <if test="path != null">
        `path` = #{path},
      </if>
      <if test="packageDone != null">
        package_done = #{packageDone},
      </if>
      <if test="uploadDone != null">
        upload_done = #{uploadDone},
      </if>
    </set>
    where id = #{id}
  </update>
</mapper>