<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.ScreenLayoutMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.ScreenLayout">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="customer_id" property="customerId" />
        <result column="screen_width" property="screenWidth" />
        <result column="screen_high" property="screenHigh" />
        <result column="pixel_density" property="pixelDensity" />
        <result column="screen_refresh_rate" property="screenRefreshRate" />
        <result column="status" property="status" />
        <result column="remarks" property="remarks" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, code, customer_id, screen_width, screen_high, pixel_density, screen_refresh_rate,
        `status`, remarks, delete_flag, create_by, create_time, update_by, update_time
    </sql>

    <select id="getByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from screen_layout
        where code = #{screenLayoutCode} and delete_flag = 0
    </select>
</mapper>
