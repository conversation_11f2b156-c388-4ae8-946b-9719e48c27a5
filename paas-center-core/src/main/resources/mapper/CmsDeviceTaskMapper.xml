<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.CmsDeviceTaskMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.DeviceTask">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
        <result column="task_id" jdbcType="BIGINT" property="taskId" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
        <result column="timeout_time" jdbcType="TIMESTAMP" property="timeoutTime" />
        <result column="file_id" jdbcType="BIGINT" property="fileId" />
        <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
        <result column="result" jdbcType="VARCHAR" property="result" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="delete_flag" jdbcType="BOOLEAN" property="deleteFlag" />
    </resultMap>
    <sql id="Base_Column_List">
        id, unique_id, task_id, `status`, device_code, timeout_time, file_id, task_content,
    start_time, end_time, error_msg, `result`, create_by, create_time, update_by, update_time,
    delete_flag
    </sql>
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        insert into device_task (unique_id, task_id, status, device_code, file_id, task_content, end_time, timeout_time, error_msg, create_by,customer_task_id,customer_id,remarks,start_time)
        values
        <foreach collection="deviceTasks" item="item" separator=",">
            (#{item.uniqueId}, #{item.taskId}, #{item.status}, #{item.deviceCode}, #{item.fileId}, #{item.taskContent},#{item.endTime},
            #{item.timeoutTime}, #{item.errorMsg}, #{item.createBy}, #{item.customerTaskId},#{item.customerId},#{item.remarks},#{item.startTime})
        </foreach>
    </insert>
    <insert id="insertBatchDefaultTimeout"  useGeneratedKeys="true" keyProperty="id">
        insert into device_task (unique_id, task_id, status, device_code, file_id, task_content, end_time, error_msg, create_by, customer_task_id, customer_id, remarks,start_time)
        values
        <foreach collection="deviceTasks" item="item" separator=",">
            (#{item.uniqueId}, #{item.taskId}, #{item.status}, #{item.deviceCode}, #{item.fileId}, #{item.taskContent},#{item.endTime},
            #{item.errorMsg}, #{item.createBy}, #{item.customerTaskId},#{item.customerId},#{item.remarks},#{item.startTime})
        </foreach>
    </insert>
    <update id="batchUpdateUniqueId" parameterType="java.util.List">
        update device_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unique_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.uniqueId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <update id="updateSetTimeout">
        UPDATE device_task
        <set>
            <if test="status != null">status = #{status},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="containerTaskId != null">container_task_id = #{containerTaskId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="timeoutTime != null">timeout_time = #{timeoutTime},</if>
        </set>
        WHERE id = #{id}
    </update>
    <select id="getLastestTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_task
        where device_code = #{padCode}
        order by id desc
        limit 1
    </select>
    <select id="listByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_task
        where task_id = #{taskId}
        and delete_flag = 0
    </select>
    <update id="updateDeviceTask">
        UPDATE device_task dt
            JOIN task t ON dt.task_id = t.id
            SET dt.bmc_task_id = #{bmcTaskId}
        WHERE t.type = #{type}
          AND dt.customer_task_id = #{customerTaskId}
    </update>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_task
        where id = #{id}
    </select>
</mapper>