<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadGroupNewMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadGroupNew">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,customer_id,group_id,
        group_name,delete_flag,create_time,
        create_by,update_time,update_by,
        remark
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from pad_group
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from pad_group
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadGroupNew" useGeneratedKeys="true">
        insert into pad_group
        ( id,customer_id,group_id
        ,group_name,delete_flag,create_time
        ,create_by,update_time,update_by
        ,remark,file_comment,sort_num)
        values (#{id,jdbcType=BIGINT},#{customerId,jdbcType=BIGINT},#{groupId,jdbcType=BIGINT}
        ,#{groupName,jdbcType=VARCHAR},#{deleteFlag,jdbcType=TINYINT},#{createTime,jdbcType=TIMESTAMP}
        ,#{createBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR}
        ,#{remark,jdbcType=VARCHAR},#{fileComment,jdbcType=VARCHAR},#{sortNum,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadGroupNew" useGeneratedKeys="true">
        insert into pad_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="customerId != null">customer_id,</if>
                <if test="groupId != null">group_id,</if>
                <if test="groupName != null">group_name,</if>
                <if test="deleteFlag != null">delete_flag,</if>
                <if test="createTime != null">create_time,</if>
                <if test="createBy != null">create_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="customerId != null">#{customerId,jdbcType=BIGINT},</if>
                <if test="groupId != null">#{groupId,jdbcType=BIGINT},</if>
                <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
                <if test="deleteFlag != null">#{deleteFlag,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadGroupNew">
        update pad_group
        <set>
                <if test="customerId != null">
                    customer_id = #{customerId,jdbcType=BIGINT},
                </if>
                <if test="groupId != null">
                    group_id = #{groupId,jdbcType=BIGINT},
                </if>
                <if test="groupName != null">
                    group_name = #{groupName,jdbcType=VARCHAR},
                </if>
                <if test="deleteFlag != null">
                    delete_flag = #{deleteFlag,jdbcType=TINYINT},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="remark != null">
                    remark = #{remark,jdbcType=VARCHAR},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>
    <update id="updateByPrimaryKey" parameterType="net.armcloud.paascenter.common.model.entity.paas.PadGroupNew">
        update pad_group
        set 
            customer_id =  #{customerId,jdbcType=BIGINT},
            group_id =  #{groupId,jdbcType=BIGINT},
            group_name =  #{groupName,jdbcType=VARCHAR},
            delete_flag =  #{deleteFlag,jdbcType=TINYINT},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            remark =  #{remark,jdbcType=VARCHAR}
        where   id = #{id,jdbcType=BIGINT} 
    </update>
</mapper>
