<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadRoomMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadRoom">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="pad_code" property="padCode" jdbcType="VARCHAR"/>
        <result column="room_code" property="roomCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, pad_code, room_code, status, type, create_by, create_time, update_by, update_time
    </sql>

    <!-- 批量插入房间 -->
    <insert id="batchInsertPadRooms" parameterType="java.util.List">
        INSERT INTO pad_room (
            pad_code, room_code, status, type, create_by, create_time
        ) VALUES
        <foreach collection="rooms" item="room" separator=",">
            (
                #{room.padCode}, #{room.roomCode}, #{room.status}, #{room.type},
                #{room.createBy}, #{room.createTime}
            )
        </foreach>
    </insert>

</mapper>
