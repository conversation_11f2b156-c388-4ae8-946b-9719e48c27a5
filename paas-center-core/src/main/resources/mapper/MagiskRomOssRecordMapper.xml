<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.MagiskRomOssRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.openapi.model.entity.MagiskRomOssRecord">
        <id column="id" property="id" />
        <result column="oss_url" property="ossUrl" />
        <result column="version" property="version" />
        <result column="replace_time" property="replaceTime" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, oss_url, version, replace_time, create_time, create_by, update_time, update_by
    </sql>

    <!-- 根据版本查询记录 -->
    <select id="selectByVersion" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM magisk_rom_oss_record
        WHERE version = #{version}
    </select>

    <!-- 查询最新创建的记录 -->
    <select id="selectLatest" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM magisk_rom_oss_record
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 更新OSS地址和替换时间 -->
    <update id="updateOssUrlAndReplaceTime">
        UPDATE magisk_rom_oss_record
        SET oss_url = #{ossUrl},
            replace_time = NOW(),
            update_time = NOW(),
            update_by = #{updateBy}
        WHERE version = #{version}
    </update>

</mapper>
