<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.bmc.mapper.CardTaskMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.bmc.CardTask">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="card_id" property="cardId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="timeout" property="timeout"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="sync_status" property="syncStatus"/>
    </resultMap>

    <insert id="insertCardTask" parameterType="net.armcloud.paascenter.common.model.entity.bmc.CardTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into card_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="serverSn != null">
                server_sn,
            </if>
            <if test="cardId != null">
                card_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="timeout != null ">
                timeout,
            </if>
            <if test="taskContent != null ">
                task_content,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="serverSn != null">
                #{serverSn},
            </if>
            <if test="cardId != null">
                #{cardId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="timeout != null">
                #{timeout},
            </if>
            <if test="taskContent != null">
                #{taskContent},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy},
            </if>
            <if test="createTime != null ">
                #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <insert id="insertCardTaskBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO card_task (task_id, server_sn, card_id, type, task_content,timeout)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.taskId}, #{item.serverSn},#{item.cardId},#{item.type},#{item.taskContent}, #{item.timeout})
        </foreach>
    </insert>

    <select id="selectExecutedServerSn" resultType="string" parameterType="integer">
        SELECT server_sn
        from card_task
        where type = #{type}
          and status = 1
        GROUP BY server_sn
    </select>

    <select id="selectExecutedServerByTypes" resultType="string" parameterType="integer">
        SELECT server_sn
        from card_task
        where type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and status = 1
        GROUP BY server_sn
    </select>

    <update id="updateCardTaskIdToNull">
        UPDATE card_task
        SET task_id     = null,
            update_time = now()
        WHERE id = #{id}
    </update>

</mapper>