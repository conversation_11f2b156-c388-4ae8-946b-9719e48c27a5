<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.ServerPadLogMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.ServerPadLog">
    <id column="id" property="id" />
    <result column="pad_code" property="padCode" />
    <result column="comms_server_id" property="commsServerId" />
    <result column="channel_id" property="channelId" />
    <result column="trade_id" property="tradeId" />
    <result column="ts" property="ts" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id, pad_code, comms_server_id, channel_id, ts, trade_id, event_type, create_time, create_by, update_time, update_by
  </sql>

  <insert id="insert">
    insert into ${tableName}(pad_code, comms_server_id, channel_id, ts, trade_id, event_type)
    value(
    #{serverPadLog.padCode}, #{serverPadLog.commsServerId}, #{serverPadLog.channelId}, #{serverPadLog.ts},
    #{serverPadLog.tradeId}, #{serverPadLog.eventType}
    )
  </insert>
</mapper>