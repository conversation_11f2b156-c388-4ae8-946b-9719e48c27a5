<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.NetStorageComputeUnitMapper">

    <select id="batchQueryAvailableUnits" resultType="java.lang.String">
        SELECT
            a.device_code,
            b.net_storage_compute_unit_id
        FROM
            device a
            inner join customer_device cd on a.id = cd.device_id
            INNER JOIN net_storage_compute_unit b ON a.device_code = b.device_code
        WHERE
            a.delete_flag = 0
            AND a.device_status = 1
            AND b.bind_flag = 0
            AND b.device_level = #{deviceLevel}
            AND b.cluster_code = #{clusterCode}
            and cd.customer_id = #{customerId}
            LIMIT #{requiredCount}
    </select>

    <select id="getAllDeviceWithComputeUnitCnt" resultType="net.armcloud.paascenter.openapi.netpadv2.dto.DeviceCodeWithComputeUnitCntDTO">
        SELECT
            a.device_code,
            count(b.net_storage_compute_unit_id) computeUnitCnt
        FROM
            device a
                INNER JOIN customer_device cd ON a.id = cd.device_id
                INNER JOIN net_storage_compute_unit b ON a.device_code = b.device_code
        WHERE
            a.delete_flag = 0
          AND a.device_status = 1
          AND b.bind_flag = 0
          and cd.delete_flag = 0
          AND b.device_level = #{deviceLevel}
          AND a.device_level = #{deviceLevel}
          AND b.cluster_code = #{clusterCode}
          AND cd.customer_id = #{customerId}
        GROUP BY
            a.device_code
    </select>

    <update id="updateBatchBindFlagByCodeList">
        update net_storage_compute_unit
        set bind_flag = #{bindFlag},
        update_time = now()
        where net_storage_compute_unit_code in
        <foreach collection="computeUnitCodeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </update>

    <update id="updateBindingByCodeList">
        update net_storage_compute_unit
        set bind_flag = 2,
        update_time = now()
        where net_storage_compute_unit_code in
        <foreach collection="computeUnitCodeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and bind_flag = 0
    </update>

    <select id="queryAvailableComputeUnit"
            resultType="net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ComputeUnitCache">
        SELECT
            a.net_storage_compute_unit_code as computeUnitCode, a.ip as ip, arm.id as armServerId
        FROM
            net_storage_compute_unit a
            inner join device d on a.device_code = d.device_code
            INNER JOIN arm_server arm ON d.arm_server_code = arm.arm_server_code
        WHERE
            d.delete_flag = 0
            AND d.device_code = #{deviceCode}
            AND a.bind_flag = 0
    </select>

    <select id="getUsedIpListByArmServerId" resultType="java.lang.String">
        select
            ip
        from net_storage_compute_unit a
        inner join device b on a.device_id = b.id
        inner join arm_server c on b.arm_server_code = c.arm_server_code
        where c.id = #{armServerId}
        and b.delete_flag = 0
    </select>

    <update id="updateIpByCode">
        update net_storage_compute_unit set ip = #{ip} where net_storage_compute_unit_code = #{computeUnitCode}
    </update>
</mapper>
