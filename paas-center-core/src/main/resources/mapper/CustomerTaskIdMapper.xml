<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.CustomerTaskIdMapper">

    <update id="updateByCustomerId" parameterType="java.lang.Long">
        update customer_task_id
        set task_id = task_id + 1
        where customer_id = #{customerId}
    </update>

    <update id="updateMultipleByCustomerId">
        update customer_task_id
        set task_id = task_id + #{num}
        where customer_id = #{customerId}
    </update>

    <select id="selectTaskIdByCustomerId" resultType="java.lang.Integer">
        select task_id
        from customer_task_id
        where customer_id = #{customerId}
    </select>

    <select id="selectCustomerTaskIds" resultType="net.armcloud.paascenter.job.dto.CustomerTaskIdDTO">
        SELECT
            customer_id,
            task_id
        FROM
            customer_task_id
    </select>
</mapper>