<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.bmc.mapper.CardMapper">

    <insert id="insertBatch" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO card (card_id, ip, netmask,gateway,
        dns, sn,mac, server_id,server_sn,node_id,position,create_by)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.cardId}, #{item.ip}, #{item.netmask},#{item.gateway},
            #{item.dns}, #{item.sn},#{item.mac},#{item.serverId},#{item.serverSn},
            #{item.nodeId},#{item.position},#{item.createBy} )
        </foreach>
    </insert>


    <select id="selectCardByServerId" resultType="net.armcloud.paascenter.common.model.entity.bmc.Card">
        SELECT
            server_id,
            card_id
        FROM
            card
        WHERE
            server_id = #{serverId}
    </select>
</mapper>