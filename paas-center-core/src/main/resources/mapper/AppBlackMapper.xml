<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.AppBlackMapper">

    <select id="selectBlackAppPkgList" resultType="java.lang.String">
        select distinct app_pkg
        from app_black
        where
    <if test="customerId != null and customerId != ''">
        (customer_id is null or customer_id = #{customerId})
    </if>
    <if test="customerId == null or customerId == ''">
        customer_id is null
    </if>
        and specification_code = #{specificationCode}
        and status = 1
    </select>
</mapper>