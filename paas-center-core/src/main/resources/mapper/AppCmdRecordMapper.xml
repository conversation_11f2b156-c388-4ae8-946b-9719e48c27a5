<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.AppCmdRecordMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.AppCmdRecord">
    <id column="id" property="id" />
    <result column="request_id" property="requestId" />
    <result column="package_name" property="packageName" />
    <result column="sync_status_done" property="syncStatusDone" />
    <result column="pad_code" property="padCode" />
    <result column="create_time" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, request_id, package_name, sync_status_done, pad_code, create_time
  </sql>

  <insert id="batchInsert" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
    insert into ${tableName}(request_id, package_name, pad_code, create_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.requestId}, #{item.packageName}, #{item.padCode},#{item.oprBy})
    </foreach>
  </insert>

  <select id="listByPadCodeAndPackageName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from ${tableName}
    where pad_code = #{padCode}
    and package_name = #{packageName}
  </select>

  <update id="batchUpdateSyncStatusDoneById">
    update ${tableName}
    set sync_status_done = 1
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <update id="batchUpdateSyncStatusDoneByRequestId">
    update ${tableName}
    set sync_status_done = 1
    where request_id in
    <foreach collection="requestIds" item="requestId" open="(" separator="," close=")">
      #{requestId}
    </foreach>
    and sync_status_done != 1
  </update>
</mapper>