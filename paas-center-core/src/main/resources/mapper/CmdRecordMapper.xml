<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.CmdRecordMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.CmdRecord">
        <id column="id" property="id"/>
        <result column="request_id" property="requestId"/>
        <result column="task_id" property="taskId"/>
        <result column="source" property="source"/>
        <result column="command" property="command"/>
        <result column="pad_code" property="padCode"/>
        <result column="comms_server_id" property="commsServerId"/>
        <result column="status" property="status"/>
        <result column="result" property="result"/>
        <result column="error_msg" property="errorMsg"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, request_id, task_id, `source`, command, pad_code, comms_server_id,`status`, `result`, error_msg, end_time,
        create_time, create_by, update_time, update_by
    </sql>

    <update id="batchUpdateByRequestId">
        update ${tableName}
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when request_id = #{item.requestId} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`result` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.result != null">
                        when request_id = #{item.requestId} then #{item.result}
                    </if>
                </foreach>
            </trim>
            <trim prefix="error_msg = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.errorMsg != null">
                        when request_id = #{item.requestId} then #{item.errorMsg}
                    </if>
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.endTime != null">
                        when request_id = #{item.requestId} then #{item.endTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        where request_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.requestId}
        </foreach>
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="list.id" parameterType="map" useGeneratedKeys="true">
        insert into ${tableName}
        (request_id, task_id, sub_task_id, `source`, command, command_content, pad_code, comms_server_id, `status`, timeout_time, error_msg)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.requestId}, #{item.taskId}, #{item.subTaskId}, #{item.source}, #{item.command},
            #{item.commandContent}, #{item.padCode}, #{item.commsServerId}, #{item.status}, #{item.timeoutTime}, #{item.errorMsg})
        </foreach>
    </insert>

    <update id="batchUpdateError">
        update ${tableName}
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.status}
                </foreach>
            </trim>
            <trim prefix="error_msg = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.errorMsg}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>

    <select id="getLastNotTimeoutResetOrRestartRequestId" resultType="java.lang.String">
        select request_id
        from ${tableName}
        where pad_code = #{padCode}
        and command in('restart', 'reset')
        and timeout_time >= now()
        order by id desc
        limit 1
    </select>

    <select id="getBySubTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where sub_task_id = #{subTaskId}
        limit 1
    </select>

    <update id="batchUpdateExecutingStatusByRequestId">
        update ${tableName}
        set status = #{status}
        where request_id in
        <foreach collection="requestIds" item="requestId" open="(" separator="," close=")">
            #{requestId}
        </foreach>
        and status = 2
    </update>

    <update id="updateCmdResultBySubTaskId">
        update ${tableName}
        set status = #{status}
        where sub_task_id  = #{subTaskId}
    </update>

    <select id="getPadCodeBySubTaskId" resultType="java.lang.String">
        select pad_code
        from ${tableName}
        where sub_task_id = #{subTaskId}
            LIMIT 1
    </select>
</mapper>