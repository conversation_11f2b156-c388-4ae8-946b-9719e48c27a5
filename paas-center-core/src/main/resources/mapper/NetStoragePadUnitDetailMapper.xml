<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.NetStoragePadUnitDetailMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.NetStoragePadUnitDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="pad_code" property="padCode" jdbcType="VARCHAR"/>
        <result column="cluster_code" property="clusterCode" jdbcType="VARCHAR"/>
        <result column="device_android_prop" property="deviceAndroidProp" jdbcType="VARCHAR"/>
        <result column="net_storage_res_apply_size" property="netStorageResApplySize" jdbcType="VARCHAR"/>
        <result column="real_phone_template_id" property="realPhoneTemplateId" jdbcType="BIGINT"/>
        <result column="net_storage_res_unit_code" property="netStorageResUnitCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, pad_code, cluster_code, device_android_prop, net_storage_res_apply_size,
        real_phone_template_id, net_storage_res_unit_code, create_time, update_time
    </sql>

    <!-- 批量插入网存实例详情 -->
    <insert id="batchInsertNetStoragePadUnitDetails" parameterType="java.util.List">
        INSERT INTO net_storage_pad_unit_detail (
            pad_code, cluster_code, device_android_prop, net_storage_res_apply_size,
            real_phone_template_id, net_storage_res_unit_code
        ) VALUES
        <foreach collection="details" item="detail" separator=",">
            (
                #{detail.padCode}, #{detail.clusterCode}, #{detail.deviceAndroidProp},
                #{detail.netStorageResApplySize}, #{detail.realPhoneTemplateId},
                #{detail.netStorageResUnitCode}
            )
        </foreach>
    </insert>

</mapper>
