<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.DcImageMapper">
    <delete id="unsubscribeDcImages">
        delete from dc_image
        where image_id = #{imageId}
        AND dc_id not in
        <foreach collection="dcIds" item="dcId" open="(" separator="," close=")">
            #{dcId}
        </foreach>
        AND delete_flag = 0
    </delete>

    <select id="imageSyncDcs" resultType="java.lang.Long">
        select distinct dc_id
        from dc_image
        where image_id = #{imageId}
        AND delete_flag = 0
        AND status != -1
    </select>
</mapper>