<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.filecenter.mapper.AppFileMapper">

    <!-- Find APP by package name and version code -->
    <select id="findByPackageAndVersion" resultType="net.armcloud.paascenter.common.model.entity.filecenter.AppFile">
        SELECT 
            *
        FROM 
            fc_app_files
        WHERE 
            package_name = #{packageName}
            AND version_code = #{versionCode}
        LIMIT 1
    </select>

</mapper> 