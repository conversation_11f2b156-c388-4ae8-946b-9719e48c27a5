<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.filecenter.mapper.UserFileMapper">

    <!-- Query file details with pagination -->
    <select id="queryFileDetails" resultType="net.armcloud.paascenter.filecenter.model.vo.FileDetailVO">
        SELECT
            uf.id AS id,
            uf.file_storage_id AS fileStorageId,
            uf.file_unique_id AS fileUniqueId,
            uf.customer_id AS customerId,
            uf.file_name AS fileName,
            uf.file_type AS fileType,
            uf.source_type AS sourceType,
            uf.original_url AS originalUrl,
            fs.file_size AS fileSize,
            fs.file_md5 AS fileMd5,
            fs.public_url AS publicUrl,
            uf.created_time AS createdTime,
            uf.updated_time AS updatedTime,
            c.customer_name AS customerName,
            c.customer_account AS customerAccount
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        LEFT JOIN
            customer c ON uf.customer_id = c.id
        WHERE 
            uf.file_status = 'valid'
            AND fs.upload_status = 'success'
            AND uf.file_type = 'file'
        <if test="customerId != null">
            AND uf.customer_id = #{customerId}
        </if>
        <if test="fileName != null and fileName != ''">
            AND uf.file_name LIKE CONCAT('%', #{fileName}, '%')
        </if>
        ORDER BY uf.created_time DESC
    </select>


    <select id="queryAppFiles" resultType="net.armcloud.paascenter.filecenter.model.vo.AppDetailsVO">
        SELECT
            uf.id AS id,
            uf.file_storage_id AS fileStorageId,
            uf.file_unique_id AS fileUniqueId,
            uf.customer_id AS customerId,
            uf.file_name AS fileName,
            uf.file_type AS fileType,
            uf.source_type AS sourceType,
            uf.original_url AS originalUrl,
            fs.file_size AS fileSize,
            fs.file_md5 AS fileMd5,
            fs.public_url AS publicUrl,
            uf.created_time AS createdTime,
            uf.file_comment AS fileComment,
            af.id AS appFileId,
            af.app_name AS appName,
            af.package_name AS packageName,
            af.version_name AS versionName,
            af.version_code AS versionCode,
            af.signature_hash AS signatureHash,
            af.main_activity AS mainActivity,
            af.permissions AS permissions,
            af.installation_requirements AS installationRequirements,
            af.is_system_app AS isSystemApp,
            af.is_debug_app AS isDebugApp,
            af.developer_name AS developerName,
            af.icon_public_url AS iconPublicUrl,
            af.supported_abis AS supportedAbis,
            af.min_sdk_version AS minSdkVersion,
            af.target_sdk_version AS targetSdkVersion,
            af.app_size AS appSize,
            af.install_location AS installLocation,
            c.customer_name AS customerName,
            c.customer_account AS customerAccount,
            uf.sort_num
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        LEFT JOIN
            fc_app_files af ON uf.file_storage_id = af.file_storage_id
        LEFT JOIN
            customer c ON uf.customer_id = c.id
        left join
                fc_user_app_files d on d.file_unique_id = uf.file_unique_id
        WHERE 
            uf.file_status = 'valid'
            AND fs.upload_status = 'success'
            AND uf.file_type = 'app'
        <if test="customerId != null">
            AND uf.customer_id = #{customerId}
        </if>
        <if test="fileName != null and fileName != ''">
            AND uf.file_name LIKE CONCAT('%', #{fileName}, '%')
        </if>
        <if test="appName != null and appName != ''">
            AND ( af.app_name LIKE CONCAT('%', #{appName}, '%') or d.app_name LIKE CONCAT('%', #{appName}, '%') )
        </if>
        <if test="pkgName != null and pkgName != ''">
            AND af.package_name LIKE CONCAT('%', #{pkgName}, '%')
        </if>
        ORDER BY
        CASE
        WHEN uf.sort_num IS NOT NULL THEN 0 ELSE 1
        END,
        COALESCE(uf.sort_num, 999999),
        uf.updated_time DESC,
        RAND()
    </select>

    <!-- queryAppDetailByAppId --> 

    <select id="queryAppDetailByAppId" resultType="net.armcloud.paascenter.filecenter.model.vo.AppDetailsVO">
        SELECT
            uf.id AS id,
            uf.file_unique_id AS fileUniqueId,
            uf.customer_id AS customerId,
            uf.file_name AS fileName,
            uf.file_type AS fileType,
            uf.source_type AS sourceType,
            uf.original_url AS originalUrl,
            uf.file_comment AS fileComment,
            fs.file_size AS fileSize,
            fs.file_md5 AS fileMd5,
            fs.public_url AS publicUrl,
            uf.created_time AS createdTime,
            af.app_name AS appName,
            af.package_name AS packageName,
            af.version_name AS versionName,
            af.version_code AS versionCode,
            af.signature_hash AS signatureHash,
            af.main_activity AS mainActivity,
            af.permissions AS permissions,
            af.installation_requirements AS installationRequirements,
            af.is_system_app AS isSystemApp,
            af.is_debug_app AS isDebugApp,
            af.developer_name AS developerName,
            af.icon_public_url AS iconPublicUrl,
            af.supported_abis AS supportedAbis,
            af.min_sdk_version AS minSdkVersion,
            af.target_sdk_version AS targetSdkVersion,
            af.app_size AS appSize,
            af.install_location AS installLocation,
            c.customer_name AS customerName,
            c.customer_account AS customerAccount
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        LEFT JOIN
            fc_app_files af ON uf.file_storage_id = af.file_storage_id
        LEFT JOIN
            customer c ON uf.customer_id = c.id
        WHERE 
            uf.file_status = 'valid'
            AND fs.upload_status = 'success'
            AND uf.file_type = 'app'
            AND uf.customer_id = #{customerId}
            AND uf.file_unique_id = #{appId}
    </select>

    <!-- queryAppDetails --> 

    <select id="queryAppDetails" resultType="net.armcloud.paascenter.filecenter.model.vo.AppDetailsVO">
        SELECT
            uf.id AS id,
            uf.file_unique_id AS fileUniqueId,
            uf.customer_id AS customerId,
            uf.file_name AS fileName,
            uf.file_type AS fileType,
            uf.source_type AS sourceType,
            uf.original_url AS originalUrl,
            fs.file_size AS fileSize,
            fs.file_md5 AS fileMd5,
            fs.public_url AS publicUrl,
            uf.created_time AS createdTime,
            uf.file_comment AS fileComment,
            af.app_name AS appName,
            af.package_name AS packageName,
            af.version_name AS versionName,
            af.version_code AS versionCode,
            af.signature_hash AS signatureHash,
            af.main_activity AS mainActivity,
            af.permissions AS permissions,
            af.installation_requirements AS installationRequirements,
            af.is_system_app AS isSystemApp,
            af.is_debug_app AS isDebugApp,
            af.developer_name AS developerName,
            af.icon_public_url AS iconPublicUrl,
            af.supported_abis AS supportedAbis,
            af.min_sdk_version AS minSdkVersion,
            af.target_sdk_version AS targetSdkVersion,
            af.app_size AS appSize,
            af.install_location AS installLocation,
            c.customer_name AS customerName,
            c.customer_account AS customerAccount
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        LEFT JOIN
            fc_app_files af ON uf.file_storage_id = af.file_storage_id
        LEFT JOIN
            customer c ON uf.customer_id = c.id
        WHERE 
            uf.file_status = 'valid'
            AND fs.upload_status = 'success'
            AND uf.file_type = 'app'
            AND uf.customer_id = #{customerId}
            <if test="appIds != null and appIds.size() > 0">
                AND uf.file_unique_id IN
                <foreach collection="appIds" item="appId" separator="," open="(" close=")">
                #{appId}
                </foreach>
            </if>
            <if test="packageName != null and packageName != ''">
                AND af.package_name LIKE CONCAT('%', #{packageName}, '%')
            </if>
        ORDER BY uf.created_time DESC
    </select>

    <!-- queryCustomerAppFile --> 

    <select id="queryCustomerAppFile" resultType="net.armcloud.paascenter.common.client.internal.vo.CustomerAppFileVO">
        SELECT
            uf.id AS customerFileId,
            uf.file_storage_id AS fileId,
            af.app_name AS appName,
            uf.customer_id AS customerId,
            af.package_name AS packageName,
            af.icon_public_url AS iconUrl,
            fs.public_url AS publicUrl,
            af.version_name AS appVersionName
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        LEFT JOIN
            fc_app_files af ON uf.file_storage_id = af.file_storage_id
        left join fc_user_app_files d on d.file_unique_id = uf.file_unique_id
        WHERE
            uf.file_type = 'app'
            AND uf.customer_id = #{customerId}
            AND uf.file_unique_id = #{appId}
            <if test="appName != null and appName != ''">
                AND ( af.app_name LIKE CONCAT('%', #{appName}, '%') or d.app_name LIKE CONCAT('%', #{appName}, '%') )
            </if>
            <if test="pkgName != null and pkgName != ''">
                AND af.package_name LIKE CONCAT('%', #{pkgName}, '%')
            </if>
            AND uf.file_status = 'valid'
            AND fs.upload_status = 'success'
    </select>

    <!-- queryCustomerFile --> 

    <select id="queryCustomerFile" resultType="net.armcloud.paascenter.common.client.internal.vo.CustomerFileVO">
        SELECT
            uf.id AS customerFileId,
            uf.file_storage_id AS fileId,
            uf.file_unique_id AS fileUniqueId,
            fs.public_url AS publicUrl,
            fs.file_md5 AS fileMd5,
            fs.file_ext AS fileExt,
            uf.file_name AS customerFileName
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        WHERE 
            uf.file_type = 'file'
            <if test="customerId != null">
                AND uf.customer_id = #{customerId}
            </if>
            <if test="fileMd5 != null and fileMd5 != ''">
                AND fs.file_md5 = #{fileMd5}
            </if>
            <if test="fileUniqueId != null and fileUniqueId != ''">
                AND uf.file_unique_id = #{fileUniqueId}
            </if>
            AND uf.file_status = 'valid'
            AND fs.upload_status = 'success'
            ORDER BY uf.updated_time DESC
            LIMIT 1
    </select>

    <!-- queryCustomerExistingApp --> 

    <select id="queryCustomerExistingApp" resultType="net.armcloud.paascenter.common.client.internal.vo.CustomerAppFileVO">
        SELECT
            uf.id AS customerFileId,
            uf.file_storage_id AS fileId,
            af.app_name AS appName,
            uf.customer_id AS customerId,
            af.package_name AS packageName,
            af.icon_public_url AS iconUrl,
            fs.public_url AS publicUrl,
            af.version_name AS appVersionName
        FROM 
            fc_user_files uf
        LEFT JOIN 
            fc_file_storage fs ON uf.file_storage_id = fs.id
        LEFT JOIN
            fc_app_files af ON uf.file_storage_id = af.file_storage_id
        WHERE
            uf.file_type = 'app'
            AND uf.id = #{userFileId}
            AND uf.file_status = 'valid'
            AND fs.upload_status = 'success'
            ORDER BY uf.updated_time DESC
            LIMIT 1
    </select>
</mapper> 