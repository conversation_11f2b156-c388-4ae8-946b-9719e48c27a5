<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.netpadv2.mapper.NetPadComputeUnitRelationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.openapi.netpadv2.entity.NetPadComputeUnitRelationDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="pad_code" property="padCode" jdbcType="VARCHAR"/>
        <result column="net_storage_compute_unit_code" property="netStorageComputeUnitCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, pad_code, net_storage_compute_unit_code, create_time, update_time, create_by, update_by
    </sql>

    <!-- 根据实例编码查询算力关联关系 -->
    <select id="selectByPadCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM net_pad_compute_unit_relation
        WHERE pad_code = #{padCode}
    </select>

    <!-- 根据实例编码列表查询算力关联关系 -->
    <select id="selectByPadCodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM net_pad_compute_unit_relation
        WHERE pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <!-- 批量插入关联关系 -->
    <insert id="batchInsert">
        INSERT INTO net_pad_compute_unit_relation (
            pad_code, net_storage_compute_unit_code, create_time, update_time, create_by, update_by
        ) VALUES
        <foreach collection="relations" item="relation" separator=",">
            (
                #{relation.padCode}, #{relation.netStorageComputeUnitCode}, #{relation.createTime}, #{relation.updateTime},
                #{relation.createBy}, #{relation.updateBy}
            )
        </foreach>
    </insert>

    <!-- 删除关联关系 -->
    <delete id="deleteByPadCode">
        DELETE FROM net_pad_compute_unit_relation WHERE pad_code = #{padCode}
    </delete>

    <!-- 批量删除关联关系 -->
    <delete id="deleteByPadCodes">
        DELETE FROM net_pad_compute_unit_relation
        WHERE pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </delete>

</mapper>
