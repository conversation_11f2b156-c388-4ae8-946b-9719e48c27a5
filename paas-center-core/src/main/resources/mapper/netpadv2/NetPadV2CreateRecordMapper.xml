<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.netpadv2.mapper.NetPadV2CreateRecordMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.openapi.netpadv2.entity.NetPadV2CreateRecord">
        <id column="pad_code" property="padCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        pad_code, create_time
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO net_pad_v2_create_record (
            pad_code, create_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.padCode}, #{record.createTime}
            )
        </foreach>
    </insert>

    <select id="countByPadCodes" resultType="int">
        SELECT COUNT(pad_code)
        FROM net_pad_v2_create_record
        WHERE pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="getByPadCodeList" resultType="java.lang.String">
        SELECT pad_code
        FROM net_pad_v2_create_record
        WHERE pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>
</mapper>
