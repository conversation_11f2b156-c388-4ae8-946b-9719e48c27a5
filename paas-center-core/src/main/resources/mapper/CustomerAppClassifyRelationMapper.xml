<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerAppClassifyRelationMapper">

    <insert id="batchInsert">
        INSERT INTO customer_app_classify_relation (customer_id, app_classify_id,file_id,app_id,app_name,package_name,app_version_no,app_version_name, create_time, create_by,update_time, update_by )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.appClassifyId},#{item.fileId},#{item.appId},#{item.appName},#{item.packageName},#{item.appVersionNo},#{item.appVersionName}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="selectAppList" resultType="net.armcloud.paascenter.openapi.model.vo.AppClassifyDetailCheckVO">
        SELECT
        cacr.id,
        cacr.customer_id as customerId,
        cacr.app_id as appId,
        cac.classify_type as classifyType
        FROM
        customer_app_classify_relation cacr
        LEFT JOIN customer_app_classify cac ON cacr.app_classify_id = cac.id
        WHERE
        cacr.customer_id = #{customerId}
          <if test="appClassifyIds != null">
              AND cacr.app_classify_id IN
              <foreach collection="appClassifyIds" item="appClassifyId" open="(" separator="," close=")">
                  #{appClassifyId}
              </foreach>
          </if>
    </select>
</mapper>
