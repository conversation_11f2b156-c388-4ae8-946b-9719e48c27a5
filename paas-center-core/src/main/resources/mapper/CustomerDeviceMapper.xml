<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerDeviceMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerDevice">
    <id column="id" property="id" />
    <result column="device_id" property="deviceId" />
    <result column="customer_id" property="customerId" />
    <result column="start_time" property="startTime" />
    <result column="expiration_time" property="expirationTime" />
    <result column="recovery_time" property="recoveryTime" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, device_id, customer_id, start_time, expiration_time, recovery_time, delete_flag,
    create_by, create_time, update_by, update_time
  </sql>

  <select id="countNotExpiredByDeviceIdAndCustomerId" resultType="int">
    select count(*)
    from customer_device
    where customer_id = #{customerId}
      and delete_flag = false
      and device_id in
    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
      #{deviceId}
    </foreach>
  </select>

  <select id="getCustomerIdByDeviceId" resultType="java.lang.Long">
    select customer_id
    from customer_device
    where device_id = #{deviceId}
      and delete_flag = false
  </select>
</mapper>