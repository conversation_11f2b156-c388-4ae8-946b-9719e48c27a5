<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.ServerTokenLogMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.ServerTokenLog">
    <id column="id" property="id" />
    <result column="pad_code" property="padCode" />
    <result column="comms_server_id" property="commsServerId" />
    <result column="token" property="token" />
    <result column="expiration_time" property="expirationTime" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id, pad_code, comms_server_id, token, expiration_time, create_time, create_by, update_time,
    update_by
  </sql>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.comms.ServerTokenLog" useGeneratedKeys="true">
    insert into server_token_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="padCode != null">
        pad_code,
      </if>
      <if test="commsServerId != null">
        comms_server_id,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="expirationTime != null">
        expiration_time,
      </if>
      <if test="imageId != null and imageId != ''">
        image_id,
      </if>
      <if test="versionCode != null and versionCode != ''">
        version_code,
      </if>
      <if test="versionName != null and versionName != ''">
        version_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="padCode != null">
        #{padCode},
      </if>
      <if test="commsServerId != null">
        #{commsServerId},
      </if>
      <if test="token != null">
        #{token},
      </if>
      <if test="expirationTime != null">
        #{expirationTime},
      </if>
      <if test="imageId != null and imageId != ''">
        #{imageId},
      </if>
      <if test="versionCode != null and versionCode != ''">
        #{versionCode},
      </if>
      <if test="versionName != null and versionName != ''">
        #{versionName},
      </if>
    </trim>
  </insert>
</mapper>