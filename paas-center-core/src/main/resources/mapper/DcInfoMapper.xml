<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.DcInfoMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.DcInfo">
        <id column="id" property="id" />
        <result column="area" property="area" />
        <result column="dc_code" property="dcCode" />
        <result column="dc_name" property="dcName" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="idc" property="idc" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        dc_code,
        dc_name,
        area,
        idc,
        create_time,
        create_by,
        update_time,
        update_by
    </sql>

    <select id="selectDcInfoByCode" parameterType="java.lang.String" resultType="net.armcloud.paascenter.openapi.model.vo.DcInfoVO">
        SELECT dc_code as dcCode, dc_name, area
        FROM dc_info pp
        where id = #{dcId}
    </select>

    <select id="getByPadCode" resultMap="BaseResultMap">
        select pdi.id,
               pdi.dc_code,
               pdi.dc_name,
               pdi.area,
               pdi.create_time,
               pdi.create_by,
               pdi.update_time,
               pdi.update_by,
               pdi.idc
        from dc_info pdi
                 join device pd on pd.dc_id = pdi.id
                 join device_pad pdp on pd.id = pdp.device_id
                 join pad pp on pdp.pad_id = pp.id
        where pdi.delete_flag = 0
          and pp.pad_code = #{padCode}
    </select>

<!--    <select id="listByCustomerId" resultMap="BaseResultMap">-->
<!--        select dc.id,-->
<!--               dc.dc_code,-->
<!--               dc.dc_name,-->
<!--               dc.area,-->
<!--               dc.internal_ip,-->
<!--               dc.public_ip,-->
<!--               dc.create_time,-->
<!--               dc.create_by,-->
<!--               dc.update_time,-->
<!--               dc.update_by,-->
<!--               dc.idc-->
<!--        from dc_info dc-->
<!--                 join device d on dc.id = d.dc_id-->
<!--                 join customer_device cd on cd.device_id = d.id-->
<!--        where cd.customer_id = #{customerId} and dc.delete_flag = 0-->
<!--        group by dc.id-->
<!--    </select>-->

    <select id="getVoByPadCode" resultType="net.armcloud.paascenter.openapi.model.vo.DcInfoVO">
        select dc.dc_name                    dcName
        from dc_info dc
                 join device d on dc.id = d.dc_id
                 join device_pad dp on dp.device_id = d.id
                 join pad p on p.id = dp.pad_id
        where p.pad_code = #{padCode}
    </select>



    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dc_info
        where id = #{dcId}
          and delete_flag = 0
    </select>

    <select id="getIdByDcCode" resultType="java.lang.Long">
        select id from dc_info where dc_code = #{dcCode}
    </select>

    <select id="list" resultType="net.armcloud.paascenter.common.model.entity.paas.DcInfo">
        select dc.id,
               dc.dc_code,
               dc.dc_name,
               dc.area,
               dc.create_time,
               dc.create_by,
               dc.update_time,
               dc.update_by,
               dc.idc
        from dc_info dc
        where dc.delete_flag = 0
        group by dc.id
    </select>


</mapper>