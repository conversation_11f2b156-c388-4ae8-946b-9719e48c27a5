<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerPoliciesMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerPolicies">
    <id column="id"  property="id" />
    <result column="customer_id"  property="customerId" />
    <result column="type"  property="type" />
    <result column="create_by"  property="createBy" />
    <result column="create_time"  property="createTime" />
    <result column="update_by"  property="updateBy" />
    <result column="update_time"  property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, customer_id, `type`, create_by, create_time, update_by, update_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from customer_policies
    where id = #{id}
  </select>

  <select id="getByCustomerIdAndType" resultMap="BaseResultMap">
    select id, customer_id, type
    from customer_policies
    where customer_id = #{customerId}
      and delete_flag = 0
  </select>
</mapper>