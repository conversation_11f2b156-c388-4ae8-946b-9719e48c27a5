<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.cms.mapper.TaskRelInstanceDetailMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.container.TaskRelInstanceDetail">
        <id column="id" property="id"/>
        <result column="master_task_id" property="masterTaskId"/>
        <result column="task_type" property="taskType"/>
        <result column="sub_task_id" property="subTaskId"/>
        <result column="instance_name" property="instanceName"/>
        <result column="identification_code" property="identificationCode"/>
        <result column="ip" property="ip"/>
        <result column="image_id" property="imageId"/>
        <result column="image_tag" property="imageTag"/>
        <result column="width" property="width"/>
        <result column="height" property="height"/>
        <result column="dpi" property="dpi"/>
        <result column="fps" property="fps"/>
        <result column="disk" property="disk"/>
        <result column="cpu" property="cpu"/>
        <result column="memory" property="memory"/>
        <result column="max_uplink_bandwidth" property="maxUplinkBandwidth"/>
        <result column="max_downlink_bandwidth" property="maxDownlinkBandwidth"/>
        <result column="container_property" property="containerProperty"/>
        <result column="container_index" property="containerIndex"/>
        <result column="android_prop" property="androidProp"/>
        <result column="device_android_prop" property="deviceAndroidProp"/>
        <result column="dns" property="dns"/>
        <result column="adi_json" property="adiJson"/>
        <result column="other_param_json" property="otherParamJson"/>
        <result column="mac" property="mac"/>
        <result column="net_storage_res_flag" property="netStorageResFlag"/>
        <result column="net_storage_res_id" property="netStorageResId"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,
        task_type,
        master_task_id,
        sub_task_id,
        instance_name,
        identification_code,
        ip,
        image_tag,
        image_id,
        width,
        height,
        dpi,
        fps,
        disk,
        cpu,
        memory,
        max_uplink_bandwidth,
        max_downlink_bandwidth,
        container_property,
        container_index,
        android_prop,
        device_android_prop,
        dns,
        adi_json,
        other_param_json,
        mac,
        net_storage_res_id,
        net_storage_res_flag
    </sql>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into task_rel_instance_detail
        (task_type, master_task_id, container_property, sub_task_id, identification_code, instance_name, ip, image_id, image_tag,
         width, height, dpi, fps, `disk`, cpu, memory, max_uplink_bandwidth, max_downlink_bandwidth, container_index, android_prop,
        device_android_prop, dns, adi_json, other_param_json, mac,net_storage_res_id,net_storage_res_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.taskType}, #{item.masterTaskId}, #{item.containerProperty}, #{item.subTaskId}, #{item.identificationCode},
             #{item.instanceName}, #{item.ip}, #{item.imageId}, #{item.imageTag}, #{item.width}, #{item.height},
             #{item.dpi}, #{item.fps}, #{item.disk}, #{item.cpu}, #{item.memory}, #{item.maxUplinkBandwidth},
             #{item.maxDownlinkBandwidth}, #{item.containerIndex}, #{item.androidProp}, #{item.deviceAndroidProp},
            #{item.dns}, #{item.adiJson}, #{item.otherParamJson}, #{item.mac},#{item.netStorageResId},#{item.netStorageResFlag})
        </foreach>
    </insert>

    <select id="getLatestByMasterTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task_rel_instance_detail
        where master_task_id = #{masterTaskId}
          and delete_flag = false
        and task_type = #{taskType}
        order by id desc
        limit 1
    </select>

    <update id="updateContainerProperty">
        update task_rel_instance_detail
        set container_property = #{containerProperty}
        where id = #{id}
    </update>



    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into task_rel_instance_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskType != null">
                task_type,
            </if>
            <if test="masterTaskId != null">
                master_task_id,
            </if>
            <if test="subTaskId != null">
                sub_task_id,
            </if>
            <if test="instanceName != null">
                instance_name,
            </if>
            <if test="identificationCode != null">
                identification_code,
            </if>
            <if test="ip != null">
                ip,
            </if>
            <if test="imageTag != null">
                image_tag,
            </if>
            <if test="imageId != null">
                image_id,
            </if>
            <if test="width != null">
                width,
            </if>
            <if test="height != null">
                height,
            </if>
            <if test="dpi != null">
                dpi,
            </if>
            <if test="fps != null">
                fps,
            </if>
            <if test="disk != null">
                `disk`,
            </if>
            <if test="cpu != null">
                cpu,
            </if>
            <if test="memory != null">
                memory,
            </if>
            <if test="maxUplinkBandwidth != null">
                max_uplink_bandwidth,
            </if>
            <if test="maxDownlinkBandwidth != null">
                max_downlink_bandwidth,
            </if>
            <if test="containerProperty != null">
                container_property,
            </if>
            <if test="androidProp != null">
                android_prop,
            </if>
            <if test="deviceAndroidProp != null and deviceAndroidProp != ''">
                device_android_prop,
            </if>
            <if test="dns != null and dns != ''">
                dns,
            </if>
            <if test="adiJson != null and adiJson != ''">
                adi_json,
            </if>
            <if test="otherParamJson != null and otherParamJson != ''">
                other_param_json,
            </if>
            <if test="mac != null and mac != ''">
                mac,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="masterTaskId != null">
                #{masterTaskId},
            </if>
            <if test="subTaskId != null">
                #{subTaskId},
            </if>
            <if test="instanceName != null">
                #{instanceName},
            </if>
            <if test="identificationCode != null">
                #{identificationCode},
            </if>
            <if test="ip != null">
                #{ip},
            </if>
            <if test="imageTag != null">
                #{imageTag},
            </if>
            <if test="imageId != null">
                #{imageId},
            </if>
            <if test="width != null">
                #{width},
            </if>
            <if test="height != null">
                #{height},
            </if>
            <if test="dpi != null">
                #{dpi},
            </if>
            <if test="fps != null">
                #{fps},
            </if>
            <if test="disk != null">
                #{disk},
            </if>
            <if test="cpu != null">
                #{cpu},
            </if>
            <if test="memory != null">
                #{memory},
            </if>
            <if test="maxUplinkBandwidth != null">
                #{maxUplinkBandwidth},
            </if>
            <if test="maxDownlinkBandwidth != null">
                #{maxDownlinkBandwidth},
            </if>
            <if test="containerProperty != null">
                #{containerProperty},
            </if>
            <if test="androidProp != null">
                #{androidProp},
            </if>
            <if test="deviceAndroidProp != null and deviceAndroidProp != ''">
                #{deviceAndroidProp},
            </if>
            <if test="dns != null and dns != ''">
                #{dns},
            </if>
            <if test="adiJson != null and adiJson != ''">
                #{adiJson},
            </if>
            <if test="otherParamJson != null and otherParamJson != ''">
                #{otherParamJson},
            </if>
            <if test="mac != null and mac != ''">
                #{mac}
            </if>
        </trim>
    </insert>


    <select id="getByMasterTaskIdAndSubTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task_rel_instance_detail
        where master_task_id = #{masterTaskId}
          and sub_task_id = #{subTaskId}
          and delete_flag = false
          and task_type = #{taskType}
    </select>




    <select id="getBySubTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task_rel_instance_detail
        where sub_task_id = #{subTaskId}
        and delete_flag = false
        and task_type = #{taskType}
    </select>
</mapper>