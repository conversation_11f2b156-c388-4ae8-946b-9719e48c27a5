<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.rtc.mapper.PadRtcTokenLogMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadRtcTokenLog">
        <id column="id" property="id"/>
        <result column="room_id" property="roomId"/>
        <result column="pad_code" property="padCode"/>
        <result column="user_id" property="userId"/>
        <result column="room_token" property="roomToken"/>
        <result column="expire" property="expire"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="publish_stream" property="publishStream"/>
        <result column="refresh_flag" property="refreshFlag"/>
        <result column="rtc_platform" property="rtcPlatform"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        room_id,
        pad_code,
        user_id,
        room_token,
        expire,
        platform_type,
        create_by,
        create_time,
        update_by,
        update_time,
        publish_stream,
        refresh_flag,
        rtc_platform
    </sql>

<!--    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">-->
<!--        insert into pad_rtc_token_log-->
<!--            (room_id, pad_code, user_id, room_token, expire)-->
<!--        values-->
<!--        <foreach collection="list" item="item" separator=",">-->
<!--            (#{item.roomId}, #{item.padCode}, #{item.userId}, #{item.roomToken}, #{item.expire})-->
<!--        </foreach>-->
<!--    </insert>-->

</mapper>