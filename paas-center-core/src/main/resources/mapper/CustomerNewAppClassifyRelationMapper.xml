<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerNewAppClassifyRelationMapper">

    <select id="selectAppListByAppIds" resultType="net.armcloud.paascenter.common.client.internal.vo.NewAppClassifyNameVO">
        select cnacr.new_app_classify_id as newAppClassifyId,cnacr.app_id as appId,cnac.classify_name as newAppClassifyName from customer_new_app_classify_relation cnacr
        left join customer_new_app_classify cnac on cnacr.new_app_classify_id = cnac.id
        where cnac.enable = 1 
        <if test="customerId != null">
            and cnacr.customer_id = #{customerId} 
        </if>
        and cnacr.app_id in
        <foreach close=")" collection="appIds" item="appId" open="(" separator=", ">
            #{appId}
        </foreach>
    </select>
</mapper>
