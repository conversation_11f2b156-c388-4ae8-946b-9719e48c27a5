<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.TaskMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.Task">
        <id column="id" property="id" />
        <result column="status" property="status" />
        <result column="type" property="type" />
        <result column="customer_id" property="customerId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, unique_id, `status`, `type`, customer_id, create_by, create_time, update_by, update_time
    </sql>

    <insert id="insertTask" parameterType="net.armcloud.paascenter.common.model.entity.task.Task" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `task`(unique_id, status, type, customer_id, task_source, create_by, update_by)
        VALUES (#{uniqueId}, #{status}, #{type}, #{customerId},#{taskSource}, #{createBy},
        #{updateBy});
    </insert>

    <update id="batchUpdateUniqueId" parameterType="java.util.List">
        update task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unique_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.uniqueId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>

    <select id="getByUniqueIdAndCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task
        where unique_id = #{uniqueId}
          and delete_flag = 0
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into task
        (unique_id, task_source, `status`, `type`, customer_id, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.uniqueId}, #{item.taskSource}, #{item.status}, #{item.type}, #{item.customerId}, #{item.createBy})
        </foreach>
    </insert>

    <select id="selectPadTaskCallbackByCode" resultType="net.armcloud.paascenter.common.model.vo.task.PadTaskCallbackVO">
        SELECT
            a.unique_id as subTaskIdUniqueId,
            a.STATUS as subTaskStatus,
            a.pad_code as padCode,
            b.unique_id as masterTaskUniqueId,
            b.type as taskBusinessType
        FROM
            pad_task a
                LEFT JOIN task b ON a.task_id = b.id
        WHERE
            a.pad_code = #{padCode}
          AND b.type IN ( 1000, 1001 )
        ORDER BY
            a.id DESC LIMIT 1
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task
        where id = #{id}
    </select>

    <select id="hasTaskId" resultType="java.lang.Long">
        SELECT
            id
        FROM
            task
        WHERE
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
              #{id}
            </foreach>
          AND customer_id = #{customerId}
          AND type = #{taskType}
    </select>
    <select id="countDeviceTask" resultType="java.lang.Long">

        SELECT
        COUNT(0)
        FROM
        task t
        LEFT JOIN device_task dt ON t.id = dt.task_id
                                        AND dt.`status` in
                                           <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
                                                #{taskStatus}
                                           </foreach>
        WHERE
        t.delete_flag =0
        AND dt.delete_flag =0
        AND t.type = #{taskType}
        AND t.`status` in
            <foreach collection="taskStatusList" item="taskStatus" open="(" separator="," close=")">
            #{taskStatus}
            </foreach>

    </select>


    <update id="batchUpdateStatus">
        update task
        set status = #{status}
        where id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and delete_flag = 0
    </update>

    <select id="listByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from task
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and delete_flag = 0
    </select>

    <select id="selectIdByCreateTime" resultType="java.lang.Long">
        SELECT
            id
        FROM
            task
        WHERE
            create_time &lt; #{date}
    </select>

    <delete id="delByIds">
        DELETE
        FROM
        task
        WHERE
        id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCleanTaskPending" resultType="net.armcloud.paascenter.job.dto.CleanTaskPendingDTO">
        SELECT
            COUNT( a.pad_code ) count,
            a.pad_code
        FROM
            pad_task a
            INNER JOIN task b ON b.id = a.task_id
        WHERE
            a.`status` IN ( 2 )
          AND a.delete_flag = 0
          AND b.type = '1003'
        GROUP BY
            a.pad_code
        HAVING
            count >=4
    </select>

    <update id="updateStatusIfChangedById">
        update task set status = #{status} where id = #{id}
         and status !=#{status}
    </update>
</mapper>