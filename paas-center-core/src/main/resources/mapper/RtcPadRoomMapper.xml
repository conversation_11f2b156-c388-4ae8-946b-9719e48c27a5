<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.rtc.mapper.RtcPadRoomMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.PadRoom">
        <id column="id" property="id"/>
        <result column="pad_code" property="padCode"/>
        <result column="room_code" property="roomCode"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        pad_code,
        room_code,
        `status`,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <select id="listByPadCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad_room
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>
</mapper>