<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.KeepAliveAppPadMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into `keep_alive_app_pad`
        (customer_id, pad_code,  relation_id,create_time,create_by,update_time,update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customerId}, #{item.padCode}, #{item.relationId}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>
</mapper>
