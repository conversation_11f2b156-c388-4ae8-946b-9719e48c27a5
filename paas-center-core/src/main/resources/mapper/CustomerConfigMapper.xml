<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerConfigMapper">

    <select id="getStreamTypeByCustomerId" resultType="java.lang.Integer">
        select stream_type from customer_config where customer_id = #{customerId}
    </select>
</mapper>