<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.PadTaskMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.PadTask">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="status" property="status" />
        <result column="pad_code" property="padCode" />
        <result column="file_id" property="fileId" />
        <result column="timeout_time" property="timeoutTime" />
        <result column="task_content" property="taskContent" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="error_msg" property="errorMsg" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="customer_task_id" property="customerTaskId" />
        <result column="customer_file_id" property="customerFileId" />
        <result column="customer_id" property="customerId" />
        <result column="result" property="result" />
        <result column="image_id" property="imageId" />
        <result column="container_task_id" property="containerTaskId" />
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, unique_id, `status`, pad_code, task_content, start_time, end_time, timeout_time, error_msg, create_by, create_time,
        update_by, update_time, customer_task_id, customer_file_id, customer_id, `result`, image_id, container_task_id,wipe_data,custom_timeout
    </sql>

    <select id="listVOByMasterTaskIdAndUniqueIds" resultType="net.armcloud.paascenter.common.model.vo.api.PadTaskViewVO">
        select id,
               unique_id                       as subTaskUniqueId,
               status                          as taskStatus,
               pad_code                        as padCode,
               result,
               task_content as taskContent,
               UNIX_TIMESTAMP(end_time) * 1000 as endTime,
               error_msg AS errorMsg
        from pad_task
        where task_id = #{masterTaskId}
        <if test="uniqueIds != null and uniqueIds.size() != 0">
            and unique_id in
            <foreach collection="uniqueIds" item="uniqueId" open="(" separator="," close=")">
                #{uniqueId}
            </foreach>
        </if>
        order by id
    </select>


    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO pad_task (unique_id, task_id, status, pad_code, file_id, task_content, end_time, timeout_time, error_msg,customer_task_id,customer_id,create_by, update_by, customer_file_id, start_time, image_id, wipe_data,last_image_id,task_mode,custom_timeout)
        VALUES
        <foreach collection="padTasks" item="item" separator=",">
            (#{item.uniqueId}, #{item.taskId}, #{item.status}, #{item.padCode}, #{item.fileId}, #{item.taskContent},#{item.endTime},
            #{item.timeoutTime}, #{item.errorMsg}, #{item.customerTaskId}, #{item.customerId},#{item.createBy}, #{item.updateBy}, #{item.customerFileId}, #{item.startTime}, #{item.imageId}, #{item.wipeData},#{item.lastImageId},#{item.taskMode},#{item.customTimeout})
        </foreach>
    </insert>

    <select id="listByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad_task
        where task_id = #{taskId}
          and delete_flag = 0
    </select>

    <update id="batchUpdateUniqueId" parameterType="java.util.List">
        update pad_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="unique_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.uniqueId}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>

    <update id="setStartTime">
        update pad_task
        set start_time = #{startTime}
        where id = #{id} and start_time is null
    </update>

    <select id="getLatestTask" resultMap="BaseResultMap">
        select
        a.id, a.task_id, a.unique_id, a.`status`,a.pad_code, a.task_content, a.start_time, a.end_time, a.timeout_time, a.error_msg, a.create_by, a.create_time,
        a.update_by, a.update_time, a.customer_task_id, a.customer_file_id, a.customer_id, a.`result`, a.image_id, a.container_task_id
        from pad_task a
        inner join task b on b.id = a.task_id
        where pad_code = #{padCode}
        and a.delete_flag = 0
        and a.status = 2
        <if test="taskTypeList != null and taskTypeList.size() > 0">
            and b.type in
            <foreach item="type" collection="taskTypeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by a.id desc
        limit 1
    </select>

    <select id="getLatestSuccTask" resultMap="BaseResultMap">
        select
        a.id, a.task_id, a.unique_id, a.`status`,a.pad_code, a.task_content, a.start_time, a.end_time, a.timeout_time, a.error_msg, a.create_by, a.create_time,
        a.update_by, a.update_time, a.customer_task_id, a.customer_file_id, a.customer_id, a.`result`, a.image_id, a.container_task_id
        from pad_task a
        inner join task b on b.id = a.task_id
        where pad_code = #{padCode}
        and a.delete_flag = 0
        and a.status = 3
        <if test="taskTypeList != null and taskTypeList.size() > 0">
            and b.type in
            <foreach item="type" collection="taskTypeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by a.id desc
        limit 1
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad_task
        where id = #{id}
    </select>



    <select id="listVOByCustomerTaskIdsAndCustomerId"
            parameterType="net.armcloud.paascenter.common.model.dto.api.TaskDetailsInfoDTO"
            resultType="net.armcloud.paascenter.common.model.vo.api.PadTaskViewVO">
        select
            t1.customer_task_id as taskId,
            t1.pad_code  as padCode,
            t1.customer_file_id  as fileId,
            t1.status  as taskStatus,
            UNIX_TIMESTAMP(t1.end_time) * 1000 as endTime,
            t1.result as taskResult,
            t1.task_content as taskContent,
            t1.error_msg AS errorMsg
        from pad_task t1
        left join task t2 on t1.task_id = t2.id
        where t1.customer_id = #{customerId}
        and t1.delete_flag = 0
        <if test="taskIds != null and taskIds.size() != 0">
            and t1.customer_task_id in
            <foreach collection="taskIds" item="customerTaskId" open="(" separator="," close=")">
                #{customerTaskId}
            </foreach>
        </if>
        <if test="types != null and types.size() != 0">
            and t2.type in
            <foreach collection="types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by t1.id
    </select>

    <select id="getPodTaskByUniqueId" resultType="net.armcloud.paascenter.common.model.entity.task.PadTask">
        select
        <include refid="Base_Column_List"/>
        from pad_task
        where unique_id = #{uniqueId}
          and delete_flag = 0
    </select>

    <select id="getFilterExecutionTaskPads" resultType="java.lang.String">
        SELECT
            pad_code
        FROM
            task t1
        JOIN pad_task t2 ON t2.task_id = t1.id
        WHERE
            t1.type = #{type}
          AND t1.delete_flag = 0
          AND t2.delete_flag = 0
          and t2.`status` in (1,2)
        <if test="list != null and list.size() != 0">
            and t2.pad_code in
            <foreach collection="list" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
    </select>

    <update id="updateNotEndTaskStatusById">
        update pad_task
        <set>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="errorMsg != null and errorMsg != ''">
                error_msg = #{errorMsg},
            </if>
            <if test="result != null and result != ''">
                result = #{result},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="timeoutTime != null">
                timeout_time = #{timeoutTime}
            </if>
        </set>
        where id = #{id}
          and status in (1, 2)
    </update>

    <select id="getHasPadTask" resultType="net.armcloud.paascenter.common.model.vo.api.HasPadTaskVo">
        SELECT
            a.type,
            b.pad_code,
            a.unique_id as masterUniqueId,
            a.id as masterTaskId,
            b.id as padTaskId,
            b.unique_id as padTaskUniqueId,
            b.`status`,
            b.customer_task_id as customerTaskId
        FROM
            task a
                INNER JOIN pad_task b ON a.id = b.task_id
        WHERE
          pad_code = #{padCode}
          AND b.`status` IN (1,2)
        ORDER BY
            b.id DESC
        LIMIT 1
    </select>

    <select id="countByPadCodesStatus" resultType="int">
        select count(*)
        from pad_task
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>

        and status in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>

        and delete_flag = false
    </select>

    <select id="selectPadCodeAndTaskId" resultType="net.armcloud.paascenter.common.model.dto.api.SelectPadCodeAndTaskIdDTO">
        SELECT
            pad_code,
            task_id
        FROM
            pad_task
        WHERE
            customer_id = #{customerId}
          AND `status` IN (1,2)
          AND pad_code IN
          <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
          </foreach>
    </select>



    <update id="batchUpdateContainerById" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE pad_task
            SET
            container_task_id = #{item.containerTaskId}
            WHERE
            id = #{item.padTaskId}
        </foreach>
    </update>




    <select id="getExecTaskPads" resultType="net.armcloud.paascenter.common.model.vo.api.HasPadTaskVo">
        SELECT
        t1.type,
        t2.pad_code,
        t1.unique_id as masterUniqueId,
        t1.id as masterTaskId,
        t2.id as padTaskId,
        t2.unique_id as padTaskUniqueId,
        t2.`status`,
        t2.customer_task_id as customerTaskId
        FROM
        task t1
        JOIN pad_task t2 ON t2.task_id = t1.id
        WHERE
        t1.type = #{type}
        AND t1.delete_flag = 0
        AND t2.delete_flag = 0
        and t2.`status` in (1,2)
        <if test="list != null and list.size() != 0">
            and t2.pad_code in
            <foreach collection="list" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
    </select>

    <select id="selectTheTypeDoingPadTask" resultType="java.lang.Long">
        select p.id
        from pad_task p
                 left join task t on p.task_id = t.id
        where p.delete_flag = 0
          and t.delete_flag = 0
          and p.status = 2
          and p.pad_code = #{padCode}
          and t.type = #{type};
    </select>

    <update id="pullModeUpdateTimeout">
        UPDATE pad_task
        SET timeout_time = #{timeoutTime},error_msg = #{errorMsg}
        WHERE
            delete_flag = 0
          AND `status` IN (2)
          AND id = #{id}
    </update>

    <!-- 查询安装应用任务重复的padCode列表 -->
    <select id="findConflictPadCodesForInstallApp" resultType="net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO">
        SELECT pt.pad_code as padCode,pt.customer_task_id  as taskId,t.status as taskStatus,p.online as vmStatus
        FROM task t
        INNER JOIN pad_task pt ON t.id = pt.task_id
        inner join pad p on pt.pad_code = p.pad_code
        WHERE pt.customer_id = #{customerId}
        AND pt.pad_code IN
        <foreach collection="padCodes" item="pad" separator="," open="(" close=")">
            #{pad}
        </foreach>
        AND pt.customer_file_id = #{customerFileId}
        AND pt.status IN (1,2)
        AND t.type = 1003
        AND pt.delete_flag = 0
    </select>


    <select id="queryPadClusterInfo" resultType="net.armcloud.paascenter.task.model.vo.PadEdgeVO">
        SELECT a.pad_code, c.device_ip, d.cluster_code, a.screen_layout_code, c.cbs_info, e.android_image_version, a.image_id
        FROM pad a
                 left join device_pad b on a.id = b.pad_id
                 left join device c on b.device_id = c.id AND c.delete_flag = 0
                 left join arm_server d on c.arm_server_code = d.arm_server_code and d.delete_flag = 0
                left join customer_upload_image e on a.image_id = e.unique_id
        where a.`status` = 1
          and a.pad_code in
        <foreach collection="padCodes" item="pad" separator="," open="(" close=")">
            #{pad}
        </foreach>

    </select>

    <select id="getLastNCompletedTasks" resultType="net.armcloud.paascenter.common.model.entity.task.PadTask">
        select
        a.id, a.task_id, a.unique_id, a.`status`,a.pad_code, a.task_content, a.start_time, a.end_time, a.timeout_time, a.error_msg, a.create_by, a.create_time,
        a.update_by, a.update_time, a.customer_task_id, a.customer_file_id, a.customer_id, a.`result`, a.image_id, a.container_task_id,b.type
        from pad_task a
        inner join task b on b.id = a.task_id
        where pad_code = #{padCode}
        and a.delete_flag = 0
        order by a.id desc
        limit 3;
    </select>

    <select id="checkTasksByTypeAndPadCode" resultType="net.armcloud.paascenter.common.model.vo.api.HasPadTaskVo">
        SELECT
            t1.id as masterTaskId,
            t2.id as padTaskId
        FROM
            task t1
        JOIN pad_task t2 ON t2.task_id = t1.id
        WHERE
            t1.type = #{type}
          AND t1.delete_flag = 0
          AND t2.delete_flag = 0
          AND t2.pad_code = #{padCode}
        <if test="statusList != null and statusList.size() != 0">
            and t2.`status` in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        order by t2.id desc
    </select>
    
    <select id="getEmptyResultTaskByTypeAndStatus" resultMap="BaseResultMap">
        SELECT
            pt.id,pt.pad_code
        FROM 
            pad_task pt
        JOIN 
            task t ON pt.task_id = t.id
        WHERE 
            pt.id = #{id}
        AND 
            t.type = #{type}
        AND 
            pt.status = #{status}
        AND 
            pt.result IS NULL
        AND 
            pt.delete_flag = 0
        AND 
            t.delete_flag = 0
    </select>

    <select id="getLatestPadTaskByType" resultMap="BaseResultMap">
        SELECT
            t2.id,t2.pad_code,t2.status,t1.id as task_id
        FROM
            task t1
        JOIN pad_task t2 ON
            t2.task_id = t1.id
        WHERE
            t1.type = #{type}
            AND t1.delete_flag = 0
            AND t2.delete_flag = 0
            AND t2.pad_code = #{padCode}
        order by
            t2.id desc
        limit 1
    </select>
</mapper>