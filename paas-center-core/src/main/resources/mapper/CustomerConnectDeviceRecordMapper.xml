<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerConnectDeviceRecordMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerConnectDeviceRecord">
    <id column="id" property="id" />
    <result column="customer_id" property="customerId" />
    <result column="device_id" property="deviceId" />
    <result column="device_ip" property="deviceIp" />
    <result column="connect_info" property="connectInfo" />
    <result column="expiration_time" property="expirationTime" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, customer_id, device_id, device_ip, connect_info, expiration_time, create_by,
    create_time, update_by, update_time
  </sql>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into customer_connect_device_record
    (customer_id, device_id, device_ip, connect_info, expiration_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.customerId}, #{item.deviceId}, #{item.deviceIp}, #{item.connectInfo}, #{item.expirationTime})
    </foreach>
  </insert>

</mapper>