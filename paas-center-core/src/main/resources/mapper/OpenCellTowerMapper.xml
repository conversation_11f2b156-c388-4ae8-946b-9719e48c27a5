<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.OpenCellTowerMapper">


    <select id="selectRandomOpenCellTower" resultType="net.armcloud.paascenter.common.model.entity.paas.OpenCellTower">
       select * from open_cell_towers where mcc = #{mcc} order by rand() limit 1
    </select>

    <select id="selectRandomIdOpenCellTower" resultType="java.lang.Integer">
        select id from open_cell_towers where mcc = #{mcc} order by rand()  limit 1
    </select>

    <select id="selectIdsByMcc" resultType="java.lang.Integer">
        select id
        from open_cell_towers
        where mcc = #{mcc};

    </select>

    <select id="queryById" resultType="net.armcloud.paascenter.common.model.entity.paas.OpenCellTower">
        select *
        from open_cell_towers
        where id = #{id};

    </select>

</mapper>