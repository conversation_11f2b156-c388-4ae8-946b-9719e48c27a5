<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CallbackInformationMapper">

    <select id="selectCallbackUrl" resultType="net.armcloud.paascenter.common.model.vo.api.CallbackUrlVO">
        SELECT pc.host, pc.callback_url
        from callback_information p
                 left JOIN customer_callback pc on p.id = pc.callback_id
        WHERE p.type = #{type}
          and pc.customer_id = #{customerId}
          and pc.enable = 1
          and pc.delete_flag = 0
         limit 1
    </select>


</mapper>