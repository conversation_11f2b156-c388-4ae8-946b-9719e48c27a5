<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerAppClassifyMapper">

    <insert id="cusInsert" parameterType="net.armcloud.paascenter.common.model.entity.paas.CustomerAppClassify" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customer_app_classify (customer_id,classify_name,classify_type, app_num, remark,create_time, create_by,update_time, update_by,delete_flag,apply_all_instances)
        VALUES (#{customerId},#{classifyName},#{classifyType},#{appNum},#{remark},#{createTime}, #{createBy}, #{updateTime}, #{updateBy},#{deleteFlag},#{applyAllInstances})
    </insert>

    <update id="updatePadNum">
        UPDATE customer_app_classify SET pad_num = pad_num + #{padNum} WHERE id = #{id}
    </update>

    <select id="getClassifyNameByAppIds" resultType="net.armcloud.paascenter.common.client.internal.vo.AppClassifyNameVO">
        SELECT
            b.app_id,
            b.package_name,
            a.classify_name,
            a.classify_type
        FROM
            customer_app_classify a
                INNER JOIN customer_app_classify_relation b ON a.id = b.app_classify_id
        WHERE
          b.customer_id = #{customerId}
          AND a.delete_flag = 0
          AND b.package_name IN
          <foreach collection="pckNames" item="pckName" open="(" separator="," close=")">
              #{pckName}
          </foreach>
    </select>
</mapper>