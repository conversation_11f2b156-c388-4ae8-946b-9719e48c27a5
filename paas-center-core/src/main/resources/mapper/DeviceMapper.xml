<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.DeviceMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.Device">
    <id column="id" property="id" />
    <result column="dc_id" property="dcId" />
    <result column="device_level" property="deviceLevel" />
    <result column="device_code" property="deviceCode" />
    <result column="device_out_code" property="deviceOutCode" />
    <result column="cloud_vendor_type" property="cloudVendorType" />
    <result column="device_status" property="deviceStatus" />
    <result column="device_ip" property="deviceIp" />
    <result column="idc" property="idc" />
    <result column="arm_server_code" property="armServerCode" />
    <result column="soc_model" property="socModel" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
    <result column="pad_allocation_status" property="padAllocationStatus" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="cluster_code" property="clusterCode" />
    <result column="mac_address" property="macAddress" />
    <result column="init_status" property="initStatus" />
    <result column="debian_sys_info" property="debianSysInfo" />
    <result column="debian_boot_info" property="debianBootInfo" />
    <result column="cbs_info" property="cbsInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dc_id, device_level, device_code, device_out_code, cloud_vendor_type, device_status,
    device_ip, idc, arm_server_code, soc_model, create_by, create_time, update_by, update_time,
    pad_allocation_status, delete_flag, cluster_code, mac_address, init_status, debian_sys_info,
    debian_boot_info, cbs_info
  </sql>

  <update id="removePadByDeviceIp">
    update device
    set pad_allocation_status =  #{masterTaskStatus}, device_level = null , update_time = now()
    where device_ip  = #{deviceIp} and delete_flag = 0
  </update>

  <update id="updatePadAllocationStatusById">
    UPDATE device
    SET pad_allocation_status = #{padAllocationStatus}
    WHERE id IN
    <foreach collection="deviceIdList" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>


  <update id="updateDeviceLevelByDeviceCode">
    update device
    set device_level = #{deviceLevel}
    where device_code in
    <foreach item="item" collection="subList" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>


  <select id="selectDeviceOutCodeByDeviceCode" resultType="java.lang.String">
    select
        DISTINCT t1.device_out_code
    from device t1
    left join customer_device t2 on t1.id = t2.device_id
    where t1.device_code in
    <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
      #{deviceCode}
    </foreach>
    <if test="customerId != null">
      and t2.customer_id = #{customerId}
    </if>
  </select>

  <select id="selectDeviceOutCodeByPadCode" resultType="java.lang.String">
    SELECT
        DISTINCT t3.device_out_code
    FROM pad t1
    JOIN device_pad t2 on t1.id = t2.pad_id
    JOIN device t3 on t2.device_id = t3.id
    where t1.pad_code in
    <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
      #{padCode}
    </foreach>
    <if test="customerId != null">
      and t1.customer_id = #{customerId}
    </if>
  </select>

  <select id="selectDeviceInfoByDeviceIpDc"
          resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
    select
        t1.device_ip as deviceIp,t1.device_out_code as deviceOutCode,t1.device_code as deviceCode,t1.dc_id as dcId, t1.soc_model as socModel,t2.customer_id as customerId, t3.cluster_public_ip as clusterPublicIp,
           t1.pad_allocation_status as padAllocationStatus
    from device t1
    left join customer_device t2 on t1.id = t2.device_id  and t2.delete_flag = 0
    left join arm_server arm on arm.arm_server_code = t1.arm_server_code
    left join edge_cluster t3 on t3.cluster_code = arm.cluster_code and t3.delete_flag = 0
    where t1.device_ip in
    <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
      #{deviceIp}
    </foreach>
    <if test="dcId != null">
      and t1.dc_id = #{dcId}
    </if>
    <if test="customerId != null">
      and t2.customer_id = #{customerId}
    </if>
    and t1.delete_flag = 0
  </select>

    <select id="selectListVirtualizeInfo"
                        resultType="net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO">
  SELECT
  t1.id as deviceId,
  t1.device_code  as deviceCode,
  t1.device_ip  as deviceIp,
  t1.arm_server_code as armServerCode,
  t2.id as armServerId,
  t2.mac_vlan as macVlan,
  t3.cluster_code as clusterCode,
  t3.dc_code as dcCode,
  t3.cluster_public_ip as clusterPublicIp,
  t6.netmask as subnet,
  t6.ip_range as ipRange,
  t6.gateway as gateway,
  t4.cpu,
  t4.memory,
  t4.storage,
  t4.host_storage_size as hostStorageSize,
  t5.customer_id as customerId
  FROM
  device t1
  LEFT JOIN arm_server t2 on t2.arm_server_code = t1.arm_server_code
  LEFT JOIN edge_cluster t3 on t3.cluster_code = t2.cluster_code
  LEFT JOIN soc_model t4 on t4.model = t1.soc_model
  LEFT JOIN customer_device t5 on t5.device_id = t1.id and t5.delete_flag = 0
  LEFT JOIN getaway_pad t6 on t6.id = t2.gateway_pad_id
  WHERE t1.device_ip in
  <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
    #{deviceIp}
  </foreach>
  AND t1.delete_flag = 0
  AND t1.pad_allocation_status in ('0','-1')
  and t1.device_status = 1
  and t3.delete_flag = 0
  and t2.delete_flag = 0
  and t4.delete_flag = 0
</select>

    <select id="selectCanDeviceByDeviceIp" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceDestroyVO">
      SELECT
        t1.id as deviceId,
        t1.device_ip  as deviceIp,
        t1.device_code  as deviceCode,
        t3.cluster_code as clusterCode,
        t3.dc_code as dcCode,
        t3.cluster_public_ip as clusterPublicIp,
        t2.mac_vlan as macVlan
      FROM
        device t1
      LEFT JOIN arm_server t2 on t2.arm_server_code = t1.arm_server_code and t2.delete_flag = 0
      LEFT JOIN edge_cluster t3 on t3.cluster_code = t2.cluster_code and t3.delete_flag = 0
      WHERE t1.device_ip in
      <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
        #{deviceIp}
      </foreach>
      AND t1.delete_flag = 0
      AND t1.pad_allocation_status in (0,2,-2,-1)
      and t1.device_status = 1
    </select>



  <select id="selectListVirtualizeInfoByNetStorageRes"
          resultType="net.armcloud.paascenter.common.client.internal.vo.VirtualizeDeviceInfoVO">
    SELECT
    t1.id as deviceId,
    t1.device_code  as deviceCode,
    t1.device_ip  as deviceIp,
    t1.arm_server_code as armServerCode,
    t2.id as armServerId,
    t2.mac_vlan as macVlan,
    t3.cluster_code as clusterCode,
    t3.dc_code as dcCode,
    t3.cluster_public_ip as clusterPublicIp,
    t6.netmask as subnet,
    t6.ip_range as ipRange,
    t6.gateway as gateway,
    t4.cpu,
    t4.memory,
    t4.storage,
    t4.host_storage_size as hostStorageSize,
    t5.customer_id as customerId
    FROM
    device t1
    LEFT JOIN arm_server t2 on t2.arm_server_code = t1.arm_server_code
    LEFT JOIN edge_cluster t3 on t3.cluster_code = t2.cluster_code
    LEFT JOIN soc_model t4 on t4.model = t1.soc_model
    LEFT JOIN customer_device t5 on t5.device_id = t1.id and t5.delete_flag = 0
    LEFT JOIN getaway_pad t6 on t6.id = t2.gateway_pad_id
    WHERE
    t1.delete_flag = 0
    and t1.device_status = 1
    and t3.delete_flag = 0
    and t2.delete_flag = 0
    and t4.delete_flag = 0
    <if test="deviceId != null">
      and t1.id = #{deviceId}
    </if>
   limit 1;
  </select>
    <select id="getDeviceCustomerByDeviceIp" resultType="net.armcloud.paascenter.common.model.vo.api.DeviceCustomerVo">
      SELECT
        t1.device_code,
        t1.device_status,
        t2.customer_id,
        t1.cbs_info,
        t1.id
      FROM
        device t1
      LEFT JOIN customer_device t2 on t1.id = t2.device_id and t2.delete_flag = 0
      left join arm_server arm on arm.arm_server_code = t1.arm_server_code
      where device_ip = #{deviceIp}
        <if test="clusterCode != null and clusterCode != ''">
        and arm.cluster_code = #{clusterCode}
        </if>
        and t1.delete_flag = 0
    </select>

    <select id="queryDeviceCustomer" resultType="net.armcloud.paascenter.common.model.vo.api.DeviceCustomerVo">
      select
            t1.device_code as deviceCode,
            t2.customer_id as customerId
      from device t1
      join customer_device t2 on t1.id = t2.device_id
      where t1.device_code in
      <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
        #{deviceCode}
      </foreach>
      and t2.delete_flag = 0
    </select>

  <select id="listByIps" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from device
    where delete_flag = false
      and device_ip in
    <foreach collection="ips" item="ip" open="(" separator="," close=")">
      #{ip}
    </foreach>
  </select>

  <select id="selectDeviceInfoByDeviceCodeDc"
          resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
    select
    t1.device_ip as deviceIp,t1.device_out_code as deviceOutCode,t1.device_code as deviceCode,t1.dc_id as dcId, t1.soc_model as socModel,t2.customer_id as customerId, t3.cluster_public_ip as clusterPublicIp,
    t1.pad_allocation_status as padAllocationStatus
    from device t1
    left join customer_device t2 on t1.id = t2.device_id  and t2.delete_flag = 0
    left join arm_server arm on arm.arm_server_code = t1.arm_server_code
    left join edge_cluster t3 on t3.cluster_code = arm.cluster_code and t3.delete_flag = 0
    where t1.device_code in
    <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
      #{deviceCode}
    </foreach>
    <if test="dcId != null">
      and t1.dc_id = #{dcId}
    </if>
    <if test="customerId != null">
      and t2.customer_id = #{customerId}
    </if>
    and t1.delete_flag = 0
  </select>

  <select id="listByDeviceCodes"
          resultType="net.armcloud.paascenter.common.model.entity.paas.Device">
    select t1.*, t3.dns as  deviceDns ,t3.netmask as deviceNetmask
    from device t1
    left join arm_server t2 on t1.arm_server_code = t2.arm_server_code  and t2.delete_flag = 0
    left join gateway_device t3 on t3.id = t2.gateway_device_id and t3.delete_flag = 0
    where t1.device_code in
    <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
      #{deviceCode}
    </foreach>
    and t1.delete_flag = 0
  </select>
    <select id="getDeviceMountVersionV1" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
      SELECT
        a.device_ip,
        ec.cluster_public_ip,
        p.pad_code
      FROM
        device a
          inner join device_pad dp on a.id = dp.device_id
          inner join pad p on p.id = dp.pad_id
          LEFT JOIN arm_server c ON a.arm_server_code = c.arm_server_code and c.delete_flag
          LEFT JOIN edge_cluster ec ON a.cluster_code = ec.cluster_code and ec.delete_flag = 0

          LEFT JOIN device_change_info b ON p.pad_code = b.pad_code
      WHERE
            a.delete_flag = 0
          AND (b.mount_version IS NULL
         OR b.mount_version != '2')
    </select>

  <select id="getDeviceInfos" resultType="net.armcloud.paascenter.openapi.model.vo.DeviceItemVO">
    select
    ase.arm_ip, de.device_out_code, de.node_id, de.position, de.device_status, de.device_code, de.device_ip, gde.netmask, gde.gateway, gde.dns, de.mac_address
    from device de
    left join arm_server ase on ase.arm_server_code = de.arm_server_code
    left join gateway_device gde on ase.gateway_device_id = gde.id
    where de.delete_flag = 0
    and de.arm_server_code = #{armServiceCode}
    <if test="nodeId != null ">
      and de.node_id = #{nodeId}
    </if>
    <if test="deviceStatus != null ">
      and de.device_status = #{deviceStatus}
    </if>
  </select>
  <insert id="saveDevice" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.Device" useGeneratedKeys="true">
    insert into device
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dcId != null">
        dc_id,
      </if>
      <if test="deviceLevel != null">
        device_level,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="deviceOutCode != null">
        device_out_code,
      </if>
      <if test="cloudVendorType != null">
        cloud_vendor_type,
      </if>
      <if test="deviceStatus != null">
        device_status,
      </if>
      <if test="deviceIp != null">
        device_ip,
      </if>
      <if test="idc != null">
        idc,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="armServerCode != null">
        arm_server_code,
      </if>
      <if test="socModel != null">
        soc_model,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
      <if test="padAllocationStatus != null">
        pad_allocation_status,
      </if>
      <if test="macAddress != null">
        mac_address,
      </if>
      <if test="initStatus != null">
        init_status,
      </if>
      <if test="debianSysInfo != null">
        debian_sys_info,
      </if>
      <if test="debianBootInfo != null">
        debian_boot_info,
      </if>
      <if test="cbsInfo != null">
        cbs_info,
      </if>
      <if test="nodeId != null">
        node_id,
      </if>
      <if test="position != null">
        position,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dcId != null">
        #{dcId,jdbcType=BIGINT},
      </if>
      <if test="deviceLevel != null">
        #{deviceLevel,jdbcType=VARCHAR},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="deviceOutCode != null">
        #{deviceOutCode,jdbcType=VARCHAR},
      </if>
      <if test="cloudVendorType != null">
        #{cloudVendorType,jdbcType=INTEGER},
      </if>
      <if test="deviceStatus != null">
        #{deviceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deviceIp != null">
        #{deviceIp,jdbcType=VARCHAR},
      </if>
      <if test="idc != null">
        #{idc,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="armServerCode != null">
        #{armServerCode,jdbcType=BIGINT},
      </if>
      <if test="socModel != null">
        #{socModel,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=INTEGER},
      </if>
      <if test="padAllocationStatus != null">
        #{padAllocationStatus},
      </if>
      <if test="macAddress != null">
        #{macAddress},
      </if>
      <if test="initStatus != null">
        #{initStatus},
      </if>
      <if test="debianSysInfo != null">
        #{debianSysInfo},
      </if>
      <if test="debianBootInfo != null">
        #{debianBootInfo},
      </if>
      <if test="cbsInfo != null">
        #{cbsInfo},
      </if>
      <if test="nodeId != null">
        #{nodeId},
      </if>
      <if test="position != null">
        #{position},
      </if>
    </trim>
  </insert>

  <select id="listByDeviceQueryDTO" resultType="net.armcloud.paascenter.common.client.internal.dto.DeviceQueryDTO">
      SELECT
      a.*
      FROM
      device a
      INNER JOIN ( SELECT DISTINCT device_id FROM device_pad b INNER JOIN pad c ON b.pad_id = c.id WHERE c.customer_id = #{customerId} ) b ON a.id = b.device_id
      inner join arm_server c on a.arm_server_code = c.arm_server_code and c.delete_flag = 0
      WHERE
      a.delete_flag = 0
      <if test="padAllocationStatus != null ">
          and a.pad_allocation_status =  #{padAllocationStatus}
        </if>
        <if test="deviceStatus != null ">
          and a.device_status =  #{deviceStatus}
        </if>
      <if test="armServerStatus != null ">
        and c.online =  #{armServerStatus}
      </if>
        <if test="armServerCode != null and armServerCode != '' ">
          and a.arm_server_code like CONCAT('%',#{armServerCode},'%')
        </if>
      <if test="idc != null and idc != '' ">
        and a.idc = #{idc}
      </if>
      <if test="clusterCode != null and clusterCode != '' ">
        and c.cluster_code like CONCAT('%',#{clusterCode},'%')
      </if>

      <if test="deviceCode != null and deviceCode != '' ">
        and a.device_code like CONCAT('%',#{deviceCode},'%')
      </if>
        <if test="deviceIp != null and deviceIp != '' ">
          and a.device_ip like CONCAT('%',#{deviceIp},'%')
        </if>
        <if test="deviceIpList != null and deviceIpList.size() > 0">
          and a.device_ip in
          <foreach collection="deviceIpList" item="ip" open="(" separator="," close=")">
            #{ip}
          </foreach>
        </if>
         order by id desc
    </select>

  <select id="selectCanDeviceByDeviceIpAndCbsUpdate" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceDestroyVO">
    SELECT
    t1.id as deviceId,
    t1.device_ip  as deviceIp,
    t1.device_code  as deviceCode,
    t3.cluster_code as clusterCode,
    t3.dc_code as dcCode,
    t3.cluster_public_ip as clusterPublicIp
    FROM
    device t1
    LEFT JOIN arm_server t2 on t2.arm_server_code = t1.arm_server_code and t2.delete_flag = 0
    LEFT JOIN edge_cluster t3 on t3.cluster_code = t2.cluster_code and t3.delete_flag = 0
    WHERE t1.device_ip in
    <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
      #{deviceIp}
    </foreach>
    AND t1.delete_flag = 0
    AND t1.pad_allocation_status in (0,2,-2,-1)
    and t1.device_status = 1
  </select>
  <select id="getDeviceInfo" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceVO">
    select distinct device_ip as deviceIp,device_status as status,pad_allocation_status as padAllocationStatus, device_code as deviceCode,b.customer_id
    from device a left join customer_device b on a.id = b.device_id and b.delete_flag = 0
    where  a.device_status = 1 and  device_ip in
    <foreach collection="deviceIps" item="deviceIp" open="(" separator="," close=")">
      #{deviceIp}
    </foreach>
    and a.delete_flag = 0
  </select>
  <select id="getDeviceInfoByCode" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceVO">
    select distinct device_ip as deviceIp,device_status as status,pad_allocation_status as padAllocationStatus, device_code as deviceCode,b.customer_id
    from device a left join customer_device b on a.id = b.device_id and b.delete_flag = 0
    where  a.device_status = 1 and  device_code in
    <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
      #{deviceCode}
    </foreach>
    and a.delete_flag = 0
  </select>
  <select id="selectPadByDeviceCode" resultType="java.lang.String">
      select t3.pad_code
      from device t1
      join device_pad t2 on t2.device_id = t1.id
      join pad t3 on t2.pad_id = t3.id
      where t1.device_code in
      <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
        #{deviceCode}
      </foreach>
      and t1.delete_flag = 0
      and t3.status = 1
  </select>
  <select id="selectTaskByTaskTypeAndTaskStatus" resultType="java.lang.Integer">
    select count(1) from pad_task
    <where>
      delete_flag = 0
      <if test="pads != null and pads.size() > 0">
        and pad_code in
        <foreach collection="pads" item="padCode" open="(" separator="," close=")">
          #{padCode}
        </foreach>
      </if>
      <if test="statuses != null and statuses.size() > 0">
        and status in
        <foreach collection="statuses" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getDeviceInfoByDeviceCode" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
    select
      t1.device_ip as deviceIp,t1.device_out_code as deviceOutCode,t1.device_code as deviceCode,t1.dc_id as dcId, t1.soc_model as socModel,t2.customer_id as customerId, t3.cluster_public_ip as clusterPublicIp,
      arm.id as armServerId, t2.customer_id, t3.cluster_code as clusterCode, t1.device_level
    from device t1
           left join customer_device t2 on t1.id = t2.device_id and t2.delete_flag = 0
           left join arm_server arm on arm.arm_server_code = t1.arm_server_code
           left join edge_cluster t3 on t3.cluster_code = arm.cluster_code and t3.delete_flag = 0
    where t1.device_code = #{deviceCode}
      and t1.delete_flag = 0
      and t2.delete_flag = 0
      and arm.delete_flag = 0
      and t3.delete_flag = 0
  </select>
  <select id="selectInitStatusGroupBy" resultType="net.armcloud.paascenter.common.model.vo.job.DeviceInitStatusVO">
    select
      SUM(CASE WHEN init_status = '1' THEN 1 ELSE 0 END) AS successCount,
      SUM(CASE WHEN init_status = '2' THEN 1 ELSE 0 END) AS initCount,
      SUM(CASE WHEN init_status = '0' THEN 1 ELSE 0 END) AS failCount
    from device
    where arm_server_code = #{armServerCode}
  </select>

  <select id="selectQiShuoNewCard" resultType="net.armcloud.paascenter.common.model.vo.job.QiShuoNewCard">
    select * from  t_import_card_record
  </select>

  <select id="selectDeviceInfos" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
    SELECT
      t1.id as deviceId,
      t1.device_ip AS deviceIp,
      t2.cluster_public_ip AS clusterPublicIp
    FROM
      device t1
        left join arm_server arm on arm.arm_server_code = t1.arm_server_code
        JOIN edge_cluster t2 ON t2.cluster_code = arm.cluster_code AND t2.delete_flag = 0
    where t1.delete_flag = 0
  </select>

  <select id="selectBatchById" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
    SELECT
    d.id as id,
    cd.customer_id as customerId,
    d.device_ip,
    d.device_code
    FROM customer_device cd
    left join device d on d.id=cd.device_id and cd.delete_flag=0
    WHERE d.id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <select id="selectDeviceIpByPadCode" resultType="java.lang.String">
    SELECT d.device_ip
    FROM device d
           LEFT JOIN device_pad dp ON d.id = dp.device_id
           LEFT JOIN pad p ON dp.pad_id = p.id
    WHERE d.delete_flag = 0
      AND p.pad_code = #{padCode}
  </select>

  <select id="allDevice" resultType="net.armcloud.paascenter.common.model.entity.paas.Device">
    SELECT
      device_ip,
      ext_life_time_info
    FROM
      device
    WHERE
      ext_life_time_info IS NOT NULL
  </select>
  <select id="existDeviceCode" resultType="int">
    select count(1) from device
    where device_code = #{deviceCode}
  </select>
  <select id="getCustomer" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceVO">
    select distinct device_ip as deviceIp,device_status as status,pad_allocation_status as padAllocationStatus,
    device_code as deviceCode,b.customer_id,c.customer_name as customerName,a.create_time as createTime
    from device a left join customer_device b on a.id = b.device_id
                  left join customer c on c.id = b.customer_id
    where  a.device_status = 1 and  device_ip  = #{deviceIp}
      and a.delete_flag = 0 and b.delete_flag = 0
    limit 1
  </select>

  <select id="selectDeviceByCustomerId" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceVO">
    SELECT a.* ,
    SUM( case when c.pad_id is null then 0 else 1 end ) AS createPadNumber
    FROM device a inner join customer_device b on a.id = b.device_id   and b.delete_flag = 0  and net_storage_res_flag = 1 and a.delete_flag = 0
    LEFT JOIN device_pad c on a.id = c.device_id
    <where>
      <if test="customerId != null">
        AND  b.customer_id = #{customerId}
      </if>
      <if test="deviceLevel != null and deviceLevel!= ''">
        AND  a.device_level = #{deviceLevel}
      </if>
    </where>
    group by a.id
  </select>
    <!-- 查询设备信息，符合特定条件 -->
    <select id="netDeviceLevelList" resultType="net.armcloud.paascenter.common.model.entity.paas.Device">
      SELECT
      a.*
      FROM
      device a
      INNER JOIN customer_device d ON a.id = d.device_id
      INNER JOIN arm_server b ON a.arm_server_code = b.arm_server_code
      AND b.delete_flag = 0
      INNER JOIN edge_cluster c ON c.cluster_code = b.cluster_code
      AND c.delete_flag = 0
      WHERE
      a.delete_flag = 0
      AND a.net_storage_res_flag = 1 AND d.customer_id = #{customerId}
      <if test="deviceCodes != null and deviceCodes.size() > 0">
        AND a.device_code IN
        <foreach collection="deviceCodes" item="deviceCode" open="(" separator="," close=")">
          #{deviceCode}
        </foreach>
      </if>
      <if test="deviceLevel != null and deviceLevel != ''">
        AND a.device_level = #{deviceLevel}
      </if>
      <if test="clusterCode != null and clusterCode != ''">
        AND c.cluster_code = #{clusterCode}
      </if>
      <if test="armServerCode != null and armServerCode != ''">
        AND a.arm_server_code = #{armServerCode}
      </if>
    </select>
  <select id="getClusterCodeByIp" resultType="java.lang.String">
    SELECT b.cluster_code FROM device a inner join arm_server b on a.arm_server_code = b.arm_server_code
    where a.device_ip = #{deviceIp} and b.delete_flag = 0 and a.delete_flag = 0 limit 1
  </select>
  <select id="selectListByCustomerId" resultType="net.armcloud.paascenter.common.model.entity.paas.Device">
    SELECT d.* from device d inner join customer_device c on d.id = c.device_id where c.delete_flag = 0 and d.delete_flag =0 and d.net_storage_res_flag = 1 and c.customer_id = #{customerId}
  </select>

  <select id="selectDeviceCbsInfoByPadCode" resultType="java.lang.String">
    SELECT d.cbs_info
    FROM device d
    LEFT JOIN device_pad dp ON d.id = dp.device_id
    LEFT JOIN pad p ON dp.pad_id = p.id
    WHERE d.delete_flag = 0
    AND p.pad_code = #{padCode}
  </select>

  <select id="getArmServerIdByDeviceCode" resultType="net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo">
    SELECT arm.id armServerId, d.*
    FROM device d
    INNER JOIN arm_server arm ON d.arm_server_code = arm.arm_server_code
    WHERE d.delete_flag = 0
    AND d.device_code = #{deviceCode}
  </select>

  <select id="getArmServerIdByDeviceIp" resultType="java.lang.Long">
    SELECT arm.id
    FROM device d
    INNER JOIN arm_server arm ON d.arm_server_code = arm.arm_server_code
    WHERE d.delete_flag = 0
    AND d.device_ip = #{deviceIp}
    LIMIT 1
  </select>

  <select id="getDeviceIpsByArmServerId" resultType="java.lang.String">
    SELECT d.device_ip
    FROM device d
    INNER JOIN arm_server arm ON d.arm_server_code = arm.arm_server_code
    WHERE d.delete_flag = 0
    AND arm.id = #{armServerId}
    AND d.device_ip IS NOT NULL
  </select>

  <select id="getDeviceModelInfo" resultType="net.armcloud.paascenter.openapi.netpadv2.service.impl.DeviceModelInfoDTO">
    SELECT
      d.device_code,
      d.device_ip,
      d.device_level,
      d.id deviceId,
      sm.cpu,
      sm.memory,
      sm.storage,
      sm.host_storage_size
    FROM
      device d
      left join soc_model sm on d.soc_model = sm.model
    WHERE
      d.delete_flag = 0
    AND d.device_code in
    <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
      #{deviceCode}
    </foreach>
  </select>
</mapper>