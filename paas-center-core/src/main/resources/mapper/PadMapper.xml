<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.PadMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.Pad">
        <id column="id" property="id" />
        <result column="pad_code" property="padCode" />
        <result column="pad_out_code" property="padOutCode" />
        <result column="device_level" property="deviceLevel" />
        <result column="pad_ip" property="padIp" />
        <result column="pad_sn" property="padSn" />
        <result column="online" property="online" />
        <result column="image_id" property="imageId" />
        <result column="cloud_vendor_type" property="cloudVendorType" />
        <result column="customer_id" property="customerId" />
        <result column="status" property="status" />
        <result column="adi_certificate_id" property="adiCertificateId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="net_storage_res_id" property="netStorageResId" />
        <result column="real_phone_template_id" property="realPhoneTemplateId" />
    </resultMap>
    <resultMap id="consoleDcInfoVo" type="net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO">
        <id column="id" property="id" />
        <result column="area" property="area" />
        <result column="dc_code" property="dcCode" />
        <result column="dc_name" property="dcName" />
        <result column="pad_code" property="padCode" />
    </resultMap>

    <sql id="Base_Column_List">
        id, pad_code, pad_out_code, device_level, pad_ip, pad_sn, image_id, cloud_vendor_type,`online`,
        customer_id, `status`, adi_certificate_id, create_by, create_time, update_by, update_time,net_storage_res_flag,
        net_storage_res_id
    </sql>
    <sql id="consoleDcInfoVo">
        dc.id,
        dc.dc_code,
        dc.dc_name,
        dc.area,
        p.pad_code
    </sql>

    <update id="deletePadByCodes">
        update pad
        set `status` = -1, customer_id = null, update_time = now()
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </update>

    <select id="selectDetailsByPadCode"  resultType="net.armcloud.paascenter.openapi.model.vo.PadDetailsVO">
        SELECT pp.pad_code,
        IFNULL(cui.unique_id, pp.image_id) as image_id,
        pp.device_level,
        pp.stream_status,
        pps.pad_status,
        pp.online,
        pd.device_status,
        pd.dc_id,
        pd.device_ip,
        pp.data_size,
        pp.data_size_used,
        pp.adb_open_status,
        pp.screen_layout_code,
        pp.mac,
        pp.cpu,
        pp.net_storage_res_id,
        pp.id as PadId,
        pp.net_storage_res_size as netStorageResSize,
        pp.net_storage_res_flag as netStorageResFlag,
        pp.customer_id as customerId,
        pp.customer_id as customerId,
        pp.real_phone_template_id as realPhoneTemplateId,
        pp.dns,
        COALESCE(NULLIF(arm.cluster_code, ''), pp.cluster_code) AS cluster_code,
        arm.arm_ip as armIp,
        arm.id as armServerId,
        pp.type,
        pp.country_code,
        pd.cbs_info
        FROM pad pp
        left join pad_status pps on pp.pad_code = pps.pad_code
        left JOIN device_pad pdp on pp.id = pdp.pad_id
        left join device pd on pd.id = pdp.device_id
        left join dc_info di on di.id=pd.dc_id
        left join customer_upload_image cui on cui.image_name=pp.image_id
        left join arm_server arm on arm.arm_server_code = pp.arm_server_code
        <where>

            <!-- 允许admin查所有实例 -->
            <if test="customerId != null and customerId >0">
                and pp.customer_id = #{customerId}
            </if>

            <!-- 按状态筛选 -->
            <if test="status != null">
                and pp.status = #{status}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and pp.pad_code in
                <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>
            <if test="padIps != null and padIps.size() > 0">
                and pp.pad_ip in
                <foreach collection="padIps" item="padIp" open="(" separator="," close=")">
                    #{padIp}
                </foreach>
            </if>
            <if test="vmStatus != null and vmStatus != ''">
                and pp.online = #{vmStatus}
            </if>
            <if test="controlStatus != null and controlStatus != ''">
                and pp.stream_status = #{controlStatus}
            </if>
            <if test="faultStatus != null and faultStatus != ''">
                and pps.pad_status = #{faultStatus}
            </if>
            <if test="deviceStatus != null and deviceStatus != ''">
                and pd.device_status = #{deviceStatus}
            </if>
            <if test="groupId != null and groupId != ''">
                and pp.group_id = #{groupId}
            </if>
            <if test="idcCode != null and idcCode != ''">
                and di.dc_code = #{idcCode}

            </if> </where>
    </select>

    <update id="updateOnline">
        update pad
        set online             = #{online},
            <if test="imageId != null and imageId != ''">
                 image_id = #{imageId},
            </if>
            <if test="dataSizeUsed != null and dataSizeUsed != ''">
                data_size_used = #{dataSizeUsed},
            </if>
            <if test="dataSize != null and dataSize != ''">
                data_size = #{dataSize},
            </if>
            <if test="dataSizeAvailable != null and dataSizeAvailable != ''">
                data_size_available = #{dataSizeAvailable},
            </if>
            <if test="rtcVersionName != null and rtcVersionName != ''">
                rtc_version_name = #{rtcVersionName},
            </if>
            <if test="rtcVersionCode != null and rtcVersionCode != ''">
                rtc_version_code = #{rtcVersionCode},
            </if>
        <if test="adbEnable != null and adbEnable != ''">
            adb_open_status = #{adbEnable},
        </if>
            disconnection_time = now()
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </update>

    <update id="removePadByDeviceIp">
        UPDATE pad
        SET status = -1, pad_ip = null, customer_id = null, update_time = now(),data_size_available = null, data_size_used= null, data_size= null, stream_type = DEFAULT(stream_type), mac = DEFAULT(mac)
        WHERE pad.status != -1
        AND pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </update>

    <update id="enablePadByDeviceIp">
        UPDATE pad
        SET status = 1, update_time = now()
        <if test="customerId != null and customerId != ''">
            , customer_id = #{customerId}
        </if>
        WHERE pad.status = 0
        AND EXISTS (
            SELECT 1
            FROM device t1
                     JOIN device_pad t2 ON t2.device_id = t1.id
            WHERE t1.device_ip = #{deviceIp}
              AND t1.delete_flag = 0
              AND t2.pad_id = pad.id
        )
    </update>

    <select id="getByCloudVendorTypeAndPadOutCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad
        where cloud_vendor_type = #{cloudVendorType}
        and pad_out_code = #{padOutCode}
    </select>


    <select id="selectPadListVO" resultType="net.armcloud.paascenter.openapi.model.vo.PadListVO">
        SELECT p.id,
               p.pad_code     as padCode,
               p.device_level as padGrade,
               ps.pad_status  as padStatus,
               p.group_id     as groupId,
               pd.idc         as idc,
               pd.idc         as idcCode,
               pd.device_ip   as deviceIp,
               p.pad_ip       as padIp,
               p.type         as padType,
               p.image_id     as imageId,
                p.adb_open_status as adbOpenStatus,
               pd.device_code as deviceCode,
               pd.arm_server_code armServerCode,
               arm.cluster_code as clusterCode,
               p.type as padType,
               p.net_storage_res_flag as netStorageResFlag
        from pad p
        left join pad_status ps on p.pad_code = ps.pad_code
        left join device_pad pdp on p.id = pdp.pad_id
        LEFT JOIN device pd on pd.id = pdp.device_id
        left join arm_server arm on arm.arm_server_code = p.arm_server_code
        where p.status = 1 and p.customer_id = #{customerId}

        <if test="padCodes != null and padCodes.size() > 0">
            and p.pad_code in
            <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
        <if test="netStorageResFlag != null ">
            and p.net_storage_res_flag = #{netStorageResFlag}
        </if>

        <if test="armServerCode != null and armServerCode != ''">
            and pd.arm_server_code = #{armServerCode}
        </if>

        <if test="deviceCode != null and deviceCode != ''">
            and pd.device_code = #{deviceCode}
        </if>

        <if test="deviceCode != null and deviceCode != ''">
            and pd.device_code = #{deviceCode}
        </if>

        <if test="idc != null and idc != ''">
            and pd.idc = #{idc}
        </if>
        <if test="padType != null and padType != ''">
            and p.type = #{padType}
        </if>

        <if test="clusterCode != null and clusterCode != ''">
            and arm.cluster_code = #{clusterCode}
        </if>

        <if test="groupIds != null and groupIds.size() > 0">
            and p.group_id in
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        ORDER BY p.id ASC
    </select>

    <select id="selectPadListVOOptimized" resultType="net.armcloud.paascenter.openapi.model.vo.PadListVO">
        SELECT p.id,
               p.pad_code     as padCode,
               p.device_level as padGrade,
               ps.pad_status  as padStatus,
               p.group_id     as groupId,
               pd.idc         as idc,
               pd.idc         as idcCode,
               pd.device_ip   as deviceIp,
               p.pad_ip       as padIp,
               p.type         as padType,
               p.image_id     as imageId,
               p.adb_open_status as adbOpenStatus,
               pd.device_code as deviceCode,
               pd.arm_server_code armServerCode,
               arm.cluster_code as clusterCode,
               p.type as padType,
               p.net_storage_res_flag as netStorageResFlag,
            p.image_id as imageId,
        arm.arm_ip as armIp
        from pad p
        left join pad_status ps on p.pad_code = ps.pad_code
        left join device_pad pdp on p.id = pdp.pad_id
        LEFT JOIN device pd on pd.id = pdp.device_id
        left join arm_server arm on arm.arm_server_code = p.arm_server_code
        where p.status = 1 and p.customer_id = #{customerId}

        <if test="lastId != null and lastId > 0">
            and p.id > #{lastId}
        </if>

        <if test="padCodes != null and padCodes.size() > 0">
            and p.pad_code in
            <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
        <if test="netStorageResFlag != null ">
            and p.net_storage_res_flag = #{netStorageResFlag}
        </if>

        <if test="armServerCode != null and armServerCode != ''">
            and pd.arm_server_code = #{armServerCode}
        </if>

        <if test="deviceCode != null and deviceCode != ''">
            and pd.device_code = #{deviceCode}
        </if>

        <if test="deviceCode != null and deviceCode != ''">
            and pd.device_code = #{deviceCode}
        </if>

        <if test="idc != null and idc != ''">
            and pd.idc = #{idc}
        </if>
        <if test="padType != null and padType != ''">
            and p.type = #{padType}
        </if>

        <if test="clusterCode != null and clusterCode != ''">
            and arm.cluster_code = #{clusterCode}
        </if>

        <if test="groupIds != null and groupIds.size() > 0">
            and p.group_id in
            <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        ORDER BY p.id ASC
        LIMIT #{rows}
    </select>

    <select id="selectPadInfoByCode" parameterType="java.lang.String"
            resultType="net.armcloud.paascenter.common.client.internal.vo.PadInfoVO">
        SELECT pp.customer_id as customerId,
               pp.pad_code as padCode,
               pps.pad_status as padStatus,
               pp.online,
               pp.stream_type as streamType
        FROM pad pp
                 left join pad_status pps on pp.pad_code = pps.pad_code
        where pp.pad_code = #{padCode}
        limit 1;
    </select>


    <select id="padIdleListVO" resultType="net.armcloud.paascenter.openapi.model.vo.PadIdleListVO">
        SELECT  pad_code,min(type) as type from idle_pad_palmcloud WHERE DATE(create_time) = CURDATE() GROUP BY pad_code
    </select>

    <select id="getDcIdByPadOutCode" resultType="java.lang.Integer">
        SELECT
            t3.dc_id
        FROM
            pad t1
        JOIN device_pad	t2 on t2.pad_id = t1.id
        JOIN device t3 on t3.id = t2.device_id
        WHERE
            pad_out_code = #{padOutCode}
    </select>

    <select id="listAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad
    </select>
    <select id="getPadCodesByGroupIds" resultType="java.lang.String">
        select pad_code from pad p
        <where>
            <if test="customerId != null and customerId != '' and customerId != 0">
                p.customer_id = #{customerId}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and p.pad_code in
                <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>
            <if test="groupIds != null and groupIds.size() > 0">
                and p.group_id in
                <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                    #{groupId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getDcIdGroupByPadCodes" resultType="net.armcloud.paascenter.common.model.vo.console.ConsoleDcInfoVO">
        select
        <include refid="consoleDcInfoVo"/>
        from pad p
        left join device_pad dp on p.id = dp.pad_id
        left join device d on d.id = dp.device_id
        left join dc_info dc on d.dc_id = dc.id and dc.delete_flag = 0
        where
        <if test="padCodes != null and padCodes.size() > 0">
            p.pad_code in
            <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                #{padCode}
            </foreach>
        </if>
    </select>

    <select id="selectPadInfosByDeviceId" resultType="net.armcloud.paascenter.common.client.internal.vo.PadInfoVO">
        SELECT
            t2.id as padId,
            t2.pad_code as padCode,
            t2.pad_ip as padIp,
            t2.pad_sn as padSn,
            t2.status
        FROM
            device_pad t1
        JOIN pad t2 on t2.id =t1.pad_id
        where t1.device_id = #{deviceId}
        Order by t2.pad_sn
        <if test="padNumber != null">
            limit #{padNumber}
        </if>
    </select>
    <select id="selectUsePadIps" resultType="java.lang.String">
        select pad_ip
        from pad
        where pad_ip in
        <foreach collection="ips" item="padIp" open="(" separator="," close=")">
            #{padIp}
        </foreach>
        and status != -1
    </select>


    <select id="selectResetByPadCodesAndCode" resultType="net.armcloud.paascenter.cms.model.request.InstanceResetRequest">
        SELECT
        t5.device_ip,
        t1.pad_code
        FROM
        pad t1
        LEFT JOIN device_pad t4 ON t1.id = t4.pad_id
        LEFT JOIN device t5 ON t4.device_id = t5.id
        LEFT JOIN arm_server t2 ON t1.arm_server_code = t2.arm_server_code and t2.delete_flag = 0
        LEFT JOIN edge_cluster t3 ON t3.cluster_code = t2.cluster_code and t3.delete_flag = 0
        WHERE
        pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        AND t3.cluster_code = #{clusterCode}

    </select>

    <select id="queryRemovePadsByDeviceIp" resultType="java.lang.String">
        SELECT pad_code
        FROM pad
        WHERE pad.status != -1
        AND EXISTS (
            SELECT 1
            FROM device t1
                     JOIN device_pad t2 ON t2.device_id = t1.id
            WHERE t1.device_ip = #{deviceIp}
          AND t1.delete_flag = 0
          AND t2.pad_id = pad.id
            )
    </select>


    <select id="queryPadCustomerId" resultType="net.armcloud.paascenter.common.client.internal.vo.PadInfoVO">
        SELECT
               pad_code as padCode,
               customer_id as customerId
        FROM pad
        WHERE
        pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        and status != -1
        and customer_id is not null
    </select>

    <select id="getPadCodesByDeviceLevel" resultType="java.lang.String">
        select pad_code
        from pad p
        <where>
            p.customer_id = #{customerId}
            <if test="padCodes != null and padCodes.size() > 0">
                and p.pad_code in
                <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>
            <if test="padGrade != null">
                and p.device_level = #{padGrade}
            </if>
            and p.status != -1
        </where>
    </select>

    <select id="countByPadIpAndCustomerId" resultType="int">
        select count(*)
        from pad
        where pad_ip = #{padIp}
          and status = 1
          and customer_id = #{customerId}
        limit 1
    </select>
    <select id="selectPadByPadCustomerDTO" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            pad
        where
            pad_code = #{padCode}
            <if test="customerId != null and customerId != 0">
                and customer_id = #{customerId}
            </if>
    </select>
    <select id="selectPadByDeviceCode" resultMap="BaseResultMap">
        select
        p.id,  p.pad_code,  p.pad_out_code,  p.device_level,  p.pad_ip,  p.pad_sn,  p.image_id,  p.cloud_vendor_type, p.`online`,
        p.customer_id,  p.`status`,  p.create_by,  p.create_time,  p.update_by,  p.update_time
        from pad p
        inner join device_pad dp on p.id = dp.pad_id and p.status in (1,0)
        inner join device d on d.id = dp.device_id
        where d.device_code in
        <foreach collection="deviceInfos" item="deviceInfo" open="(" separator="," close=")">
            #{deviceInfo.deviceCode}
        </foreach>
        <if test="status != null">
            and p.status in
            <foreach collection="status" item="stu" open="(" separator="," close=")">
                #{stu}
            </foreach>
        </if>

    </select>

    <select id="getPadIpsByDeviceId" resultType="java.lang.String">
        SELECT
           distinct pad_ip
        FROM
            pad
        WHERE
            EXISTS ( SELECT 1 FROM device_pad t2 WHERE t2.device_id = #{deviceId} AND t2.pad_id = pad.id )
          AND pad_ip IS NOT NULL
    </select>

    <select id="listStreamTypeByPadCodes" resultType="net.armcloud.paascenter.openapi.model.vo.GetStreamTypeVO">
        select pad_code    as padCode,
               stream_type as streamType
        from pad
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="listEdgeClusterInfosByPadCodes" resultType="net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO">
        SELECT
        t1.id as padId,
        t1.pad_code as padCode,
        t1.pad_ip as padIp,
        t1.customer_id as customerId,
        t1.online as online,
        t1.pad_out_code as padOutCode,
        t1.soc_model as socModel,
        t5.device_ip as deviceIp,
        t3.cluster_code as clusterCode,
        t3.cluster_name as clusterName,
        t3.cluster_public_ip as clusterPublicIp
        FROM
        pad t1
        LEFT JOIN device_pad t4 ON t1.id = t4.pad_id
        LEFT JOIN device t5 ON t4.device_id = t5.id
        LEFT JOIN arm_server t2 ON t2.arm_server_code = t1.arm_server_code
        LEFT JOIN edge_cluster t3 ON t3.cluster_code = t2.cluster_code and t3.delete_flag = 0
        WHERE t1.pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <update id="updateMacById">
        UPDATE pad p1
            LEFT JOIN pad p2 ON p1.mac = #{mac} AND p1.id != p2.id
            SET p1.mac = #{mac}
        WHERE p1.id = #{id}
          AND p2.id IS NULL;
    </update>

    <select id="listPadEdgeClusterInfo" resultType="net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO">
        SELECT
        p.id as padId,
        p.pad_code as padCode,
        p.pad_ip as padIp,
        p.customer_id as customerId,
        p.online as online,
        p.pad_out_code as padOutCode,
        p.soc_model as socModel,
        d.device_ip as deviceIp,
        ec.cluster_code as clusterCode,
        ec.cluster_name as clusterName,
        ec.cluster_public_ip as clusterPublicIp
        FROM
        pad p
        LEFT JOIN device_pad dp ON p.id = dp.pad_id
        LEFT JOIN device d ON dp.device_id = d.id
        LEFT JOIN arm_server arm ON p.arm_server_code = arm.arm_server_code
        LEFT JOIN edge_cluster ec ON ec.cluster_code = arm.cluster_code and ec.delete_flag = 0
        WHERE p.pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        and p.status != -1
    </select>

    <update id="updateTypeByIds">
        update pad
        set type = #{type}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateAdiCertificateIdById">
        update pad
        set adi_certificate_id = #{adiCertificateId}
        where id = #{id}
    </update>
    <update id="cancelAdiCertificate">
        update pad
        set adi_certificate_id = 0
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </update>

    <update id="updateScreenLayoutCodeByIds">
        update pad
        set screen_layout_code = #{screenLayoutCode}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updatePadLayoutCode">
        update pad set screen_layout_code = #{screenLayoutCode} where pad_code = #{padCode}
    </update>
    <update id="updatePadDns">
        update pad set dns = #{dns} where pad_code = #{padCode}
    </update>

    <select id="listByPadCodes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pad
        where pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="getPadEdgeClusterInfosByPadCodes"
            resultType="net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO">
        SELECT
        t1.id as padId,
        t1.pad_code as padCode,
        t1.pad_ip as padIp,
        t1.customer_id as customerId,
        t1.online as online,
        t1.pad_out_code as padOutCode,
        t1.soc_model as socModel,
        t5.device_ip as deviceIp,
        t3.cluster_code as clusterCode,
        t3.cluster_name as clusterName,
        t3.cluster_public_ip as clusterPublicIp
        FROM
        pad t1
        LEFT JOIN device_pad t4 ON t1.id = t4.pad_id
        LEFT JOIN device t5 ON t4.device_id = t5.id
        LEFT JOIN arm_server arm ON t5.arm_server_code = arm.arm_server_code
        LEFT JOIN edge_cluster t3 ON arm.cluster_code = t1.cluster_code and t3.delete_flag = 0
        WHERE t1.pad_code IN
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
        and t1.status != -1
    </select>

    <select id="selectValidPadCodeByCustomerId" resultType="net.armcloud.paascenter.common.client.internal.vo.PadInfoVO">
        select pad_code as padCode,device_level as deviceLevel from pad where customer_id = #{customerId} and status in (0,1)
    </select>
    <select id="selectPadInfoByCodeList" resultType="net.armcloud.paascenter.common.client.internal.vo.PadInfoVO">
        SELECT pp.customer_id as customerId,
               pp.pad_code as padCode,
               pps.pad_status as padStatus,
               pp.online,
               pp.stream_type as streamType
        FROM pad pp
                 left join pad_status pps on pp.pad_code = pps.pad_code
        where pp.pad_code in
        <foreach collection="padCodeList" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>
    <select id="getPadByGroupIds" resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select p.* from pad p
        <where>
            <if test="customerId != null and customerId != '' and customerId != 0">
                p.customer_id = #{customerId}
            </if>
            <if test="padCodes != null and padCodes.size() > 0">
                and p.pad_code in
                <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
                    #{padCode}
                </foreach>
            </if>
            <if test="groupIds != null and groupIds.size() > 0">
                and p.group_id in
                <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
                    #{groupId}
                </foreach>
            </if>
        </where>

    </select>

    <select id="getByPadCode" resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select <include refid="Base_Column_List"/>
        from pad
        where pad_code = #{padCode}
        and status != -1
    </select>

    <select id="getPadEdgeClusterInfoByPadCode"
            resultType="net.armcloud.paascenter.common.client.internal.vo.PadEdgeClusterVO">
        SELECT
            t1.id as padId,
            t1.pad_code as padCode,
            t1.pad_ip as padIp,
            t1.customer_id as customerId,
            t1.online as online,
            t1.pad_out_code as padOutCode,
            t1.soc_model as socModel,
            t5.device_ip as deviceIp,
            t3.cluster_code as clusterCode,
            t3.cluster_name as clusterName,
            t3.cluster_public_ip as clusterPublicIp
        FROM
            pad t1
                LEFT JOIN device_pad t4 ON t1.id = t4.pad_id
                LEFT JOIN device t5 ON t4.device_id = t5.id
                LEFT JOIN arm_server t2 ON t1.arm_server_code = t2.arm_server_code and t2.delete_flag = 0
                LEFT JOIN edge_cluster t3 ON t3.cluster_code = t2.cluster_code and t3.delete_flag = 0
        WHERE t1.pad_code = #{padCode}
          and t1.status != -1
    </select>
    <select id="existPadByCode" parameterType="java.lang.String" resultType="int">
        select count(*)
        from pad
        where pad_code = #{padCode}
    </select>
    <select id="selectPadByDeviceCodeList" resultType="net.armcloud.paascenter.common.client.internal.vo.PadInfoVO">
        select
        p.id,  p.pad_code,  ps.pad_status as padStatus
        from pad p
        inner join device_pad dp on p.id = dp.pad_id and p.status in (1,0)
        inner join device d on d.id = dp.device_id
        inner join pad_status ps on p.pad_code = ps.pad_code
        where
        p.status = 1
        and d.device_code in
        <foreach collection="deviceCodeList" item="deviceCode" open="(" separator="," close=")">
            #{deviceCode}
        </foreach>
 </select>


    <update id="updateAdiCertificateIdByPadCode">
        update pad
        set adi_certificate_id = #{adiCertificateId}
        where pad_code = #{padCode}
    </update>


    <select id="selectPadAndDeviceInfo" resultType="net.armcloud.paascenter.openapi.model.vo.PadAndDeviceInfoVO">
        select p.pad_code,d.device_ip,arm.cluster_code,p.pad_ip
        from pad as p
        left join device_pad as dp on p.id = dp.pad_id
        left join device as d on dp.device_id = d.id
        left join arm_server arm on arm.arm_server_code = p.arm_server_code
        where p.pad_code = #{padCode} and d.delete_flag = 0;
    </select>

    <select id="getPadCodeByOfflineDevice" resultType="java.lang.String">
        select p.pad_code from pad p
        left join device_pad dp on dp.pad_id = p.id
        left join device d on d.id = dp.device_id
        where d.device_status = 0
        and p.pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>
    </select>

    <update id="updatePad">
        update pad
        set device_level = #{deviceLevel},data_size = #{storageCapacity}
        where status != -1 and pad_code in
        <foreach collection="padCodes" item="padCode" open="(" separator="," close=")">
            #{padCode}
        </foreach>

    </update>

    <update id="batchUpdatePadIp">
        <foreach collection="list" item="pad" index="index" separator=";">
            UPDATE pad
            SET pad_ip = #{pad.padIp}
            <if test="pad.armServerCode != null and pad.armServerCode != ''">
                ,arm_server_code = #{pad.armServerCode}
            </if>
            <if test="pad.netStorageResId != null and pad.netStorageResId != ''">
                ,net_storage_res_id = #{pad.netStorageResId}
            </if>
            WHERE pad_code = #{pad.padCode}
        </foreach>
    </update>

    <update id="batchUpdatePads">
        UPDATE pad
        SET
        device_level =
        <foreach collection="list" item="pad" separator=" " open="CASE id" close="END">
            WHEN #{pad.id} THEN #{pad.deviceLevel}
        </foreach>,
        net_storage_res_size =
        <foreach collection="list" item="pad" separator=" " open="CASE id" close="END">
            WHEN #{pad.id} THEN #{pad.netStorageResSize}
        </foreach>
        WHERE id IN
        <foreach collection="list" item="pad" separator="," open="(" close=")">
            #{pad.id}
        </foreach>
    </update>

    <select id="groupNetPadByDeviceLevel" resultType="net.armcloud.paascenter.openapi.model.vo.NetPadDeviceVO">
            SELECT
            SUM(CASE WHEN net_storage_res_flag = 1 THEN 1 ELSE 0 END) AS totalNumber,
            device_level
            FROM
            pad
            WHERE
            <if test="customerId != null">
                customer_id = #{customerId}
            </if>
            <if test="clusterCode != null and clusterCode != ''">
                AND cluster_code = #{clusterCode}
            </if>
            AND net_storage_res_flag = 1
            GROUP BY
            device_level
    </select>






    <select id="selectPadByPadIp" resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select p.pad_code from pad p
        left join device_pad dp on p.id = dp.pad_id
        left join device d on d.id = dp.device_id
        left join arm_server arm on arm.arm_server_code = p.arm_server_code
        where p.pad_ip = #{padIp} and arm.cluster_code = #{clusterCode} and p.`status` in (0,1) and d.delete_flag = 0;
    </select>

    <select id="selectPadByPadCodes" resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select id,pad_code as padCode,task_mode as taskMode,customer_id as customerId, net_storage_res_id as netStorageResId,online,image_id as imageId  from pad where `status` in (0,1) and pad_code in
        <foreach collection="padCodes" item="padCode" separator="," open="(" close=")">
            #{padCode}
        </foreach>
    </select>

    <select id="selectPadByPadCode" resultType="net.armcloud.paascenter.common.model.entity.paas.Pad">
        select id,pad_code as padCode,task_mode as taskMode,customer_id as customerId,net_storage_res_id as netStorageResId,image_id as imageId, cluster_code from pad where `status` in (0,1) and pad_code  = #{padCode}
    </select>

    <select id="countPullModePadByPadCodes" resultType="java.lang.Integer">
        select count(pad_code) from pad where `status` in (0,1) and task_mode = 1 and pad_code in
        <foreach collection="padCodes" item="padCode" separator="," open="(" close=")">
            #{padCode}
        </foreach>
    </select>
    <select id="getImageIdByPadCode"
            resultType="net.armcloud.paascenter.common.client.internal.vo.PadImageDetailVO">
        SELECT
            a.pad_code padCode,
            b.android_image_version imageVersion,
            c.android_image_version adiVersion,
            a.type,
            pp.properties_values padProperties
        FROM
            pad a
                INNER JOIN customer_upload_image b ON a.image_id = b.unique_id
                LEFT JOIN real_phone_template c ON a.real_phone_template_id = c.id
                LEFT JOIN pad_properties pp on pp.pad_code = a.pad_code
        WHERE
            a.pad_code = #{padCode}
    </select>

    <update id="updateTaskModeById">
        update pad set task_mode = #{taskMode} where id = #{id};
    </update>

    <!-- 批量插入Pad实例 -->
    <insert id="batchInsertPads" parameterType="java.util.List">
        INSERT INTO pad (
            pad_code, pad_out_code, cloud_vendor_type, device_level, customer_id, group_id, status,
            image_id, pad_sn, online, stream_status, stream_type, create_by, create_time, soc_model,
            cpu, memory, storage, screen_layout_code, real_phone_template_id, type, dns, data_size,
            net_storage_res_id, net_storage_res_size, net_storage_res_flag, cluster_code, country_code, mac
        ) VALUES
        <foreach collection="pads" item="pad" separator=",">
            (
                #{pad.padCode}, #{pad.padOutCode}, #{pad.cloudVendorType}, #{pad.deviceLevel},
                #{pad.customerId}, #{pad.groupId}, #{pad.status}, #{pad.imageId}, #{pad.padSn},
                #{pad.online}, #{pad.streamStatus}, #{pad.streamType}, #{pad.createBy}, #{pad.createTime},
                #{pad.socModel}, #{pad.cpu}, #{pad.memory}, #{pad.storage}, #{pad.screenLayoutCode},
                #{pad.realPhoneTemplateId}, #{pad.type}, #{pad.dns}, #{pad.dataSize},
                #{pad.netStorageResId}, #{pad.netStorageResSize}, #{pad.netStorageResFlag}, #{pad.clusterCode}, #{pad.countryCode}, #{pad.mac}
            )
        </foreach>
    </insert>

    <select id="selectUsePadIpsByArmServer" resultType="java.lang.String">
        select pad_ip
        from pad p
        inner join arm_server ser on p.arm_server_code = ser.arm_server_code
        where ser.id = #{armServerId}
        and p.status != -1
    </select>

    <select id="selectByMacList" resultType="java.lang.String">
        select mac from pad where mac in
        <foreach collection="macSet" item="mac" open="(" separator="," close=")">
            #{mac}
        </foreach>
    </select>
</mapper>
