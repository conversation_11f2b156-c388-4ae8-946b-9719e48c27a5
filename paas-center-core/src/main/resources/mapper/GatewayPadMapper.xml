<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.GatewayPadMapper">
    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.GatewayPad">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="netmask" jdbcType="VARCHAR" property="netmask" />
        <result column="gateway" jdbcType="VARCHAR" property="gateway" />
        <result column="ip_range" jdbcType="VARCHAR" property="ipRange" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <resultMap id="GetawayPadVo" type="net.armcloud.paascenter.openapi.model.vo.GatewayPadVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="netmask" jdbcType="VARCHAR" property="netmask" />
        <result column="gateway" jdbcType="VARCHAR" property="gateway" />
        <result column="ip_range" jdbcType="VARCHAR" property="ipRange" />
        <result column="delete_flag" jdbcType="TINYINT" property="deleteFlag" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="TIMESTAMP" property="createBy" />
        <result column="update_by" jdbcType="TIMESTAMP" property="updateBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, netmask, gateway, ip_range, status ,delete_flag, create_by, create_time, update_by,
    update_time
    </sql>
    <sql id="GetawayPadVo">
        gp.id, gp.netmask, gp.gateway, gp.ip_range, gp.delete_flag, gp.create_time,gp.status,d.dict_label as statusName, gp.create_by, gp.update_time,gp.update_by
    </sql>

    <select id="selectList" resultMap="GetawayPadVo">
        select
        <include refid="GetawayPadVo"/>
        from getaway_pad gp
        left join dict d on gp.status = d.dict_value and d.dict_type = 'customer_state'
        where delete_flag = 0
        <if test="gateway != null and gateway != '' ">
            and gp.gateway = #{gateway}
        </if>
        <if test="status != null">
            and gp.status = #{status}
        </if>
        order by gp.create_time asc
    </select>
    <select id="selectById" parameterType="java.lang.Long" resultMap="GetawayPadVo">
        select
        <include refid="Base_Column_List" />
        from getaway_pad
        where id = #{id,jdbcType=BIGINT}
    </select>
    <update id="delete">
        update getaway_pad set delete_flag = #{status} where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="countByNameAndNotDeleted" resultType="int" parameterType="string">
        SELECT COUNT(*)
        FROM getaway_pad
        WHERE gateway = #{gateway}
          AND delete_flag = 0
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.armcloud.paascenter.common.model.entity.paas.GatewayPad" useGeneratedKeys="true">
        insert into getaway_pad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="netmask != null">
                netmask,
            </if>
            <if test="gateway != null">
                gateway,
            </if>
            <if test="ipRange != null">
                ip_range,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="netmask != null">
                #{netmask,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="ipRange != null">
                #{ipRange,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="net.armcloud.paascenter.common.model.entity.paas.GatewayPad">
        update getaway_pad
        <set>
            <if test="netmask != null">
                netmask = #{netmask,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                gateway = #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="ipRange != null">
                ip_range = #{ipRange,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateGatewayPadStatus">
        update getaway_pad set status = #{status} where id = #{id}
    </update>
</mapper>