<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.openapi.mapper.CustomerUploadImageMapper">
    <select id="getAndroidVersionByImageUniqueId" resultType="java.lang.Integer">
        select android_image_version from customer_upload_image
        where unique_id = #{uniqueId}
    </select>

    <select id="getCustomerUploadImageInfoByUniqueId" resultType="net.armcloud.paascenter.openapi.model.vo.SelectImageInfoVO">
        select unique_id as imageId,
               status as  status,
               fail_msg as failMsg
        from customer_upload_image
        where unique_id = #{uniqueId}
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
          and delete_flag = 0;

    </select>

    <!-- getImageParameterByImageUniqueId --> 

    <select id="getImageParameterByImageUniqueId" resultType="java.lang.String">
        select image_parameter from customer_upload_image
        where unique_id = #{uniqueId}
    </select>

    <select id="getImageParameterByImageUniqueIdList"
            resultType="net.armcloud.paascenter.openapi.netpadv2.dto.CustomerUploadImageParameterDTO">
        select unique_id as imageId,
               image_parameter as imageParameter,
            image_url as imageUrl
        from customer_upload_image
        where unique_id in
        <foreach collection="imageIdList" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </select>
</mapper>