<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.callback.mapper.CallbackCustomerAccessMapper">

    <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.paas.CustomerAccess">
        <id column="id" property="id" />
        <result column="access_key_id" property="accessKeyId" />
        <result column="secret_access_key" property="secretAccessKey" />
        <result column="customer_id" property="customerId" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, access_key_id, secret_access_key, customer_id, `status`, create_by, create_time,
        update_by, update_time
    </sql>

    <select id="getByCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from customer_access
        where customer_id = #{customerId}
          and status = 1
    </select>
</mapper>