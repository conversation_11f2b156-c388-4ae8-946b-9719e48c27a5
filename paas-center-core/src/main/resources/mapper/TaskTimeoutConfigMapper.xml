<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.task.mapper.TaskTimeoutConfigMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.task.TaskTimeoutConfig">
    <id column="id" property="id" />
    <result column="task_type" property="taskType" />
    <result column="timeout_millisecond" property="timeoutMillisecond" />
    <result column="delete_flag" property="deleteFlag" />
    <result column="create_by" property="createBy" />
    <result column="create_time" property="createTime" />
    <result column="update_by" property="updateBy" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, task_type, timeout_millisecond, delete_flag, create_by, create_time, update_by,
    update_time
  </sql>

  <select id="getByTaskType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from task_timeout_config
    where task_type = #{taskType}
    and delete_flag = 0
    limit 1
  </select>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from task_timeout_config
    where delete_flag = 0
  </select>

</mapper>