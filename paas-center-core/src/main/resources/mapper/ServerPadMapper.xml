<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.armcloud.paascenter.commscenter.mapper.ServerPadMapper">
  <resultMap id="BaseResultMap" type="net.armcloud.paascenter.common.model.entity.comms.ServerPad">
    <id column="id" property="id" />
    <result column="pad_code" property="padCode" />
    <result column="comms_server_id" property="commsServerId" />
    <result column="channel_id" property="channelId" />
    <result column="trade_id" property="tradeId" />
    <result column="ts" property="ts" />
    <result column="create_time" property="createTime" />
    <result column="create_by" property="createBy" />
    <result column="update_time" property="updateTime" />
    <result column="update_by" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    id, pad_code, comms_server_id, channel_id, ts, trade_id, create_time, create_by, update_time, update_by
  </sql>

  <insert id="insert">
      insert into server_pad(pad_code, comms_server_id, channel_id, ts, trade_id)
      value (#{padCode}, #{commsServerId}, #{channelId}, #{ts}, #{tradeId})
  </insert>

  <select id="getByPadCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from server_pad
    where pad_code = #{padCode}
  </select>

  <select id="getByPadCode2" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from server_pad
    where pad_code = #{padCode}
    AND comms_server_id IS NOT NULL
    LIMIT 1
  </select>

  <select id="listPadCodeByServerId" resultType="java.lang.String">
    select pad_code from server_pad where comms_server_id = #{serverId}
  </select>
</mapper>