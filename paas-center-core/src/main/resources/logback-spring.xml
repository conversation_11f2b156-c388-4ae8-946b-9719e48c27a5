<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="app"/>
    <springProperty scope="context" name="slsEnabled" source="aliyun.sls.enabled" defaultValue="false"/>

    <property name="app-name" value="${appName}"/>
    <property name="log_dir" value="logs"/>
    <property name="pattern"
              value="[%X{traceId:-}] ${appName} %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -| %msg%n"/>
    <property name="maxHistory" value="2"/>
    <property name="maxFileSize" value="100MB"/>
    <property name="totalSizeCap" value="10GB"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 应用日志文件 - 按时间和大小双重切片 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}/${app-name}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_dir}/${app-name}_%d{yyyy-MM-dd}_%i.log.gz</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${pattern}</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}/${app-name}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_dir}/${app-name}-error_%d{yyyy-MM-dd}_%i.log.gz</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${pattern}</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 业务日志文件 -->
    <appender name="BUSINESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}/${app-name}-business.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_dir}/${app-name}-business_%d{yyyy-MM-dd}_%i.log.gz</fileNamePattern>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
                <pattern>${pattern}</pattern>
            </layout>
        </encoder>
    </appender>

    <!-- SLS日志Appender - 接收所有级别的日志 -->
    <appender name="SLS" class="net.armcloud.paascenter.common.log.SlsLogbackAppender">
        <!-- 不设置过滤器，接收所有级别的日志，与文件日志保持一致 -->
    </appender>

    <!-- SLS异步Appender包装器 - 高并发优化 -->
    <appender name="ASYNC_SLS" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>30</discardingThreshold>  <!-- 队列70%满时开始丢弃SLS日志 -->
        <queueSize>65536</queueSize>  <!-- 64K队列，专门应对SLS高并发 -->
        <includeCallerData>false</includeCallerData>  <!-- 关闭调用栈，提高性能 -->
        <maxFlushTime>3000</maxFlushTime>  <!-- SLS快速刷新 -->
        <neverBlock>true</neverBlock>  <!-- 永不阻塞主线程 -->
        <appender-ref ref="SLS"/>
    </appender>

    <!-- 异步Appender包装器 - 高并发优化 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>20</discardingThreshold>  <!-- 队列80%满时开始丢弃 -->
        <queueSize>32768</queueSize>  <!-- 32K队列，应对高并发 -->
        <includeCallerData>false</includeCallerData>  <!-- 关闭调用栈，提高性能 -->
        <maxFlushTime>5000</maxFlushTime>  <!-- 最大刷新时间 -->
        <appender-ref ref="APP_FILE"/>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>  <!-- 错误日志不丢弃 -->
        <queueSize>8192</queueSize>  <!-- 错误日志相对较少 -->
        <includeCallerData>false</includeCallerData>
        <maxFlushTime>5000</maxFlushTime>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!-- 业务日志配置 -->
    <logger name="BUSINESS" level="INFO" additivity="false">
        <appender-ref ref="BUSINESS_FILE"/>
        <appender-ref ref="ASYNC_SLS"/>
    </logger>

    <!-- 第三方库日志级别控制 -->
    <logger name="com.aliyun" level="WARN"/>
    <logger name="org.apache.http" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="com.alibaba.druid" level="WARN"/>
    <logger name="org.mybatis" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="WARN"/>

    <!-- Root Logger - 确保所有日志都同时输出到文件和SLS -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC_APP_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
        <appender-ref ref="ASYNC_SLS"/>
    </root>

</configuration>