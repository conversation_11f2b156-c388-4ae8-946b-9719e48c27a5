

-- 1. 实例与算力单元关联关系表
CREATE TABLE `net_pad_compute_unit_relation` (
                                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                 `pad_code` varchar(64) NOT NULL COMMENT '实例编码',
                                                 `net_storage_compute_unit_code` varchar(64) NOT NULL COMMENT '算力单元编码',
                                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                                 `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                                 PRIMARY KEY (`id`),
                                                 UNIQUE KEY `uk_pad_code` (`pad_code`),
                                                 KEY `idx_compute_unit_code` (`net_storage_compute_unit_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网存实例v2与算力单元关联关系表';

-- 2. 实例与存储关联关系表
CREATE TABLE `net_pad_res_unit_relation` (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                             `pad_code` varchar(64) NOT NULL COMMENT '实例编码',
                                             `net_storage_res_unit_code` varchar(64) NOT NULL COMMENT '存储单元编码',
                                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                             `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                             `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `uk_pad_code` (`pad_code`),
                                             KEY `idx_storage_unit_code` (`net_storage_res_unit_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网存实例v2与网络存储关联关系表';


-- 网存实例V2创建记录表（简化版本）
CREATE TABLE `net_pad_v2_create_record` (
                                            `pad_code` varchar(64) NOT NULL COMMENT '实例编码，主键',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            PRIMARY KEY (`pad_code`),
                                            KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网存实例V2创建记录表';

ALTER TABLE `armcloud_new_paas`.`pad_task`
    ADD COLUMN `custom_timeout` int NULL COMMENT '客户自定义超时时间（秒）',
    ADD COLUMN `type` varchar(10) NULL COMMENT '任务类型';

ALTER TABLE `armcloud_new_paas`.`pad`
    ADD COLUMN `country_code` varchar(10) NULL COMMENT '国家代码';


INSERT INTO `armcloud_new_paas`.`task_timeout_config` (`task_type`, `timeout_millisecond`, `delete_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (1303, 900000, 0, NULL, now(), NULL, now());
INSERT INTO `armcloud_new_paas`.`task_timeout_config` (`task_type`, `timeout_millisecond`, `delete_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (1302, 900000, 0, NULL, now(), NULL, now());
INSERT INTO `armcloud_new_paas`.`task_timeout_config` (`task_type`, `timeout_millisecond`, `delete_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (1301, 900000, 0, NULL, now(), NULL, now());

ALTER TABLE `armcloud_new_paas`.`net_storage_res_off_log`
    ADD INDEX `idx_padCode`(`pad_code`) USING BTREE COMMENT '实例编号索引';

ALTER TABLE `armcloud_new_paas`.`pad`
    ADD INDEX `idx_mac`(`mac`) USING BTREE;

INSERT INTO `armcloud_new_paas`.`dict` (`language`, `dict_name`, `dict_label`, `dict_type`, `dict_value`, `dict_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('zh', '任务类型', '网存2.0开机', 'task_type', '1301', 1, 0, NULL, '2024-12-03 18:59:00', NULL, '2025-03-18 11:04:07');
INSERT INTO `armcloud_new_paas`.`dict` (`language`, `dict_name`, `dict_label`, `dict_type`, `dict_value`, `dict_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('zh', '任务类型', '网存2.0关机', 'task_type', '1302', 1, 0, NULL, '2024-12-03 18:59:00', NULL, '2025-03-18 11:04:07');
INSERT INTO `armcloud_new_paas`.`dict` (`language`, `dict_name`, `dict_label`, `dict_type`, `dict_value`, `dict_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('zh', '任务类型', '网存2.0删除', 'task_type', '1303', 1, 0, NULL, '2024-12-03 18:59:00', NULL, '2025-03-18 11:04:07');

INSERT INTO `armcloud_new_paas`.`dict` (`language`, `dict_name`, `dict_label`, `dict_type`, `dict_value`, `dict_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('zh', '实例任务类型', '网存2.0开机', 'instance_task_type', '1301', 1, 0, NULL, '2024-12-03 18:59:00', NULL, '2025-03-18 11:04:07');
INSERT INTO `armcloud_new_paas`.`dict` (`language`, `dict_name`, `dict_label`, `dict_type`, `dict_value`, `dict_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('zh', '实例任务类型', '网存2.0关机', 'instance_task_type', '1302', 1, 0, NULL, '2024-12-03 18:59:00', NULL, '2025-03-18 11:04:07');
INSERT INTO `armcloud_new_paas`.`dict` (`language`, `dict_name`, `dict_label`, `dict_type`, `dict_value`, `dict_sort`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('zh', '实例任务类型', '网存2.0删除', 'instance_task_type', '1303', 1, 0, NULL, '2024-12-03 18:59:00', NULL, '2025-03-18 11:04:07');

INSERT INTO `armcloud_new_paas`.`callback_information` (`type`, `description`, `delete_flag`, `create_time`, `create_by`, `update_time`, `update_by`, `task_business_type`) VALUES (30, '网存2.0开机', 0, now(), '',  now(), '', '1301');
INSERT INTO `armcloud_new_paas`.`callback_information` (`type`, `description`, `delete_flag`, `create_time`, `create_by`, `update_time`, `update_by`, `task_business_type`) VALUES (31, '网存2.0关机', 0, now(), '',  now(), '', '1302');
INSERT INTO `armcloud_new_paas`.`callback_information` (`type`, `description`, `delete_flag`, `create_time`, `create_by`, `update_time`, `update_by`, `task_business_type`) VALUES (32, '网存2.0删除', 0, now(), '',  now(), '', '1303');


