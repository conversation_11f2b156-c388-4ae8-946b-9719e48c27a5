-- 网络存储单元删除记录表
CREATE TABLE `net_storage_res_unit_deleted` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `original_id` BIGINT NOT NULL COMMENT '原记录ID',
    `net_storage_res_unit_id` BIGINT NOT NULL COMMENT '网络存储详情ID',
    `customer_id` BIGINT NOT NULL COMMENT '客户ID',
    `shutdown_flag` INT DEFAULT 0 COMMENT '是否有实例开关机 0: 关机,1: 开机',
    `net_storage_res_unit_code` VARCHAR(255) NOT NULL COMMENT '网络存储详情Code',
    `cluster_code` VARCHAR(255) NOT NULL COMMENT '集群Code',
    `pad_code` VARCHAR(255) NOT NULL COMMENT '实例Code',
    `net_storage_res_unit_size` BIGINT NOT NULL COMMENT '网络存储大小(单位:GB)',
    `remark` VARCHAR(500) COMMENT '备注',
    `create_time` DATETIME COMMENT '原记录创建时间',
    `update_time` DATETIME COMMENT '原记录更新时间',
    `create_by` VARCHAR(255) COMMENT '原记录创建者',
    `update_by` VARCHAR(255) COMMENT '原记录更新者',
    `deleted_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '删除时间',
    `deleted_by` VARCHAR(255) COMMENT '删除操作者',
    `delete_reason` VARCHAR(500) COMMENT '删除原因',
    PRIMARY KEY (`id`),
    INDEX `idx_original_id` (`original_id`),
    INDEX `idx_customer_id` (`customer_id`),
    INDEX `idx_net_storage_res_unit_code` (`net_storage_res_unit_code`),
    INDEX `idx_deleted_time` (`deleted_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网络存储单元删除记录表';
