# NoticeEdgeClusterManager 使用说明

## 概述

`NoticeEdgeClusterManager` 是一个基于Redis实现的集群通知失败计数管理器，提供分钟级的失败计数功能，当失败次数超过阈值时自动发送钉钉预警。

## 功能特性

- ✅ **分钟级计数**：基于Redis实现精确的分钟级失败计数
- ✅ **自动预警**：失败次数超过阈值时自动发送钉钉预警
- ✅ **美化消息**：使用Markdown格式美化预警消息
- ✅ **防重复预警**：预警间隔控制，避免频繁发送预警
- ✅ **异常处理**：完善的异常处理机制
- ✅ **高性能**：基于Redis原子操作，支持高并发场景

## 核心方法

### 1. recordNotificationFailure(String clusterAddress)

记录集群通知失败计数

**参数：**
- `clusterAddress`: 集群通知地址

**返回值：**
- `int`: 当前分钟内的失败次数

**示例：**
```java
@Autowired
private NoticeEdgeClusterManager noticeEdgeClusterManager;

// 记录失败
int failureCount = noticeEdgeClusterManager.recordNotificationFailure("http://cluster1:8080/api/notify");
log.info("当前分钟内失败次数: {}", failureCount);
```

### 2. getFailureCount(String clusterAddress)

获取指定集群地址当前分钟内的失败次数

**参数：**
- `clusterAddress`: 集群通知地址

**返回值：**
- `int`: 当前分钟内的失败次数

**示例：**
```java
int currentFailures = noticeEdgeClusterManager.getFailureCount("http://cluster1:8080/api/notify");
if (currentFailures > 3) {
    log.warn("集群通知失败次数较高: {}", currentFailures);
}
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| FAILURE_THRESHOLD | 5 | 失败计数阈值，超过此值发送预警 |
| COUNTER_EXPIRE_MINUTES | 1 | 计数器过期时间（分钟） |
| ALERT_INTERVAL_MINUTES | 5 | 预警发送间隔（分钟） |

## Redis Key 设计

### 计数器Key格式
```
paas-center-core:counter:cluster_notification_failure:{yyyyMMddHHmm}:{地址hash}
```

**示例：**
```
paas-center-core:counter:cluster_notification_failure:202508051430:1234567890
```

### 预警间隔Key格式
```
paas-center-core:cache:cluster_notification_alert:{地址hash}
```

## 预警消息格式

当失败次数超过阈值时，会发送如下格式的钉钉预警：

```markdown
## 🚨 集群通知失败预警

### 📊 预警详情
- **集群地址：** `http://cluster1:8080/api/notify`
- **失败次数：** 6 次
- **统计周期：** 1分钟内
- **预警阈值：** 5 次
- **预警时间：** 2025-08-05 14:30:25

### 🔍 问题分析
集群通知在短时间内出现多次失败，可能原因：
- 🌐 网络连接异常
- 🖥️ 目标集群服务不可用
- ⚙️ 集群配置错误
- 🔒 认证或权限问题

### 🛠️ 建议操作
1. **检查网络连通性** - 确认到目标集群的网络是否正常
2. **验证集群状态** - 检查目标集群服务是否正常运行
3. **核实配置信息** - 确认集群地址和配置参数是否正确
4. **查看详细日志** - 分析具体的错误信息和堆栈跟踪

### ⚠️ 影响评估
- **影响范围：** 集群通知功能
- **紧急程度：** 🔴 高
- **建议处理时间：** 立即处理
```

## 使用示例

### 基本使用

```java
@Service
@AllArgsConstructor
public class ClusterNotificationService {
    
    private final NoticeEdgeClusterManager noticeEdgeClusterManager;
    
    public boolean sendNotification(String clusterAddress, String message) {
        try {
            // 发送通知逻辑
            boolean success = doSendNotification(clusterAddress, message);
            
            if (!success) {
                // 记录失败
                int failureCount = noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
                log.warn("通知发送失败，失败次数: {}", failureCount);
            }
            
            return success;
        } catch (Exception e) {
            // 异常也记录失败
            noticeEdgeClusterManager.recordNotificationFailure(clusterAddress);
            throw e;
        }
    }
}
```

### 健康检查

```java
public boolean isClusterHealthy(String clusterAddress) {
    int failureCount = noticeEdgeClusterManager.getFailureCount(clusterAddress);
    return failureCount < 3; // 失败次数小于3认为健康
}
```

### 批量处理

```java
public void sendBatchNotifications(List<String> clusters, String message) {
    for (String cluster : clusters) {
        try {
            sendNotification(cluster, message);
        } catch (Exception e) {
            log.error("发送到集群 {} 失败", cluster, e);
        }
    }
}
```

## 注意事项

1. **地址格式**：集群地址应该是完整的URL，包含协议、主机、端口等信息
2. **并发安全**：基于Redis原子操作，天然支持并发场景
3. **内存使用**：计数器会自动过期，不会占用过多Redis内存
4. **预警频率**：有预警间隔控制，避免短时间内重复发送预警
5. **异常处理**：所有方法都有完善的异常处理，不会影响主业务流程

## 监控建议

1. **Redis监控**：监控Redis的内存使用和连接状态
2. **预警统计**：统计预警发送频率，分析系统稳定性
3. **失败趋势**：分析失败计数趋势，提前发现问题
4. **集群状态**：结合集群监控，全面了解系统健康状态

## 扩展建议

1. **自定义阈值**：可以为不同集群设置不同的失败阈值
2. **多级预警**：可以设置多个预警级别（警告、严重、紧急）
3. **历史统计**：可以增加历史失败统计功能
4. **自动恢复**：可以增加自动重试和恢复机制
