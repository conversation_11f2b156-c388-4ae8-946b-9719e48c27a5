#### [**网存实例V2批量开机**]

用于对多个网存实例进行批量开机操作，支持传入DNS配置、国家编码、安卓属性等参数进行个性化配置。该接口支持首次开机和非首次开机的不同处理流程，具有完善的参数校验、资源分配和异常处理机制。

**接口地址**

> /openapi/netpadv2/net/storage/batch/boot/on

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名|示例值|参数类型|是否必填|参数描述|
|---|---|---|---|---|
|padCodes|["ACN250321HRKNE3F", "ACN250321HRKNE3G"]|String[]|是|需要开机的实例编码列表，数量范围1-200个|
|dns|8.8.8.8|String|否|DNS配置|
|countryCode|SG|String|否|国家编码(具体查看:[https://chahuo.com/country-code-lookup.html](https://chahuo.com/country-code-lookup.html))|
|androidProp|{"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"}|Object|否|安卓属性配置，参考 [安卓改机属性列表](https://docs.armcloud.net/cn/server/InstanceAndroidPropList.html)|
|timeout|1800|Integer|否|超时时间（秒），范围300-7200秒（5分钟-120分钟）|

**响应参数**

| 参数名         | 示例值              | 参数类型     | 参数描述          |
| ----------- | ---------------- | -------- | ------------- |
| code        | 200              | Integer  | 状态码（200表示成功）  |
| msg         | success          | String   | 接口请求状态信息      |
| ts          | 1742536327373    | Long     | 时间戳           |
| data        | [ {...} ]        | Object[] | 实例任务信息列表      |
| ├─ taskId   | 13023            | Integer  | 后台任务ID        |
| ├─ padCode  | ACN250321HRKNE3F | String   | 实例编码          |
| ├─ vmStatus | 0                | Integer  | 实例在线状态（0表示离线，1表示在线） |
| ├─ taskStatus | 1              | Integer  | 任务状态（-1：任务已存在，请勿重复提交；1：任务已添加） |

**请求示例**

```
{
  "padCodes": ["ACN250321HRKNE3F", "ACN250321HRKNE3G"],
  "dns": "8.8.8.8",
  "countryCode": "US",
  "androidProp": {"persist.sys.cloud.wifi.mac": "D2:48:83:70:66:0B"},
  "timeout": 1800
}
```

**响应示例**

```
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "taskId": 13023,
      "padCode": "ACN250321HRKNE3F",
      "vmStatus": 0,
      "taskStatus": 1
    },
    {
      "taskId": 13024,
      "padCode": "ACN250321HRKNE3G",
      "vmStatus": 0,
      "taskStatus": 1
    }
  ],
  "ts": 1742536327373
}
```

**错误码**

| 错误码    | 错误说明                    | 操作建议                     |
| ------ |-------------------------|--------------------------|
| 100000 | 请求参数不能为空                | 检查请求参数是否完整               |
| 100000 | 实例编码列表不能为空              | 确保padCodes参数不为空          |
| 100000 | 实例数量范围1-200             | 确保padCodes数量在1-200范围内    |
| 100000 | 超时时间必须在5分钟-120分钟之间      | 确保timeout参数在300-7200秒范围内 |
| 110042 | 存在不属于当前用户的实例            | 检查实例是否属于当前用户             |
| 110071 | 存在非网存实例                 | 确认实例类型为网存实例              |
| 110079 | 存在未释放算力的实例              | 等待算力释放后重试                |
| 111070 | 存在正在开机的实例，无法重复操作        | 等待当前开机操作完成后重试            |
| 111076 | 存在正在关机的实例，无法同时操作        | 等待关机操作完成后重试              |
| 111071 | 非开机失败且未关机的实例不允许操作开机     | 确认实例状态为关机或开机失败状态         |
| 111074 | 网存实例批量开机失败（系统异常）        | 系统异常，请稍后重试或联系技术支持        |
| 110073 | 算力资源不足，当前剩余%s个算力单元      | 等待算力资源释放或联系管理员           |
| 110074 | 获取算力单元失败，请重试            | 稍后重试或联系管理员               |
| 110075 | 获取实例IP失败，请重试            | 稍后重试                     |
| 110076 | ARM服务器网段不存在             | 联系管理员检查网段配置              |
| 110077 | 获取实例MAC失败，请重试           | 稍后重试                     |
| 110039 | 任务添加失败                  | 稍后重试或联系技术支持              |
| 2200014 | 当前集群下没有可用的算力资源,请联系管理员处理 | 联系管理员处理算力资源问题            |
