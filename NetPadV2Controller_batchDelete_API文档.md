#### **网存实例V2批量删除**

用于批量删除网存实例，支持传入多个实例进行批量删除操作。删除操作会创建异步任务，通过任务ID可以查询删除进度和结果。

**接口地址**

> /openapi/netpadv2/net/storage/batch/delete

**请求方式**

> POST

**请求数据类型**

> application/json

**请求Body参数**

|参数名|示例值|参数类型|是否必填|参数描述|
|---|---|---|---|---|
|padCodes|["ACN250321HRKNE3F", "ACN250321HRKNE3G"]|String[]|是|需要删除的实例编码列表，数量范围1-200个|
|timeout|1800|Integer|否|超时时间（秒），范围300-7200秒（5分钟-120分钟）|

**响应参数**

| 参数名         | 示例值              | 参数类型     | 参数描述          |
| ----------- | ---------------- | -------- | ------------- |
| code        | 200              | Integer  | 状态码（200表示成功）  |
| msg         | success          | String   | 接口请求状态信息      |
| ts          | 1742536327373    | Long     | 时间戳           |
| data        | [ {...} ]        | Object[] | 删除任务信息列表      |
| ├─ padCode  | ACN250321HRKNE3F | String   | 实例编码          |
| ├─ vmStatus | 0                | Integer  | 实例状态          |
| ├─ taskId   | 13023            | Integer  | 后台删除任务ID      |

**请求示例**

```json
{
  "padCodes": ["ACN250321HRKNE3F", "ACN250321HRKNE3G"],
  "timeout": 1800
}
```

**响应示例**

```json
{
  "msg": "success",
  "code": 200,
  "data": [
    {
      "padCode": "ACN250321HRKNE3F",
      "vmStatus": 0,
      "taskId": 13023
    },
    {
      "padCode": "ACN250321HRKNE3G", 
      "vmStatus": 0,
      "taskId": 13024
    }
  ],
  "ts": 1742536327373
}
```

**错误码**

| 错误码    | 错误说明                       | 操作建议                       |
| ------ | -------------------------- |----------------------------|
| 100001 | 请求参数不能为空                   | 检查请求体是否为空                  |
| 100001 | 实例编码列表不能为空                 | 检查padCodes参数是否传递           |
| 100001 | 实例数量范围1-200                | 检查padCodes数组长度是否在有效范围内     |
| 100001 | 超时时间必须在5分钟-120分钟之间         | 检查timeout参数是否在300-7200秒范围内 |
| 110028 | 实例不存在                      | 检查传入的实例编码是否正确              |
| 110042 | 存在不属于当前用户的实例               | 检查实例是否属于当前登录用户             |
| 110071 | 存在非网存实例                    | 确认传入的实例都是网存实例              |
| 111070 | 存在正在开机的实例           | 等待开机操作完成后再进行删除             |
| 111073 | 非关机状态实例不允许删除               | 确保所有实例都处于关机状态再进行删除         |
| 111077 | 存在正在删除的实例               | 等待删除操作完成后再进行删除             |
| 220010 | 一次只允许操作一个用户下的网存实例          | 确保批量操作的实例都属于同一用户           |

